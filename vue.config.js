const path = require('path')

const CompressionWebpackPlugin = require('compression-webpack-plugin')

const compress = new CompressionWebpackPlugin(
  {
    filename: '[path][base].gz',
    algorithm: 'gzip',
    threshold: 10240,
    test: new RegExp(
      '\\.(' +
      ['js', 'css'].join('|') +
      ')$'
    ),
    minRatio: 0.8,
    deleteOriginalAssets: false
  }
)

const port = 7000

function resolve(dir) {
  return path.join(__dirname, dir)
}

// cdn链接
const cdn = {
  // cdn：模块名称和模块作用域命名（对应window里面挂载的变量名称）
  externals: {
    'vue': 'Vue',
    'vue-router': 'VueRouter',
    'moment': 'moment',
    'vuex': 'Vuex',
    'element-ui': 'ELEMENT',
    'core-js': 'core-js'
  },
  // cdn的css链接
  css: [
    'https://front-end-huawei-cdn.devops.cndinfo.com/npm/element-ui@2.15.6/lib/theme-chalk/index.css'
  ],
  // cdn的js链接
  js: [
    'https://front-end-huawei-cdn.devops.cndinfo.com/lang/zh.min.js',
    'https://front-end-huawei-cdn.devops.cndinfo.com/lang/en.min.js',
    'https://front-end-huawei-cdn.devops.cndinfo.com/npm/vue@2.6.14/dist/vue.min.js',
    'https://front-end-huawei-cdn.devops.cndinfo.com/npm/vuex@3.6.2/dist/vuex.min.js',
    'https://front-end-huawei-cdn.devops.cndinfo.com/npm/element-ui@2.15.6/lib/index.min.js',
    'https://front-end-huawei-cdn.devops.cndinfo.com/npm/element-ui@2.15.6/lib/umd/locale/en.min.js',
    'https://front-end-huawei-cdn.devops.cndinfo.com/npm/element-ui@2.15.6/lib/umd/locale/zh-CN.min.js',
    'https://front-end-huawei-cdn.devops.cndinfo.com/npm/core-js@2.6.5/client/shim.min.js',
    'https://front-end-huawei-cdn.devops.cndinfo.com/npm/moment@2.29.1/moment.min.js',
    'https://front-end-huawei-cdn.devops.cndinfo.com/npm/vue-router@3.5.2/dist/vue-router.min.js',
    'https://res.wx.qq.com/open/js/jweixin-1.6.0.js'
  ]
}
const webpack = require('webpack')

module.exports = {
  publicPath: '/',
  outputDir: 'dist',
  assetsDir: 'assets',
  transpileDependencies: ['core-js', 'cnd-icon', 'cnd-horizon-utils', 'cnd-utils', 'element-ui'],
  productionSourceMap: process.env.NODE_ENV === 'development',
  lintOnSave: true, // 如果为false，就是取消eslint规则的检查
  devServer: {
    port,
    open: true,
    hot: true,
    overlay: {
      warnings: false,
      errors: true
    },
    proxy: {
      // change xxx-api/login => mock/login
      // detail: https://cli.vuejs.org/config/#devserver-proxy
      [process.env.VUE_APP_BASE_API]: {
        // target: `http://172.18.39.200:8080/esc`,
        target: `http://10.193.2.162:32660/esc`,
        // target: 'http://172.18.49.116:8080/esc',
        changeOrigin: true,
        pathRewrite: {
          ['^' + process.env.VUE_APP_BASE_API]: ''
        }
      },
      // /custom-api
      [process.env.VUE_APP_CUSTOM_API]: {
        target: 'http://10.193.2.162:32664/',
        changeOrigin: true,
        pathRewrite: {
          ['^' + process.env.VUE_APP_CUSTOM_API]: ''
        }
      }
    }
  },
  configureWebpack: ({
    devtool: false,
    module: {
      rules: [
        {
          test: /\.js$/,
          use: ['thread-loader'],
          include: [
            resolve('src'),
            resolve('node_modules/element-ui/src'),
            resolve('node_modules/core-js'),
            resolve('node_modules/cnd-horizon-utils/src'),
            resolve('node_modules/cnd-utils/src')
          ]
        }
      ]
    },
    resolve: {
      alias: {
        '@': resolve('src'),
        'assets': resolve('src/assets'),
        'components': resolve('src/components'),
        'api': resolve('src/api'),
        'layout': resolve('src/layout'),
        'static': resolve('src/static')
      }
    },
    externals: cdn.externals,
    plugins: [
      compress
    ]
  }),
  chainWebpack: config => {
    // config.output.filename('assets/js/[name].[hash:8].js').chunkFilename('assets/js/[name].[hash:8].js').end()
    config.plugin('extract-css').tap(args => [{
      filename: 'assets/css/[name].[hash:8].css',
      chunkFilename: 'assets/css/[name].[hash:8].css'
    }])
    if (process.env.NODE_ENV !== 'development') {
      config.optimization.minimize(true)
      config.optimization.splitChunks({
        chunks: 'all'
      })
    }

    // ============注入cdn start============
    config.plugin('html').tap(args => {
      // 生产环境或本地需要cdn时，才注入cdn
      // if (isProduction) {
      args[0].cdn = cdn
      // }
      return args
    })
    // ============注入cdn start============
    // 移除 prefetch 插件
    config.plugins.delete('prefetch')
    // 移除 preload 插件
    config.plugins.delete('preload')
    // 本地测试npm包
    config.resolve.symlinks(false)

    config
      .plugin('ignore')
      .use(
        new webpack.ContextReplacementPlugin(/moment[/\\]locale$/, /zh-cn$/)
      )
  },
  filenameHashing: false,
  // css相关配置
  css: {
    // // 是否使用css分离插件 ExtractTextPlugin
    extract: true,
    // 开启 CSS source maps?
    sourceMap: false
    // // css预设器配置项
    // loaderOptions: {
    //   // // pass options to sass-loader
    //   sass: {
    //     // 引入全局变量样式
    //     prependData: `@import '@/styles/index.scss';`
    //   }
    // }
    // // 启用 CSS modules for all css / pre-processor files.
    // requireModuleExtension: false
  }

}
