const path = require('path')
const { name } = require('./package.json')

const CompressionWebpackPlugin = require('compression-webpack-plugin')
const port = 7008

function resolve(dir) {
  return path.join(__dirname, dir)
}

const compress = new CompressionWebpackPlugin(
  {
    filename: '[path][base].gz',
    algorithm: 'gzip',
    threshold: 10240,
    test: new RegExp(
      '\\.(' +
      ['js'].join('|') +
      ')$'
    ),
    minRatio: 0.8,
    deleteOriginalAssets: false
  }
)

// cdn链接
const cdn = {
  // cdn：模块名称和模块作用域命名（对应window里面挂载的变量名称）
  externals: {
    vue: 'Vue',
    'vue-router': 'VueRouter',
    moment: 'moment',
    vuex: 'Vuex',
    'element-ui': 'ELEMENT',
    'core-js': 'core-js',
    xlsx: 'XLSX',
    'decimal.js': 'decimal.js'
  },
  // cdn的css链接
  css: [
    'https://front-end-huawei-cdn.devops.cndinfo.com/npm/element-ui@2.15.6/lib/theme-chalk/index.css'
  ],
  // cdn的js链接
  js: [
    'https://front-end-huawei-cdn.devops.cndinfo.com/lang/en.min.js',
    'https://front-end-huawei-cdn.devops.cndinfo.com/lang/zh.min.js',
    'https://front-end-huawei-cdn.devops.cndinfo.com/npm/vue@2.6.14/dist/vue.min.js',
    'https://front-end-huawei-cdn.devops.cndinfo.com/npm/vuex@3.6.2/dist/vuex.min.js',
    'https://front-end-huawei-cdn.devops.cndinfo.com/npm/element-ui@2.15.6/lib/index.min.js',
    'https://front-end-huawei-cdn.devops.cndinfo.com/npm/element-ui@2.15.6/lib/umd/locale/en.min.js',
    'https://front-end-huawei-cdn.devops.cndinfo.com/npm/element-ui@2.15.6/lib/umd/locale/zh-CN.min.js',
    'https://front-end-huawei-cdn.devops.cndinfo.com/npm/core-js@2.6.5/client/shim.min.js',
    'https://front-end-huawei-cdn.devops.cndinfo.com/npm/moment@2.29.1/moment.min.js',
    'https://front-end-huawei-cdn.devops.cndinfo.com/npm/vue-router@3.5.2/dist/vue-router.min.js',
    'https://front-end-huawei-cdn.devops.cndinfo.com/npm/xlsx@0.17.3/xlsx.mini.js',
    'https://front-end-huawei-cdn.devops.cndinfo.com/npm/decimal.js@10.3.1/decimal.min.js'
  ]
}

module.exports = {
  transpileDependencies: ['core-js', 'cnd-icon', 'cnd-horizon-utils', 'cnd-utils', 'cnd-horizon-ui', 'cndinfo-ui', 'element-ui'],
  runtimeCompiler: true,
  publicPath: process.env.NODE_ENV === 'development' ? '/' : '/child/egl/',
  outputDir: 'dist',
  assetsDir: 'static',
  productionSourceMap:
    process.env.NODE_ENV === 'development',
  devServer: {
    headers: { 'Access-Control-Allow-Origin': '*' },
    overlay: {
      warnings: false,
      errors: true
    },
    port: port,
    open: true,
    inline: true,
    hot: true,
    proxy: {
      // change xxx-api/login => mock/login
      // detail: https://cli.vuejs.org/config/#devserver-proxy
      [process.env.VUE_APP_BASE_API]: {
        // mock地址
        // target: 'http://121.37.190.7:8081/horizon',
        // target: 'http://172.27.254.14:32625/esc',
        // 测试
        // target: 'http://172.18.39.200:8080/esc',
        // target: 'http://172.18.49.116:8080/esc',
        // 预生产
        // target: 'http://172.18.39.131:3001/api',

        // 测试
        target: `http://10.193.2.162:32660/esc`,
        // 预生产
        // target: `http://10.193.2.162:32760/esc`,
        changeOrigin: true,
        pathRewrite: {
          ['^' + process.env.VUE_APP_BASE_API]: ''
        }
      },
      '/custom-api': {
        target: 'http://10.193.2.162:32664/',
        changeOrigin: true,
        pathRewrite: {
          '^/custom-api': ''
        }
      }
    }
  },
  configureWebpack: ({
    name: name,
    devtool: false,
    module: {
      rules: [
        {
          test: /\.js$/,
          use: ['thread-loader']
        }
      ]
    },
    resolve: {
      alias: {
        '@': resolve('src'),
        assets: resolve('src/assets'),
        components: resolve('src/components'),
        api: resolve('src/api'),
        layout: resolve('src/layout'),
        static: resolve('src/static')
      }
    },
    output: {
      // 把子应用打包成 umd 库格式
      library: `${name}-[name]`,
      libraryTarget: 'umd',
      jsonpFunction: `webpackJsonp_${name}`
    },
    externals: cdn.externals,
    plugins: [
      compress
    ]
  }),
  chainWebpack: (config) => {
    // config.output.filename('assets/js/[name].[hash:8].js').chunkFilename('assets/js/[name].[hash:8].js').end()
    // config.plugin('extract-css').tap(args => [{
    //   filename: 'assets/css/[name].[hash:8].css',
    //   chunkFilename: 'assets/css/[name].[hash:8].css'
    // }])
    if (process.env.NODE_ENV !== 'development') {
      config.optimization.minimize(true)
      config.optimization.splitChunks({
        chunks: 'all'
      })
    }

    // ============注入cdn start============
    config.plugin('html').tap((args) => {
      // 生产环境或本地需要cdn时，才注入cdn
      // if (isProduction) {
      args[0].cdn = cdn
      // }
      return args
    })
    // 移除 prefetch 插件
    config.plugins.delete('prefetch')
    // 移除 preload 插件
    config.plugins.delete('preload')
    // 本地测试npm包
    config.resolve.symlinks(false)
    if (process.env.ENV === 'production') {
      config.module
        .rule('images')
        .use('image-webpack-loader')
        .loader('image-webpack-loader')
        .options({
          mozjpeg: { progressive: true, quality: 65 },
          optipng: { enabled: false },
          pngquant: { quality: [0.65, 0.9], speed: 4 },
          gifsicle: { interlaced: false }
          // webp: { quality: 75 }
        })

      config.optimization.minimizer('terser').tap((args) => {
        args[0].terserOptions.compress['warnings'] = false
        args[0].terserOptions.compress['drop_console'] = true
        args[0].terserOptions.compress['drop_debugger'] = true
        // args.terserOptions.compress['pure_funcs'] = ['console.*'];
        return [...args]
      })
    }
  },
  filenameHashing: false,
  // css相关配置
  css: {
    // 是否使用css分离插件 ExtractTextPlugin
    extract: true,
    // 开启 CSS source maps?
    sourceMap: false,
    // css预设器配置项
    loaderOptions: {
      // pass options to sass-loader
      // sass: {
      //   // 引入全局变量样式
      //   // data: ``
      // }
    }
    // 启用 CSS modules for all css / pre-processor files.
    // requireModuleExtension: false
  }

}
