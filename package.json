{"name": "egl", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "build:staging": "vue-cli-service build --mode staging", "build:testing": "vue-cli-service build --mode testing", "lint": "eslint --fix   --ext .js,.vue src --format=pretty", "serve14": "n exec 14.21.3 npm run serve"}, "lint-staged": {"*.{js,vue}": ["prettier --write ./src", "npm run lint"]}, "dependencies": {"minimatch": "5.1.6", "@ag-grid-community/vue": "^25.3.0", "@ag-grid-enterprise/all-modules": "^25.3.0", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^1.0.2", "clipboard": "^2.0.8", "cnd-horizon-ui": "1.5.56", "cnd-horizon-utils": "^1.0.62", "cnd-icon": "^1.1.6", "crypto-js": "^4.2.0", "es6-promise": "4.2.8", "exceljs": "^4.4.0", "file-saver": "^2.0.5", "install": "^0.13.0", "vue": "^2.6.14", "vue-class-component": "^7.2.6", "vue-i18n": "^8.25.0", "vue-property-decorator": "^9.1.2", "vue-router": "^3.5.2", "vuex": "^3.6.2", "xlsx": "^0.17.0", "xlsx-color": "^0.14.30"}, "devDependencies": {"minimatch": "5.1.6", "@vue/cli-plugin-babel": "^4.5.13", "@vue/cli-plugin-eslint": "^4.5.13", "@vue/cli-plugin-router": "^4.5.13", "@vue/cli-plugin-vuex": "^4.5.13", "@vue/cli-service": "^4.5.13", "@vue/eslint-config-prettier": "^6.0.0", "babel-eslint": "^10.1.0", "compression-webpack-plugin": "^6.0.5", "eslint": "^7.25.0", "eslint-loader": "^4.0.2", "eslint-plugin-html": "^6.1.2", "eslint-plugin-prettier": "^3.4.0", "eslint-plugin-vue": "^7.15.2", "husky": "^7.0.1", "prettier": "^2.3.2", "sass": "^1.37.5", "sass-loader": "^10.1.1", "speed-measure-webpack-plugin": "^1.5.0", "vue-template-compiler": "^2.6.14", "babel-plugin-component": "^1.1.1", "core-js": "^3.16.1", "eslint-formatter-pretty": "^4.1.0", "eslint-plugin-prettier-vue": "^3.1.0", "lint-staged": "^11.1.2", "custom-event-polyfill": "^1.0.7", "whatwg-fetch": "^3.6.2"}, "resolutions": {"minimatch": "5.1.6"}}