{"name": "HORIZON", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build:staging": "vue-cli-service build --mode staging", "build:testing": "vue-cli-service build --mode testing", "build": "vue-cli-service build", "lint": "eslint --fix   --ext .js,.vue src --format=pretty", "serve14": "n exec 14.21.3 npm run serve"}, "lint-staged": {"*.{js,vue}": ["prettier --write ./src", "npm run lint"]}, "dependencies": {"minimatch": "5.1.6", "@fingerprintjs/fingerprintjs": "^4.5.1", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^1.0.2", "cnd-horizon-utils": "^1.0.52", "cnd-icon": "^1.1.6", "echarts": "^5.4.3", "cnd-horizon-ui": "1.5.56", "es6-promise": "4.2.8", "fuse.js": "^4.0.0-beta", "normalize.css": "^8.0.1", "nprogress": "^0.2.0", "qiankun": "^2.9.3", "qrcode": "^1.5.3", "screenfull": "^5.1.0", "vconsole": "^3.14.7", "vue": "^2.6.14", "vue-i18n": "^8.25.0", "vue-router": "^3.5.2", "vuex": "^3.6.2"}, "devDependencies": {"minimatch": "5.1.6", "@vue/cli-plugin-babel": "^4.5.13", "@vue/cli-plugin-eslint": "^4.5.13", "@vue/cli-plugin-router": "^4.5.13", "@vue/cli-plugin-vuex": "^4.5.13", "@vue/cli-service": "^4.5.13", "@vue/eslint-config-prettier": "^6.0.0", "babel-eslint": "^10.1.0", "babel-plugin-component": "^1.1.1", "babel-plugin-transform-remove-console": "^6.9.4", "compression-webpack-plugin": "^6.0.5", "core-js": "^3.19.0", "eslint": "^7.25.0", "eslint-config-prettier": "^8.3.0", "eslint-formatter-pretty": "^4.1.0", "eslint-plugin-prettier": "^3.4.0", "eslint-plugin-prettier-vue": "^3.1.0", "eslint-plugin-vue": "^7.15.2", "hard-source-webpack-plugin": "^0.13.1", "husky": "^7.0.1", "lint-staged": "^11.1.2", "prettier": "^2.3.2", "sass": "^1.37.5", "sass-loader": "^10.1.1", "speed-measure-webpack-plugin": "^1.3.3", "vue-html": "^1.0.0", "vue-html-loader": "^1.2.4", "vue-template-compiler": "^2.6.14", "custom-event-polyfill": "^1.0.7", "whatwg-fetch": "^3.6.2"}, "resolutions": {"minimatch": "5.1.6"}}