version: "3"
services:
  portal:
    build:
      context: ./
      dockerfile: ./docker/Dockerfile
      args:
        NODE_ENV: production
        NGINX_PORT: 80
        API_ENV: api
        API_URL: http://172.27.254.14:32625
        WEBAPP: esc
        SYSTEM_URL: 110.80.15.114:30404
        SCM_URL: 110.80.15.114:30405
        EGL_URL: 110.80.15.114:30406
        SOCKET_URL: ws://172.27.254.14:32625/esc
    restart: always
    image: docker.cndinfo.com/esc/portal:latest
    volumes:
      - /home/<USER>/releases:/home/<USER>/releases
    ports:
      - 80:80
    container_name: portal
    networks:
      - app-net
networks:
  app-net:
    external:
      name: app-net
