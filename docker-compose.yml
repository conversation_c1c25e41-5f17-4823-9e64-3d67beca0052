version: "3"
services:
  ecs:
    build:
      context: ./
      dockerfile: ./docker/Dockerfile
      args:
        NODE_ENV: 
        NGINX_PORT: 80
        API_ENV: api
        API_URL: http://172.27.254.14:32625
        WEBAPP: esc
    restart: always
    image: docker.cndinfo.com/esc/esc:latest
    ports:
      - 80:80
    container_name: ecs
    networks:
      - app-net
networks:
  app-net:
    external:
      name: app-net
