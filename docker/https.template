client_max_body_size 4096m;

access_log access.log main;
error_log  error.log warn;

server {
    listen       ${NGINX_PORT} ;
    server_name  127.0.0.1 localhost;

   
    gzip on;
    gzip_vary on;
    gzip_min_length 10240;
    gzip_proxied expired no-cache no-store private auth;
    gzip_types text/plain application/javascript application/x-javascript text/javascript text/xml text/css;
    gzip_static on;
    gzip_disable "MSIE [1-6]\.";

    #   指定允许跨域的方法，*代表所有
    add_header Access-Control-Allow-Methods *;

    #   预检命令的缓存，如果不缓存每次会发送两次请求
    add_header Access-Control-Max-Age 3600;
    #   带cookie请求需要加上这个字段，并设置为true
    add_header Access-Control-Allow-Credentials true;

    #   表示允许这个域跨域调用（客户端发送请求的域名和端口） 
    #   $http_origin动态获取请求客户端请求的域   不用*的原因是带cookie的请求不支持*号
    add_header Access-Control-Allow-Origin $http_origin;

    #   表示请求头的字段 动态获取
    add_header Access-Control-Allow-Headers $http_access_control_request_headers;

    #   OPTIONS预检命令，预检命令通过时才发送请求
    #   检查请求的类型是不是预检命令
    if ($request_method = OPTIONS){
        return 200;
    } 
    location / {
        root /usr/share/nginx/html;
        index   index.html index.htm;
        try_files $uri $uri/ /index.html;
    }

    
    location @router {
        rewrite ^.*$ /index.html last;
    }

    location /${API_ENV} {
        proxy_set_header Host $host;
        proxy_set_header Cookie $http_cookie;       
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_pass ${API_URL};
        #proxy_cookie_domain ${API_DOMAIN} ${NGINX_SERVER};
        proxy_cookie_path  /${WEBAPP} /${API_ENV} ;
    }

}