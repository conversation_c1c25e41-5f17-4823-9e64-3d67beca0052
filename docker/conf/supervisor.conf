[supervisord]
nodaemon=true
[inet_http_server]
port = *:9001
username = admin
password = admin#@!123


[rpcinterface:supervisor] 
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface

[include]
files = /etc/supervisor/conf.d/*.conf

[program:nodejs]
command=/usr/local/bin/npm run dev
stdout_logfile=/var/log/apps/app.log
stdout_logfile_maxbytes=0
stderr_logfile=/var/log/apps/error.log
stdout_events_enabled=true
stderr_events_enabled=true

[eventlistener:stdout] 
command = supervisor_stdout 
buffer_size = 100 
events = PROCESS_LOG 
result_handler = supervisor_stdout:event_handler
