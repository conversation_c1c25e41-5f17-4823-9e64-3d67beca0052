
FROM harbor.devops.cndinfo.com/library/alpine:latest AS installer
LABEL "C&D" <<EMAIL>>

WORKDIR /tmp

RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories && \ 
  apk add --no-cache autoconf libtool zlib  && \
   apk add --no-cache --update nodejs npm 

COPY package.json /tmp/package.json

RUN   npm config set registry https://npm.devops.cndinfo.com && \
   npm install -g yarn && \
   yarn config set registry https://npm.devops.cndinfo.com/ && \
  yarn config set chromedriver_cdnurl http://npm.taobao.org/mirrors/chromedriver && \
  yarn config set phantomjs_cdnurl hhttp://npm.taobao.org/mirrors/phantomjs && \
  yarn config set sass_binary_site http://npm.taobao.org/mirrors/node-sass

RUN yarn --netwrok -timeout 2000000

RUN cd /tmp && rm -rf /tmp/node_modules &&  yarn install 

FROM harbor.devops.cndinfo.com/library/alpine:latest as builder

RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories && \ 
apk --no-cache add libintl && \
apk --no-cache add --virtual .gettext gettext && \
      cp /usr/bin/envsubst /usr/local/bin/envsubst && \
      apk del .gettext && \
  apk add --no-cache autoconf libtool zlib  && \
   apk add --no-cache --update nodejs npm  && \
   npm install -g yarn && \
    yarn config set registry https://npm.devops.cndinfo.com/ 

WORKDIR /apps

COPY --from=installer /tmp/node_modules /apps/node_modules

COPY . /apps

ARG NODE_ENV

ARG SYSTEM_URL
ENV SYSTEM_URL $SYSTEM_URL
ARG SCM_URL
ENV SCM_URL $SCM_URL
ARG TEXTILE_URL
ENV TEXTILE_URL $TEXTILE_URL
ARG MALLADMIN_URL
ENV MALLADMIN_URL $MALLADMIN_URL
ARG EDITOR_URL
ENV EDITOR_URL $EDITOR_URL
ARG WIIS_URL
ENV WIIS_URL $WIIS_URL
ARG EGL_URL
ENV EGL_URL $EGL_URL

ARG SOCKET_URL
ENV SOCKET_URL $SOCKET_URL

RUN envsubst '$SYSTEM_URL,$SCM_URL,$TEXTILE_URL,$MALLADMIN_URL,$EDITOR_URL,$WIIS_URL,$EGL_URL,$SOCKET_URL' < /apps/docker/.env.template > /apps/.env.${NODE_ENV}
RUN  if [ -n "$NODE_ENV" ] && [ "$NODE_ENV" != "production" ]  ; \
        then yarn run build:${NODE_ENV}; \
     else \
      yarn run build;  \
     fi 

FROM harbor.devops.cndinfo.com/library/nginx:alpine 
RUN rm -rf /usr/share/nginx/html
COPY --from=builder /apps/docker/https.template /etc/nginx/conf.d/https.template
# COPY --from=builder /apps/docker/https.conf /etc/nginx/conf.d/default.conf

ARG API_ENV
ENV API_ENV $API_ENV
ARG NGINX_PORT
ENV NGINX_PORT $NGINX_PORT
ARG API_URL
ENV API_URL $API_URL
ARG WEBAPP
ENV WEBAPP $WEBAPP
COPY --from=builder /apps/dist /usr/share/nginx/html
RUN  chmod -R 777 /usr/share/nginx/html && ln -snf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime \
    && echo "Asia/Shanghai" > /etc/timezone
ENV TZ Asia/Shanghai
RUN envsubst '$$API_ENV,$$NGINX_PORT,$$API_URL,$$WEBAPP' < /etc/nginx/conf.d/https.template > /etc/nginx/conf.d/default.conf
RUN cat /etc/nginx/conf.d/default.conf
EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]

