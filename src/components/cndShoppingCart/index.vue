<!--
    @component CndShoppingCart 钢贸购物车业务组件
    @props
        {String} title 购物车中的标题 ''
        {String} buttonTile 购物车按钮标题 ''
        {Boolean} closeOnClickMask 点击蒙版层关闭 true
        {String} submitText 提交按钮文案 ''
        {Array} countList 购物车合计数组 具体参考src/components/steelTradeAggrid/gridTotal.vue
        {Array} mergeConfig 传入(工作台)数据与购物车数据混合方式及字段
            @param {String} field 对应columns(表格)列的字段名
            @param {String} max 最大值限制若不设置则直接相加 并且不会触发超出值提示
            @param {String} ['add', 'cover'] 必填
                add为两个值相加
                cover为覆盖 后者覆盖前者
            demo:
              mergeConfig: [{
                field: 'confirmSurplus',
                mode: 'add',
                max: 'surplus'
              }, {
                field: 'aaaaa',
                mode: 'cover'
              }]
    @props table 属性与steeltradeAggird用法一致
        {Array} columns 主表列配置 [] 具体参考aggrid用法
        {String} rowKey 主表键值(唯一) 必填
        {String} childRowKey 子表键值(唯一) 必填
        {String} childrenListKey 子表数据存在的key '_details'
    @events
        {Function} submit 返回购物车单层级数据 内已混合主表数据
        {Function} prompt 返回所有超出的数据 以及新旧值
    @methods
        {Function} addToCart 传入表格选中的表格数据
        {Function} getCartData 返回当前购物车所有数据
    <AUTHOR>
    @date 2021-02-04 10:30:23
-->
<template>
  <drawer
    ref="drawer"
    :title="buttonTitle"
    :close-on-click-mask="closeOnClickMask"
    :badge-value="cartTableData.length"
  >
    <div class="shopping-cart">
      <div class="shopping-cart-title">{{ title }}</div>
      <div class="shopping-cart-content">
        <steelTradeAggrid
          ref="aggrid"
          :column-defs="columns"
          :row-data="cartTableData"
          :auto-load-data="false"
          :paginationinif="false"
          :row-key="carRowKey"
          :full-row-type="fullRowType"
          table-selection="multiple"
          :enable-filter="false"
          @rowValueChanged="rowValueChangeds"
        />
      </div>
      <div class="shopping-cart-footer">
        <el-button
          size="small"
          :disabled="!cartTableData.length || !selectedNumber"
          @click="deleteToCart"
        >
          {{ $t('btns.delete') }}
        </el-button>
        <div class="rg-content">
          <span class="total">
            <gridTotal
              flag="footer"
              :count-list="countList"
            />
          </span>
          <template v-if="$slots.opeartions"><slot name="opeartions" /></template>
          <span style="margin-right: 10px;" />
          <el-button
            v-show="showBtn"
            size="small"
            type="primary"
            :disabled="!cartTableData.length || !selectedNumber || submitDisabled"
            @click="submit"
          >
            {{ submitText }}
          </el-button>
        </div>
      </div>
    </div>
  </drawer>
</template>

<script>
import drawer from './drawer'
import gridTotal from '../steelTradeAggrid/gridTotal'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import { deepClone } from '@/components/steelTradeAggrid/utils.js'
import { Message } from 'element-ui'
import store from '@/store'
import {
  getParameterLimit
} from '@/api/logistics/saleDelivery/common'
export default {
  components: {
    drawer,
    gridTotal,
    steelTradeAggrid
  },
  props: {
    title: {
      type: String,
      default: ''
    },
    buttonTitle: {
      type: String,
      default: ''
    },
    submitText: {
      type: String,
      default: ''
    },
    closeOnClickMask: {
      type: Boolean,
      default: true
    },
    columns: {
      type: Array,
      default: () => []
    },
    rowKey: {
      type: String,
      required: true
    },
    childRowKey: {
      type: String,
      required: true
    },
    childrenListKey: {
      type: String,
      default: '_details'
    },
    mergeConfig: {
      type: Array,
      default: () => []
    },
    countList: {
      type: Array,
      default: () => []
    },
    validateKey: {
      type: Array,
      default: () => []
    },
    fullRowType: {
      type: String,
      default: null
    },
    curConfig: {
      type: Object,
      default: () => {}
    },
    submitDisabled: {
      type: Boolean,
      default: false
    },
    btnKey: {
      type: String,
      default: ''
    },
    isSubtable: {
      type: Boolean,
      default: true
    },
    rowValueChanged: {
      type: Function,
      default: null
    },
    errorDict: {
      type: Object,
      default: () => {}
    },
    compare: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      showBtn: true,
      cartTableData: [],
      selectedNumber: 0,
      allowLength: 200
    }
  },
  computed: {
    carRowKey() {
      return this.isSubtable ? this.childRowKey : this.rowKey
    }
  },
  watch: {
    cartTableData: {
      deep: true,
      handler(newValue) {
        const selectList = newValue.filter(item => item._selected)
        this.selectedNumber = selectList.length
        this.$emit('selectedChange', selectList)
      }
    }

  },
  mounted() {
    if (this.btnKey) {
      const authenticationList = store.getters.curBtnAuthList
      this.showBtn = authenticationList.includes(this.btnKey)
    }
    this.getAllowLength()
  },
  methods: {
    // 出仓条数做成可配置
    getAllowLength() {
      getParameterLimit({
        code: 'FHDMX-LIMIT'
      }).then(res => {
        if (res.data.value) {
          this.allowLength = Number(res.data.value)
        }
      })
    },
    /**
     * 将选中的数据合并进入购物车数据
     * @param {Array} 选中数据
     */
    checkLength(selectedData, length) {
      return new Promise((resolve, reject) => {
        this.$confirm(`数据超过${this.allowLength}条，系统将自动截取前${this.allowLength}条数据，是否同意`, this.$t('grid.others.prompt'), {
          confirmButtonText: this.$t('btns.confirm'),
          cancelButtonText: this.$t('btns.cancel'),
          type: 'warning'
        }).then(() => {
          const slicedArr = selectedData.slice(0, length)
          resolve(slicedArr)
        })
      })
    },
    async addToCart(list) {
      const {
        rowKey,
        childRowKey,
        cartTableData,
        mergeConfig
      } = this
      let promptList = []
      const cartList = deepClone(list)
      let flatCartList = this.generateCartTableData(cartList)
      const carListLength = Number(cartTableData?.length)
      if (carListLength > (this.allowLength - 1)) {
        this.$message.warning(`购物车已存在${this.allowLength}条数据，不可再添加`)
        return
      }
      const _length = carListLength + Number(flatCartList?.length)
      if (_length > this.allowLength) {
        const sliceLength = this.allowLength - carListLength
        const res = await this.checkLength(flatCartList, sliceLength)
        if (res) {
          flatCartList = res
        }
      }
      if (this.validateKey.length && flatCartList.length) {
        if (!this.validateData(flatCartList, cartTableData)) {
          return false
        }
      }

      if (cartTableData.length) {
        const ROWKEY = this.isSubtable ? childRowKey : rowKey
        const tableChildRowKeyList = cartTableData.map(item => item[ROWKEY])
        flatCartList.forEach(mergeItem => {
          if (tableChildRowKeyList.includes(mergeItem[ROWKEY])) {
            const target = cartTableData.find(row =>
              row[ROWKEY] === mergeItem[ROWKEY]
            )

            if (mergeConfig && mergeConfig.length) {
              const mergePrompt = []
              mergeConfig.forEach(config => {
                const mergeResult = this.mergeTarget(target, mergeItem, config)
                if (mergeResult.overstep) {
                  mergePrompt.push(deepClone({
                    [`_old${config.field}`]: target[config.field],
                    [`_new${config.field}`]: mergeItem[config.field],
                    ...target
                  }))
                }
                target[config.field] = mergeResult.value
              })

              if (mergePrompt.length) {
                promptList = promptList.concat(mergePrompt.reduce((prev, next) => {
                  return Object.assign(prev, next)
                }, {}))
              }
            } else {
              console.warn('购物车与即将加入的数据发现重复, 并未配置mergeConfig, 请注意!')
            }
          } else {
            cartTableData.push(mergeItem)
          }
        })
      } else {
        cartTableData.push(...flatCartList)
      }

      if (promptList.length) {
        this.generatePrompt(promptList)
      }

      this.$refs.aggrid.refreshTable()
    },
    /**
     * 校验需一致字段
     * @param {Array} 选中数据
     * @return {Boolean} 是否校验通过
     */
    validateData(selectList, cartList = []) {
      const errorDict = {
        'sCompanyId': this.$t('grid.title.company'),
        'sSupplierId': this.$t('grid.others.supplier'),
        'sWarehouseId': this.$t('grid.others.warehouse'),
        'sCustomerId': this.$t('grid.others.customer'),
        'sIsDisplay': this.$t('grid.others.documentType'),
        'sProjectType': '购销方式',
        'sManagementId': '经营单位',
        'sExtend5': '车船号',
        'sVesselNo': '车船号',
        'sExtend11': '仓库装卸费',
        ...this.errorDict
      }

      const errorList = []
      selectList.forEach(prod => {
        this.validateKey.forEach(key => {
          if (prod[key] !== (cartList.length ? cartList[0][key] : selectList[0][key])) {
            if (!errorList.includes(key)) {
              errorList.push(key)
            }
          }
        })
      })

      if (errorList.length) {
        this.$message({
          type: 'error',
          message: errorList.reduce((prev, next, index) => {
            prev = prev + `${errorDict[next]}、`
            if (index + 1 === errorList.length) {
              prev = prev.substr(0, prev.length - 1) + this.$t('grid.others.inconsistentPleaseSelectAgain')
            }
            return prev
          }, '')
        })
        return false
      } else {
        return true
      }
    },
    // 从购物车数据中删除
    deleteToCart() {
      this.$confirm(`${this.$t('grid.tips.removeSelectionFrom')}${this.title}${this.$t('grid.tips.deleteFrom')}`, this.$t('grid.others.prompt'), {
        confirmButtonText: this.$t('btns.confirm'),
        cancelButtonText: this.$t('btns.cancel'),
        type: 'warning'
      }).then(() => {
        const {
          rowKey,
          childRowKey,
          cartTableData
        } = this
        const selectList = cartTableData.filter(item => item._selected)
        selectList.forEach(item => {
          let index = null
          index = cartTableData.findIndex(target => {
            return target[rowKey] === item[rowKey] &&
                    target[childRowKey] === item[childRowKey]
          })
          cartTableData.splice(index, 1)
        })
        this.$message({
          type: 'success',
          message: this.$t('grid.tips.deletionSuccess')
        })
      })
    },
    /**
     * 提交
     * @return {Array} 返回购物车全部数据
     */
    submit() {
      this.$refs.aggrid.gridApi.stopEditing()
      const selectedList = deepClone(this.cartTableData).filter(item => item._selected)
      this.$emit('submit', selectedList)
    },
    getCartData() {
      this.$refs.aggrid.gridApi.stopEditing()
      return deepClone(this.cartTableData)
    },
    /**
     * 超出数量信息提示方法
     * @param {Array} 超出的源数组
     */
    generatePrompt(promptList) {
      this.$emit('prompt', promptList)
    },
    /**
     * 通过合并规则计算出合并后的某一个值
     * 目标与计算目标将影响config.mode为cover(覆盖)的逻辑 mergeItem优先级大于target
     * @param {Object} target 目标
     * @param {Object} margeItem 计算目标
     * @param {Array} config 规则配置
     * @return {Object}
     *    @param {Boolean} overstep 是否超出最大值
     *    @param {String, Number} value 根据规则计算出的值
     */
    mergeTarget(target, mergeItem, config) {
      if (config.mode === 'add') {
        const value = target[config.field] + mergeItem[config.field]
        const overstep = config.max && value > target[config.max]

        return {
          overstep: overstep,
          value: overstep ? target[config.field] : value
        }
      }
      if (config.mode === 'cover') {
        return {
          overstep: false,
          value: mergeItem[config.field]
        }
      }
    },
    /**
     * 将购物车数据flat为单层级数据供给购物车展示
     * 单层级后的单条数据包含分类主表数据
     * 覆盖优先级为 Object.assign(category, child)
     * @param {Array} 购物车数据
     * @return {Array} 单层级购物车数据
     */
    generateCartTableData(list) {
      return list.reduce((prev, next) => {
        const category = deepClone(next)
        category._selected = true
        if (this.isSubtable) {
          if (category[this.childrenListKey]) {
            delete category[this.childrenListKey]
          } else {
            throw new Error('请检查childrenListKey是否配置正确!')
          }
          return prev.concat(next[this.childrenListKey].map(child => {
            return deepClone(Object.assign(category, child))
          }))
        } else {
          return prev.concat(deepClone(Object.assign(category, next)))
        }
      }, [])
    },
    // 清空购物车
    clearCart() {
      this.cartTableData = []
    },
    closeDrawer() {
      this.$refs.drawer.closeSidebar()
    },
    rowValueChangeds(params) {
      const { data, rowIndex } = params
      if (typeof this.rowValueChanged === 'function') {
        this.rowValueChanged(params)
      } else {
        const { vCurQty, vCurPkgQty, vLeftQty, vLeftPkgQty } = this.curConfig
        if (params.data.sIsDisplay === '0') {
          if (data.vCurContractQty > params.data.vLeftContractQty) {
            data.vCurQty = params.data.vLeftContractQty
          }
        }
        if (data.sIsDisplay) {
          if (data.sIsDisplay === '0') {
            if (+data[vCurQty] > +data[vLeftQty]) {
              data[vCurQty] = +data[vLeftQty]
              data[vCurPkgQty] = +data[vLeftQty]
            } else {
              if (parseInt(+data[vCurQty]) !== parseFloat(+data[vCurQty])) {
                data[vCurQty] = +data[vLeftQty]
                data[vCurPkgQty] = +data[vLeftQty]
                Message({ message: '输入值需为整数,请重新输入', type: 'warning' })
              }
            }
            this.cartTableData[rowIndex] = data
            this.$refs.aggrid.refreshTable()
            return
          }
        }
        if (this.compare) {
          if (+data[vCurQty] === +data[vLeftQty]) {
            if (+data[vCurPkgQty] !== +data[vLeftPkgQty]) {
              Message.closeAll()
              Message({ message: this.$t('grid.others.ifTheRemainingQuanteUsedUpKey'), type: 'warning' })
            }
          }
          if (+data[vCurPkgQty] === +data[vLeftPkgQty]) {
            if (+data[vCurQty] !== +data[vLeftQty]) {
              Message.closeAll()
              Message({ message: this.$t('grid.others.theRemainingPiecesAeUsedUpKey'), type: 'warning' })
            }
          }
        }
      }
      this.cartTableData[rowIndex] = data
      this.$refs.aggrid.refreshTable()
    }
  }
}
</script>

<style lang="scss" scoped>
  .shopping-cart {
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 10px;
    &-title {
      font-size: 14px;
      color: #0D468B;
    }
    &-content {
      flex: 1;
      margin: 10px 0;
      .steel-trade-aggrid {
        height: 100%;
        max-height: 100%;
      }
    }
    &-footer {
      display: flex;
      justify-content: space-between;
      .rg-content {
        display: flex;
        align-items: center;
        height: 100%;
        margin-right: -10px;
        .total {
          margin-right: 10px;
        }
        .el-button {
          margin-right: 10px;
        }
      }
    }
  }
</style>
