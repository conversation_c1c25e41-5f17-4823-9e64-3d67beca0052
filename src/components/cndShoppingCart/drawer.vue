<template>
  <div
    ref="cartList"
    :class="{show:show}"
    class="steeltrade-container"
  >
    <div class="steeltrade-background" @click="closeSidebar" />
    <div class="steeltrade">
      <el-badge
        :value="badgeValue"
        class="steeltrade-cart-badge"
      >
        <div
          class="handle-button"
          @click="show=!show"
        >
          <div class="handle-button-title">{{ title }}</div>

          <transition name="elTranslateX" mode="out-in">
            <div v-if="show">
              <cnd-icon
                v-if="show"
                name="cnd-arrow-right-circle"
                width="1.5em"
                height="1.5em"
              />
            </div>
            <cnd-icon
              v-else
              name="cnd-arrow-left-circle"
              width="1.5em"
              height="1.5em"
            />
          </transition>
        </div>
      </el-badge>
      <div class="steeltrade-items">
        <cnd-icon
          class="title-close-btn"
          name="cnd-close-circle"
          width="1.5em"
          height="1.5em"
          @click="closeSidebar"
        />
        <slot />
      </div>
    </div>
  </div>
</template>

<script>

export default {
  name: 'ShoppingCartDrawer',
  props: {
    title: {
      type: String,
      default: ''
    },
    closeOnClickMask: {
      type: Boolean,
      default: true
    },
    badgeValue: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      show: false
    }
  },
  watch: {
    show(value) {
      if (value) {
        this.addClass(document.body, 'show-steel-trade')
      } else {
        this.removeClass(document.body, 'show-steel-trade')
      }
    }
  },
  // mounted() {
  //   this.insertToBody()
  // },
  // beforeDestroy() {
  //   const elx = this.$refs.cartList
  //   elx.remove()
  // },
  methods: {
    closeSidebar() {
      if (this.closeSidebar) {
        this.show = false
      }
    },
    // insertToBody() {
    //   const elx = this.$refs.cartList
    //   const body = document.querySelector('body')
    //   body.insertBefore(elx, body.firstChild)
    // },
    /**
     * Check if an element has a class
     * @param {HTMLElement} elm
     * @param {string} cls
     * @returns {boolean}
     */
    hasClass(ele, cls) {
      return !!ele.className.match(new RegExp('(\\s|^)' + cls + '(\\s|$)'))
    },
    /**
     * Add class to element
     * @param {HTMLElement} elm
     * @param {string} cls
     */
    addClass(ele, cls) {
      if (!this.hasClass(ele, cls)) ele.className += ' ' + cls
    },
    /**
     * Remove class from element
     * @param {HTMLElement} elm
     * @param {string} cls
     */
    removeClass(ele, cls) {
      if (this.hasClass(ele, cls)) {
        const reg = new RegExp('(\\s|^)' + cls + '(\\s|$)')
        ele.className = ele.className.replace(reg, ' ')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.steeltrade-container {
  .steeltrade-background {
    position: fixed;
    top: 0;
    left: 0;
    opacity: 0;
    transition: opacity .3s cubic-bezier(.7, .3, .1, 1);
    background: rgba(0, 0, 0, .2);
    z-index: -1;
  }
}

.steeltrade {
  width: 100%;
  // max-width: 260px;
  max-width: 80vw;
  height: 350px;
  position: fixed;
  top: 384px;
  right: 0;
  box-shadow: 0px 0px 15px 0px rgba(0, 0, 0, .05);
  transition: all .25s cubic-bezier(.7, .3, .1, 1);
  transform: translate(100%);
  background: #fff;
  z-index: 2000;
  &-items {
    height: 100%;
    .title-close-btn {
      cursor: pointer;
      position: absolute;
      right: 10px;
      top: 11px;
    }
  }
  .handle-button {
    position: absolute;
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: space-between;
    width: 35px;
    height: 210px;
    left: -35px;
    top: 0;
    text-align: center;
    font-size: 14px;
    padding: 10px 0;
    color: #fff;
    background-color: #0d468b;
    border-radius:  0 0 0 8px;
    cursor: pointer;
    &-title {
      width: 16px;
      line-height: 1.2;
      text-align: center;
      word-break: break-all;
      white-space: pre-wrap;
    }
  }
}

.show {
  transition: all .3s cubic-bezier(.7, .3, .1, 1);
  .steeltrade-background {
    z-index: 1999;
    opacity: 1;
    width: 100%;
    height: 100%;
  }
  .steeltrade {
    transform: translate(0);
  }
}

@keyframes elementIn {
  0% {
    opacity: 0;
    transform: translateX(24px)
  }
  100% {
    opacity: 1;
    transform: translateX(0px)
  }
}
@keyframes elementHide {
  0% {
    opacity: 1;
    transform: translateX(0px)
  }
  100% {
    opacity: 0;
    transform: translateX(24px)
  }
}
.elTranslateX-enter-active {
  animation: elementIn .3s;
}
.elTranslateX-leave-active {
  animation: elementHide .3s;
}
.elTranslateX-enter, .elTranslateX-leave-to {
  opacity: 0;
}
::v-deep .el-badge__content.is-fixed {
  transform: translateY(-50%) translateX(-50%) !important;
}
</style>
<style>
.show-steel-trade {
  overflow: hidden;
  position: relative;
  width: 100%;
}
.steeltrade-cart-badge{
  position: absolute;
}
.steeltrade-cart-badge .el-badge__content {
  left: -35px !important;
  right: unset !important;
  transform: translateY(-50%) translateX(-50%) !important;
  background-color: #EE0033 !important;
  border: 0 !important;
}
.steeltrade-cart-badge .el-badge__content.is-fixed {
  transform: translateY(-50%) translateX(-50%) !important;
}
</style>
