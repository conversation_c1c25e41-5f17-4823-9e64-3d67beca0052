<!--
 * @Description:导出按钮
-->
<template>
  <div class="text export-btn" @contextmenu.prevent="onMontextmenu">
    <el-dropdown split-button type="success" size="mini" @click="handleClick">
      <div @click="onExportPage">{{ btnTxt || $t('excel.export') }}</div>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item>
          <div @click="onExportPage">导出当前页</div>
        </el-dropdown-item>
        <el-dropdown-item class="mt-5">
          <div @click="onExportSelect">导出选中数据</div>
        </el-dropdown-item>
        <el-dropdown-item v-if="exportAll" class="mt-5">
          <div @click="oneEportAll">导出所有查询结果</div>
        </el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
  </div>
</template>

<script>
import axios from 'axios'
import { MessageUtil } from 'cnd-horizon-utils'

export default {
  name: 'ExportBtn',
  props: {
    btnTxt: {
      type: String,
      default: ''
    },
    api: {
      type: String,
      default: ''
    },
    postParams: {
      type: Object,
      default: null
    },
    isMore: {
      type: Boolean,
      default: true
    },
    exportAll: {
      type: Boolean,
      default: false
    },
    fileName: {
      type: String,
      default: 'fileName'
    }
  },
  data() {
    return {
      defaultConfig: {
        baseURL: '',
        mode: 'cors',
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json;charset=utf-8'
        },
        responseType: 'json'
      }
    }
  },
  created() {
    this.initAxios()
  },
  methods: {
    initAxios() {
      axios.interceptors.response.use(
        (response) => {
          if (response.config && response.config.responseType === 'blob') {
            const blob = new Blob([response.data], {
              type: 'msexcel'
            })
            const filename = `${this.fileName}.xls`
            if ('download' in document.createElement('a')) {
              const downloadElement = document.createElement('a')
              let href = ''
              if (window.URL) {
                href = window.URL.createObjectURL(blob)
              } else {
                href = window.webkitURL.createObjectURL(blob)
              }
              downloadElement.href = href
              downloadElement.download = filename
              document.body.appendChild(downloadElement)
              downloadElement.click()
              if (window.URL) {
                window.URL.revokeObjectURL(href)
              } else {
                window.webkitURL.revokeObjectURL(href)
              }
              document.body.removeChild(downloadElement)
            } else {
              navigator.msSaveBlob(blob, filename)
            }
            return
          }
          return Promise.resolve(response.data)
        },
        (error) => {
          const { response } = error
          let message = '异常'
          let status
          if (!response) {
            message = '请求超时...'
          } else {
            message = response.statusText
            status = response.status
          }
          MessageUtil.warning(message)
          return Promise.reject({ message, status })
        }
      )
      axios.defaults.timeout = 30000
    },
    post(url, data, config) {
      return axios.post(
        url,
        data,
        Object.assign({}, this.defaultConfig, config)
      )
    },
    onExportSelect() {
      this.$emit('onexportselect')
    },
    onExportPage() {
      this.$emit('onexportpage')
    },
    oneEportAll() {
      if (this.api && this.postParams) {
        this.post(this.api, this.postParams, { responseType: 'blob' })
      }
    },
    onMontextmenu() {
      if (!this.isMore) {
        return
      }
      this.isShow = true
    }
  }
}
</script>
<style lang='scss' scoped>
.export-btn {
  position: relative;
  display: inline-block;
}
</style>

