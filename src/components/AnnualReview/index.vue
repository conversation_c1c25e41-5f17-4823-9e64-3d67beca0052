<template>
  <div class="annual-review">
    <div v-if="showBtn" class="btn" data-content="年度报告" @click="showImages"></div>
    <div v-if="showImage" class="report">
      <div class="report-container" @wheel="handleScroll">
        <div v-if="!share" class="mask" />
        <div v-if="!share" class="close-button" @click.stop="closeImages">×</div>
        <el-carousel
          ref="carousel"
          height="100vh"
          direction="vertical"
          indicator-position="none"
          :autoplay="false"
          @change="handleCarouselChange"
        >
          <el-carousel-item
            v-for="(image, index) in images"
            :key="image"
            :name="index"
          >
            <div class="carousel-container">
              <div
                ref="imageContainerRef"
                class="image-container"
                :style="{
                  backgroundImage: `url(${require(`./assets/images/${image.path}`)})`,
                }"
                @click="handleImageClick($event, index)"
              >
                <div
                  v-if="image.data"
                  class="text-container"
                  :style="{
                    top: `${image.textPosition.top}%`,
                    left: `${image.textPosition.left}%`,
                    fontSize: `${image.path === 'image_08.png' ? '2.5vh' : '2.8vh'}`,
                  }"
                  @click="handleTextClick"
                  v-html="image.data"
                >
                </div>
                <div v-if="((image.path === 'image_10.png' || image.path === 'image_11.png'))" class="echart-container">
                  <div v-if="reportData.sIsBusiness === '1' && reportData.sFirstContractCode" id="myChart" class="myChart"></div>
                  <div v-if="!share" class="qr-code" >
                    <img class="qr-code-image" :src="qrCodeImage" />
                    <div class="qr-code-text">
                      扫一扫分享
                    </div>
                  </div>
                </div>
                <div v-if="((image.path === 'image_10.png' || image.path === 'image_11.png'))" class="text-footer">
                  <p>{{getRandomStatement()}}</p>
                </div>
              </div>
            </div>
          </el-carousel-item>
        </el-carousel>
      </div>
    </div>
  </div>
</template>
<script>

import * as echarts from 'echarts'
import { annualReport } from '@/api/user'
import QRCode from 'qrcode'
const statements = [
  '星光不问赶路人',
  '在前方，还有许多精彩等待着。',
  '好事永不消逝',
  '积土成山，非一日之踏',
  '行者无疆，志者无穷',
  '青山常在，绿水长流'
]
export default {
  data() {
    return {
      images: null,
      currentIndex: 0,
      showImage: false,
      reportData: null,
      showBtn: false,
      qrCodeImage: ''
    }
  },
  computed: {
    share() {
      return location.pathname === '/share'
    }
  },

  mounted() {
    this.getAnnualReport()
    document.addEventListener('keydown', this.handleKeyDown)
  },
  methods: {
    async generateQRCode(account) {
      try {
        const data = `${location.origin}/share?account=${account}`
        const qrCodeDataURL = await QRCode.toDataURL(data)
        this.qrCodeImage = qrCodeDataURL
      } catch (error) {
        console.error('Failed to generate QR code:', error)
      }
    },
    getAnnualReport() {
      const userInfo = JSON.parse(sessionStorage.getItem('userInfo'))
      const account = this.share ? this.$route.query.account : userInfo?.account
      annualReport({
        sUserAccount: account,
        year: '2023'
      }).then((res) => {
        if (res.data) {
          this.reportData = res.data
          if (this.share) {
            this.showBtn = false
            if (res.data.sIsBusiness === '1' && res.data.sFirstContractCode) {
              this.setImagesShare(res.data)
            } else {
              this.setImagesShareV2(res.data)
            }
          } else {
            this.showBtn = res.data.vShowAnnual === '1'
            if (res.data.sIsBusiness === '1' && res.data.sFirstContractCode) {
              this.setImages(res.data)
            } else {
              this.setImagesV2(res.data)
            }
            this.generateQRCode(userInfo?.account)
          }
        }
      })
    },
    getRandomStatement() {
      const randomIndex = Math.floor(Math.random() * statements.length)
      return '「' + statements[randomIndex] + '」'
    },
    formatAmountToBillion(amount) {
      if (typeof amount !== 'number' || isNaN(amount)) {
        return 0
      }
      const amountInBillion = (amount / 1e8).toFixed(2)
      return Number(amountInBillion)
    },
    formatAmount(amount) {
      if (typeof amount !== 'number' || isNaN(amount)) {
        return '0元'
      }

      if (amount >= 1e8) {
        // 超过亿，格式化为亿元
        const amountInBillion = (amount / 1e8).toFixed(2)
        return Number(amountInBillion) + '亿元'
      } else if (amount >= 1e4) {
        // 1万到1亿之间，格式化为万元
        const amountInTenThousand = (amount / 1e4).toFixed(2)
        return Number(amountInTenThousand) + '万元'
      } else {
        // 未超过1万，格式化为元
        return amount.toFixed(2) + '元'
      }
    },
    extractDateComponent(dateString, component) {
      const dateObject = new Date(dateString)
      switch (component) {
        case 'year':
          return dateObject.getFullYear()
        case 'month':
          return dateObject.getMonth() + 1
        case 'day':
          return dateObject.getDate()
        case 'hour':
          return dateObject.getHours()
        case 'minute':
          return dateObject.getMinutes()
        case 'second':
          return dateObject.getSeconds()
        default:
          return null
      }
    },
    setImages(data) {
      let images = [
        {
          path: 'image_01.png'
        },
        {
          path: 'image_02.png',
          textPosition: { top: 30, left: 50 },
          data: `
              <p>亲爱的<span style="color:#FFE843">${data.sUserName}</span></p>
              <p><span style="color:#FFE843">${this.extractDateComponent(data.sUserCreateTime, 'year')}</span>年<span style="color:#FFE843">${this.extractDateComponent(data.sUserCreateTime, 'month')}</span>月<span style="color:#FFE843">${this.extractDateComponent(data.sUserCreateTime, 'day')}</span>日</p>
              <p>是您与云钢第一次相遇的日子</p>
              <p>您经手的第一个在云钢作业的合同是</p>
              <p style="color:#00EBFF;word-wrap: break-word;">${data.sFirstContractCode}</span></p>
              <p>${data.sAnnualYear}</p>
              <p>这一年我们一起经历的</p>
              <p>请听我一点点告诉您</p>
            `
        },
        {
          path: 'image_03.png',
          textPosition: { top: 65, left: 50 },
          data: `
              <p>一年里，你共有<span style="color:#FFE843">${data.sWorkDays}</span>天都在云钢工作</p>
              <p>您最晚登录的时间是</p>
              <p><span style="color:#FFE843">${this.extractDateComponent(data.sLastLoginDate, 'hour')}</span>点<span style="color:#FFE843">${this.extractDateComponent(data.sLastLoginDate, 'minute')}</span>分</p>
              <p>深夜中忙碌的身影</p>
              <p>不会只有大厦亮着的灯光静静注视着</p>
              <p>还有<span style="color:#00EBFF">云钢</span>默默替您记录着</p>
            `
        },
        {
          path: 'image_04.png',
          textPosition: { top: 20, left: 50 },
          data: `
              <p>最忙碌的一天</p>
              <p><span style="color:#FFE843">${this.extractDateComponent(data.sMaxinmumStockDate, 'year')}</span>年<span style="color:#FFE843">${this.extractDateComponent(data.sMaxinmumStockDate, 'month')}</span>月<span style="color:#FFE843">${this.extractDateComponent(data.sMaxinmumStockDate, 'day')}</span>日</p>
              <p>您在那天完成了<span style="color:#FFE843">${data.sMaxinmumStockQty}</span>个单据</p>
              <p>在云钢的一分一秒</p>
              <p>都在为公司添砖加瓦</p>
              `
        },
        {
          path: 'image_05.png',
          textPosition: { top: 27, left: 50 },
          data: `
              <div style="text-align: right;">
                <p>
                  <span style="color:#001E8E;background: #00EBFF;padding:4px">超级开票月</span>
                </p>
                <p><span style="color:#FFE843;font-size:70px;line-height: 1;">${data.sMaxinmumInvoiceMouth}</span>月</p>
                <p>您创造了本年最高的月开票金额</p>
                <p><span style="color:#00EBFF">${data.sMaxinmumInvoiceAmt}</span>元</p>
                <p>超过了<span style="color:#FFE843;">${data.sAnnualInvoicePercent * 100}</span>%的钢贸用户</p>
              </div>
            `
        },
        {
          path: 'image_06.png',
          textPosition: { top: 68, left: 50 },
          data: `
              <p>我们始终相信</p>
              <p>付出终会有回报</p>
              <p>这一年</p>
              <p>云钢帮您处理了<span style="color:#FFE843;">${data.sAnnualContractQty}</span>个合同</p>
              <p>合同金额高达<span style="color:#00EBFF;">${this.formatAmountToBillion(data.sAnnualContractAmt)}亿</span>元</p>
            `
        },
        {
          path: 'image_07.png',
          textPosition: { top: 22, left: 50 },
          data: `
              <div style="text-align: center;">
                <p>您经手的<span style="color:#FFE843;">${data.sAnnualSettleQty}</span>个结算单</p>
                <p>给公司带来了<span style="color:#00EBFF;">${data.sAnnualSettleProfit}</span>元的利润</p>
                <p>拥有如此傲人的成绩</p>
                <p>请为自己鼓鼓掌</p>
              </div>
            `
        },
        {
          path: 'image_08.png',
          textPosition: { top: 29, left: 50 },
          data: `
              <p>因为有您在</p>
              <p>我们才会越来越好</p>
              <p>今年我们累计完成需求<span style="color:#00EBFF;">11</span>条</p>
              <p>上线大需求有</p>
              <p>钢贸发货、自营、分货、赊销、加工等功能</p>
              <p>成功对接中拓、黔钢联、钢联的44个仓库</p>
              <p>云钢走的每一步</p>
              <p>都是为了提升您和客户的使用体验</p>
            `
        },
        {
          path: 'image_09.png',
          textPosition: { top: 64, left: 50 },
          data: `
              <div style="margin-left:30px">
                <p><span style="color:#00EBFF;">过去的一年</span></p>
                <p>感谢您对云钢的大力支持</p>
                <p><span style="color:#FFE843;">新的一年</span></p>
                <p>云钢也会与您同在</p>
                <p>愿您来过的所有日子</p>
                <p>都会和美好不期而遇</p>
              </div>
            `
        },
        {
          path: 'image_10.png',
          textPosition: { top: 22, left: 50 },
          data: `
            <div style="text-align: center;">
              <p style="line-height:1.5">亲爱的<span style="color:#FFE843;font-size: 30px;">${data.sUserName}</span></p>
              <p style="line-height:1.5">您在云钢的年度词条</p>
              <p style="color: white;text-shadow: 0 0 0 #00EAFF, 0 0 20px #00EAFF;font-size: 40px;line-height:1.5">${data.sBusinessEntry}</p>
            </div>
            `
        }
      ]
      if (!data.sMaxinmumStockQty) {
        images = images.filter(item => item.path !== 'image_04.png')
      }
      if (!data.sMaxinmumInvoiceAmt) {
        images = images.filter(item => item.path !== 'image_05.png')
      }
      if (!data.sAnnualContractAmt) {
        images = images.filter(item => item.path !== 'image_06.png')
      }
      if (!data.sAnnualSettleQty || data.sAnnualSettleProfit < 0) {
        images = images.filter(item => item.path !== 'image_07.png')
      }
      this.images = images
    },
    setImagesV2(data) {
      this.images = [
        {
          path: 'image_01.png'
        },
        {
          path: 'image_02.png',
          textPosition: { top: 30, left: 50 },
          data: `
              <p>亲爱的<span style="color:#FFE843">${data.sUserName}</span></p>
              <p><span style="color:#FFE843">${this.extractDateComponent(data.sUserCreateTime, 'year')}</span>年<span style="color:#FFE843">${this.extractDateComponent(data.sUserCreateTime, 'month')}</span>月<span style="color:#FFE843">${this.extractDateComponent(data.sUserCreateTime, 'day')}</span>日</p>
              <p>是您与云钢第一次相遇的日子</p>
            `
        },
        {
          path: 'image_03.png',
          textPosition: { top: 65, left: 50 },
          data: `
              <p>一年里，你共有<span style="color:#FFE843">${data.sWorkDays}</span>天都在云钢工作</p>
              <p>您最晚登录的时间是</p>
              <p><span style="color:#FFE843">${this.extractDateComponent(data.sLastLoginDate, 'hour')}</span>点<span style="color:#FFE843">${this.extractDateComponent(data.sLastLoginDate, 'minute')}</span>分</p>
              <p>深夜中忙碌的身影</p>
              <p>不会只有大厦亮着的灯光静静注视着</p>
              <p>还有<span style="color:#00EBFF">云钢</span>默默替您记录着</p>
            `
        },
        {
          path: 'image_09.png',
          textPosition: { top: 64, left: 50 },
          data: `
              <div style="margin-left:30px">
                <p><span style="color:#00EBFF;">过去的一年</span></p>
                <p>感谢您对云钢的大力支持</p>
                <p><span style="color:#FFE843;">新的一年</span></p>
                <p>云钢也会与您同在</p>
                <p>愿您来过的所有日子</p>
                <p>都会和美好不期而遇</p>
              </div>
            `
        },
        {
          path: 'image_10.png',
          textPosition: { top: 40, left: 50 },
          data: `
            <div style="text-align: center;">
              <p style="line-height:1.5">亲爱的<span style="color:#FFE843;font-size: 30px;">${data.sUserName}</span></p>
              <p style="line-height:1.5">您在云钢的年度词条</p>
              <p style="color: white;text-shadow: 0 0 0 #00EAFF, 0 0 20px #00EAFF;font-size: 40px;line-height:1.5">常来云钢看看</p>
            </div>
            `
        }

      ]
    },
    setImagesShare() {
      this.images = [{
        path: 'image_11.png',
        textPosition: { top: 22, left: 50 },
        data: `
            <div style="text-align: center;">
              <p style="line-height:1.5">亲爱的<span style="color:#FFE843;font-size: 30px;">${this.reportData.sUserName}</span></p>
              <p style="line-height:1.5">您在云钢的年度词条</p>
              <p style="color: white;text-shadow: 0 0 0 #00EAFF, 0 0 20px #00EAFF;font-size: 40px;line-height:1.5">${this.reportData.sBusinessEntry}</p>
            </div>
            `
      }
      ]
      this.$nextTick(() => {
        this.showImage = true
        this.currentIndex = 0
        this.$nextTick(() => {
          this.drawLine()
        })
      })
    },
    setImagesShareV2(data) {
      this.images = [
        {
          path: 'image_11.png',
          textPosition: { top: 40, left: 50 },
          data: `
            <div style="text-align: center;">
              <p style="line-height:1.5">亲爱的<span style="color:#FFE843;font-size: 30px;">${data.sUserName}</span></p>
              <p style="line-height:1.5">您在云钢的年度词条</p>
              <p style="color: white;text-shadow: 0 0 0 #00EAFF, 0 0 20px #00EAFF;font-size: 40px;line-height:1.5">常来云钢看看</p>
            </div>
            `
        }
      ]
      this.$nextTick(() => {
        this.showImage = true
        this.currentIndex = 0
        this.$nextTick(() => {
          this.drawLine()
        })
      })
    },
    handleCarouselChange(e) {},
    handleImageClick(event) {
      const clickY = event.clientY - event.target.getBoundingClientRect().top
      const imageHeight = event.target.clientHeight
      if (
        this.currentIndex === 0 &&
        (clickY < imageHeight * 0.2 || event.deltaY < 0)
      ) {
        return
      }
      if (
        this.currentIndex === this.images.length - 1 &&
        (clickY > imageHeight * 0.8 || event.deltaY > 0)
      ) {
        return
      }
      if (clickY > imageHeight * 0.8) {
        this.currentIndex = (this.currentIndex + 1) % this.images.length
        this.$refs.carousel.next()
      } else if (clickY < imageHeight * 0.2) {
        this.currentIndex =
          (this.currentIndex - 1 + this.images.length) % this.images.length
        this.$refs.carousel.prev()
      }
    },

    handleScroll(event) {
      if (event.deltaY > 0) {
        if (this.currentIndex < this.images.length - 1) {
          this.currentIndex = (this.currentIndex + 1) % this.images.length
          this.$refs.carousel.next()
        }
      } else {
        if (this.currentIndex > 0) {
          this.currentIndex =
            (this.currentIndex - 1 + this.images.length) % this.images.length
          this.$refs.carousel.prev()
        }
      }
    },

    handleTextClick() {},

    handleKeyDown(event) {
      if (this.share) {
        return
      }
      if (event.key === 'Escape' || event.key === 'Esc') {
        this.closeImages()
      }
    },
    closeImages() {
      this.showImage = false
    },
    showImages() {
      this.showImage = true
      this.currentIndex = 0
      this.$nextTick(() => {
        this.drawLine()
      })
    },
    drawLine() {
      if (!document.getElementById('myChart')) {
        return
      }
      const myChart = echarts.init(document.getElementById('myChart'))
      const { sAnnualContractQty, sAnnualContractAmt, sAnnualStockQty, sAnnualInvoiceAmt, sAnnualSettleProfit } = this.reportData
      myChart.setOption({
        series: [
          {
            type: 'gauge',
            startAngle: 90,
            endAngle: -270,
            pointer: {
              show: false
            },
            progress: {
              show: true,
              overlap: false,
              roundCap: true,
              clip: false,
              itemStyle: {
                borderWidth: 1,
                borderColor: '#464646'
              }
            },
            axisLine: {
              lineStyle: {
                width: 100,
                color: [
                  [1, 'rgba(0, 0, 0, 0)'] // 设置颜色为透明
                ]
              }
            },
            splitLine: {
              show: false,
              distance: 0,
              length: 10
            },
            axisTick: {
              show: false
            },
            axisLabel: {
              show: false,
              distance: 50
            },
            data: [
              {
                value: 75,
                // name: '',
                // title: {
                //   offsetCenter: ['-30%', '-95%']
                // },
                detail: {
                  valueAnimation: true,
                  offsetCenter: ['-70%', '-93%'],
                  formatter: (value, index) => {
                    return `合同金额(${this.formatAmount(sAnnualContractAmt)})`
                  },
                  color: '#F46BFF'
                },
                itemStyle: {
                  color: new echarts.graphic.LinearGradient(0, 1, 1, 1, [
                    { offset: 0, color: '#F46BFF' },
                    { offset: 1, color: '#A75BFF' }
                  ])
                }
              },
              {
                value: 65,
                // name: '',
                // title: {
                //   offsetCenter: ['-30%', '-82%']
                // },
                detail: {
                  valueAnimation: true,
                  offsetCenter: ['-70%', '-79%'],
                  formatter: (value, index) => {
                    return `累计开票金额(${this.formatAmount(sAnnualInvoiceAmt)})`
                  },
                  color: '#F3C9FF'
                },
                itemStyle: {
                  color: new echarts.graphic.LinearGradient(0, 1, 1, 1, [
                    { offset: 0, color: '#F3C9FF' },
                    { offset: 1, color: '#FF60F2' }
                  ])
                }
              },
              {
                value: 55,
                // title: {
                //   offsetCenter: ['-44%', '-69%']
                // },
                detail: {
                  valueAnimation: true,
                  offsetCenter: ['-70%', '-66%'],
                  formatter: (value, index) => {
                    return `累计结算利润(${this.formatAmount(sAnnualSettleProfit)})`
                  },
                  color: '#96D0FF'
                },
                itemStyle: {
                  color: new echarts.graphic.LinearGradient(0, 1, 1, 1, [
                    { offset: 0, color: '#96D0FF' },
                    { offset: 1, color: '#3DB0FF' }
                  ])
                }
              },
              {
                value: 45,
                // title: {
                //   offsetCenter: ['-40%', '-56%']
                // },
                detail: {
                  valueAnimation: true,
                  offsetCenter: ['-70%', '-53%'],
                  formatter: (value, index) => {
                    return `合同数量(${sAnnualContractQty}个)`
                  },
                  color: '#B2FFCE'
                },
                itemStyle: {
                  color: new echarts.graphic.LinearGradient(0, 1, 1, 1, [
                    { offset: 0, color: '#B2FFCE' },
                    { offset: 1, color: '#1BE26F' }
                  ])
                }
              },
              {
                value: 35,
                // title: {
                //   offsetCenter: ['-40%', '-43%']
                // },
                detail: {
                  valueAnimation: true,
                  offsetCenter: ['-70%', '-41%'],
                  formatter: (value, index) => {
                    return `操作物流单据量(${sAnnualStockQty}个)`
                  },
                  color: '#FFFD97'
                },
                itemStyle: {
                  color: new echarts.graphic.LinearGradient(0, 1, 1, 1, [
                    { offset: 0, color: '#FFFD97' },
                    { offset: 1, color: '#FFC827' }
                  ])
                }
              }

            ],
            title: {
              fontSize: '1.2vh',
              color: '#ffffff'
            },
            detail: {
              width: '100%',
              lineHeight: 1,
              fontSize: '1.2vh',
              color: '#ffffff'
              // borderColor: 'inherit',
              // borderRadius: 20,
              // borderWidth: 1
            }
          }
        ],
        animationDuration: 3000,
        animationDurationUpdate: 3000,
        animationEasing: 'linear',
        animationEasingUpdate: 'linear'
      })
    }
  }
}
</script>
<style lang="scss">
@import './assets/styles/index.scss';
</style>
