@font-face {
  font-family: 'SanJiChunLianZiTiJian';
  /* 设置字体名称 */
  src: url('./assets/fonts/SanJiChunLianZiTiJian-2.ttf') format('truetype');
  /* 引入字体文件 */
  font-weight: normal;
  font-style: normal;
}
.annual-review {
  .btn {
    margin-top: 6px;
    width: 102px;
    height: 36px;
    border-radius: 18px;
    color: white;
    background-image: linear-gradient(
      to top right,
      #fd004c,
      #fe9000,
      #fff020,
      #3edf4b,
      #3363ff,
      #b102b7,
      #fd004c
    );
    font-size: 16px;
    font-weight: bold;
    position: relative;
    letter-spacing: 2px;
  }
  .btn:after {
    content: attr(data-content);
    position: absolute;
    top: 0.1em;
    left: 0.1em;
    right: 0.1em;
    bottom: 0.1em;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #125092;
    border-radius: 18px;
  }
  .btn:hover {
    cursor: pointer;
  }
  .report {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 9999999;
  }

  .report-container {
    width: 100vw;
    /* 宽度相对于视口宽度 */
    height: 100vh;
    /* 高度相对于视口高度 */
    overflow: hidden;
    /* 隐藏溢出部分 */
    position: relative;
  }

  .mask {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: white;
    opacity: 0.5;
    /* 调整透明度 */
    z-index: 1;
    /* 确保遮罩层在图片上方 */
  }

  .carousel-container {
    width: 100vw;
    height: 100vh;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 2;
  }
  .image-container {
    width: 100vw;
    height: 100vh;
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
    position: relative;
  }
  .close-button {
    position: fixed;
    top: 0;
    right: 26px;
    cursor: pointer;
    font-size: 40px;
    color: #000;
    z-index: 3;
  }
  .text-container {
    font-family: SanJiChunLianZiTiJian;
    position: absolute;
    width: 50vh;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #ffffff;
    font-size: 22px;
  }

  .text-container p {
    margin: 12px 0;
    line-height: 1em;
  }

  .text-footer {
    position: absolute;
    color: #7eb5ce;
    font-family: none;
    line-height: 1.5;
    width: 50vh;
    top: 76%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 2vh;
    text-align: center;
  }

  .echart-container {
    width: 100vh;
    height: 50vh;
    position: absolute;
    top: 45%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
  .myChart {
    width: 50%;
    height: 95%;
    position: absolute;
    top: 68%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
  .qr-code {
    top: 135%;
    left: 30%;
    position: absolute;
    transform: translate(-50%, -50%);
    &-image {
      margin-bottom: -1vh;
      height: 10vh;
    }
    &-text {
      font-size: 1.5vh;
      color: #f8f8fa;
      line-height: 1;
      text-align: center;
    }
  }
}
