<template>
    <el-dialog
        title=""
        :visible.sync="showDialog"
        width="70%"
        center
        :modal="closeDialog"
        :close-on-click-modal="closeDialog"
        :lose-on-press-escape="closeDialog"
        show-close
    >
        <el-tabs v-model="activeName" type="border-card">
            <el-tab-pane v-for="(item,i) in content" :key="i" disabled :name="`tabs-${i}`" class="tab-message">
                <div slot="label" class="tab-title" :class="{'tab-title-bg':iconSucc[i] || activeName === `tabs-${i}` }" :title="item.sTitle">
                    <i v-show="iconSucc[i]" class="el-icon-success"></i>
                    {{ item.sTitle }}
                </div>
                <div class="details layout-content">
                  <div class="details-title">{{ curDetail.sTitle }}</div>
                  <div class="details-hint">
                      {{ handleTime(curDetail.sPublishTimes)  }}<span>|</span>{{ curDetail.sCreatorName }}
                  </div>
                  <div class="details-line" />
                  <div class="details-content" :class="flieLen">
                    <Editor
                      v-model="curDetail.sContent"
                      class="editor"
                      :default-config="{
                        readOnly: true
                      }"
                      :mode="mode"
                      @onCreated="onCreated"
                    />
                  </div>
                  <div class="details-upload-file">
                    <p v-for="(val, index) in uploadList" :key="index">
                      <span @click="upLoadFile(val.sId, val.sName)">{{ val.sName }}</span>
                    </p>
                  </div>
                </div>
                <div class="close-btn">
                  <el-button v-if="i !== (content.length - 1)" type="primary" size="mini" @click="handleChange(i, item)">我已知晓,下一个</el-button>
                  <el-button v-else type="primary" size="mini" @click="handleClose(i,item)">我已知晓</el-button>
                   <p v-if="i !== (content.length - 1)" class="read-all"  @click="handleCloseAll(i,item)">全部已读</p>
                </div>
                <!-- <p class="read-all"  @click="handleCloseAll(i,item)">全部已读</p> -->
            </el-tab-pane>
        </el-tabs>
    </el-dialog>
</template>

<script>
import { Moment } from 'cnd-utils'
import { getAnnounceList, browsingStatus, browsingDetailById, postList } from '@/api/message.js'
import axios from 'axios'
import { getToken } from 'cnd-horizon-utils/src/utils/auth'
import { Editor } from '@wangeditor/editor-for-vue'
export default {
  name: 'DialogMessage',
  components: { Editor },
  data() {
    return {
      showDialog: false,
      closeDialog: false,
      activeName: 'tabs-0',
      iconSucc: [],
      // 消息数据--------模拟
      curSNoticeId: '',
      content: [
        // {
        //   sUserId: '9219a9dc-b54b-480f-b8eb-fcd9220d8ce7',
        //   sNoticeId: '3638911182395662336',
        //   sTitle: '212',
        //   sPublishTimes: '1231654645',
        //   sInformType: '1',
        //   sRead: '1',
        //   sInvalid: '1',
        //   sCreator: 'cjd',
        //   sCreateTime: '2024-09-27T15:37:11'
        // },
        // {
        //   sUserId: '9219a9dc-b54b-480f-b8eb-fcd9220d8ce7',
        //   sNoticeId: '3638911182395662336',
        //   sTitle: '212',
        //   sPublishTimes: '1231654645',
        //   sInformType: '1',
        //   sRead: '1',
        //   sInvalid: '1',
        //   sCreator: 'cjd',
        //   sCreateTime: '2024-09-27T15:37:11'
        // }
      ],
      pagination: {
        page: 0,
        limit: 9999
      },
      curDetail: {},
      uploadList: [],
      // 富文本编辑器mode/editor
      mode: 'default',
      editor: null
    }
  },
  computed: {
    flieLen() {
      let res
      if (this.uploadList.length > 2) {
        res = ''
      } else if (this.uploadList.length === 2) {
        res = 'details-content-two'
      } else if (this.uploadList.length === 1) {
        res = 'details-content-one'
      } else {
        res = 'details-content-three'
      }
      return res
    }
  },
  mounted() {
    this.getAnnounce()
  },
  methods: {
    getAnnounce() {
      return new Promise((resolve, reject) => {
        getAnnounceList({
          sRead: '0',
          sInvalid: '0',
          sInformType: '1'
        },
        this.pagination
        ).then(res => {
          this.content = res.data.content
          if (this.content.length) {
            this.showDialog = true
            this.getBrowsingDetail(this.content[0])
            browsingStatus([this.content[0].sNoticeId]).catch(res => {})
          }
          resolve(res.data)
        }).catch(() => {
          this.showDialog = false
          reject([])
        })
      })
    },
    getBrowsingDetail(val) {
      browsingDetailById(val.sId).then((res) => {
        console.log('获取模态弹窗列表--用户公告详情', res.data)
        this.curDetail = res.data
      })
      postList({ sBusId: val.sNoticeId, pageNo: 0, pageSize: 99 }).then((res) => {
        this.uploadList = res.data.content
      })
    },
    async handleChange(val, item) {
      if (val !== undefined) {
        this.iconSucc.push(true)
        await this.getBrowsingDetail(this.content[val + 1])
        this.activeName = `tabs-${val + 1}`
        browsingStatus([item.sNoticeId]).catch(res => {})
      }
    },
    handleClose(val, item) {
      this.showDialog = false
      this.$store.dispatch('user/firstLoading', false)
      browsingStatus([item.sNoticeId]).catch(res => {})
    },
    handleCloseAll() {
      this.showDialog = false
      this.$store.dispatch('user/firstLoading', false)
      // 缺一个全部已读的消息状态管理接口
      const sIds = this.content.map((item, index) => {
        return item.sNoticeId
      })
      browsingStatus(sIds).catch(res => {})
    },
    // 时间格式
    handleTime(time) {
      return Moment.time('YYYY-MM-DD HH:mm:ss', time)
    },
    upLoadFile(sId, sName) {
      console.log('params--', sId, sName)
      axios.post(
        process.env.VUE_APP_BASE_API + '/annex/oss/download/' + sId,
        {},
        {
          responseType: 'blob',
          headers: {
            Authorization: getToken()
          }
        }
      ).then((res) => {
        const blob = new Blob([res.data]) // 处理文档流
        const fileName = sName
        const elink = document.createElement('a')
        elink.download = fileName
        elink.style.display = 'none'
        elink.href = URL.createObjectURL(blob)
        document.body.appendChild(elink)
        elink.click()
        URL.revokeObjectURL(elink.href) // 释放URL 对象
        document.body.removeChild(elink)
      })
    },
    // 初始化编辑器
    onCreated(editor) {
      this.editor = Object.seal(editor) // 一定要用 Object.seal() ，否则会报错
    }
  }
}
</script>
<style src="@wangeditor/editor/dist/css/style.css"></style>
<style lang="scss" scoped>
.tab-message {
    position: relative;
    .close-btn {
        position: absolute;
        bottom: 0px;
        left: 50%;
        transform: translateX(-50%);
    }
}
.tab-title {
    // padding: 0 20px;
    color: #000;
    overflow:hidden;
    text-overflow:ellipsis;
    white-space:nowrap;
}
.tab-title-bg {
    background-color: rgb(24, 144, 255);
    color: #fff;
    padding: 0 7px;
}
.details {
  margin: 0 auto;
  padding: 0 20px 20px 20px;
  width: 94%;
   min-height: 600px;
  &-title {
    font-size: 20px;
    color: rgb(85, 85, 85);
    line-height: 28px;
    text-align: center;
  }
  &-hint {
    font-size: 14px;
    color: rgb(144, 157, 153);
    line-height: 20px;
    text-align: center;
    span {
        font-size: 13px;
      display: inline-block;
      padding: 0 10px;
    }
  }
  &-line {
    width: 100%;
    height: 1px;
    margin: 5px auto 0 auto;
  }
  &-content {
    height: 380px;
    padding: 20px;
    overflow-y: auto;
    border-top: 1px solid #dfe4ed;
    border-bottom: 1px solid #dfe4ed;
    text-align: inherit;
  }
  &-content-one {
    height: 455px;
  }
  &-content-two {
    height: 450px;
  }
  &-content-three {
    height: 470px;
  }
  &-upload-file {
    // margin-top: 20px;
    padding-left: 20px;
    text-align: left;
    color: rgb(24, 144, 255);
    font-size: 14px;
    cursor: pointer;
    max-height: 105px;
    overflow: auto;
  }
  &-upload-file p {
    margin-bottom: 15px;
  }
}
.read-all {
    color: rgb(85, 85, 85);
    text-decoration-line: underline;
    text-align: center;
    cursor: pointer;
    display: inline-block;
    margin-left: 20px;
}
::v-deep .el-dialog__header,
::v-deep .el-dialog__body {
    padding: 0 !important;
    .el-tabs__header {
        padding-right: 30px !important;
    }
}
::v-deep .el-dialog__header .el-dialog__headerbtn {
  z-index: 99;
  top: 13px;
  right: 7px;
}
::v-deep .el-tabs__nav .el-tabs__item {
    padding: 0;
    width: 150px;
    text-align: center;
    overflow:hidden;
    text-overflow:ellipsis;
    white-space:nowrap;
}
::v-deep .el-tabs--top.el-tabs--border-card>.el-tabs__header .el-tabs__item:nth-child(2) {
    padding: 0 !important;
}
::v-deep .el-tabs--top.el-tabs--border-card>.el-tabs__header .el-tabs__item:last-child {
    padding-right: 0 !important;
}
</style>
