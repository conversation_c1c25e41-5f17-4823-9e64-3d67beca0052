<!--
    @component searchform 查询条件组件
    @props
        {Array} formItems - 搜索表单配置  []
        {String} labelWidth - 表单label宽度  '120px'
    @formItems {Object}
        属性名 | 描述 | 类型 | 默认值
        label 项目名 String -
        value 项目值 对应提交给服务端的key [String|Array] -
        placeholder 输入框占位文本 [String|Array] -
        default 默认值 null -
        hidden 隐藏 隐藏该项目 但值依旧存在
        disabled 禁用 Boolean false
        required 必填 Boolean false
        errorMsg 校验错误信息 String -
        customWidth 删格化占用栏位 一行24格 具体继承自cnd-form-item、elcol、elrow删格化参数
        itemType 该项的类型 String 默认为存在
          occultation 可展开收起 隐藏的项
          display 一直存在的 不可展开收起
        type 该项的类型 elInput, elSelect, elDatePicker, cndInputDialog
          elSelect 下拉框
              dict 对应的字典key 用于服务端请求 也可是数组需要和服务端对应 [Array|String]
                  [{ sCodeName: 'name', sCodeValue: 123 }]
                  or 'base.yes-no'
          elDatePicker 日期选择框
              *!!* value、placeholder、default 转为Array传输格式
              value 对应用户选择的第一个时间第二个时间 Array
                  ['startTime', 'endTime']
              default 默认值
                  ['2020-10-01', new Date().getTime()],
              placeholder 输入框占位文本 Array
          dateType 日期类型选择  datetimerange为日期时间选择器，如果不传为默认daterange
          cndInputDialog 全文检索框
              labelDefault 全文检索框的文本值
              dialogType 对应的业务弹窗类型
                  area 地区
                  artno 货号
                  cost 核算组
                  company 公司
                  customer 客商、供应商、客户
                  currency 货币
                  depart 部门
                  measurementunit 单位
                  staff 人员
                  creater 创建人
                  warehouse 仓库
                  portcity 港口
                  finance 财务组织 // 暂无
                  project 项目 // 暂无
              customerType 若dialogType类型为customer时需要传
                  null/undefined 返回所有
                  "10" 客商、客户、发票客户
                  "20" 供应商、开票单位、物流供应商
                  "30" 委托方
                  "40" 服务供应商
                  "50" 货主
                  "99" 其他
    @methods
        submit Function 点击查询并通过校验 才会有回调事件
        getSearchData Function 获取表单数据
          @return Object
    @demo
        /src/views/aggridDemo/masterDetailLazy.vue
    <AUTHOR>
    @date 2020-12-17 14:30:42
    @updata 2020-12-30 15:44
-->
<template>
  <cnd-form-card-list
    ref="panelSerch"
    :active-panel="steelActivePanel"
    class-name="filter-collapse-container"
    :change="(v) => (steelActivePanel = v)"
  >
    <cnd-form-card :title="$t('grid.others.queryConditions')" name="1">
      <cnd-filter
        ref="cndFilter"
        v-resize="filterWidth"
        ref-form="searchInfo"
        :model="searchInfo"
        :rules="searchRules"
        :no-more="!Object.keys(splitFormitems[1]).length"
        show-button
        :params-button="paramsButton"
        :label-width="labelWidth"
        @action="filterAction"
      >
        <template
          v-for="(formItemObj, typeIndex) in splitFormitems"
          :slot="typeIndex ? 'occultationForm' : 'displayForm'"
        >
          <!-- <el-row v-for="(v, k) in formItemObj" :key="k"> -->
          <cnd-form-item
            v-for="(item, index) in formItemObj"
            :key="index"
            :label="item.label"
            :custom-width="+item.customWidth || undefined"
            :prop="
              ['elDatePicker','elCheckbox','doubleInput','doubleInputNumber'].includes(item.type)
                ? item.value[0]
                : item.value
            "
            :error-msg="item.errorMessage || $t('components.pleaseEnter')"
          >
            <!-- 输入框 -->
            <el-input
              v-if="item.type === 'elInput'"
              v-model="searchInfo[item.value]"
              size="mini"
              :placeholder="
                item.placeholder
                  ? item.placeholder
                  : `${$t('components.pleaseEnter')}${item.label}`
              "
              :clearable="true"
              :disabled="item.disabled || false"
              @keyup.native.stop.enter="submit(true, true)"
              @clear="submit(false, isAutoQuery)"
            />
            <div v-else-if="item.type === 'elInputText'" class="flexCC">
              <el-input
                v-model="searchInfo[item.value]"
                size="mini"
                :placeholder="
                  item.placeholder
                    ? item.placeholder
                    : `${$t('components.pleaseEnter')}${item.label}`
                "
                :clearable="true"
                :disabled="item.disabled || false"
                :rows="3"
                type="textarea"
                @keyup.native.stop.enter="submit(true, true)"
                @clear="submit(false, isAutoQuery)"
              />
            </div>

            <div v-else-if="item.type === 'doubleInput'" class="flexCC">
              <el-input
                v-model="searchInfo[item.value[0]]"
                size="mini"
                :placeholder="
                  item.placeholder
                    ? `${$t('components.pleaseEnter')}${item.placeholder[0]}`
                    : ''
                "
                :clearable="true"
                :disabled="item.disabled || false"
                @keyup.native.stop.enter="submit(true, true)"
                @clear="submit(false, isAutoQuery)"
              />
              <el-input
                v-model="searchInfo[item.value[1]]"
                size="mini"
                :placeholder="
                  item.placeholder
                    ? `${$t('components.pleaseEnter')}${item.placeholder[1]}`
                    : ''
                "
                :clearable="true"
                :disabled="item.disabled || false"
                @keyup.native.stop.enter="submit(true, true)"
                @clear="submit(false, isAutoQuery)"
              />
            </div>
            <div v-else-if="item.type === 'doubleInputNumber'" class="flexCC">
              <cnd-input-number
                v-model="searchInfo[item.value[0]]"
                size="mini"
                :placeholder="
                  item.placeholder
                    ? `${$t('components.pleaseEnter')}${item.placeholder[0]}`
                    : ''"
                :type="item.numberType || 'number'"
                :negative="item.negative || false"
                :disabled="item.disabled || false"
                @keyup.native.stop.enter="submit(true, true)"
                @clear="submit(false, isAutoQuery)"
              />
              <div style="width:10%;text-align:center;">-</div>
              <cnd-input-number
                v-model="searchInfo[item.value[1]]"
                size="mini"
                :placeholder="
                  item.placeholder
                    ? `${$t('components.pleaseEnter')}${item.placeholder[1]}`
                    : ''"
                :type="item.numberType || 'number'"
                :negative="item.negative || false"
                :disabled="item.disabled || false"
                @keyup.native.stop.enter="submit(true, true)"
                @clear="submit(false, isAutoQuery)"
              />
            </div>
            <!-- 复选框 -->
            <el-checkbox
              v-else-if="item.type === 'elCheckbox'"
              v-model="searchInfo[item.value]"
              :true-label="item.trueLabel"
              :false-label="item.falseLabel"
              :checked="item.checked"
              :label="item.value"
              :disabled="item.disabled"
            >{{ item.placeholder }}</el-checkbox>

            <!-- 下拉框 -->
            <el-select
              v-else-if="item.type === 'elSelect'"
              v-model="searchInfo[item.value]"
              size="mini"
              filterable
              clearable
              :multiple="item.multiple ? item.multiple : false"
              :placeholder="item.placeholder"
              :disabled="item.disabled || false"
              @change="
                selectGet(
                  typeof item.dict === 'string'
                    ? dictList[hyphenate(item.dict)]
                    : item.dict,
                  searchInfo[item.value],
                  item.selectKey,
                  item.value
                )
              "
            >
              <el-option
                v-if="!item.multiple && !item.allHide"
                key="all"
                :label="$t('grid.others.all')"
                :value="undefined"
              />
              <el-option
                v-for="option in item.dict instanceof Array
                  ? item.dict
                  : dictList[hyphenate(item.dict)]"
                :key="option.sCodeValue"
                :label="option.sCodeName"
                :value="option.sCodeValue"
              />
            </el-select>
            <!-- 级联选择 -->
            <el-cascader
              v-else-if="item.type === 'elCascader'"
              v-model="searchInfo[item.value]"
              size="mini"
              clearable
              filterable
              :options="item.dict"
              :placeholder="item.placeholder"
              :disabled="item.disabled || false"
              :props="item.props ? item.props : {}"
              @change="
                cascaderGet(
                  item.dict,
                  searchInfo[item.value],
                  item.selectKey,
                  item.value
                )
              "
            />
            <!-- 日期选择 -->
            <el-date-picker
              v-else-if="
                item.type === 'elDatePicker' && item.value.length === 2
              "
              v-model="dateTimePick[`${item.value[0]}${item.value[1]}`]"
              :disabled="item.disabled || false"
              :type="item.dateType ? item.dateType : 'daterange'"
              size="mini"
              :unlink-panels="item.unlinkPanels"
              :start-placeholder="
                item.placeholder && item.placeholder.length
                  ? item.placeholder[0]
                  : $t('grid.others.startDate')
              "
              :end-placeholder="
                item.placeholder && item.placeholder.length > 1
                  ? item.placeholder[1]
                  : $t('grid.others.endDate')
              "
              :default-time="['00:00:00', '23:59:59']"
              :picker-options="
                item.pickerOptions ? item.pickerOptions : pickerOptions
              "
              align="right"
              @change="handleChangeTime($event, item.value)"
            />

            <el-date-picker
              v-else-if="
                item.type === 'elDatePicker' && item.value.length !== 2
              "
              v-model="dateTimePick[`${item.value[0]}`][0]"
              :disabled="item.disabled || false"
              :type="item.dateType ? item.dateType : 'daterange'"
              size="mini"
              :placeholder="
                item.placeholder && item.placeholder.length
                  ? item.placeholder[0]
                  : $t('grid.others.startDate')
              "
              :picker-options="
                item.pickerOptions ? item.pickerOptions : pickerOptions
              "
              align="right"
              @change="handleChangeTime($event, item.value)"
            />
            <!-- 点击弹窗 -->
            <!-- <cnd-input-dialog
                v-else-if="item.type === 'cndInputDialog'"
                v-model="searchInfo[`_preview${item.value}`]"
                :placeholder="item.placeholder"
                @focus="openDialog(item.value, item.dialogType)"
              /> -->
            <horizon-search-select
              v-else-if="item.type === 'cndInputDialog'"
              v-model="searchInfo[`_preview${item.value}`]"
              :default-url="item.defaultUrl"
              :type="item.dialogType"
              :customer-type="getCustomerType(item)"
              :artno-type="item.artnoType"
              :categories="item.categories"
              :search-key="item.searchKey"
              :disabled="item.disabled || false"
              :multiple="item.multiple ? item.multiple : false"
              :clearable="item.clearable"
              :label="item.label"
              :placeholder="item.placeholder"
              :option="item.option"
              :method="item.method"
              :currency="item.currency"
              :label-default="item.labelDefault"
              :content="item.content"
              :cancel-page="item.cancelPage"
              :other-options="item.otherOptions"
              :first-call="item.firstCall"
              @change="handleChangeSelect($event, item)"
            />

            <horizon-search-select-item
              v-else-if="item.type === 'cndInputDialogItem'"
              v-model="searchInfo[`_preview${item.value}`]"
              :default-url="item.defaultUrl"
              :type="item.dialogType"
              :customer-type="getCustomerType(item)"
              :artno-type="item.artnoType"
              :categories="item.categories"
              :search-key="item.searchKey"
              :disabled="item.disabled || false"
              :multiple="item.multiple ? item.multiple : false"
              :clearable="item.clearable"
              :label="item.label"
              :placeholder="item.placeholder"
              :option="item.option"
              :method="item.method"
              :currency="item.currency"
              :label-default="item.labelDefault"
              :content="item.content"
              :cancel-page="item.cancelPage"
              :other-options="item.otherOptions"
              :first-call="item.firstCall"
              @change="handleChangeSelect($event, item)"
            />
          </cnd-form-item>
          <!-- </el-row> -->
          <!-- <el-row :key="typeIndex">
            <cnd-form-item
              v-for="(item, index) in formItemList"
              :key="index"
              :label="item.label"
              :custom-width="+item.customWidth || undefined"
              :prop="item.type === 'elDatePicker' ? item.value[0] : item.value"
              :error-msg="item.errorMessage || '请输入'"
            >
              <el-input
                v-if="item.type === 'elInput'"
                v-model="searchInfo[item.value]"
                :placeholder="item.placeholder"
                :clearable="true"
                :disabled="item.disabled || false"
                @keyup.native.stop.enter="submit"
                @clear="submit(false)"
              />

              <el-select
                v-else-if="item.type === 'elSelect'"
                v-model="searchInfo[item.value]"
                :placeholder="item.placeholder"
                :disabled="item.disabled || false"
                @change="selectGet(dictList[hyphenate(item.dict)], searchInfo[item.value], item.selectKey, item.value)"
              >
                <el-option key="all" label="全部" :value="undefined" />
                <el-option
                  v-for="option in item.dict instanceof Array
                    ? item.dict
                    : dictList[hyphenate(item.dict)]"
                  :key="option.sCodeValue"
                  :label="option.sCodeName"
                  :value="option.sCodeValue"
                />
              </el-select>

              <el-date-picker
                v-else-if="item.type === 'elDatePicker'"
                v-model="dateTimePick[`${item.value[0]}${item.value[1]}`]"
                :disabled="item.disabled || false"
                :type="item.dateType ? item.dateType : 'daterange'"
                size="mini"
                :start-placeholder="
                  item.placeholder && item.placeholder.length
                    ? item.placeholder[0]
                    : '开始日期'
                "
                :end-placeholder="
                  item.placeholder && item.placeholder.length > 1
                    ? item.placeholder[1]
                    : '结束日期'
                "
                :picker-options="item.pickerOptions ? item.pickerOptions : pickerOptions"
                :default-time="['00:00:00', '23:59:59']"
                @change="handleChangeTime($event, item.value)"
              />

              <horizon-search-select
                v-else-if="item.type === 'cndInputDialog'"
                v-model="searchInfo[`_preview${item.value}`]"
                :type="item.dialogType"
                :placeholder="item.placeholder"
                :disabled="item.disabled || false"
                :customer-type="getCustomerType(item)"
                @change="handleChangeSelect($event, item)"
              />
            </cnd-form-item>
          </el-row> -->
        </template>
        <template slot="controlBtnGroup">
          <slot name="others-button" />
          <el-tooltip
            v-model="tips"
            class="item"
            popper-class="key-tips"
            effect="dark"
            content="Ctrl + Enter"
            placement="top-start"
          >
            <el-button
              type="primary"
              size="mini"
              native-type="submit"
              @click="submit(true, true)"
            >
              {{ $t("components.inquiry") }}
            </el-button>
          </el-tooltip>
          <el-button size="mini" @click="reset">
            {{ $t("components.reset") }}
          </el-button>
        </template>
      </cnd-filter>
    </cnd-form-card>

    <!-- 若扩展业务组件请在下方添加 -->
    <!-- 人员 -->
    <!-- <horizon-staff-dialog
      v-if="dialogVisible.staff !== undefined"
      title="人员查询"
      org-type="100"
      :append-to-body="true"
      :visible="dialogVisible.staff"
      @onSelect="onDialogSelect($event, 'staff')"
    /> -->
    <!-- 核算组 -->
    <!-- <horizon-cost-dialog
      v-if="dialogVisible.cost !== undefined"
      title="核算组查询"
      :append-to-body="true"
      :visible="dialogVisible.cost"
      @onSelect="onDialogSelect($event, 'cost')"
    /> -->
    <!-- 客商、供应商、客户 -->
    <!-- <horizon-customer-dialog
      v-if="dialogVisible.customer !== undefined"
      title="客商查询"
      :append-to-body="true"
      :visible="dialogVisible.customer"
      @onSelect="onDialogSelect($event, 'customer', 'sId', 'sName')"
    /> -->
    <!-- 公司 -->
    <!-- <horizon-company-dialog
      v-if="dialogVisible.company !== undefined"
      title="公司查询"
      :append-to-body="true"
      :visible="dialogVisible.company"
      @onSelect="onDialogSelect($event, 'company')"
    /> -->
    <!-- 部门 -->
    <!-- <horizon-depart-dialog
      v-if="dialogVisible.depart !== undefined"
      title="部门查询"
      :append-to-body="true"
      :visible="dialogVisible.depart"
      @onSelect="onDialogSelect($event, 'depart')"
    /> -->
    <!-- 财务组织 -->
    <!-- <horizon-finance-dialog
      v-if="dialogVisible.finance !== undefined"
      title="财务组织查询"
      :append-to-body="true"
      :visible="dialogVisible.finance"
      @onSelect="onDialogSelect($event, 'finance')"
    /> -->
    <!-- 仓库 -->
    <!-- <horizon-warehouse-dialog
      v-if="dialogVisible.warehouse !== undefined"
      title="仓库查询"
      :append-to-body="true"
      :visible="dialogVisible.warehouse"
      @onSelect="onDialogSelect($event, 'warehouse', 'sId', 'sName')"
    /> -->
    <!-- 项目 -->
    <!-- <cnd-dialog-table
      v-if="dialogVisible.project !== undefined"
      title="项目选择"
      show-search
      :visible="dialogVisible.project"
      :column-defs="[
        { field: 'sName', headerName: '项目名称', width: 100 },
        { field: 'sCode', headerName: '项目号', width: 100 },
      ]"
      :req-config="{
        method: 'post',
        url: '/prj/prjproject/dialog/page/{pageNo}/{pageSize}',
        keyword: 'sName',
        params: {
          pageNo: 0,
          pageSize: 20,
        },
        dataFormat: {
          key: 'content',
          total: 'totalElements',
        },
      }"
      :show-pagination="false"
      @onSelect="onDialogSelect($event, 'project', 'sId', 'sCode')"
    /> -->
  </cnd-form-card-list>
</template>

<script>
// 保存参数 选择参数
// 必填校验功能
import { DictUtil } from 'cnd-horizon-utils'
import moment from 'moment'

export default {
  name: 'CndSearchForm',
  props: {
    // 表单项配置列表
    formItems: {
      type: Array,
      required: true
    },
    // 表单label宽度
    labelWidth: {
      type: String,
      default: '85px'
    },
    paramsButton: {
      type: Boolean,
      default: true
    },
    // 面板激活状态
    activePanel: {
      type: String,
      default: '1'
    },
    triggerFilter: {
      type: Boolean,
      default: false
    },
    // 是否自动查询
    autoQuery: {
      type: String,
      default: ''
    },
    lineLimit: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      steelActivePanel: '1',
      formItemsReset: this.deepClone(this.formItems),
      // formItemsReset: JSON.parse(JSON.stringify(this.formItems)),
      tips: false,
      // isAutoQuery: false,
      isAutoQuery:
        this.autoQuery !== ''
          ? this.autoQuery
          : this.getAutoQuery('VUE_APP_AUTO_QUERY')
            ? this.getAutoQuery('VUE_APP_AUTO_QUERY')
            : false,
      width: document.body.clientWidth,
      num: 0,
      dictList: {},
      searchInfo: this.initForm(),
      searchRules: this.initFormRules(),
      // activeDialog: null,
      // dialogVisible: this.initDialogVisible(),
      dateTimePick: this.initDateTimePick(),
      splitFormitems: [],
      pickerOptions: {
        shortcuts: [
          {
            text: this.$t('grid.others.lastWeek'),
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: this.$t('grid.others.mostRecentMonth'),
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: this.$t('grid.others.lastThreeMonths'),
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
              picker.$emit('pick', [start, end])
            }
          }
        ]
      },
      isClickReset: false
    }
  },
  watch: {
    activePanel(value) {
      this.steelActivePanel = value
      try {
        console.log(value)
        if (Array.isArray(value)) {
          window.collectEvent('advanced_search_toggle', {
            toggle_type: value.length === 2 ? 'unfold' : 'fold'
          })
        }
      } catch (e) {
        console.log(e)
      }
    }
  },
  beforeMount() {
    this.loadDict()
    this.filterWidth()
  },
  created() {
    window.addEventListener('keydown', this.handkeyCode, true) // 开启监听键盘按下事件
  },
  methods: {
    forEach(array, iteratee) {
      let index = -1
      const length = array.length
      while (++index < length) {
        iteratee(array[index], index)
      }
      return array
    },
    deepClone(target, map = new WeakMap()) {
      if (typeof target === 'object') {
        const isArray = Array.isArray(target)
        const cloneTarget = isArray ? [] : {}

        if (map.get(target) || !target) {
          return target
        }
        map.set(target, cloneTarget)

        const keys = isArray ? undefined : Object.keys(target)
        this.forEach(keys || target, (value, key) => {
          if (keys) {
            key = value
          }
          cloneTarget[key] = this.deepClone(target[key], map)
        })

        return cloneTarget
      } else {
        return target
      }
    },
    handkeyCode(event) {
      if (event.ctrlKey && event.keyCode === 13) {
        this.submit(true, true)
      }
      // let key = null
      // console.log('window.event', window.event)
      // console.log('window.keyCode', window.event.keyCode)
      // if (window.event === undefined) {
      //   key = e.keyCode
      // } else {
      //   key = window.event.keyCode
      // }
      // console.log('key', key)
      // if (key === 13) {
      //   this.submit('', true)
      // }
    },
    getAutoQuery(name) {
      return window.configs &&
        window.configs[name] &&
        process.env.NODE_ENV !== 'development'
        ? window.configs[name]
        : process.env[name]
    },
    filterWidth() {
      const width = document.body.clientWidth
      width > 1200
        ? (this.num = 8)
        : width < 1200 && width > 767
          ? (this.num = 4)
          : (this.num = 2)
      this.splitFormItem(this.num)
    },
    splitFormItem(num) {
      const splitFormItems = [[], []]
      this.formItems.forEach((item, index) => {
        if (!item.hidden) {
          this.lineLimit
            ? index < num
              ? splitFormItems[0].push(item)
              : splitFormItems[1].push(item)
            : splitFormItems[0].push(item)
        }
      })
      this.splitFormitems = splitFormItems

      // const splitFormItems = [[], []]
      // let occultationNum = 0
      // let displayFormNum = 0
      // this.formItems.forEach((item, index) => {
      //   if (!item.hidden) {
      //     if (item.itemType && item.itemType === 'occultation') {
      //       if (splitFormItems[1][tempIndex]) {
      //         splitFormItems[1][tempIndex].push(item)
      //       } else {
      //         splitFormItems[1][tempIndex] = [item]
      //       }
      //       occultationNum++
      //     } else {
      //       const tempIndex = Math.floor(displayFormNum / 4)
      //       if (splitFormItems[0][tempIndex]) {
      //         splitFormItems[0][tempIndex].push(item)
      //       } else {
      //         splitFormItems[0][tempIndex] = [item]
      //       }
      //       displayFormNum++
      //     }
      //   }
      // })
      // this.splitFormitems = splitFormItems
    },
    initDateTimePick(formItems = this.formItems) {
      const result = {}
      formItems.forEach((item) => {
        if (item.type === 'elDatePicker') {
          const dateValue =
            item.default && item.default.length
              ? item.default
              : [
                moment()
                  .subtract(3, 'months')
                  .startOf('day')
                  .format('YYYY-MM-DDTHH:mm:ss'),
                moment().endOf('day').format('YYYY-MM-DDTHH:mm:ss')
              ]
          if (item.value[1]) {
            result[`${item.value[0]}${item.value[1]}`] = dateValue
          } else {
            result[`${item.value[0]}`] = dateValue
          }
        }
      })
      return result
    },
    // 初始化表单值
    initForm(formItems = this.formItems, reset = false) {
      return formItems.reduce((prev, next) => {
        if (next.type === 'elDatePicker') {
          const dateValue =
            next.default && next.default.length
              ? next.default
              : [
                moment()
                  .subtract(3, 'months')
                  .startOf('day')
                  .format('YYYY-MM-DDTHH:mm:ss'),
                moment().endOf('day').format('YYYY-MM-DDTHH:mm:ss')
              ]
          dateValue[1] &&
            (dateValue[1] =
              moment(dateValue[1]).format('LTS') === '00:00:00'
                ? moment(dateValue[1]).endOf('day')._d
                : moment(dateValue[1]).format('YYYY-MM-DD HH:mm:ss'))
          prev[next.value[0]] = dateValue[0]
          prev[next.value[1]] = dateValue[1]
          reset &&
            next.value[1] &&
            next.default &&
            (this.dateTimePick[`${next.value[0]}${next.value[1]}`] = [
              next.default[0],
              next.default[1]
            ])
          reset &&
            !next.value[1] &&
            next.default &&
            (this.dateTimePick[`${next.value[0]}`] = [next.default[0]])
          return prev
        }

        // 若为弹窗选择储存name以用于保存参数
        if (
          next.type === 'cndInputDialog' ||
          next.type === 'cndInputDialogItem'
        ) {
          prev[`_preview${next.value}`] = next.labelDefault || undefined
        }
        if (next.type === 'elCheckbox') {
          prev[next.value] =
            next.disabled && reset ? this.searchInfo[next.value] : false
        } else {
          prev[next.value] =
            next.disabled && reset ? this.searchInfo[next.value] : next.default
        }
        return prev
      }, {})
    },
    // 初始化弹窗显示值控制对象
    // initDialogVisible(formItems = this.formItems) {
    //   return formItems.reduce((prev, next) => {
    //     if (next.dialogType) {
    //       prev[next.dialogType] = false
    //     }
    //     return prev
    //   }, {})
    // },
    initFormRules() {
      const result = {}
      this.formItems.forEach((item) => {
        if (item.required) {
          if (item.type === 'elDatePicker') {
            result[item.value[0]] = [
              {
                validator: (rule, value, cb) => {
                  if (
                    this.searchInfo[item.value[0]] ||
                    this.searchInfo[item.value[1]]
                  ) {
                    cb()
                  } else {
                    cb(new Error('no'))
                  }
                },
                trigger: ['blur', 'change'],
                required: true
              }
            ]
          } else {
            result[item.value] = [
              {
                required: true,
                trigger: ['blur', 'change']
              }
            ]
          }
        }
      })

      return result
    },
    // 加载字典
    loadDict() {
      const needLoadDict = this.unique(
        this.formItems
          .map((item) => item.dict)
          .filter((item) => {
            return item && !(item instanceof Array)
          })
      )
      if (needLoadDict.length) {
        DictUtil.getDict(needLoadDict, (res) => {
          res.forEach((item) => {
            this.$set(this.dictList, this.hyphenate(item.sCodeType), item.dicts)
          })
        })
      }
    },
    // 选择参数回填
    filterAction(params) {
      this.searchInfo = params
      // this.searchInfo = Object.assign(this.searchInfo, params)
      const datePcikerItem = this.formItems.filter(
        (item) => item.type === 'elDatePicker'
      )
      datePcikerItem.forEach((item) => {
        this.dateTimePick[`${item.value[0]}${item.value[1]}`] =
          [params[item.value[0]], params[item.value[1]]] ||
          this.dateTimePick[`${item.value[0]}${item.value[1]}`]
      })
      // this.submit()
      this.submit(true, true)
    },
    // 获取customerType
    getCustomerType(item) {
      if (
        (item.type !== 'cndInputDialog' &&
          item.type !== 'cndInputDialogItem') ||
        item.dialogType !== 'customer'
      ) { return undefined }
      const proxyDict = {
        10: '发票客户,客户',
        20: '开票单位,物流供应商,供应商',
        30: '委托方',
        40: '服务供应商',
        50: '货主'
        // '99': '其他'
      }
      if (Object.prototype.hasOwnProperty.call(item, 'customerType')) {
        return item.customerType
      }
      if (item.label) {
        const statusList = Object.keys(proxyDict).sort()
        const dictIndex = statusList.findIndex((key) => {
          return proxyDict[key]
            .split(',')
            .some((name) => name.includes(item.label))
        })
        return item.deteleType
          ? undefined
          : dictIndex < 0
            ? '99'
            : statusList[dictIndex]
      }
      return undefined
    },
    // 选择日期
    handleChangeTime(dateArray, valueArray) {
      if (valueArray[1]) {
        this.searchInfo[valueArray[0]] = dateArray
          ? moment(dateArray[0]).format('YYYY-MM-DD HH:mm:ss')
          : undefined
        // this.searchInfo[valueArray[0]] = dateArray
        //   ? dateArray[0]
        //   : undefined
        // this.searchInfo[valueArray[1]] = dateArray
        //   ? dateArray[1]
        //   : undefined
        this.searchInfo[valueArray[1]] = dateArray
          ? moment(dateArray[1]).format('LTS') === '00:00:00'
            ? moment(dateArray[1]).endOf('day')._d
            : moment(dateArray[1]).format('YYYY-MM-DD HH:mm:ss')
          : undefined
        // this.searchInfo[valueArray[1]] = dateArray
        //   ? new Date(new Date(new Date((dateArray[1].getFullYear() + '-' + (dateArray[1].getMonth() + 1) + '-' + dateArray[1].getDate()).replace(/-/g, '/')).getTime()).getTime() + 24 * 60 * 60 * 1000 - 1)
        //   : undefined
      } else {
        this.searchInfo[valueArray[0]] = dateArray
          ? moment(dateArray).format('YYYY-MM-DD HH:mm:ss')
          : undefined
        // this.searchInfo[valueArray[0]] = dateArray
        //   ? new Date(new Date(new Date((dateArray.getFullYear() + '-' + (dateArray.getMonth() + 1) + '-' + dateArray.getDate()).replace(/-/g, '/')).getTime()).getTime() + 24 * 60 * 60 * 1000 - 1)
        //   : undefined
      }
      this.submit(false, this.isAutoQuery)
    },
    // 打开弹窗
    // openDialog(valueKey, dialogType) {
    //   this.activeDialog = valueKey
    //   this.dialogVisible[dialogType] = true
    // },
    handleChangeSelect(val, item) {
      switch (item.dialogType) {
        case 'creater':
          this.searchInfo[item.value] = val ? val.name : undefined
          break
        case 'applicant':
          this.searchInfo[item.value] = val ? val.id : undefined
          break
        default:
          if (item.multiple) {
            this.searchInfo[item.value] = val
              ? val.map((item) => {
                return item.sId
              })
              : undefined
          } else {
            this.searchInfo[item.value] = val
              ? item.option && item.option['value']
                ? val[item.option['value']]
                : val.sId
              : undefined
          }
      }
      this.submit(false, this.isAutoQuery)
    },
    // 弹窗确认、取消回调
    // onDialogSelect(select, dialogType, idKey = 'id', nameKey = 'name') {
    //   const isTree =
    //     select &&
    //     select.length &&
    //     Object.prototype.hasOwnProperty.call(select[0], 'children')

    //   if (
    //     select &&
    //     select.length &&
    //     (isTree ? !!select[0].children.length : true)
    //   ) {
    //     const selecetFormItem = this.formItems.filter(
    //       (item) =>
    //         item.value === this.activeDialog && item.dialogType === dialogType
    //     )[0]

    //     this.searchInfo[selecetFormItem.value] = isTree
    //       ? select[0].children[0][idKey]
    //       : select[0][idKey]
    //     this.searchInfo[`_preview${selecetFormItem.value}`] = isTree
    //       ? select[0].children[0][nameKey]
    //       : select[0][nameKey]
    //   }
    //   this.dialogVisible[dialogType] = false
    // },
    submit(flagFold = true, search = false) {
      this.validate().then((valid) => {
        if (valid) {
          this.tips = false
          this.$emit('searchValue', this.searchInfo)
          search && this.$emit('search')
          flagFold && (this.steelActivePanel = '')
          try {
            const searchDataObject = this.getSearchData()
            const allSearchConditionArr = Object.keys(searchDataObject)
            const nullSearchParams = []
            const searchConditionArr = []
            for (let i = 0; i < allSearchConditionArr.length; i++) {
              const item = allSearchConditionArr[i]
              if (searchDataObject[item]) {
                searchConditionArr.push(item)
              } else {
                nullSearchParams.push(item)
              }
            }
            if (this.isClickReset) {
              this.isClickReset = false
              return
            }
            window.collectEvent('advanced_search_trigger', {
              valid_search_params: searchConditionArr,
              null_search_params: nullSearchParams,
              trigger_type: flagFold ? '点击查询按钮' : '选择下拉框 '
            })
          } catch (e) {
            console.log(e)
          }
        }
      })
    },
    selectGet(list, oldValue, selectKey, FormKey) {
      let obj = {}
      obj = list.find((item) => {
        return item.sCodeValue === oldValue
      })
      if (selectKey) {
        this.searchInfo[FormKey] = obj[selectKey]
      }
      this.submit(false, this.isAutoQuery)
    },
    cascaderGet(list, oldValue, selectKey, FormKey) {
      console.log('cascaderGet-->', list, oldValue, selectKey, FormKey)
      let obj = {}
      obj = list.find((item) => {
        return item.sCodeValue === oldValue
      })
      if (selectKey) {
        this.searchInfo[FormKey] = obj[selectKey]
      }
      this.submit(false, this.isAutoQuery)
    },
    getSearchData() {
      // const reg = new RegExp(/\s/)
      const result = {}
      const datePcikerItem = this.formItems.filter(
        (item) => item.type === 'elDatePicker'
      )
      Object.keys(this.searchInfo)
        .filter((key) => !key.includes('_preview'))
        .forEach((key) => {
          result[key] =
            typeof this.searchInfo[key] === 'string'
              ? this.searchInfo[key]?.replace(/^\s+|\s+$/g, '')
              : this.searchInfo[key]
                ? this.searchInfo[key]
                : undefined
        })
      if (datePcikerItem.length) {
        datePcikerItem.forEach((item) => {
          item.value.forEach((date) => {
            result[date] = result[date]
              ? moment(result[date]).format('YYYY-MM-DDTHH:mm:ss')
              : undefined
          })
        })
      }
      return result
    },
    validate() {
      return new Promise((resolve, reject) => {
        const validate = this.$refs.cndFilter.validate()
        resolve(validate)
      })
    },
    reset() {
      this.searchInfo = this.initForm(this.formItemsReset, true)
      this.isClickReset = true
      // this.dateTimePick = this.initDateTimePick(this.formItemsReset)
      this.submit(false, true)
      try {
        window.collectEvent('advanced_search_reset_click')
      } catch (e) {
        console.log(e)
      }
    },
    // 点(.)以及连字符(-)转驼峰
    hyphenate(str) {
      return str.replace(/(\w*)[. -](\w*)/g, ($1, $2, $3) => {
        return $2 + $3[0].toUpperCase() + $3.slice(1)
      })
    },
    // 去重
    unique(arr) {
      return arr.filter((item, index, arr) => {
        return arr.indexOf(item, 0) === index
      })
    }
  }
}
</script>
