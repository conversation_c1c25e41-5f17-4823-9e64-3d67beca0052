<template>
  <div class="header-message">
    <!-- <el-button type="primary" @click="sendMassage">发送信息</el-button> -->
    <el-popover
      v-model="popover"
      popper-class="header-message-popover"
      placement="bottom"
      width="345"
      trigger="click"
    >
      <div
        v-if="(unreadMessageList && unreadMessageList.length > 0) || (unReadAnnounceList && unReadAnnounceList.length > 0)"
        class="message-banner"
      >
        <el-button
          class="btn-more"
          type="text"
          @click="toInfoDetail()"
        >更多 <i class="el-icon-arrow-right" /></el-button>
        <el-tabs v-model="activeName">
            <el-tab-pane
                label="公告"
                name="announce"
            >
              <div slot="label" class="tab-header-label">公告<span v-show="unreadAnnounceTotal > 0" class="tab-header-label-num">{{unreadAnnounceTotal}}</span></div>
                <ul>
                    <li
                        v-for="(item, index) in unReadAnnounceList"
                        :key="index"
                        class="border-bottom flexCB message-font"
                        @click="toConnoceList(item)"
                    >
                      <div>
                        <p class="li-icon">通知</p>
                        <p class="li-value">{{ item.sMsgTitle }}</p>
                      </div>
                        <span class="li-time">{{
                          renderTime(item.sSendTime ? item.sSendTime : item.sCreateTime)
                        }}</span>
                    </li>
                </ul>
            </el-tab-pane>
            <el-tab-pane
                label="消息"
                name="message"
            >
              <div slot="label" class="tab-header-label">消息<span v-show="unreadMessageTotal > 0" class="tab-header-label-num">{{unreadMessageTotal}}</span></div>
                <ul>
                    <li
                        v-for="(item, index) in unreadMessageList"
                        :key="index"
                        class="border-bottom flexCB message-font"
                        @click="toNotifications(item)"
                    >
                        <div>
                          <p class="li-icon">通知</p>
                          <p class="li-value">{{ item.sMsgTitle }}</p>
                        </div>
                        <span class="li-time">{{
                          renderTime(item.sSendTime ? item.sSendTime : item.sCreateTime)
                        }}</span>
                    </li>
                </ul>
            </el-tab-pane>
        </el-tabs>
        <el-button
          class="w100 btn-close"
          type="text"
          @click="popover = false"
        >收起<i class="el-icon-arrow-up"></i></el-button>
      </div>
      <div v-else class="p-10 text-c">
        <p>{{ $t('grid.others.noData') }}</p>
        <el-button
          class="btn-more"
          type="text"
          style="padding: 0;margin-top: 10px;"
          @click="toInfoDetail()"
        >查看历史通知 <i class="el-icon-arrow-right" /></el-button>
      </div>
      <cnd-icon slot="reference" name="cnd-remind" class-name="search-icon" />
    </el-popover>
    <p v-show="unreadTotal > 0" class="unread-total flexCC">
      {{ unreadTotal }}
    </p>
  </div>
</template>

<script>
import { MessageUtil } from 'cnd-horizon-utils'
import { mapGetters, mapActions } from 'vuex'
import { getMessageList, sendMassage } from '@/api/message.js'
import { getBellList } from '@/api/message.js'
import actions from '@/shared/action'
function buildWebSocketUrl() {
  if (process.env.NODE_ENV === 'development') {
    return 'ws://esctest.esteellink.com/websocket'
  }
  const hostname = location.hostname
  const ipv4Regex = /^(\d{1,3}\.){3}\d{1,3}$/
  const protocol = ipv4Regex.test(hostname) ? 'ws' : 'wss'
  const url = `${protocol}://${hostname}/websocket`
  return url
}
export default {
  name: 'HeaderMessage',
  filters: {
    timeRule(t) {
      return t.replace('T', ' ')
    }
  },
  data() {
    return {
      popover: false,
      // 用户信息
      userInfo: '',
      // 协议路径
      path: '/ws/imserver/',
      // 未读消息列表
      unreadMessageList: [],
      unreadMessageTotal: 0,
      activeName: 'announce',
      // 未读公告列表
      unReadAnnounceList: [],
      unreadAnnounceTotal: 0,
      pagination: {
        page: 0,
        limit: 9999
      },
      websocketCount: 0,
      isManuallyClosed: false
    }
  },
  computed: {
    ...mapGetters(['socket']),
    unreadTotal() {
      return this.unreadMessageTotal + this.unreadAnnounceTotal + this.websocketCount
    }
  },
  watch: {
    popover(nv, ov) {
      nv && this.getUnreadList()
      nv && this.getBellList()
    }
  },
  created() {
    // 初始化socket连接
    this.userInfo = JSON.parse(sessionStorage.getItem('userInfo'))
    !this.socket && this.initSocket()

    actions.setGlobalState({ bellType: this.activeName })

    this.getUnreadList()
    this.getBellList()
  },
  destroyed() {
    this.isManuallyClosed = true
    // 实例化socket
    this.closeSocket()
    this.setSocket('')
  },
  methods: {
    ...mapActions({
      setSocket: 'app/setSocket'
    }),
    // 测试发送信息
    sendMassage() {
      sendMassage({ data: '测试信息' }).then((res) => {
        console.log('发送成功')
      })
    },
    // 初始化连接
    initSocket() {
      const userId = this.userInfo.id
      const wsUrl = buildWebSocketUrl()
      this.websocketCount = 0
      console.log('ws协议--', `${wsUrl}${this.path}${userId}`)
      if (typeof WebSocket === 'undefined') {
        MessageUtil.warning(this.$t('grid.tips.yourBrowserDoesNotSNotWorkKey')) // 页面弹出成功提示
      } else {
        this.connectSocket(wsUrl)
      }
    },
    // 连接 WebSocket
    connectSocket(wsUrl) {
      const socketInstance = new WebSocket(`${wsUrl}${this.path}${this.userInfo.id}`)
      this.setSocket(socketInstance)
      this.socket.onopen = this.onSocketOpen
      this.socket.onerror = this.onSocketError
      this.socket.onmessage = this.onSocketMessage
      this.socket.onclose = this.onSocketClose
    },
    onSocketOpen() {
      console.log('WebSocket 连接成功')
    },
    onSocketError() {
      console.log('WebSocket 连接发生错误')
    },
    onSocketMessage(msg) {
      if (msg.data === '连接成功') return
      try {
        const msgData = JSON.parse(msg.data)
        if (typeof msgData === 'object') {
          this.unreadMessageList.push(msgData)
          this.websocketCount++
        }
      } catch (error) {
        console.error('WebSocket 消息解析失败:', error)
      }
    },
    onSocketClose() {
      console.log('WebSocket 连接关闭')
      // console.log('WebSocket 连接关闭，正在尝试重连...')
      // if (!this.isManuallyClosed) {
      //   setTimeout(() => this.reconnectSocket(), 3000)
      // }
    },
    reconnectSocket() {
      const wsUrl = buildWebSocketUrl()
      this.connectSocket(wsUrl)
    },
    closeSocket() {
      if (this.socket) {
        this.isManuallyClosed = true
        this.socket.close()
      }
    },
    send() {
      const params = {}
      this.socket.send(params)
    },
    // 获取未读公告列表
    getBellList() {
      return new Promise((resolve, reject) => {
        getBellList(this.pagination).then(res => {
          this.unReadAnnounceList = res.data.content
          this.unreadAnnounceTotal = res.data.totalElements
          resolve(res.data)
        }).catch(() => {
          reject([])
        })
      })
    },
    // 跳转到公告列表页面
    toConnoceList(messaage) {
      console.log('跳转到公告列表页面', messaage)
      this.popover = false
      let toPath = '/egl/messagelist'
      if (this.$route.name === '/egl/messagelist') { toPath = `/redirect${toPath}` }
      this.$router.push({
        path: toPath,
        query: messaage
          ? {
            queryType: messaage.sBizType,
            sId: messaage.sBizId,
            random: Math.random()
          }
          : ''
      })
    },
    // 获取未读消息列表
    getUnreadList() {
      const params = {
        userId: this.userInfo.id
      }
      getMessageList(params)
        .then((res) => {
          if (res?.data?.content) {
            this.unreadMessageList = res.data.content
            this.unreadMessageTotal = res.data.totalElements
          }
        })
        .catch((err) => {
          console.log(err)
        })
    },
    // 跳转到消息列表页面
    toNotifications(messaage) {
      console.log('跳转到消息列表页面', messaage)
      this.popover = false
      this.unreadTotal--
      let toPath = '/egl/messagelist'
      if (this.$route.name === '/egl/messagelist') { toPath = `/redirect${toPath}` }
      this.$router.push({
        path: toPath,
        query: messaage
          ? {
            queryType: 'message',
            msgId: messaage.sId,
            random: Math.random()
          }
          : ''
      })
    },
    // 更多
    toInfoDetail() {
      if (this.activeName === 'announce') {
        actions.setGlobalState({ bellType: this.activeName })
        this.toConnoceList()
      } else {
        actions.setGlobalState({ bellType: this.activeName })
        this.toNotifications()
      }
    },
    renderTime(date) {
      if (!date) {
        return ''
      }
      const dateee = new Date(date).toJSON()
      return new Date(+new Date(dateee) + 8 * 3600 * 1000)
        .toISOString()
        .replace(/T/g, ' ')
        .replace(/\.[\d]{3}Z/, '')
    }
  }
}
</script>

<style lang="scss" scoped>
.header-message {
  position: relative;
  .unread-total {
    position: absolute;
    top: 5px;
    right: -2px;
    width: 17px;
    height: 17px;
    background-color: red;
    color: #ffffff;
    border-radius: 50%;
    font-size: 12px;
    padding: 0 2px 0 0;
    margin: 0;
    pointer-events: none;
  }
  .search-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
  }
}
.header-message-popover .message-banner {
    position: relative;
    padding: 10px;
    ul {
        max-height: 150px !important;
        margin-top: 5px!important;
        li {
            display: flex;
            justify-content: space-between !important;
            margin-left: 10px;
            border: none !important;
            padding: 7px 5px 7px 10px !important;
            div {
              display: flex;
              justify-content: start !important;
              line-height: 20px;
              .li-icon {
                color: #409EFF;
                padding: 2px 4px;
                background: rgba(0, 153, 255, 0.11);
                border-radius: 2px;
              }
              .li-value {
                  color: rgb(51, 51, 51);
                  margin-left: 15px;
                  font-size: 12px;
              }
            }
        }
    }
    .btn-close {
        color: rgb(144, 157, 153);
        font-size: 12px;
        padding: 5px 0;
        span i{
            margin-left: 12px !important;
        }
    }
    .btn-more {
        position: absolute !important;
        right: 10px !important;
        top: 9px !important;
        color: #606266 !important;
        z-index: 99 !important;
        font-size: 12px;

    }
}
::v-deep .el-tabs__header {
    background: none;
    padding: 0 20px !important;
    margin: 4px 0  !important;
    .el-tabs__item {
        height: 28px;
        line-height: 28px;
    }
    .el-tabs__active-bar {
        height: 4px;
    }
    .tab-header-label {
      position: relative
    }
    .tab-header-label-num {
        background-color: red;
        border-radius: 50%;
        font-size: 12px;
        position: absolute;
        right: -16px;
        top: 0px;
        width: 17px;
        height: 17px;
        line-height: 18px;
        text-align: center;
        color: #ffffff;
    }
}
::v-deep .el-tabs__content {
    height: 90%;
}
::v-deep .el-tabs {
    border: none !important;
}
</style>
