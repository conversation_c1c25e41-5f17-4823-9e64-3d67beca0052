<template>
  <div class="header-message">
    <!-- <el-button type="primary" @click="sendMassage">发送信息</el-button> -->
    <el-popover
      v-model="popover"
      popper-class="header-message-popover"
      placement="bottom"
      width="300"
      trigger="click"
    >
      <div
        v-if="unreadMessageList && unreadMessageList.length > 0"
        class="message-banner"
      >
        <h4>{{ $t('grid.others.latestNews') }}</h4>
        <ul>
          <li
            v-for="(item, index) in unreadMessageList"
            :key="index"
            class="border-bottom flexCB message-font"
            @click="toNotifications(item)"
          >
            <p>{{ item.sMsgTitle }}</p>
            <span>{{
              renderTime(item.sSendTime ? item.sSendTime : item.sCreateTime)
            }}</span>
          </li>
        </ul>
        <el-button
          class="w100 border-top"
          type="text"
          @click="toNotifications()"
        >
          {{ $t('login.viewAllInformation') }}
        </el-button>
      </div>
      <div v-else class="p-10 text-c">{{ $t('grid.others.noData') }}</div>
      <cnd-icon slot="reference" name="cnd-remind" class-name="search-icon" />
    </el-popover>
    <p v-show="unreadTotal > 0" class="unread-total flexCC">
      {{ unreadTotal }}
    </p>
  </div>
</template>

<script>
import { MessageUtil } from 'cnd-horizon-utils'
import { mapGetters, mapActions } from 'vuex'
import { getMessageList, sendMassage } from '@/api/message.js'
function buildWebSocketUrl() {
  if (process.env.NODE_ENV === 'development') {
    return 'ws://esctest.esteellink.com/websocket'
  }
  const hostname = location.hostname
  const ipv4Regex = /^(\d{1,3}\.){3}\d{1,3}$/
  const protocol = ipv4Regex.test(hostname) ? 'ws' : 'wss'
  const url = `${protocol}://${hostname}/websocket`
  return url
}
export default {
  name: 'HeaderMessage',
  filters: {
    timeRule(t) {
      return t.replace('T', ' ')
    }
  },
  data() {
    return {
      popover: false,
      // 用户信息
      userInfo: '',
      // 协议路径
      path: '/ws/imserver/',
      // 未读消息列表
      unreadMessageList: [],
      unreadTotal: 0
    }
  },
  computed: {
    ...mapGetters(['socket'])
  },
  watch: {
    popover(nv, ov) {
      nv && this.getUnreadList()
    }
  },
  created() {
    // 初始化socket连接
    this.userInfo = JSON.parse(sessionStorage.getItem('userInfo'))
    console.log('socket前--', this.socket)
    !this.socket && this.initSocket()
    setTimeout(() => {
      // console.log('socket后--', this.socket)
      // this.close()
    }, 3000)
    // 获取未读消息列表
    this.getUnreadList()
  },
  destroyed() {
    // 销毁连接
    this.close()
    // 实例化socket
    this.setSocket('')
  },
  methods: {
    renderTime(date) {
      if (!date) {
        return ''
      }
      const dateee = new Date(date).toJSON()
      return new Date(+new Date(dateee) + 8 * 3600 * 1000)
        .toISOString()
        .replace(/T/g, ' ')
        .replace(/\.[\d]{3}Z/, '')
    },
    ...mapActions({
      setSocket: 'app/setSocket'
    }),
    // 测试发送信息
    sendMassage() {
      sendMassage({ data: '测试信息' }).then((res) => {
        console.log('发送成功')
      })
    },
    // 跳转到消息列表页面
    toNotifications(messaage) {
      this.popover = false
      this.unreadTotal--
      let toPath = '/system/notifications'
      if (this.$route.name === '/system/notifications') { toPath = `/redirect${toPath}` }
      this.$router.push({
        path: toPath,
        query: messaage
          ? {
            msgId: messaage.sId,
            random: Math.random()
          }
          : ''
      })
    },
    compare(property) {
      return function(a, b) {
        var value1 = a[property]
        var value2 = b[property]
        return value2 - value1
      }
    },
    // 获取未读消息列表
    getUnreadList() {
      const params = {
        userId: this.userInfo.id
      }
      getMessageList(params)
        .then((res) => {
          if (res?.data?.content) {
            this.unreadMessageList = res.data.content
            this.unreadTotal = res.data.totalElements
          }
        })
        .catch((err) => {
          console.log(err)
        })
    },
    // 初始化连接
    initSocket() {
      const userId = this.userInfo.id
      const ws = buildWebSocketUrl()
      // console.log('用户信息--', userId)
      console.log('ws协议--', `${ws}${this.path}${userId}`)
      if (typeof WebSocket === 'undefined') {
        MessageUtil.warning(this.$t('grid.tips.yourBrowserDoesNotSNotWorkKey')) // 页面弹出成功提示
      } else {
        // 实例化socket
        this.setSocket(new WebSocket(`${ws}${this.path}${userId}`))
        // 监听socket连接
        this.socket.onopen = this.open
        // 监听socket错误信息
        this.socket.onerror = this.error
        // 监听socket消息
        this.socket.onmessage = this.getMessage
        // 销毁监听
        this.socket.onclose = this.close
      }
    },
    open() {
      // console.log('socket连接成功')
    },
    error() {
      console.log('连接错误')
    },
    getMessage(msg) {
      // console.log('msg--', msg)
      if (msg.data === '连接成功') return
      const msgData = JSON.parse(msg.data)
      // console.log('msgData--', typeof (msgData))
      if (typeof msgData !== 'object') return
      this.unreadMessageList.push(msgData)
      console.log(this.unreadMessageList, 'unreadMessageList-')
      this.unreadTotal++
    },
    send() {
      const params = {}
      this.socket.send(params)
    },
    close() {
      console.log('socket已经关闭')
    }
  }
}
</script>

<style lang="scss" scoped>
.header-message {
  position: relative;
  .unread-total {
    position: absolute;
    top: 5px;
    right: -2px;
    width: 17px;
    height: 17px;
    background-color: red;
    color: #ffffff;
    border-radius: 50%;
    font-size: 12px;
    padding: 0 2px 0 0;
    margin: 0;
  }
  .search-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
  }
}
</style>
