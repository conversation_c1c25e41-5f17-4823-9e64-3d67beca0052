<!--
 * @Description:导出按钮
-->
<template>
  <div class="text export-btn">
    <el-dropdown split-button type="primary" size="mini" :disabled="disabled">
      <div @click="onExport('page')">{{ btnTxt || $t('excel.export') }}</div>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item>
          <div @click="onExport('select')">{{ $t('excel.exportSelectedData') }}</div>
        </el-dropdown-item>
        <el-dropdown-item>
          <div @click="onExport('page')">{{ $t('excel.exportThisPage') }}</div>
        </el-dropdown-item>
        <el-dropdown-item>
          <div @click="onEportAll">{{ $t('excel.exportAll') }}</div>
        </el-dropdown-item>
        <el-dropdown-item v-if="childrenApiUrl">
          <div @click="onEportChildren">{{ childerBtnTxt }}</div>
        </el-dropdown-item>
        <el-dropdown-item v-if="downloadBatch">
          <div @click="onDownloadBatch">{{ batchBtnTxt }}</div>
        </el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
  </div>
</template>

<script>
import moment from 'moment'
import { exportAll } from '@/api/export'
import { deepClone } from '@/components/steelTradeAggrid/utils.js'
export default {
  name: 'ExportBtn',
  props: {
    disabled: {
      type: Boolean,
      defalut: false
    },
    btnTxt: {
      type: String,
      default: ''
    },
    childerBtnTxt: {
      type: String,
      default: '导出子表'
    },
    apiUrl: {
      type: String,
      default: ''
    },
    // 明细API
    childrenApiUrl: {
      type: String,
      default: ''
    },
    postParams: {
      type: Object,
      default: null
    },
    // 明细导出
    childrenPostParams: {
      type: Object,
      default: null
    },
    // 明细导出校验 函数输出：true/false
    childernValidateFn: {
      type: Function,
      default: null
    },
    childrenFileName: {
      type: String,
      default: '明细导出'
    },
    fileName: {
      type: String,
      default: '表格导出'
    },
    exportConfig: {
      type: Array,
      default: () => []
    },
    getGridApi: {
      type: Function,
      default: () => {}
    },
    // 主子表导出 需要配置 子表list key
    childrenListKey: {
      type: String,
      default: null
    },
    childRowKey: {
      type: String,
      default: 'sId'
    },
    /* isParentMajor
     * true: 以主表为主，当子表为空时，主表正常导出
     * false: 以子表为主，当子表为空时，主表不导出
     * tip如果父子表都存在相同value,则以子表为主
    */
    isParentMajor: {
      type: Boolean,
      default: false
    },
    isloadDetail: {
      type: Boolean,
      default: false
    },
    detailParams: {
      type: Object,
      default: () => {}
    },
    idsKey: {
      type: String,
      default: 'sIds'
    },
    downloadBatch: {
      type: Boolean,
      default: false
    },
    batchBtnTxt: {
      type: String,
      default: '导出附件'
    },
    // 导出子表使用--例采购合同进程
    childrenSelectedData: {
      type: Array,
      default: () => []
    },
    needSelectedData: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      labelList: [],
      keyList: []
    }
  },
  watch: {
    exportConfig: {
      deep: true,
      immediate: true,
      handler(newVal) {
        const labelList = []
        const keyList = []
        const setValueObj = {}
        newVal.forEach(el => {
          labelList.push(el.label)
          keyList.push(el.value)
          if (el.setValue) {
            setValueObj[el.value] = el.setValue
          }
        })
        this.labelList = labelList
        this.keyList = keyList
        this.setValueObj = setValueObj
      }
    }
  },
  methods: {
    hasValue(data) {
      if (typeof data === 'string' || Array.isArray(data)) {
        return data.length > 0
      }
      return false
    },
    onExport(flag = 'page') {
      if (this.isloadDetail) {
        /*  isloadDetail为true时，子级为懒加载，故为调用接口导出
         *  detailParams.sIds 为当前页的ids.toString()
         *  detailParams.selectIds 为已选父级的ids.toString()
         */

        /*  已选导出：this.detailParams里有key: selectIds时，调用接口，否者，走前端导出
         *  本页导出：sIds 存在时，调用接口
         */
        if (flag === 'select') {
          if (('selectIds' in this.detailParams)) {
            if (this.hasValue(this.detailParams.selectIds)) {
              this.onEportAll(flag, { [this.idsKey]: this.detailParams.selectIds })
              return
            } else {
              return this.$message.error('请勾选主级数据！')
            }
          }
        }
        if (flag === 'page') {
          if (this.hasValue(this.detailParams.sIds)) {
            this.onEportAll(flag, { [this.idsKey]: this.detailParams.sIds })
          } else {
            return this.$message.error('列表本页暂无数据！')
          }
          return
        }
      }
      // 1、获取选择数据rowData
      // 2、合并数据(主子表)
      // 3、处理数据获得exportData
      let exportData = []
      if (this.isParentMajor) {
        const rowData = this.getParentRowData(flag)
        const subtableData = this.getParentData(rowData)
        exportData = this.formatJson(this.keyList, subtableData)
      } else {
        const rowData = this.getRowData(flag)
        if (this.childrenListKey) {
          const subtableData = this.getSubData(rowData)
          exportData = this.formatJson(this.keyList, subtableData)
        } else {
          exportData = this.formatJson(this.keyList, rowData)
        }
      }
      if (!exportData.length) {
        this.$message.error('请选择要导出的数据')
        return
      }
      this.exportHandler(exportData)
    },
    getParentRowData(flag) {
      const list = []
      const gridApi = this.getGridApi()
      gridApi.forEachNodeAfterFilterAndSort((node, index) => {
        if (node.data) {
          if (flag === 'page') {
            list.push(node.data)
          } else {
            const item = JSON.parse(JSON.stringify(node.data))
            item[this.childrenListKey] = item[this.childrenListKey].filter(child => item._selectedKeys.includes(child[this.childRowKey]))
            if (item._selectedKeys.length || item._selected) {
              list.push(item)
            }
          }
        }
      })
      return list
    },
    getRowData(flag) {
      const list = []
      const gridApi = this.getGridApi()
      gridApi.forEachNodeAfterFilterAndSort((node, index) => {
        if (node.data) {
          if (flag === 'page') {
            list.push(node.data)
          } else {
            if (this.childrenListKey) {
              const item = JSON.parse(JSON.stringify(node.data))
              item[this.childrenListKey] = item[this.childrenListKey].filter(child => item._selectedKeys.includes(child[this.childRowKey]))
              if (item._selectedKeys.length) {
                list.push(item)
              }
            } else {
              if (node.data._selected) {
                list.push(node.data)
              }
            }
          }
        }
      })
      return list
    },
    getParentData(list) {
      return list.reduce((prev, next) => {
        const category = deepClone(next)
        if (category[this.childrenListKey]) {
          delete category[this.childrenListKey]
        } else {
          throw new Error('请检查childrenListKey是否配置正确!')
        }
        if (next[this.childrenListKey].length) {
          return prev.concat(next[this.childrenListKey].map(child => {
            return deepClone(Object.assign(category, child))
          }))
        } else {
          return prev.concat([deepClone(Object.assign(category, next))])
        }
      }, [])
    },
    getSubData(list) {
      return list.reduce((prev, next) => {
        const category = deepClone(next)
        if (category[this.childrenListKey]) {
          delete category[this.childrenListKey]
        } else {
          throw new Error('请检查childrenListKey是否配置正确!')
        }
        return prev.concat(next[this.childrenListKey].map(child => {
          return deepClone(Object.assign(category, child))
        }))
      }, [])
    },
    formatJson(filterVal, jsonData) {
      return jsonData.map(v => {
        v = this.handleNodeData(v)
        return filterVal.map(j => v[j])
      })
    },
    handleNodeData(data) {
      const nodeData = JSON.parse(JSON.stringify(data))
      for (const key in this.setValueObj) {
        nodeData[key] = this.setValueObj[key](nodeData[key])
      }
      return nodeData
    },
    exportHandler(data) {
      import('@/vendor/Export2Excel').then(excel => {
        excel.export_json_to_excel({
          header: this.labelList,
          data: data,
          filename: `${this.fileName}${moment().format('YYYY-MM-DD')}`
        })
      })
    },
    onEportAll(type = 'all', detailParams = {}) {
      const params = {
        ...this.postParams,
        ...type !== 'all' ? detailParams : {}
      }
      exportAll(this.apiUrl, params).then(res => {
        try {
          const enc = new TextDecoder('utf-8')
          const jsonString = enc.decode(new Uint8Array(res))
          res = JSON.parse(jsonString)
          this.$message.error(res.message)
        } catch (err) {
          console.log('err: ', err)
          const link = document.createElement('a')
          const blob = new Blob([res], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
          link.style.display = 'none'
          link.href = URL.createObjectURL(blob)
          link.download = `${this.fileName}${moment().format('YYYY-MM-DD')}`
          document.body.appendChild(link)
          link.click()
        }
      })
    },
    onEportChildren() {
      if (typeof (this.childernValidateFn) === 'function') {
        const isTrue = this.childernValidateFn()
        if (isTrue === false) {
          return false
        }
      }
      if (this.needSelectedData) {
        if (!this.childrenSelectedData.length || this.childrenSelectedData.length > 1) {
          this.$message.warning(this.$t('grid.others.pleaseSelectAData'))
          return false
        }
      }
      exportAll(this.childrenApiUrl, this.childrenPostParams).then(res => {
        try {
          const enc = new TextDecoder('utf-8')
          const jsonString = enc.decode(new Uint8Array(res))
          res = JSON.parse(jsonString)
          if (res.code === '0000') {
            if (res.message === 'ok') {
              this.$message.success('后台导出中，文件发送至邮箱，请检查')
            } else {
              this.$message.info(res.message)
            }
          } else {
            this.$message.error(res.message)
          }
        } catch (err) {
          console.log('err: ', err)
          const link = document.createElement('a')
          const blob = new Blob([res], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
          link.style.display = 'none'
          link.href = URL.createObjectURL(blob)
          link.download = `${this.childrenFileName}${moment().format('YYYY-MM-DD')}`
          document.body.appendChild(link)
          link.click()
        }
      })
    },
    onDownloadBatch() {
      this.$emit('onDownloadBatch')
    }
  }
}
</script>
<style lang='scss' scoped>
.export-btn {
  position: relative;
  display: inline-block;
}
</style>

