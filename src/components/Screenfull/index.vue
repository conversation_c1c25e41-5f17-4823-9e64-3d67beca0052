<template>
  <div>
    <cnd-icon v-show="!isFullscreen" name="cnd-fullScreen" @click="click" />
    <cnd-icon v-show="isFullscreen" name="cnd-fullScreen-close" @click="click" />
  </div>
</template>

<script>
import screenfull from 'screenfull'
import { MessageUtil } from 'cnd-horizon-utils'

export default {
  name: 'Screenfull',
  data() {
    return {
      isFullscreen: false
    }
  },
  mounted() {
    this.init()
  },
  beforeDestroy() {
    this.destroy()
  },
  methods: {
    click() {
      if (!screenfull.isEnabled) {
        MessageUtil.warning(this.$t('grid.tips.currentBrowserIsNotSupported'))
        return false
      }
      screenfull.toggle()
    },
    change() {
      this.isFullscreen = screenfull.isFullscreen
    },
    init() {
      if (screenfull.isEnabled) {
        screenfull.on('change', this.change)
      }
    },
    destroy() {
      if (screenfull.isEnabled) {
        screenfull.off('change', this.change)
      }
    }
  }
}
</script>

<style scoped>
.screenfull-svg {
  display: inline-block;
  cursor: pointer;
  fill: #5a5e66;;
  width: 20px;
  height: 20px;
  vertical-align: 10px;
}
</style>
