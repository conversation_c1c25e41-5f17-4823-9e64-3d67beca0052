<template>
  <div>
    <el-date-picker
      v-model="value"
      size="mini"
      format="yyyy-MM-dd HH:mm:ss"
      :type="pickerType"
      :placeholder="$t('grid.others.selectDate')"
    />
  </div>
</template>

<script>
export default {
  name: 'TableSelect',
  data() {
    return {
      value: null,
      pickerType: 'date'
    }
  },
  mounted() {
    this.$nextTick(() => {
      if (this.$refs.input) {
        this.$refs.input.focus()
      }
    })
  },
  beforeMount() {
    this.value = this.params.value
  },
  methods: {
    getValue() {
      return this.value
    }
  }
}
</script>

<style scoped></style>
