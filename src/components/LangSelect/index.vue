<template>
  <el-dropdown trigger="click" class="international" @command="handleSetLanguage">
    <div>
      <cnd-icon name="cnd-language" class-name="international-icon" />
    </div>
    <el-dropdown-menu slot="dropdown">
      <el-dropdown-item :disabled="language==='zh_CN'" command="zh_CN">
        中文
      </el-dropdown-item>
      <el-dropdown-item :disabled="language==='en'" command="en">
        English
      </el-dropdown-item>
      <el-dropdown-item :disabled="language==='es'" command="es">
        Español
      </el-dropdown-item>
    </el-dropdown-menu>
  </el-dropdown>
</template>

<script>
import configSubApp from '@/subapp.config' // 子应用配置
// import { messageUtil } from '@/utils/common'
import store from '@/store'
import actions from '@/shared/action'

export default {
  computed: {
    language() {
      return this.$store.getters.language
    }
  },
  methods: {
    loadApp(appName) {
      // const loadingInstance = Loading.service({
      //   lock: true,
      //   text: '正在加载应用...',
      //   spinner: 'el-icon-loading',
      //   background: '#FAFAFA',
      //   target: '.app-class'
      // })
      // store.dispatch('app/appLoading', true)
      const appInfo = configSubApp.filter((item) => {
        return item.name === appName
      })
      // console.log('应用信息--', appInfo)
      if (appInfo && appInfo.length > 0) {
        setTimeout(() => {
          // const tempObj = {}
          // tempObj[appName] = loadMicroApp(appInfo[0], {
          //   excludeAssetFilter: false
          // })
          // store.dispatch('app/setSystemMicro', tempObj)
          // Promise.all([store.state.app.microSystem[appName].mountPromise])
          //   .then((resolve, reject)  => {
          //     console.log('--------')
          //     store.dispatch('app/appLoading', false)
          //     loadingInstance.close()
          //     reject()
          //   })
          //   .catch((err) => {
          //     console.log('--------')
          //     store.dispatch('app/appLoading', false)
          //     loadingInstance.close()
          //     messageUtil.error(this.$t('tips.loadingFailedPleaseTryAgain'))
          //   })
          console.log('store.state.app.microSystem[appName]', store.state.app.microSystem[appName])
          store.state.app.microSystem[appName].unmount().then(() => {
            store.state.app.microSystem[appName].mount()
          })
        }, 10)
      } else {
        // loadingInstance.close()
      }
    },
    handleSetLanguage(lang) {
      this.$i18n.locale = lang
      this.$store.dispatch('app/setLanguage', lang)
      actions.setGlobalState({ locale: lang })
      this.$message({
        message: 'Switch Language Success',
        type: 'success'
      })
      this.$nextTick(() => {
        // window.location.reload()
        const appName = location.pathname.split('/')[1]
        this.loadApp(appName)
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.cnd-icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
</style>
