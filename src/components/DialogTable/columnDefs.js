import { handleCellWidth } from './fieldDict'

const lastDefMeter = {
  headerName: '',
  field: 'fill',
  flex: 1,
  width: 0,
  minWidth: 0
}

// 编辑单元格 标题为蓝色
const handleHeaderDefEdit = (item) => {
  const headerComponentParams = {
    menuIcon: 'fa-bars',
    template:
        '<div class="ag-cell-label-container" role="presentation">' +
        '  <span ref="eMenu" class="ag-header-icon ag-header-cell-menu-button"></span>' +
        '  <div ref="eLabel" class="ag-header-cell-label" role="presentation">' +
        '    <span ref="eSortOrder" class="ag-header-icon ag-sort-order" ></span>' +
        '    <span ref="eSortAsc" class="ag-header-icon ag-sort-ascending-icon" ></span>' +
        '    <span ref="eSortDesc" class="ag-header-icon ag-sort-descending-icon" ></span>' +
        '    <span ref="eSortNone" class="ag-header-icon ag-sort-none-icon" ></span>' +
        '    <span ref="eText" class="ag-header-cell-text" role="columnheader" style="color: #3E8DDC;"></span>' +
        '    <span ref="eFilter" class="ag-header-icon ag-filter-icon"></span>' +
        '  </div>' +
        '</div>'
  }
  if (item.children) {
    item.children.map(e => {
      if (e.editable) {
        e.headerComponentParams = headerComponentParams
      }
    })
  } else {
    if (item.editable) {
      return {
        ...item,
        headerComponentParams: headerComponentParams
      }
    }
  }
  return item
}

const handleItemWidth = (item, firHeaderName) => {
  if (item.width) {
    return item
  }
  const cellWidth = handleCellWidth(item.headerName, firHeaderName)
  let headerWidth = 0
  if (!firHeaderName) {
    headerWidth = (item.headerName.length + 2) * 14 + 22
  }

  if (cellWidth) {
    const width = (+cellWidth > +headerWidth) ? cellWidth : headerWidth
    if (item.headerName !== 'checkbox') {
      console.log(`${firHeaderName || ''}${item.headerName}`, item.field, `${width}px`)
    }
    return {
      ...item,
      width: (+cellWidth > +headerWidth) ? cellWidth : headerWidth
    }
  }
  return item
}
const handleCellRenderer = (columnDefs) => {
  return columnDefs.map(item => {
    if (!item.cellRenderer && !item.hideRender) {
      return {
        ...item,
        cellRenderer: params => {
          const showValue = params.valueFormatted ? params.valueFormatted : params.value
          return `<span title="${showValue || ''}">${showValue || ''}</span>`
        }
      }
    }
    return item
  })
}
const handleGridColumnDefs = (columnDefs) => {
  const middleColumnDefs = handleCellRenderer(columnDefs)
  const spreadColumnDef = null
  return [
    spreadColumnDef,
    ...middleColumnDefs,
    lastDefMeter
  ].filter(item => item).map(item => {
    return handleHeaderDefEdit(handleItemWidth(item))
  })
}
export {
  handleGridColumnDefs
}
