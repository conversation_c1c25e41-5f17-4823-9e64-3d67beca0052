<template>
  <formDialog
    :visible="visible"
    :form-items="formDialogItems"
    width="350px"
    height="60px"
    title="提示"
    @close="onClose"
    @success="onSuccess"
  />
</template>

<script>
import { stockCommonForceApprovalModify } from '@/api/logistics/saleDelivery/saleorder'
import formDialog from '@/components/formDialog'
export default {
  name: 'ForceApprovalDialog',
  components: {
    formDialog
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    id: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      formDialogItems: [
        {
          label: '强控特批',
          value: 'param',
          type: 'elSelect',
          customWidth: 22,
          required: true,
          allHide: true,
          dict: 'base.yes-no',
          errorMessage: this.$t('components.pleaseSelect')
        }
      ]
    }
  },
  methods: {
    onSuccess(data) {
      console.log('data: ', data)
      // this.$emit('success', data)
      stockCommonForceApprovalModify(this.id, { param: data.param }).then(res => {
        this.$emit('success')
        this.$emit('close')
        this.$message.success('强控特批修改成功')
      })
    },
    onClose() {
      this.$emit('close')
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
