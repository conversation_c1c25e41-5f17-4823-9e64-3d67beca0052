<template>
  <cnd-dialog
    :visible.sync="visible"
    :fullscreen="false"
    :title="title"
    :width="width"
    :height="height"
    @close="handleClose"
  >
    <template slot="content">
      <div>
        <el-form
          ref="form"
          :label-width="labelWidth"
          size="small"
          :model="formData"
          :rules="formRules"
          @submit.native.prevent
        >
          <cnd-form-item
            v-for="(item, index) in formItems"
            :key="index"
            :label="item.label"
            :custom-width="+item.customWidth || undefined"
            :prop="item.value"
            :error-msg="item.errorMessage || $t('components.pleaseEnter')"
          >
            <el-popover
              v-if="item.tips"
              placement="top-start"
              trigger="hover"
              :content="item.tips"
              popper-class="form-popperclass"
              style="margin-right: 10px"
            >
              <cnd-icon slot="reference" name="cnd-question-mark" color="#409EFF" />
            </el-popover>
            <template v-if="item.type === 'elInput'">
              <el-input
                v-model="formData[item.value]"
                size="mini"
                :placeholder="
                  item.placeholder
                    ? item.placeholder
                    : `${$t('components.pleaseEnter')}${item.label}`
                "
                clearable
                :disabled="item.disabled || false"
                @keyup.native.stop.enter="submit(true)"
                @clear="submit(false)"
              />
            </template>
            <template v-else-if="item.type === 'cndInputNumber'">
              <cnd-input-number
                v-model="formData[item.value]"
                size="mini"
                :placeholder="
                  item.placeholder
                    ? item.placeholder
                    : `${$t('components.pleaseEnter')}${item.label}`
                "
                clearable
                style="width: 100%"
                :negative="item.negative || false"
                :disabled="item.disabled || false"
                :type="item.numberType || 'number'"
                :decimal-digit="item.decimalDigit || 6"
                @keyup.native.stop.enter="submit(false)"
                @clear="submit(false)"
              />
            </template>
            <template v-else-if="item.type === 'elInputTextArea'">
              <el-input
                v-model="formData[item.value]"
                size="mini"
                type="textarea"
                :rows="item.rows || 2"
                :placeholder="
                  item.placeholder
                    ? item.placeholder
                    : `${$t('components.pleaseEnter')}${item.label}`
                "
                clearable
                :disabled="item.disabled || false"
                @keyup.native.stop.enter="submit(true)"
                @clear="submit(false)"
              />
            </template>
            <template v-else-if="item.type === 'cndInputDialog'">
              <horizon-search-select
                v-model="formData[`_preview${item.value}`]"
                :default-url="item.defaultUrl"
                :type="item.dialogType"
                :customer-type="getCustomerType(item)"
                :artno-type="item.artnoType"
                :categories="item.categories"
                :search-key="item.searchKey"
                :disabled="item.disabled || false"
                :multiple="item.multiple ? item.multiple : false"
                :clearable="item.clearable"
                :label="item.label"
                :placeholder="item.placeholder"
                :option="item.option"
                :method="item.method"
                :currency="item.currency"
                :label-default="item.labelDefault"
                :content="item.content"
                :cancel-page="item.cancelPage"
                :other-options="item.otherOptions"
                :first-call="item.firstCall"
                @change="handleChangeSelect($event, item)"
              />
            </template>
            <template v-else-if="item.type === 'cndInputDialogItem'">
              <horizon-search-select-item
                v-model="formData[`_preview${item.value}`]"
                :default-url="item.defaultUrl"
                :type="item.dialogType"
                :customer-type="getCustomerType(item)"
                :artno-type="item.artnoType"
                :categories="item.categories"
                :search-key="item.searchKey"
                :disabled="item.disabled || false"
                :multiple="item.multiple ? item.multiple : false"
                :clearable="item.clearable"
                :label="item.label"
                :placeholder="item.placeholder"
                :option="item.option"
                :method="item.method"
                :currency="item.currency"
                :label-default="item.labelDefault"
                :content="item.content"
                :cancel-page="item.cancelPage"
                :other-options="item.otherOptions"
                :first-call="item.firstCall"
                @change="handleChangeSelect($event, item)"
              />
            </template>
            <!-- 下拉框 -->
            <template v-else-if="item.type === 'elSelect'">
              <el-select
                v-model="formData[item.value]"
                size="mini"
                filterable
                clearable
                :multiple="item.multiple ? item.multiple : false"
                :placeholder="item.placeholder"
                :disabled="item.disabled || false"
                @change="
                  selectGet(
                    typeof item.dict === 'string'
                      ? dictList[hyphenate(item.dict)]
                      : item.dict,
                    formData[item.value],
                    item.selectKey,
                    item.value
                  )
                "
              >
                <el-option
                  v-if="!item.multiple && !item.allHide"
                  key="all"
                  :label="$t('grid.others.all')"
                  :value="undefined"
                />
                <el-option
                  v-for="option in item.dict instanceof Array
                    ? item.dict
                    : dictList[hyphenate(item.dict)]"
                  :key="option.sCodeValue"
                  :label="option.sCodeName"
                  :value="option.sCodeValue"
                />
              </el-select>
            </template>
          </cnd-form-item>
        </el-form>
      </div>
    </template>
    <template slot="footer">
      <el-button
        size="mini"
        @click="handleClose"
      >{{ $t('btns.cancel') }}</el-button>
      <el-button
        type="primary"
        size="mini"
        @click="submit(true)"
      >{{ $t('btns.confirm') }}</el-button>
    </template>

  </cnd-dialog>
</template>

<script>
import { DictUtil } from 'cnd-horizon-utils'
import moment from 'moment'

export default {
  props: {
    formItems: {
      type: Array,
      required: true
    },
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '弹窗'
    },
    width: {
      type: String,
      default: undefined
    },
    height: {
      type: String,
      default: undefined
    },
    labelWidth: {
      type: String,
      default: '85px'
    },
    successNoVisible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      dictList: {},
      formData: {},
      formRules: {},
      dateTimePick: '',
      formItemsReset: null,
      pickerOptions: {
        shortcuts: [
          {
            text: this.$t('grid.others.lastWeek'),
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: this.$t('grid.others.mostRecentMonth'),
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: this.$t('grid.others.lastThreeMonths'),
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
              picker.$emit('pick', [start, end])
            }
          }
        ]
      }
    }
  },
  watch: {
    formItems: {
      handler(newFields) {
        // 根据传入的字段重新设置表单数据和验证规则
        this.formData = this.initForm()
        this.formRules = this.initFormRules()
        this.dateTimePick = this.initDateTimePick()
        this.formItemsReset = this.deepClone(this.formItems)
      },
      immediate: true
    }
  },
  beforeMount() {
    this.loadDict()
  },
  methods: {
    initForm(formItems = this.formItems, reset = false) {
      return formItems.reduce((prev, next) => {
        if (next.type === 'elDatePicker') {
          const dateValue =
            next.default && next.default.length
              ? next.default
              : [
                moment()
                  .subtract(3, 'months')
                  .startOf('day')
                  .format('YYYY-MM-DDTHH:mm:ss'),
                moment().endOf('day').format('YYYY-MM-DDTHH:mm:ss')
              ]
          dateValue[1] &&
            (dateValue[1] =
              moment(dateValue[1]).format('LTS') === '00:00:00'
                ? moment(dateValue[1]).endOf('day')._d
                : moment(dateValue[1]).format('YYYY-MM-DD HH:mm:ss'))
          prev[next.value[0]] = dateValue[0]
          prev[next.value[1]] = dateValue[1]
          if (reset) {
            this.dateTimePick[`${next.value[0]}${next.value[1]}`] = [
              dateValue[0],
              dateValue[1]
            ]
          }
          return prev
        }

        // 若为弹窗选择储存name以用于保存参数
        if (
          next.type === 'cndInputDialog' ||
          next.type === 'cndInputDialogItem'
        ) {
          prev[`_preview${next.value}`] = next.labelDefault || undefined
        }
        if (next.type === 'elCheckbox') {
          prev[next.value] =
            next.disabled && reset ? this.formData[next.value] : false
        } else {
          prev[next.value] =
            next.disabled && reset ? this.formData[next.value] : next.default
        }

        return prev
      }, {})
    },
    initFormRules() {
      const result = {}
      this.formItems.forEach((item) => {
        if (item.required) {
          if (item.type === 'elDatePicker') {
            result[item.value[0]] = [
              {
                validator: (rule, value, cb) => {
                  if (
                    this.formData[item.value[0]] ||
                    this.formData[item.value[1]]
                  ) {
                    cb()
                  } else {
                    cb(new Error('no'))
                  }
                },
                trigger: ['blur', 'change'],
                required: true
              }
            ]
          } else {
            result[item.value] = [
              {
                required: true,
                trigger: ['blur', 'change'],
                ...(item.validator ? { validator: item.validator } : {})
              }
            ]
          }
        }
      })
      return result
    },
    initDateTimePick(formItems = this.formItems) {
      const result = {}
      formItems.forEach((item) => {
        if (item.type === 'elDatePicker') {
          const dateValue =
            item.default && item.default.length
              ? item.default
              : [
                moment()
                  .subtract(3, 'months')
                  .startOf('day')
                  .format('YYYY-MM-DDTHH:mm:ss'),
                moment().endOf('day').format('YYYY-MM-DDTHH:mm:ss')
              ]
          if (item.value[1]) {
            result[`${item.value[0]}${item.value[1]}`] = dateValue
          } else {
            result[`${item.value[0]}`] = dateValue
          }
        }
      })
      return result
    },
    forEach(array, iteratee) {
      let index = -1
      const length = array.length
      while (++index < length) {
        iteratee(array[index], index)
      }
      return array
    },
    deepClone(target, map = new WeakMap()) {
      if (typeof target === 'object') {
        const isArray = Array.isArray(target)
        const cloneTarget = isArray ? [] : {}

        if (map.get(target) || !target) {
          return target
        }
        map.set(target, cloneTarget)

        const keys = isArray ? undefined : Object.keys(target)
        this.forEach(keys || target, (value, key) => {
          if (keys) {
            key = value
          }
          cloneTarget[key] = this.deepClone(target[key], map)
        })

        return cloneTarget
      } else {
        return target
      }
    },
    unique(arr) {
      return arr.filter((item, index, arr) => {
        return arr.indexOf(item, 0) === index
      })
    },
    loadDict() {
      const needLoadDict = this.unique(
        this.formItems
          .map((item) => item.dict)
          .filter((item) => {
            return item && !(item instanceof Array)
          })
      )
      if (needLoadDict.length) {
        DictUtil.getDict(needLoadDict, (res) => {
          res.forEach((item) => {
            this.$set(this.dictList, this.hyphenate(item.sCodeType), item.dicts)
          })
        })
      }
    },
    getCustomerType(item) {
      if (
        (item.type !== 'cndInputDialog' &&
          item.type !== 'cndInputDialogItem') ||
        item.dialogType !== 'customer'
      ) { return undefined }
      const proxyDict = {
        10: '发票客户,客户',
        20: '开票单位,物流供应商,供应商',
        30: '委托方',
        40: '服务供应商',
        50: '货主'
        // '99': '其他'
      }
      if (Object.prototype.hasOwnProperty.call(item, 'customerType')) {
        return item.customerType
      }
      if (item.label) {
        const statusList = Object.keys(proxyDict).sort()
        const dictIndex = statusList.findIndex((key) => {
          return proxyDict[key]
            .split(',')
            .some((name) => name.includes(item.label))
        })
        return item.deteleType
          ? undefined
          : dictIndex < 0
            ? '99'
            : statusList[dictIndex]
      }
      return undefined
    },
    handleChangeSelect(val, item) {
      console.log('val, item: ', val, item)
      switch (item.dialogType) {
        case 'creater':
          this.formData[item.value] = val ? val.name : undefined
          break
        case 'applicant':
          this.formData[item.value] = val ? val.account : undefined
          break
        default:
          if (item.multiple) {
            this.formData[item.value] = val
              ? val.map((i) => {
                return item.option && item.option['value']
                  ? i[item.option['value']]
                  : i.sId
              })
              : undefined
          } else {
            this.formData[item.value] = val
              ? item.option && item.option['value']
                ? val[item.option['value']]
                : val.sId
              : undefined
          }
      }
      this.$emit('change', this.formData, this.getFormData(), item)
    },
    selectGet(list, oldValue, selectKey, FormKey) {
      let obj = {}
      obj = list.find((item) => {
        return item.sCodeValue === oldValue
      })
      if (selectKey) {
        this.formData[FormKey] = obj[selectKey]
      }
    },
    handleClose() {
      this.$refs.form.resetFields()
      this.formData = this.initForm(this.formItemsReset, true)
      this.$emit('close')
    },
    getFormData() {
      const result = {}
      const datePickerItems = this.formItems.filter(
        (item) => item.type === 'elDatePicker'
      )
      Object.keys(this.formData)
        .filter((key) => !key.includes('_preview'))
        .forEach((key) => {
          const value = this.formData[key]
          result[key] =
        typeof value === 'string'
          ? value.trim()
          : value !== undefined && value !== null
            ? value
            : undefined
        })
      if (datePickerItems.length) {
        datePickerItems.forEach((item) => {
          item.value.forEach((date) => {
            result[date] = result[date]
              ? moment(result[date]).format('YYYY-MM-DDTHH:mm:ss')
              : undefined
          })
        })
      }
      return result
    },
    // 点(.)以及连字符(-)转驼峰
    hyphenate(str) {
      return str.replace(/(\w*)[. -](\w*)/g, ($1, $2, $3) => {
        return $2 + $3[0].toUpperCase() + $3.slice(1)
      })
    },
    submit(type = true) {
      this.$refs.form.validate((valid) => {
        if (valid && type) {
          this.$emit('success', this.getFormData(), this.formData)
          !this.successNoVisible && this.handleClose()
        }
      })
    }
  }
}
</script>
<style>
.form-popperclass{
  font-size: 12px !important;
  padding: 10px;
}
</style>
<style scoped>
::v-deep .el-form-item__content{
  display: flex !important;
}
</style>
