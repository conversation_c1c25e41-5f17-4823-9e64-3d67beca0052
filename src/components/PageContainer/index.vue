<!--
    @component PageContainer 新样式的布局组件（白底、圆角、标题），
                                 原型界面见 https://rp.mockplus.cn/run/xF26xszY8k0x/_loYmj4QJU9/EYV7Lm-py?cps=expand&rps=collapse&nav=1&ha=0&la=0&fc=0&out=0&rt=1
    @props
        {String} header 标题 ''
    <AUTHOR>
    @date 2022-8-8 16:29:00
    @update 2022-8-8 16:29:00
-->
<template>
  <div
    class="page-container bg-white m-10 br-4 flexV"
    style="height: calc(100% - 20px);"
  >
    <slot name="header">
      <p
        v-if="header"
        class="page-title border-bottom p-t-15 p-b-15 p-l-10"
      >
        {{ header }}
      </p>
    </slot>
    <slot />
  </div>
</template>

<script>
export default {
  name: 'PageContainer',
  props: {
    header: {
      type: String,
      default: ''
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
