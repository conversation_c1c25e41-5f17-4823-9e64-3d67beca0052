<template>
  <div style="padding: 0 15px;" @click="toggleClick">
    <cnd-icon :name="isActive ? 'cnd-menu-shrink' : 'cnd-menu-unfold' " class-name="hamburger" />
  </div>
</template>

<script>
export default {
  name: '<PERSON><PERSON>',
  props: {
    isActive: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    toggleClick() {
      this.$emit('toggleClick')
    }
  }
}
</script>

<style scoped>
.hamburger {
  display: inline-block;
  vertical-align: middle !important;
  width: 20px !important;
  height: 20px !important;
  color: #5a5e66;
}

.hamburger.is-active {
  transform: rotate(180deg);
}
</style>
