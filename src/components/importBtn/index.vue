<template>
  <div style="display: inline-block" :style="type != '' ? 'width:100%' : ''">
    <el-upload
      ref="upload"
      class="custom-upload"
      :action="newAction"
      :limit="limit"
      :before-upload="handleBeforeUpload"
      :headers="responseHeaders"
      :data="params"
      :accept="accept"
      :on-success="uploadSuccess"
      :on-error="uploadError"
      :on-progress="uploadProgress"
      :show-file-list="showFileList"
      :auto-upload="autoUpload"
      :disabled="disabled"
    >
      <el-button
        v-if="type == ''"
        :id="id"
        :disabled="disabled"
        size="mini"
        type="primary"
        @click="handleUpload"
      >{{ btnText }}</el-button>
      <span v-if="type != ''" :id="type" class="custom-upload-btn">
        {{ btnText }}
      </span>
      <promptModal
        v-if="promptModalVisible"
        :dialog-visible="promptModalVisible"
        :action-success-url="actionSuccessUrl"
        :action-success-vaild="actionSuccessVaild"
        :row-data="modalRowData"
        :success-mark="successMark"
        :is-pre-import="isPreImport"
        :editable-fields="editableFields"
        :title="title || $t('grid.others.importMatch')"
        @close="handleClose"
        @handle="handleUploadBtn"
        @onSuccess="promptSuccess"
      />
    </el-upload>
  </div>
</template>

<script>
import { MessageUtil } from 'cnd-horizon-utils'
import { Loading, Message } from 'element-ui'
import promptModal from './promptModal'
import { postList } from '@/api/contract'
import { getToken } from 'cnd-horizon-utils/src/utils/auth'

export default {
  name: 'ImportBtn',
  components: { promptModal },
  props: {
    action: {
      type: String,
      default: ''
    },
    actionSuccessUrl: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: ''
    },
    accept: {
      type: String,
      default: '.xls,.xlsx'
    },
    title: {
      type: String,
      default: ''
    },
    params: {
      type: Object,
      default: () => {}
    },
    headers: {
      type: Object,
      default: () => {}
    },
    limit: {
      type: Number,
      default: 1
    },
    showFileList: {
      type: Boolean,
      default: false
    },
    autoUpload: {
      type: Boolean,
      default: true
    },
    btnText: {
      type: String,
      default: '导入'
    },
    disabled: {
      type: Boolean,
      default: false
    },
    isSync: {
      type: Boolean,
      default: false
    },
    successMark: {
      type: String,
      default: 'id'
    },
    queryParams: {
      type: Object,
      default: () => {}
    },
    id: {
      type: String,
      default: 'uploadBtn'
    },
    actionSuccessVaild: {
      type: String,
      default: ''
    },

    // 是否为预导入
    isPreImport: {
      type: Boolean,
      default: false
    },

    // 可编辑字段
    editableFields: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      loadingInstance: null,
      responseHeaders: Object.assign(
        {
          Authorization: getToken()
        },
        this.headers
      ),

      promptModalVisible: false,
      modalRowData: [],
      newAction: this.action
    }
  },
  watch: {
    action(val) {
      this.newAction = val
    }
  },
  methods: {
    resetFileInput() {
      if (this.$refs.upload) {
        this.$refs['upload'].clearFiles()
      }
    },
    hasValue(data) {
      if (typeof data === 'string' || Array.isArray(data)) {
        return data.length > 0
      }
      if (typeof data === 'boolean') {
        return data
      }
      return false
    },
    handleUpload(e) {
      this.$emit('handleUpload', e)
    },
    handleClose() {
      this.promptModalVisible = false
      this.resetFileInput()
    },
    handleUploadBtn() {
      this.promptModalVisible = false
      let uploadBtn = null
      if (this.type === '') {
        uploadBtn = document.getElementById(this.id)
      } else {
        uploadBtn = document.getElementById(this.type)
      }
      uploadBtn.click()
    },
    handleBeforeUpload(file) {
      this.loadingInstance = Loading.service({
        fullscreen: true,
        text: this.$t('grid.others.dataImportingPleaseWait'),
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.8)'
      })
      this.$emit('beforeUpload', file)
    },
    uploadSuccess(res, file, fileList) {
      const data = res.data
      this.loadingInstance && this.loadingInstance.close()
      if (this.isSync) {
        if (res.code === '0000') {
          Message({
            message: '后台导入中，请等待通知',
            duration: 3000,
            type: 'info'
          })
        } else {
          MessageUtil.error(res.message || this.$t('grid.others.importFailed'))
        }
      } else {
        if (!data) {
          if (res.message === 'ok') {
            MessageUtil.error(this.$t('grid.others.importFailed'))
          } else {
            MessageUtil.error(
              res.message || this.$t('grid.others.importFailed')
            )
          }
        } else if (
          data.sStockReceiptId ||
          data.sid ||
          data.sId ||
          data.sConThirdDeliveryId ||
          data.sUpId ||
          data.thirdDeliverDetailIds ||
          data.escOnRoadDetailIds ||
          data.sConStockReceiptAdjustId ||
          data.tskAllotId ||
          data.escCustomerExtId ||
          data.sSaleInvoiceId ||
          this.hasValue(data[this.successMark])
        ) {
          if (this.isPreImport) {
            this.promptModalVisible = true
            this.modalRowData = data.excelDataList
          } else {
            MessageUtil.success(this.$t('grid.others.importSuccessful'))
            this.$emit('success', res, file, fileList)
          }
        } else if (data.dataStatus === '-2' && this.queryParams) {
          this.$confirm(data.errorMessage, this.$t('grid.others.prompt'), {
            confirmButtonText: this.$t('btns.confirm'),
            cancelButtonText: this.$t('btns.cancel'),
            dangerouslyUseHTMLString: true,
            type: 'warning'
          }).then(() => {
            this.newAction =
              this.action + '?' + new URLSearchParams(this.queryParams)
            this.resetFileInput()
            this.$nextTick(() => {
              this.$refs['upload'].handleStart(fileList[0].raw)
              this.$refs.upload.submit()
              this.newAction = this.action
            })
          })
        } else {
          if (data.sIsShowTips === '1') {
            this.$confirm(data.sShowTips, this.$t('grid.others.prompt'), {
              confirmButtonText: this.$t('btns.confirm'),
              cancelButtonText: this.$t('btns.cancel'),
              dangerouslyUseHTMLString: true,
              type: 'warning'
            }).then(() => {
              this.continueImport(data.excelDataList)
            })
          } else {
            this.promptModalVisible = true
            this.modalRowData = data.excelDataList
            this.$emit('error', res, file, fileList)
          }
        }
      }
      this.resetFileInput()
    },
    continueImport(data) {
      if (this.actionSuccessUrl) {
        postList(data, this.actionSuccessUrl).then((res) => {
          const data = res.data
          if (!data) {
            if (res.message === 'ok') {
              this.$message.error(this.$t('grid.others.importFailed'))
            } else {
              this.$message.error(
                res.message || this.$t('grid.others.importFailed')
              )
            }
          } else if (
            data.sStockReceiptId ||
            data.sid ||
            data.sId ||
            data.sConThirdDeliveryId ||
            data.sUpId ||
            data.thirdDeliverDetailIds ||
            data.escOnRoadDetailIds ||
            data.sConStockReceiptAdjustId ||
            data.tskAllotId ||
            data.escCustomerExtId ||
            data.sSaleInvoiceId ||
            this.hasValue(data[this.successMark])
          ) {
            this.$message.success(this.$t('grid.others.importSuccessful'))
            this.$emit('success', res)
          } else {
            const excelError = data.excelDataList.find(
              (excel) => excel.dataStatus === '-1'
            )
            this.$message.error(
              excelError?.errorMessage ?? this.$t('grid.others.importFailed')
            )
            this.$emit('error', res)
          }
        })
      } else {
        this.$message.error(this.$t('请配置actionSuccessUrl'))
      }
    },
    uploadError(error) {
      console.log('error object:', JSON.stringify(error, null, 2))

      let errorMessage = 'Request failed'

      if (error && error.status) {
        switch (error.status) {
          case 404:
            errorMessage = '资源未找到'
            break
          case 500:
            errorMessage = '服务器内部错误'
            break
          case 403:
            errorMessage = '无访问权限'
            break
          case 401:
            errorMessage = '未授权访问'
            break
          case 504:
            errorMessage = '请求超时'
            break
          default:
            errorMessage = `HTTP状态码：${error.status}`
        }
      }

      this.$message.error(errorMessage)
      this.isLoading = false
      this.loadingInstance && this.loadingInstance.close()
    },
    uploadProgress(res, file) {
      this.$emit('uploadProgress', res, file)
    },
    promptSuccess(data) {
      this.promptModalVisible = false
      this.$emit('success', data)
    }
  }
}
</script>
<style>
.el-upload--text {
  width: 100% !important;
}
.custom-upload {
  display: block !important;
  width: 100% !important;
}
.custom-upload-btn {
  display: inline-block;
  width: 100% !important;
  /* border: 1px solid #409EFF; */
  white-space: nowrap;
  box-sizing: border-box;
}
</style>
