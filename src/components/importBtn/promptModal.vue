<template>
  <cnd-dialog
    :title="title || $t('grid.others.importMatch')"
    append-to-body
    height="500"
    width="80%"
    :fullscreen="false"
    :visible.sync="dialogVisible"
    @close="handleClose"
  >
    <template slot="content">
      <auto-wrap class="mt-10">
        <steelTradeAggrid
          ref="aggrid"
          class="mb-10"
          :column-defs="columnDefs"
          :row-data="tableData"
          :paginationinif="false"
          :table-selection="isPreImport ? 'multiple': null"
        />
      </auto-wrap>
    </template>
    <template slot="footer">
      <div class="dialog-footer">
        <div v-if="isPreImport" class="error-tips">
          <span>请勾选需要导入的数据（标识需为“成功”）</span>
        </div>
        <div v-else class="error-tips">{{ hasSuccess ? $t('grid.others.dataMatchFailedPleaseReImport')+'或导入成功记录' : $t('grid.others.dataMatchFailedPleaseReImport') }}
        </div>
        <div>
          <el-button size="mini" @click="handleClose">{{ $t('btns.cancel') }}</el-button>
          <template v-if="!isPreImport">
            <el-button v-if="hasSuccess" type="primary" size="mini" @click="importSuccessData">导入成功记录</el-button>
            <el-button type="primary" size="mini" @click="handleDetermine">{{ $t('btns.confirm') }}</el-button>
          </template>
          <template v-else>
            <el-button type="primary" size="mini" @click="importSuccessData">{{ $t('btns.confirm') }}</el-button>
          </template>
        </div>
      </div>
    </template>
  </cnd-dialog>
</template>

<script>
import {
  SteelFormat
} from 'cnd-horizon-utils'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import { postList } from '@/api/contract'
export default {
  components: {
    steelTradeAggrid
  },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    },
    rowData: {
      type: Array,
      default: () => []
    },
    actionSuccessUrl: {
      type: String,
      default: ''
    },
    successMark: {
      type: String,
      default: 'id'
    },
    actionSuccessVaild: {
      type: String,
      default: ''
    },
    // 是否为预导入
    isPreImport: {
      type: Boolean,
      default: false
    },

    // 可编辑字段
    editableFields: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      tableData: this.initTableData(),
      columnDefs: this.initColumnDefs()
    }
  },
  computed: {
    hasSuccess() {
      return this.tableData.some(item => (+item._dataStatus) !== -1) && this.actionSuccessUrl !== ''
    }
  },
  methods: {
    initTableData() {
      return this.rowData.map((item, index) => {
        Object.keys(item).forEach(key => {
          if (typeof item[key] === 'number') {
            item[key] === SteelFormat.formatThousandthSign(item[key])
          }
        })
        return {
          ...item,
          index: index + 1,
          _dataStatus: item.dataStatus,
          dataStatus: (+item.dataStatus) === -1 ? this.$t('grid.others.failure') : this.$t('grid.others.success'),
          _selected: false,
          _selectedKeys: []
        }
      })
    },
    initColumnDefs() {
      const { schema, sortColumns } = this.rowData[0]
      const result = sortColumns.map(item => {
        return {
          headerName: schema[item],
          field: item,
          width: item === 'dataStatus' ? 100 : undefined,
          cellStyle: params => {
            return {
              textAlign: item.includes('Qty') || item.includes('Qtx') ? 'right' : 'left',
              color: item === 'dataStatus' ? params.value === this.$t('grid.others.failure') ? '#EE0033' : '#67C23A' : '#000'
            }
          },
          editable: this.editableFields.length > 0 ? this.editableFields.includes(item) : false
        }
      })

      result.unshift({
        headerName: this.$t('grid.title.serialNumber'),
        field: 'index',
        width: 70,
        cellStyle: { textAlign: 'center' }
      })
      return result
    },
    async importSuccessData() {
      if (this.actionSuccessVaild !== '') {
        const data = await this.checkBeforeSuccessImport()
        if (data) {
          this.importSuccessDataV2()
        }
      } else {
        this.importSuccessDataV2()
      }
    },
    importSuccessDataV2() {
      if (this.editableFields.length > 0) {
        this.$refs.aggrid.gridApi.stopEditing()
      }
      let successData = this.tableData.filter(item => (+item._dataStatus) !== -1)
      console.log('successData: ', successData)
      if (this.isPreImport) {
        successData = successData.filter(item => item._selected)
        if (successData.length === 0) {
          this.$message.warning('请勾选标识为成功的数据进行导入')
          return
        }
      }
      postList(successData, this.actionSuccessUrl).then((res) => {
        const data = res.data
        if (!data) {
          if (res.message === 'ok') {
            this.$message.error(this.$t('grid.others.importFailed'))
          } else {
            this.$message.error(res.message || this.$t('grid.others.importFailed'))
          }
        } else if (
          data.sStockReceiptId ||
          res.data.sid ||
          res.data.sId ||
          res.data.sConThirdDeliveryId ||
          data.sUpId ||
          data.thirdDeliverDetailIds ||
          data.escOnRoadDetailIds ||
          data.sConStockReceiptAdjustId ||
          data.tskAllotId ||
          data.escCustomerExtId ||
          data.sSaleInvoiceId ||
          data[this.successMark]
        ) {
          this.$message.success(this.$t('grid.others.importSuccessful'))
          this.$emit('onSuccess', res)
        } else {
          const excelError = data.excelDataList.find(excel => excel.dataStatus === '-1')
          if (excelError && excelError.errorMessage) {
            this.$message.error(excelError.errorMessage)
          }
        }
      })
    },
    checkBeforeSuccessImport() {
      const successData = this.tableData.filter(item => (+item._dataStatus) !== -1)
      return new Promise((resolve, reject) => {
        postList(successData, this.actionSuccessVaild).then((res) => {
          if (res.data.checkFlag) {
            resolve(true)
          } else {
            this.$confirm(res.data.msg, this.$t('grid.others.prompt'), {
              dangerouslyUseHTMLString: true,
              confirmButtonText: this.$t('btns.confirm'),
              cancelButtonText: this.$t('btns.cancel'),
              type: 'warning'
            }).then(() => {
              resolve(true)
            }).catch(() => {
              reject(false)
            })
          }
        })
      })
    },
    handleDetermine() {
      this.$emit('handle')
    },
    handleClose() {
      this.$emit('close')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-footer{
  display: flex;
  justify-content: space-between;
  align-items: center;
  .error-tips{
    color: #EE0033;
  }
}
</style>
