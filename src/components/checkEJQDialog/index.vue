<template>
  <cnd-dialog
    v-if="visible"
    :visible="visible"
    :fullscreen="false"
    append-to-body
    title="选择签收人"
    width="350px"
    height="40px"
    @close="close"
  >
    <template slot="content">
      <div>
        <el-form
          :model="form"
          label-width="60px"
          size="small"
          @submit.native.prevent
        >
          <cnd-form-item
            label="签约人"
            :custom-width="22"
          >
            <el-select
              v-model="form.ejqCustomerId"
              :placeholder="$t('components.pleaseSelect')"
              @change="change"
            >
              <el-option
                v-for="item in selectList"
                :key="item.accountId"
                :label="`${showAccount ? item.account + item.name : item.mobile + item.name}`"
                :value="item.accountId"
              />
            </el-select>
          </cnd-form-item>
        </el-form>
      </div>
    </template>
    <template slot="footer">
      <el-button
        size="mini"
        @click="close"
      >{{ $t('btns.cancel') }}</el-button>
      <el-button
        type="primary"
        size="mini"
        @click="submit"
      >{{ $t('btns.confirm') }}{{ $t('grid.others.submit') }}</el-button>
    </template>
  </cnd-dialog>
</template>

<script>
import { getAllSignPhone } from '@/api/common'
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    accountId: {
      type: String,
      default: ''
    },
    showAccount: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      form: {
        sIsNeed: '1',
        ejqCustomerId: ''
      },
      selectList: [],
      selectedData: {}
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.form = {
          sIsNeed: '1',
          ejqCustomerId: ''
        }
        this.getSelectList()
      }
    }
  },
  methods: {
    formatPhone(value) {
      return value.replace(/.*-/, '')
    },
    getSelectList() {
      getAllSignPhone(this.accountId).then(res => {
        this.selectList = res.data.map(item => {
          item.account = this.formatPhone(item.account)
          return item
        })
      })
    },
    close() {
      this.$emit('close')
    },
    change(e) {
      this.selectedData = this.selectList.find(el => el.accountId === e)
    },
    submit() {
      if (this.form.ejqCustomerId) {
        const obj = Object.assign({}, this.selectedData, this.form)
        this.$emit('success', obj)
      } else {
        this.$message.error('请选择签约人')
      }
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
