<template>
  <!-- 主子表 头部全选框 -->
  <!-- 头部标题复选框 -->
  <el-checkbox
    v-if="showHeaderSelect"
    :value="isChecked"
    :indeterminate="indeterminate"
    @change="handleCheckAllChange"
  />
</template>

<script>
export default {
  computed: {
    showHeaderSelect() {
      const { showHeaderSelect = true } = this.params.context
      return showHeaderSelect
    },
    isChecked() {
      const { dataKey = 'rowData' } = this.params.context
      const rowData = this.params.context.componentParent[dataKey]
      const { childrenListKey = '_details' } = this.params.context
      if (!rowData.length) { return false }
      const flag = rowData.every(item => {
        if (item[childrenListKey] && item._selectedKeys && item._selected) {
          return item._selectedKeys.length === item[childrenListKey].length
        }
        return false
      })
      return flag
    },
    indeterminate() {
      const { dataKey = 'rowData' } = this.params.context
      const rowData = this.params.context.componentParent[dataKey]
      return this.isChecked || !rowData.length ? false : rowData.some(item => (item._selectedKeys && item._selectedKeys.length) || item._selected)
    }
  },
  methods: {
    handleCheckAllChange(e) {
      console.log('headerSelect')
      const { childrenListKey = '_details', apiKey = 'gridApi', childRowKey } = this.params.context
      const fatherapi = this.params.context.componentParent[apiKey]
      fatherapi.forEachNode(async(node) => {
        node.data._selected = e
        if (e && !node.data[childrenListKey].length && typeof this.params.context.loadDetail === 'function') {
          node.data[childrenListKey] = await this.params.context.loadDetail(node.data)
        }
        if (e && node.data[childrenListKey]) {
          node.data._selectedKeys = node.data[childrenListKey].map(item => { return item[childRowKey] })
        } else {
          node.data._selectedKeys = []
        }
        const rowNode = fatherapi.getDetailGridInfo(`detail_${node.id}`)
        rowNode && rowNode.api.refreshCells({ force: true })
      })
      fatherapi.refreshCells({ force: true })
    }
  }
}
</script>
