<template>
  <!-- 单表 头部全选框 -->
  <el-checkbox
    v-if="!isSingle"
    :value="isChecked"
    :indeterminate="indeterminate"
    @change="handleCheckAllChange"
  />
</template>

<script>
export default {
  computed: {
    isSingle() {
      const { tableSelection } = this.params.context
      return tableSelection === 'single'
    },
    isChecked() {
      const { dataKey = 'rowData' } = this.params.context
      const rowData = this.params.context.componentParent[dataKey]
      if (!rowData.length) { return false }
      const flag = rowData.every(item => {
        return item._selected
      })
      return flag
    },
    indeterminate() {
      const { dataKey = 'rowData' } = this.params.context
      const rowData = this.params.context.componentParent[dataKey]
      return this.isChecked || !rowData.length ? false : rowData.some(item => item._selected)
    }
  },
  methods: {
    handleCheckAllChange(e) {
      console.log('headerDefSelect')
      this.params.context.onHeaderDefSelect(e)
      const { apiKey = 'gridApi' } = this.params.context
      const fatherapi = this.params.context.componentParent[apiKey]
      fatherapi.forEachNode((node) => {
        node.data._selected = e
      })
      fatherapi.refreshCells({ force: true })
    }
  }
}
</script>
