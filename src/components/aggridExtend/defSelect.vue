<template>
  <!-- 单表 行选择框 -->
  <el-checkbox
    v-if="!params.data._hiddenCheckbox"
    :checked="params.data._selected && isChecked"
    @change="handleCheckAllChange"
  />

</template>

<script>
export default {
  data() {
    return {
    }
  },
  computed: {
    isChecked() {
      const { _selected } = this.params.data
      return _selected
    }
  },
  mounted() {
    const { _selected } = this.params.data
    if (typeof _selected !== 'boolean') {
      console.error('_selected配置未初始化', _selected, this.params.data)
    }
  },
  methods: {
    handleCheckAllChange(e) {
      console.log('defSelect')
      this.params.context.onDefSelect(this.params, e)
      this.params.node.selected = e
      const { apiKey = 'gridApi', tableSelection, rowKey = 'sId' } = this.params.context
      if (tableSelection === 'single') {
        if (e) {
          const fatherapi = this.params.context.componentParent[apiKey]
          fatherapi.forEachNode((node) => {
            if (node.data[rowKey] === this.params.data[rowKey]) {
              node.data._selected = e
            } else {
              node.data._selected = false
            }
          })
          fatherapi.refreshCells({ force: true })
        } else {
          this.params.data._selected = e
          this.params.api.refreshCells({ force: true })
        }
      } else {
        this.params.data._selected = e
        this.params.api.refreshCells({ force: true })
      }
    }
  }
}
</script>
