<template>
  <!-- 主子表 -->
  <!-- 子级 行复选框 -->
  <span>
    <div v-if="countList&&countList.length>0">
      <aggridCount flag="footer" :count-list="countList" />
    </div>
    <el-checkbox
      v-else
      :checked="isChecked"
      @change="handleCheckAllChange"
    />
  </span>
</template>

<script>
import aggridCount from '../steelTradeAggrid/gridTotal'
export default {
  components: { aggridCount },
  computed: {
    countList() {
      return this.params.data._childCount
    },
    isChecked() {
      const { apiKey = 'gridApi' } = this.params.context
      const fatherapi = this.params.context.componentParent[apiKey]

      if (!fatherapi) {
        throw new Error('请确认父组件的componentParent指向的gridApi是否存在')
      }
      const selected = fatherapi.getRowNode(this.params.data.parentId).data._selectedKeys
      // !!selected.length &&
      return selected.includes(this.params.data[this.params.context.childRowKey])
    }
  },
  methods: {
    handleCheckAllChange(e) {
      console.log('childSelect')
      const { childrenListKey = '_details', apiKey = 'gridApi', childRowKey } = this.params.context
      const fatherapi = this.params.context.componentParent[apiKey]
      this.params.node.selected = e
      fatherapi.forEachNode((node) => {
        if (this.params.data.parentId === node.id) {
          if (e) {
            node.data._selectedKeys.push(this.params.data[childRowKey])
          } else {
            node.data._selectedKeys = node.data._selectedKeys.filter(item => item !== this.params.data[childRowKey])
          }
          node.data._selected = node.data._selectedKeys.length === node.data[childrenListKey].length
        }
      })
      this.params.api.refreshCells({ force: true })
      fatherapi.refreshCells({ force: true })
    }
  }
}
</script>
