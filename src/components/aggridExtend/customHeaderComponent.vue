<template>
  <div class="ag-cell-label-container ag-header-cell-sorted-none">
    <span ref="eMenu" class="ag-header-icon ag-header-cell-menu-button" @click="handleMenuClick"><span class="ag-icon ag-icon-menu" unselectable="on" /></span>
    <div ref="eLabel" class="ag-header-cell-label custom-header" unselectable="on" @click="handleTextClick">
      <span ref="eText" class="ag-header-cell-text" unselectable="on">
        <el-popover
          v-if="params.popoverContent"
          placement="bottom-start"
          trigger="hover"
          class="ag-custom-header-popver"
        >
          <div v-html="params.popoverContent" />
          <span slot="reference">
            {{ params.displayName }}
            <i class="el-icon-warning-outline custom-icon" />
          </span>
        </el-popover>
        <span v-else>{{ params.displayName }}</span>
      </span>
      <span ref="eFilter" class="ag-header-icon ag-header-label-icon ag-filter-icon ag-hidden"><span class="ag-icon ag-icon-filter" unselectable="on" /></span>
      <span ref="eSortOrder" :class="{ 'ag-hidden': sortState === 'none' }" class="ag-header-icon ag-header-label-icon ag-sort-order ag-hidden" />
      <span ref="eSortAsc" :class="{ 'ag-hidden': sortState !== 'asc' }" class="ag-header-icon ag-header-label-icon ag-sort-ascending-icon"><span class="ag-icon ag-icon-asc" unselectable="on" /></span>
      <span ref="eSortDesc" :class="{ 'ag-hidden': sortState !== 'desc' }" class="ag-header-icon ag-header-label-icon ag-sort-descending-icon"><span class="ag-icon ag-icon-desc" unselectable="on" /></span>
      <span ref="eSortNone" class="ag-header-icon ag-sort-none-icon" :class="{ 'ag-hidden': sortState !== 'none' }" />
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      sortState: 'none' // 'none', 'asc', 'desc'

    }
  },
  created() {
    this.$parent.$on('sortChanged', this.handleSortChanged)
  },
  beforeDestroy() {
    this.$parent.$off('sortChanged', this.handleSortChanged)
  },
  methods: {
    handleSortChanged(event) {
      const sortedColumns = event.columnApi.getColumnState()
      const currentSortedColumn = sortedColumns.filter(column => column.sort)
      if (this.params.column.colId !== currentSortedColumn[0]?.colId) {
        this.sortState = 'none'
      }
    },
    handleMenuClick() {
      this.params.showColumnMenu(this.$refs.eMenu)
    },
    handleTextClick(e) {
      if (this.sortState === 'none') {
        this.sortState = 'asc'
      } else if (this.sortState === 'asc') {
        this.sortState = 'desc'
      } else {
        this.sortState = 'none'
      }
      if (this.sortState === 'asc') {
        this.params.setSort('asc', e.shiftKey)
      } else if (this.sortState === 'desc') {
        this.params.setSort('desc', e.shiftKey)
      } else {
        this.params.setSort(null, e.shiftKey)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.ag-custom-header-popver{
  .custom-icon{
    font-size: 14px;
  }
}
</style>
