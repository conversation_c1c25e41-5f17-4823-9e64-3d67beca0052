<template>
  <!-- 主子表 -->
  <!-- 父级 行复选框 -->
  <el-checkbox
    :indeterminate="indeterminate"
    :checked="params.data._selected && isChecked"
    @change="handleCheckAllChange"
  />
</template>

<script>
export default {
  computed: {
    isChecked() {
      const { _selectedKeys, _selected } = this.params.data
      const { childrenListKey = '_details' } = this.params.context
      if (_selectedKeys && this.params.data[childrenListKey]) {
        return _selectedKeys.length === this.params.data[childrenListKey].length && _selected
      }
      return false
    },
    indeterminate() {
      const { _selectedKeys } = this.params.data
      const { childrenListKey = '_details' } = this.params.context
      if (_selectedKeys && this.params.data[childrenListKey]) {
        return !!_selectedKeys.length && _selectedKeys.length !== this.params.data[childrenListKey].length
      }
      return false
    }
  },
  mounted() {
    const { _selected, _selectedKeys } = this.params.data
    if (typeof _selected !== 'boolean') {
      console.error('_selected配置未初始化')
    }
    if (typeof _selectedKeys !== 'object') {
      console.error('_selectedKeys配置未初始化')
    }
  },
  methods: {
    async handleCheckAllChange(e) {
      console.log('parentSelect')
      const { childrenListKey = '_details', childRowKey } = this.params.context
      this.params.data._selected = e
      this.params.node.selected = e
      // 调用子表更新
      if (e && !this.params.data[childrenListKey].length && typeof this.params.context.loadDetail === 'function') {
        this.params.data[childrenListKey] = await this.params.context.loadDetail(this.params.data)
      }
      if (e && this.params.data[childrenListKey]) {
        this.params.data._selectedKeys = this.params.data[childrenListKey].map(item => {
          return item[childRowKey]
        })
      } else {
        this.params.data._selectedKeys = []
      }
      this.params.api.refreshCells({ force: true })
      const detailNode = this.params.node.detailNode
      detailNode && detailNode.detailGridInfo && detailNode.detailGridInfo.api.refreshCells({ force: true })
    }
  }
}
</script>
