<template>
  <cnd-dialog
    v-if="visible"
    :visible="visible"
    :fullscreen="false"
    append-to-body
    title="选择签收人"
    height="400px"
    width="1000px"
    @close="close"
  >
    <template slot="content">
      <div class="btn-group">
        <div class="text" />
        <div>
          <el-button
            type="primary"
            size="mini"
            @click="add()"
          >新增</el-button>
        </div>
      </div>
      <auto-wrap>
        <steelTradeAggrid
          ref="aggrid"
          :column-defs="columnDefs"
          :row-data="rowData"
          :load-data="loadData"
          :auto-load-data="false"
          :table-selection="null"
          :paginationinif="false"
          @selectedChange="selectedChange"
          @cellValueChanged="cellValueChanged"
        />
      </auto-wrap>
    </template>
    <template slot="footer">
      <div class="footer-container">
        <div class="footer-container-text">*二次提交电签所有附件需重新签署</div>
        <div>
          <el-button
            size="mini"
            @click="close"
          >{{ $t('btns.cancel') }}</el-button>
          <el-button
            type="primary"
            size="mini"
            @click="submit"
          >{{ $t('btns.confirm') }}{{ $t('grid.others.submit') }}</el-button>
        </div>
      </div>
    </template>
  </cnd-dialog>
</template>

<script>
import Vue from 'vue'
import { getSignInfo } from '@/api/common'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import customMiddleware from '@/components/agComponent/middleware'
import { deepClone } from '@/components/steelTradeAggrid/utils.js'
export default {
  components: {
    steelTradeAggrid
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    accountId: {
      type: String,
      default: ''
    },
    params: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      selectList: [],
      selectedData: {},
      columnDefs: [
        {
          field: 'sCustomerTypeName',
          headerName: '类型',
          editable: true,
          cellEditorFramework: Vue.extend(customMiddleware.createComponent(
            'AgGridSelect',
            {
              mark: 'sCustomerTypeName',
              config: {
                label: 'label',
                value: 'value'
              },
              filterable: false,
              remote: false,
              searchLimit: 0,
              placeholder: this.$t('grid.others.pleaseEnterTheSearch'),
              queryMethod: (params) => {
                return new Promise((resolve, reject) => {
                  resolve([
                    {
                      label: '原合同客户',
                      value: '原合同客户'
                    },
                    {
                      label: '提货客户',
                      value: '提货客户'
                    }
                  ])
                })
              }
            },
            {
              getOption: (option, params, middleware) => {
                this.rowData[params.rowIndex].sCustomerTypeName = option.value
                const findObj = this.orgRowData.find(e => e.sCustomerTypeName === option.value)
                this.rowData[params.rowIndex].sCustomerName = findObj.sCustomerName
                this.rowData[params.rowIndex].accountVo = findObj.accountVo
                this.rowData[params.rowIndex].accountVoList = findObj.accountVoList
                this.rowData[params.rowIndex].allFileConfigSelectedVoList = findObj.allFileConfigSelectedVoList
                this.rowData[params.rowIndex].fileConfigSelectedVoList = findObj.fileConfigSelectedVoList
                this.rowData[params.rowIndex].sCustomerId = findObj.sCustomerId
                this.rowData[params.rowIndex].sCustomerType = findObj.sCustomerType
                this.rowData[params.rowIndex]._account = findObj._account
                this.rowData[params.rowIndex]._fileName = findObj._fileName
                this.$refs.aggrid.gridApi.refreshCells(params)
              }
            }
          ))
        },
        {
          field: 'sCustomerName',
          headerName: '签约客商'
        },
        {
          field: '_account',
          headerName: '签约人',
          width: 250,
          editable: true,
          cellEditorFramework: Vue.extend(customMiddleware.createComponent(
            'AgGridSelect',
            {
              mark: '_account',
              config: {
                label: '_account',
                value: '_account'
              },
              filterable: false,
              remote: false,
              searchLimit: 0,
              placeholder: this.$t('grid.others.pleaseEnterTheSearch'),
              queryMethod: (params) => {
                return new Promise((resolve, reject) => {
                  resolve(params.data.accountVoList)
                })
              }
            },
            {
              getOption: (option, params, middleware) => {
                this.rowData[params.rowIndex].accountVo = option
                this.rowData[params.rowIndex]._account = option._account
                this.$refs.aggrid.gridApi.refreshCells(params)
              }
            }
          ))
        },
        {
          field: '_fileName',
          headerName: '签约附件',
          minWidth: 360,
          editable: true,
          cellEditorFramework: Vue.extend(customMiddleware.createComponent(
            'AgGridSelect',
            {
              mark: 'allFileConfigSelectedVoList',
              config: {
                label: 'sFileName',
                value: 'sFileName'
              },
              filterable: false,
              remote: false,
              multiple: true,
              searchLimit: 0,
              placeholder: this.$t('grid.others.pleaseEnterTheSearch'),
              queryMethod: (params) => {
                return new Promise((resolve, reject) => {
                  resolve(params.data.fileConfigSelectedVoList)
                })
              }
            },
            {
              getOption: (option, params, middleware) => {
                this.rowData[params.rowIndex]._fileName = option.sFileName
                this.rowData[params.rowIndex].allFileConfigSelectedVoList = option
                this.$refs.aggrid.gridApi.refreshCells(params)
              }
            }
          ))
        },
        {
          field: '',
          headerName: this.$t('grid.others.operation'),
          onCellClicked: (params) => {
            this.rowData.splice(params.rowIndex, 1)
          },
          cellRenderer: (params) => {
            const sHtml = '<span style=color:red;cursor:pointer>' + this.$t('btns.delete') + '</span>'
            return params.data._hiddenCheckbox ? params.value : sHtml
          }
        }
      ],
      rowData: [],
      orgRowData: []
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.rowData = []
        this.$nextTick(() => {
          this.$refs.aggrid.loadTableData()
        })
      }
    }
  },
  methods: {
    formatPhone(value) {
      return value.replace(/.*-/, '')
    },
    loadData() {
      return new Promise((resolve, reject) => {
        getSignInfo(this.params).then(res => {
          const rowData = res.data.map((item, index) => {
            // item._selected = false
            // item._account = null
            // item._fileName = null
            // item.accountVoList = item.accountVoList.map(e => {
            //   e._account = this.formatPhone(e.account) + e.name
            //   return e
            // })
            // return item
            return {
              ...item,
              _selected: false,
              _account: null,
              _fileName: null,
              accountVoList: item.accountVoList.map(e => {
                e._account = this.formatPhone(e.account) + e.name
                return e
              })
            }
          })
          this.orgRowData = deepClone(rowData)
          this.rowData = rowData
          resolve(res.data)
        }).catch(() => {
          reject(0)
        })
      })
    },
    add() {
      const obj = this.orgRowData.find(e => e.sCustomerType === '1')
      this.rowData.push(deepClone(obj))
    },
    close() {
      this.$emit('close')
    },
    submit() {
      this.$refs.aggrid.gridApi.stopEditing()
      this.$nextTick(() => {
        this.$emit('success', this.rowData)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.footer-container{
  display: flex;
  justify-content: space-between;
   &-text{
    color: red;
    font-size: 14px;
   }
}
</style>
