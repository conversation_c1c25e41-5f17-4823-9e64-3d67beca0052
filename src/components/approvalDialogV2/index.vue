<template>
  <el-drawer
    :visible="visible"
    :with-header="false"
    :modal-append-to-body="false"
    :append-to-body="true"
    :direction="direction"
    size="40%"
    :before-close="handleClose"
    class="approvalDialog"
  >
    <div class="approval-container">
      <div :style="`height: calc(100% - ${soltBtn ? '130px' : '0px'})`">
        <el-col :span="10" class="approval-opinion border-except-right">
          <div class="border-bottom approval-title">{{ $t('grid.others.approvalComments') }}</div>
          <div
            :class="
              flowList.length
                ? 'approval-content'
                : 'flex-center approval-content'
            "
          >
            <el-timeline v-if="flowList.length">
              <el-timeline-item
                v-for="(item, index) in flowList"
                :key="index"
                :class="{ pointer}"
                :color="contentStatus(item, index)"
                :timestamp="renderTime(item.approvalDate)"
                @click.native="handleChangeInstructions(item)"
              >
                <div>
                  <span v-if="item.executorStatus" class="mr-5" style="font-size: 16px">{{
                    item.proxyReviewPersonName
                  }}</span>
                  <span v-else>{{
                    item.submitPersonName
                  }}</span>
                </div>
                <div>
                  <p
                    :style="`color: ${ contentStatus(item, index) }`"
                    class="approval-content-comment"
                  >
                    {{ item.executorStatus }}
                  </p>
                </div>
              </el-timeline-item>
            </el-timeline>
            <div v-else style="color: #8c8c8c">{{ $t('grid.others.noApprovalCommentsAtThisTime') }}</div>
          </div>
        </el-col>
        <el-col :span="14" style="height: 100%" class="border">
          <div class="border-bottom approval-title">{{ $t('grid.others.approvalInstructions') }}</div>
          <div class="approval-content" v-html="explainData" />
        </el-col>
      </div>
      <div v-if="soltBtn" class="approval-btn">
        <slot />
      </div>
    </div>
  </el-drawer>
</template>

<script>
import { postList } from '@/api/contract'

export default {
  components: {},
  props: {
    // 弹窗显隐
    visible: {
      type: Boolean,
      default: false
    },
    soltBtn: {
      type: Boolean,
      default: false
    },
    id: {
      type: String,
      default: ''
    },
    url: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: ''
    },
    sheetCode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      direction: 'rtl',
      explainData: null,
      flowList: []
    }
  },
  computed: {},
  watch: {
    visible(nv, ov) {
      if (nv) {
        this.getProgress()
      } else {
        this.explainData = null
      }
    }
  },
  created() {
  },
  methods: {
    handleChangeInstructions(data) {
      this.explainData = data.approvalOpinion.replace(/\n/g, '<br>')
    },
    renderTime(date) {
      if (!date) {
        return ''
      }
      const dateee = new Date(date).toJSON()
      return new Date(+new Date(dateee) + 8 * 3600 * 1000).toISOString().replace(/T/g, ' ').replace(/\.[\d]{3}Z/, '')
    },
    getProgress() {
      postList({
        sId: this.id,
        sSheetCode: this.sheetCode
      }, `/erp/approval/info`).then(res => {
        if (res.data?.approvalDetails?.latelyApprovalList.length > 0) {
          this.explainData = res.data.approvalDetails.latelyApprovalList[0]?.approvalOpinion &&
    res.data.approvalDetails.latelyApprovalList[0].approvalOpinion.replace(/\n/g, '<br>')
          this.flowList = res.data.approvalDetails.latelyApprovalList
        } else {
          this.flowList = []
        }
      })
    },
    contentStatus(item, index, type) {
      if (item.executorStatus && item.executorStatus.indexOf('驳回') !== -1) {
        return '#ef5f5f'
      }
      if (!item.executorStatus) {
        return '#C0C4CC'
      }
      return '#7ac757'
    },
    // 关闭/取消
    handleClose() {
      this.$emit('handleClose')
    },
    sortKey(array, key) {
      return array.sort((a, b) => {
        const x = a[key]
        const y = b[key]
        return x < y ? 1 : x > y ? -1 : 0
      })
    },
    sortArr(arr) {
      const b = []
      for (let i = 0; i < arr.length; i++) {
        if (!arr[i].parentId.length) b.push(arr[i])
      }

      for (let i = 0; i < arr.length; i++) {
        for (let j = 0; j < b.length; j++) {
          if (arr[i].parentId[0] === b[j].id) b.push(arr[i])
        }
      }
      return b
    }
  }
}
</script>
