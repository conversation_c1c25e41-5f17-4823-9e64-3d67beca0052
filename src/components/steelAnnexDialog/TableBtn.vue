<template>
  <div class="flexC h100">
    <a :href="params.data.url" target="_blank" class="flexC">
      <cnd-icon class="eyes-icon" name="cnd-eyes" width="1.2rem" height="1.2rem" />
    </a>
    <cnd-icon name="cnd-download" class="ml-10 download-icon pointer" width="1.2rem" height="1.2rem" @click="download" />
  </div>
</template>

<script>
import Vue from 'vue'
import axios from 'axios'
import { MessageUtil } from 'cnd-horizon-utils'
import { getToken } from 'cnd-horizon-utils/src/utils/auth'

export default Vue.extend({
  data() {
    return {}
  },
  methods: {
    download() {
      // console.log('params--', this.params)
      // window.open(this.params.data.url)
      axios.post(
        process.env.VUE_APP_BASE_API + '/annex/oss/download/' + this.params.data.sId,
        {},
        {
          responseType: 'blob',
          headers: {
            Authorization: getToken()
          }
        }
      ).then((res) => {
        const blob = new Blob([res.data]) // 处理文档流
        const fileName = this.params.data.sName
        const elink = document.createElement('a')
        elink.download = fileName
        elink.style.display = 'none'
        elink.href = URL.createObjectURL(blob)
        document.body.appendChild(elink)
        elink.click()
        URL.revokeObjectURL(elink.href) // 释放URL 对象
        document.body.removeChild(elink)
        this.$nextTick(() => {
          MessageUtil.success(this.$t('tips.downloadSuccessful'))
        })
      })
    },
    // 格式化获取url
    formatUrl(params = {}, url) {
      let urlStr = ''
      if (url.indexOf('{') > -1) {
        const urlArr = url.split('/')
        // console.log('urlArr前--', urlArr)
        urlArr.forEach((item, index) => {
          if (index !== 0) {
            let temp
            if (item.indexOf('{') > -1) {
              // console.log(item.substring(1, item.length - 1))
              temp = `/${params[item.substring(1, item.length - 1)]}`
            } else {
              temp = `/${item}`
            }
            urlStr += temp
          }
        })
        // console.log('urlStr--', urlStr)
      } else {
        urlStr = url
      }
      return urlStr
    }
  }
})
</script>

<style scoped lang="scss">
.download-icon:hover, .eyes-icon:hover {
  color: #66B1FF;
}
</style>
