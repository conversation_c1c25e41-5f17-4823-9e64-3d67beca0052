<template>
  <cnd-dialog
    v-if="visible"
    class-name="annex_dialog dialog-wrap"
    class="annex_dialog"
    :title="curConfig.title"
    :visible="visible"
    :show-close="true"
    :fullscreen="false"
    :append-to-body="appendToBody"
    width="1000px"
    :height="height"
    @close="handleClose"
  >
    <template slot="content">
      <div class="mb-10 flexC">
        <!-- 主上传按钮 -->
        <el-upload
          ref="upload"
          class="upload-area"
          :disabled="disabledBtnComputed.scan"
          action="actionUrl"
          :on-exceed="handleExceed"
          :limit="10"
          :file-list="curFileList"
          :auto-upload="false"
          multiple
          :show-file-list="false"
          :on-change="filesChange"
          :on-remove="fileRemove"
          :http-request="uploadFiles"
        >
          <el-button
            slot="trigger"
            type="primary"
            size="mini"
            :disabled="disabledBtnComputed.scan"
          >
            {{ $t('btns.upload') }}
          </el-button>
        </el-upload>

        <!-- 批量删除按钮 -->
        <el-button
          type="danger"
          size="mini"
          :disabled="disabledBtnComputed.del || !hasSelectedFiles"
          @click="batchDelete"
        >
          {{ $t('btns.delete') }}
        </el-button>

        <!-- 批量下载按钮 -->
        <el-button
          type="primary"
          size="mini"
          :disabled="!hasSelectedFiles"
          @click="batchUploadAll"
        >
          {{ $t('grid.others.batchDownload') }}
        </el-button>

        <!-- 右侧上传配置 -->
        <cnd-btn-position v-if="rightUploadConfig.length > 0" top="50" right="5">
          <span v-for="(item, index) in rightUploadConfig" :key="index">
            <el-upload
              v-if="shouldShowRightUpload(item)"
              ref="uploadRight"
              class="upload-area"
              :disabled="getRightUploadDisabled(item)"
              action="actionUrl"
              :on-exceed="handleExceedRight"
              :limit="10"
              :file-list="curFileListRight"
              :auto-upload="false"
              :multiple="getRightUploadMultiple(item)"
              :show-file-list="false"
              :on-change="(e, v) => filesChangeRight(e, v, item)"
              :on-remove="fileRemoveRight"
              :http-request="() => uploadFilesRight(item)"
            >
              <el-button
                slot="trigger"
                type="primary"
                size="mini"
                :disabled="getRightUploadDisabled(item)"
              >
                {{ item.btnName }}
              </el-button>
            </el-upload>
          </span>
        </cnd-btn-position>
      </div>

      <!-- 文件拖拽区域和表格 -->
      <div
        class="flexV h100 file-drop-zone"
        :class="{ 'drag-area': !disabledBtnComputed.scan, 'dragging': isDragging }"
        @drop.prevent="handleFileDrop"
        @dragover.prevent="handleDragOver"
        @dragenter.prevent="handleDragEnter"
        @dragleave.prevent="handleDragLeave"
      >
        <steel-trade-aggrid
          ref="steelTradeAggrid"
          class="ag-theme-balham grid-class steel-trade-aggrid"
          style="height: 100%;"
          :column-defs="curConfig.columnDefs"
          :row-data="rowData"
          :row-key="'sId'"
          :table-selection="rowSelection"
          :menu-id="'steelAnnexDialog'"
          :load-data="loadData"
          :framework-components="frameworkComponents"
          :auto-load-data="false"
          @selectedChange="selectedChange"
          @cellValueChanged="onCellValueChanged"
          @rowDoubleClicked="onRowDoubleClicked"
        />
      </div>
    </template>
  </cnd-dialog>
</template>

<script>
import 'cnd-icon'
import SteelTradeAggrid from '@/components/steelTradeAggrid'
import { postList, delList, putList } from '@/api/contract'
import { MessageUtil } from 'cnd-horizon-utils'
import TableBtn from './TableBtn'
import axios from 'axios'
import { getToken } from 'cnd-horizon-utils/src/utils/auth'
import { getDictet, getCnDitc } from '@/api/logistics/saleDelivery/saleorder'
import store from '@/store'
import Vue from 'vue'

// 常量定义
const DEFAULT_PAGE_NO = 0
const DEFAULT_PAGE_SIZE = 10
const FILE_UPLOAD_LIMIT = 10
const DRAG_DELAY = 100

export default {
  name: 'SteelAnnexDialog',
  components: {
    SteelTradeAggrid,
    TableBtn
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    radio: {
      type: Boolean,
      default: false
    },
    appendToBody: {
      type: Boolean,
      default: true
    },
    rightUploadConfig: {
      type: Array,
      default: () => []
    },
    bizId: {
      type: String,
      default: ''
    },
    disabledBtn: {
      type: Object,
      default: () => ({
        scan: false,
        upload: false,
        del: false
      })
    },
    api: {
      type: String,
      default: '/annex/oss/uploads'
    },
    option: {
      type: Object,
      default: () => ({})
    },
    uploadOption: {
      type: Object,
      default: () => ({})
    },
    allowedFileTypes: {
      type: Array,
      default: null
    },
    size: {
      type: Number,
      default: 0
    },
    remarkConfig: {
      type: Object,
      default: () => ({
        showRemark: false,
        isEditable: false
      })
    },
    // 限制上传按钮个数
    limitLen: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      frameworkComponents: {
        TableBtn: Vue.extend(TableBtn)
      },
      defaultBtn: {
        scan: false,
        upload: false,
        del: false
      },
      curFileList: [],
      curFileListRight: [],
      uploadTimer: null,
      rowData: [],
      selectedRightDataNodes: [],
      curConfig: this.createTableConfig(),
      selectOps: {
        'esc.files.define': []
      },
      sExtend3: '1',
      isDragging: false,
      validatedFiles: new Set()
    }
  },
  computed: {
    disabledBtnComputed() {
      return { ...this.defaultBtn, ...this.disabledBtn }
    },
    rowSelection() {
      return this.radio ? 'single' : 'multiple'
    },
    height() {
      return '400'
    },
    authenticationList() {
      return store.getters.curBtnAuthList || []
    },
    hasSelectedFiles() {
      return this.selectedRightDataNodes.length > 0
    }
  },
  watch: {
    visible: {
      handler(newVal) {
        if (newVal) {
          this.$nextTick(() => {
            this.loadTableData()
          })
        } else {
          this.resetComponent()
        }
      },
      immediate: false
    }
  },
  created() {
    this.initSelectOptions()
  },
  methods: {
    // 初始化相关方法
    createTableConfig() {
      return {
        title: this.$t('grid.others.uploadAttachment'),
        url: `/annex/oss/page/{pageNo}/{pageSize}`,
        keyword: 'param',
        columnDefs: [
          {
            field: 'sName',
            headerName: '文件名称',
            width: 200,
            cellStyle: { textAlign: 'left' }
          },
          {
            field: 'sFileSize',
            headerName: this.$t('grid.others.size'),
            width: 100,
            cellStyle: { textAlign: 'right' },
            valueFormatter: this.formatFileSize
          },
          {
            field: 'vCreatorName',
            headerName: this.$t('grid.title.createdBy'),
            width: 100,
            cellStyle: { textAlign: 'left' }
          },
          {
            field: 'sCreateTime',
            headerName: this.$t('grid.others.creationDate'),
            width: 150,
            cellStyle: { textAlign: 'left' },
            valueFormatter: this.formatDateTime
          },
          {
            field: 'sExtend1',
            headerName: '标注',
            width: 120,
            cellStyle: { textAlign: 'left' },
            valueGetter: params => getCnDitc(params, this.selectOps['esc.files.define'], 'sExtend1')
          },
          // 根据showRemark属性决定是否显示备注列
          ...(this.remarkConfig.showRemark ? [{
            field: 'sRemark',
            headerName: '备注',
            width: 150,
            cellStyle: { textAlign: 'left' },
            editable: () => {
              return this.remarkConfig.isEditable
            }
          }] : []),
          {
            headerName: this.$t('grid.others.operation'),
            width: 100,
            cellRenderer: 'TableBtn'
          }
        ]
      }
    },

    async initSelectOptions() {
      try {
        const result = await getDictet(['esc.files.define'])
        this.selectOps['esc.files.define'] = result.data[0].dicts
      } catch (error) {
        console.error('初始化选择项失败:', error)
      }
    },

    resetComponent() {
      this.$refs.upload?.clearFiles()
      this.curFileList = []
      this.curFileListRight = []
      this.rowData = []
      this.selectedRightDataNodes = []
    },

    loadTableData() {
      this.$refs.steelTradeAggrid?.loadTableData()
    },

    // 格式化相关方法
    formatFileSize(params) {
      const limit = params.value || 0
      const units = ['B', 'KB', 'MB', 'GB']
      let size = limit
      let unitIndex = 0

      while (size >= 1024 && unitIndex < units.length - 1) {
        size /= 1024
        unitIndex++
      }

      const formattedSize = size.toFixed(2)
      const [integer, decimal] = formattedSize.split('.')

      return decimal === '00' ? integer + units[unitIndex] : formattedSize + units[unitIndex]
    },

    formatDateTime(params) {
      return params.value ? params.value.substring(0, params.value.length - 3) : ''
    },

    formatUrl(params = {}, url) {
      let urlStr = ''
      if (url.indexOf('{') > -1) {
        const urlArr = url.split('/')
        urlArr.forEach((item, index) => {
          if (index !== 0) {
            let temp
            if (item.indexOf('{') > -1) {
              temp = `/${params[item.substring(1, item.length - 1)]}`
            } else {
              temp = `/${item}`
            }
            urlStr += temp
          }
        })
      } else {
        urlStr = url
      }
      return urlStr
    },

    // 权限相关方法
    shouldShowRightUpload(item) {
      if (typeof item.showBtn === 'boolean') return item.showBtn
      if (typeof item.showBtn === 'string' && item.showBtn !== '') {
        return this.authenticationList.includes(item.showBtn)
      }
      return true
    },

    getRightUploadDisabled(item) {
      return typeof item.disabled === 'boolean' ? item.disabled : this.disabledBtnComputed.scan
    },

    getRightUploadMultiple(item) {
      return typeof item.multiple === 'boolean' ? item.multiple : true
    },

    // 文件处理相关方法
    validateFile(file, allowedFileTypes) {
      // 文件类型验证
      if (allowedFileTypes?.length > 0) {
        const fileExtension = file.name.split('.').pop().toLowerCase()
        const isValidType = allowedFileTypes.some(ext => ext.toLowerCase() === fileExtension)

        if (!isValidType) {
          const allowedTypesMessage = allowedFileTypes.join('、')
          this.$message.error(`抱歉，请上传 ${allowedTypesMessage} 格式文件`)
          return false
        }
      }

      // 文件大小验证
      if (this.size && file.size > this.size * 1024 * 1024) {
        this.$message.error(`抱歉，请上传小于 ${this.size} MB的文件`)
        return false
      }

      return true
    },
    // validatedFiles的作用是避免validateFile函数重复调用
    handleFilesChange(file, fileList, allowedFileTypes, curFileListKey, uploadMethod) {
      const isVaild = this.validateFile(file, allowedFileTypes)
      if (!isVaild) {
        this[curFileListKey] = fileList.filter(f => {
          if (this.validatedFiles.has(f.uid)) return true // 已验证过的直接保留
          const valid = f.uid === file.uid ? isVaild : this.validateFile(f, allowedFileTypes)
          if (valid) this.validatedFiles.add(f.uid)
          return valid
        })
        return
      }

      this[curFileListKey] = fileList
      this.debounceUpload(uploadMethod)
    },

    debounceUpload(uploadMethod) {
      clearTimeout(this.uploadTimer)
      this.uploadTimer = setTimeout(uploadMethod, DRAG_DELAY)
    },

    filesChange(file, fileList) {
      this.handleFilesChange(
        file,
        fileList,
        this.allowedFileTypes,
        'curFileList',
        this.uploadFiles
      )
    },

    filesChangeRight(file, fileList, item) {
      this.handleFilesChange(
        file,
        fileList,
        item.allowedFileTypes,
        'curFileListRight',
        () => this.uploadFilesRight(item)
      )
    },

    fileRemove(file, fileList) {
      this.curFileList = fileList
    },

    fileRemoveRight(file, fileList) {
      this.curFileListRight = fileList
    },

    // 拖拽相关方法
    handleFileDrop(event) {
      if (this.disabledBtnComputed.scan) return

      const dt = event.dataTransfer
      const isFromAgGrid = event.target.closest('.ag-row') !== null

      if (!dt.types.includes('Files') || isFromAgGrid) return

      const files = Array.from(dt.files)
      if (files.length === 0) return

      this.curFileList.push(...files.map(file => ({ raw: file })))
      this.isDragging = false
      this.uploadFiles()
    },

    handleDragOver(event) {
      if (this.shouldAllowDrop(event)) {
        event.dataTransfer.dropEffect = 'copy'
      }
    },

    handleDragEnter(event) {
      if (this.shouldAllowDrop(event)) {
        this.isDragging = true
      }
    },

    handleDragLeave(event) {
      if (this.shouldAllowDrop(event)) {
        const rect = event.currentTarget.getBoundingClientRect()
        const { clientX: x, clientY: y } = event

        if (x <= rect.left || x >= rect.right || y <= rect.top || y >= rect.bottom) {
          this.isDragging = false
        }
      }
    },

    shouldAllowDrop(event) {
      return event.dataTransfer.types.includes('Files') && !event.target.closest('.ag-row')
    },

    // 上传相关方法
    createUploadFormData(files, additionalOptions = {}) {
      const formData = new FormData()
      formData.append('bizId', this.bizId)
      formData.append('sExtend3', this.sExtend3)

      // 添加上传选项
      Object.entries({ ...this.uploadOption, ...additionalOptions }).forEach(([key, value]) => {
        formData.append(key, value)
      })

      // 添加文件
      files.forEach(file => {
        formData.append('files', file.raw)
      })

      return formData
    },

    async uploadFiles() {
      if (this.curFileList.length === 0) return
      if (this.limitLen !== '' && Number(this.limitLen) > 0) {
        const disabledDate = this.rowData.length >= Number(this.limitLen)
        if (disabledDate || (this.curFileList.length > Number(this.limitLen))) {
          this.curFileList = []
          MessageUtil.warning('只允许上传' + this.limitLen + '份附件！')
          return
        }
      }

      try {
        const formData = this.createUploadFormData(this.curFileList)
        await postList(formData, this.api)

        this.curFileList = []
        this.$refs.upload.clearFiles()

        this.$nextTick(() => {
          this.loadTableData()

          MessageUtil.success(this.$t('grid.others.uploadedSuccessfully'))
        })
      } catch (error) {
        this.curFileList = []
        console.error('上传错误：', error)
      }
    },

    async uploadFilesRight(item) {
      if (this.curFileListRight.length === 0) return

      try {
        const formData = this.createUploadFormData(this.curFileListRight, item.option)
        const api = item.api || this.api
        const result = await postList(formData, api)

        if (result.code === '0000') {
          this.curFileListRight = []
          this.loadTableData()

          this.$nextTick(() => {
            MessageUtil.success(this.$t('grid.others.uploadedSuccessfully'))
          })
        }
      } catch (error) {
        await this.handleUploadError(error, item)
      }
    },

    async handleUploadError(error, item) {
      if (error.message && JSON.stringify(item).includes('isOverwrite')) {
        this.$message.closeAll()

        try {
          await this.$confirm(error.message, this.$t('grid.others.prompt'), {
            confirmButtonText: this.$t('btns.confirm'),
            cancelButtonText: this.$t('btns.cancel'),
            type: 'warning'
          })

          const formData = this.createUploadFormData(this.curFileListRight, {
            ...item.option,
            isOverwrite: '1'
          })

          await postList(formData, item.api || this.api)
          this.curFileListRight = []
          this.$refs.steelTradeAggrid?.refreshTable()

          this.$nextTick(() => {
            MessageUtil.success(this.$t('grid.others.uploadedSuccessfully'))
          })
        } catch (confirmError) {
          this.curFileListRight = []
        }
      } else {
        this.curFileListRight = []
        console.error('上传错误：', error)
      }
    },

    // 下载相关方法
    batchUploadAll() {
      if (!this.hasSelectedFiles) return

      const downloadPromises = this.selectedRightDataNodes.map(item =>
        this.downloadFile(item.sId, item.sName)
      )

      Promise.all(downloadPromises).then(() => {
        this.$nextTick(() => {
          MessageUtil.success(this.$t('tips.downloadSuccessful'))
        })
      }).catch(error => {
        console.error('批量下载失败:', error)
      })
    },

    async downloadFile(sId, sName) {
      try {
        const response = await axios.post(
          `${process.env.VUE_APP_BASE_API}/annex/oss/download/${sId}`,
          {},
          {
            responseType: 'blob',
            headers: { Authorization: getToken() }
          }
        )

        this.createDownloadLink(response.data, sName)
      } catch (error) {
        console.error('下载文件失败:', error)
        throw error
      }
    },

    createDownloadLink(blob, fileName) {
      const url = URL.createObjectURL(new Blob([blob]))
      const link = document.createElement('a')

      link.download = fileName
      link.href = url
      link.style.display = 'none'

      document.body.appendChild(link)
      link.click()

      URL.revokeObjectURL(url)
      document.body.removeChild(link)
    },

    // 删除相关方法
    async batchDelete() {
      if (!this.hasSelectedFiles) {
        MessageUtil.warning(this.$t('grid.others.pleaseSelectTheFileToBeDeleted'))
        return
      }

      try {
        await this.$confirm(
          this.$t('grid.tips.isTheDeletionConfirmed'),
          this.$t('grid.others.prompt'),
          {
            confirmButtonText: this.$t('btns.confirmKey'),
            cancelButtonText: this.$t('btns.cancel'),
            type: 'warning'
          }
        )

        const delIds = this.selectedRightDataNodes.map(item => item.sId)
        await delList(delIds, '/annex/oss/removes')

        this.loadTableData()
        this.selectedRightDataNodes = []

        this.$nextTick(() => {
          MessageUtil.success(this.$t('tips.deletedSuccessfully'))
        })
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除错误：', error)
        }
      }
    },

    // 表格相关方法
    selectedChange(selectedData) {
      this.selectedRightDataNodes = selectedData.filter((item) => item._selected) || []
    },

    handleRowDataChange(newVal) {
      if (!newVal?.length) return

      const hasUndefinedFields = newVal.some(item =>
        item._selected === undefined || item._selectedKeys === undefined
      )

      if (hasUndefinedFields) {
        const updatedData = newVal.map(item => ({
          ...item,
          _selected: item._selected ?? false,
          _selectedKeys: item._selectedKeys ?? []
        }))

        this.$nextTick(() => {
          this.rowData = updatedData
        })
      }
    },

    onCellValueChanged(event) {
      try {
        if (event?.colDef?.field === 'sRemark') {
          this.updateRemark(event)
        }
      } catch (error) {
        console.error('处理单元格值改变事件出错:', error)
      }
    },

    onRowDoubleClicked(event) {
      if (event._isEditCell) {
        return
      }
      if (event?.data?.url) {
        window.open(event.data.url)
      }
    },

    async updateRemark(params) {
      try {
        if (!params?.data) {
          console.error('updateRemark: 参数无效', params)
          return
        }

        const { oldValue = '', newValue = '', data } = params

        if (oldValue === newValue) return

        const result = await putList(
          { ...data, sRemark: newValue },
          '/annex/oss/v2/modify'
        )
        if (result.code === '0000') {
          MessageUtil.success('备注更新成功')
        }
      } catch (error) {
        console.error('备注更新失败:', error)
      } finally {
        this.loadTableData()
      }
    },

    // 数据加载方法
    async loadData(pagination) {
      try {
        const params = {
          pageNo: pagination?.page ?? DEFAULT_PAGE_NO,
          pageSize: pagination?.limit ?? DEFAULT_PAGE_SIZE,
          sBusId: this.bizId,
          ...this.option
        }

        const result = await postList(params, this.formatUrl(params, this.curConfig.url))

        if (!result?.data) {
          console.error('返回数据结构异常:', result)
          this.rowData = []
          return { content: [], totalElements: 0 }
        }

        const content = result.data.content || []
        const processedData = content.map(item => ({
          ...item,
          _selected: false,
          _selectedKeys: []
        }))

        this.rowData = processedData

        return {
          content: processedData,
          totalElements: result.data.totalElements || 0
        }
      } catch (error) {
        console.error('附件列表查询错误:', error)
        this.rowData = []
        throw error
      }
    },

    // 限制处理方法
    handleExceed(files, fileList) {
      const totalCount = files.length + fileList.length
      MessageUtil.warning(
        `当前限制选择 ${FILE_UPLOAD_LIMIT} 个文件，本次选择了 ${files.length} 个文件，共选择了 ${totalCount} 个文件`
      )
    },

    handleExceedRight(files, fileList) {
      this.handleExceed(files, fileList)
    },

    // 关闭处理方法
    handleClose(value = []) {
      this.$emit('onSelect', value)
    }
  }
}
</script>

<style lang="scss" scoped>
.file-drop-zone {
  position: relative;

  &.drag-area::after {
    content: '将文件拖放到此处';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.1);
    border: 2px dashed #409eff;
    pointer-events: none;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: #409eff;
    opacity: 0;
    transition: opacity 0.3s;
    z-index: 1;
  }

  &.drag-area.dragging::after {
    opacity: 1;
  }
}

:deep(.ag-row-drag) {
  cursor: move;
}

.steel-trade-aggrid {
  height: 100%;
}
</style>
