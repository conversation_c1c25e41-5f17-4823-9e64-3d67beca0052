import store from '@/store'
//
// 按钮权限
const has = {
  inserted: function(el, binding) {
    if (!window.__POWERED_BY_QIANKUN__) return
    control(el, binding)
  },
  update: function(el, binding) {
    let otherCondition = binding.value
    if (typeof otherCondition !== 'boolean' && !otherCondition) otherCondition = true
    const displayStatus = otherCondition ? 'inline-block' : 'none'
    el.style.display = displayStatus
  }
}

// 按钮权限控制
const control = function(el, binding) {
  let otherCondition = binding.value
  if (typeof otherCondition !== 'boolean' && !otherCondition) otherCondition = true
  // console.log('otherCondition--', otherCondition)
  if (otherCondition) {
    const value = binding.arg
    // console.log('当前页按钮权限-指令-', store.getters.curBtnAuthList)
    // console.log('value-指令-', value)
    const authenticationList = store.getters.curBtnAuthList
    // 当前页授权按钮，为空则默认都无权限
    if (!authenticationList || !authenticationList.length || authenticationList.length === 0) {
      el.remove()
    }
    // 若传入按钮key为空，则展示
    // if (!value || !value.length) {
    //   return true
    // }

    if (value && value instanceof Array && value.length > 0) {
      !value.every(item => authenticationList.includes(item)) && el.remove()
    } else if (typeof value === 'string') {
      // console.log('按钮权限控制', value, !authenticationList.includes(value))
      authenticationList.map(item => {
        if (item === value) {
          // console.log('按钮权限控制1', value)
        }
      })
      !authenticationList.includes(value) && el.remove()
      // authenticationList.indexOf(value) === -1 && el.remove()
    } else {
      // console.log('按钮权限控制2')
      el.remove()
    }
  } else if (typeof otherCondition === 'boolean') {
    el.style.display = 'none'
  }
}

export default {
  has
}
