// /////////////////////////////////////////////公共窗口组件///////////////////////////////////////////////////////////
export const ToggleDialogMixin = {
  props: {
    title: String,
    type: String,
    dialogVisible: Boolean,
    keyword: String
  },
  data() {
    return {
      infoData: {},
      infoList: [],
      refreshData: true,
      visible: this.dialogVisible
    }
  },
  watch: {
    keyword() {
      this.refreshData = true
    },
    dialogVisible(val) {
      this.visible = val
    },
    visible() {
      this.$nextTick(() => {
        if (this.$refs && this.$refs.autoFocusInput) {
          this.$refs.autoFocusInput.focus()
        }
      })
    }
  }
}

export const CndRoleDialogType = {
  TYPE_TRUSTER: 'TYPE_TRUSTER', // 选择委托方
  TYPE_COMPANY: 'TYPE_COMPANY', // 选择公司
  TYPE_DEPARTMENT: 'TYPE_DEPARTMENT', // 选择部门
  TYPE_MEMBER: 'TYPE_MEMBER', // 选择人员
  TYPE_CHECK_GROUP: 'TYPE_CHECK_GROUP', // 选择核算组
  TYPE_CLIENT: 'TYPE_CLIENT', // 选择客户
  TYPE_SUPPLIER: 'TYPE_SUPPLIER' // 选择供应商
}
