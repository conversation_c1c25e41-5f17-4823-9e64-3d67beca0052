<template>
  <div>
    <cnd-input-number v-model="value" v-limit="{value: value,max:maxVal,min:minVal,set:setValue}" :decimal-digit="decimalDigit" type="number" size="mini" placeholder="输入内容" clearable />
  </div>
</template>

<script>
export default {
  name: 'TableInput',
  directives: {
    limit: function(el, binding) {
      const data = binding.value
      if (data.min && +data.value < data.min) {
        binding.value.set(data.min)
      }
      if (data.max && +data.value > data.max) {
        binding.value.set(data.max)
      }
    }
  },
  data() {
    return {
      value: null,
      maxVal: null,
      minVal: null,
      decimalDigit: 4
    }
  },
  mounted() {
    this.$nextTick(() => {
      if (this.$refs.input) {
        this.$refs.input.focus()
      }
    })
  },
  beforeMount() {
    this.initFn()
  },
  methods: {
    initFn() {
      let maxVal = null; let minVal = null
      const { max, min, decimal } = this.params.colDef
      if (max) {
        if (typeof max === 'string') {
          maxVal = this.params.data[max]
        } else {
          maxVal = max
        }
      }
      if (min) {
        if (typeof min === 'string') {
          minVal = this.params.data[min]
        } else {
          minVal = min
        }
      }
      if (decimal || decimal === 0) {
        this.decimalDigit = decimal
      }
      this.minVal = minVal
      this.maxVal = maxVal
      this.setValue(this.params.value)
    },
    setValue(value) {
      this.value = value
    },
    getValue() {
      return +this.value
    }
  }
}
</script>

<style scoped></style>
