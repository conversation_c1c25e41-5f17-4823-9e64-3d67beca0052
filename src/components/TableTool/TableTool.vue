<!--
 * @Description: 表格工具
-->
<template>
  <div class="table-tool" :style="height ? { height: ~~height + (pagination ? 40: 0)+ 'px' } : {}">
    <!-- 表头统计 -->
    <div
      v-if="$slots.tableHeadCount"
      class="default-box"
    >
      <slot name="tableHeadCount" />
    </div>
    <!-- 表格 -->
    <ag-grid-vue
      ref="agGrid"
      class="ag-theme-balham grid-class"
      :style="height ? { height: height + 'px' } : {}"
      :grid-options="gridOptions"
      :column-defs="_columnDefs"
      :row-data="rowData"
      :framework-components="frameworkComponents"
      :default-col-def="defaultColDef"
      :modules="AllModules"
      @grid-ready="onGridReady"
    />
    <div
      v-if="hasPages"
      class="default-box flexC table-tool-bottom"
    >
      <div v-if="$slots.tableFooterCount">
        <!-- 底部统计 -->
        <slot name="tableFooterCount" />
      </div>
      <div class="grid-class no-padding">
        <!-- 分页器 -->
        <cnd-pagination
          v-if="pagination"
          :total="pagination.total"
          :page="pagination.pageNo"
          :limit="pagination.pageSize"
          :event="pageChange"
        />
      </div>
    </div>
  </div>
</template>

<script>
import TableTool from './TableTool'

export default TableTool
</script>
<style lang="scss" scoped>
.table-tool {
  display: flex;
  height: 100%;
  flex-direction: column;
}
.ag-theme-balham .ag-ltr .ag-cell {
  background: #fff;
}
.ag-root-wrapper-body.ag-layout-normal {
  min-height: 100px !important;
}
.table-tool-bottom {
  padding: 0 10px;
  background: #fff;
  border: {
    top: 0px;
  }
}
</style>
