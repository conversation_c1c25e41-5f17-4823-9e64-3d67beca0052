/*
 * @Description: js
 */
import { AgGridVue } from '@ag-grid-community/vue'
import { AllModules } from 'cndinfo-ui/src/utils/agGridModules'
import { AG_GRID_LOCALE_CN } from 'cndinfo-ui/lib/lang/ag-grid.zh.js'

import { dialogEditor, dialogRenderer } from './Dialog/index'
import { inputEditor } from './Input/index'
import { handleCellWidth } from '../steelTradeAggrid/fieldDict.js'

const RELATION_KEY = '__relationFiled_'
export default {
  name: 'TableTool',
  components: { AgGridVue },
  props: {
    columnDefs: { // 主表表头
      type: Array,
      default: () => []
    },
    resListKey: { // 主表数据key
      type: String,
      default: 'content'
    },
    resPagetionsKey: { // 响应数据的pagetions信息所在层
      type: String,
      default: 'content'
    },
    childColumnDefs: { // 细节表头
      type: Array,
      default: () => []
    },
    childListKey: { // 细节数据key
      type: String,
      default: 'content'
    },
    defRowData: { // 表格默认数据
      type: Array,
      default: null
    },
    hasPages: { // 展示分页
      type: Boolean,
      default: true
    },
    isList: { // 是列表
      type: Boolean,
      default: false
    },
    masterDetail: { // 主行
      type: Boolean,
      default: false
    },
    isRowMaster: { // 是否允许扩展
      type: Function,
      default: null
    },
    req: { // 接口请求
      type: Function,
      default: null
    },
    paramsIdKey: { // id主键
      type: String,
      default: ''
    },
    id: { // 搜索id
      type: String,
      default: ''
    },
    height: { // 表格高度
      type: [String, Number],
      default: ''
    },
    isRowCount: { // 添加统计行
      type: Boolean,
      default: false
    },
    rowInfo: { // 统计行信息
      type: Object,
      default: null
    },
    editType: { // 可编辑: fullRow 整行编辑
      type: String,
      default: ''
    },
    excelStyles: { // 导出excel样式
      type: Object,
      default: null
    },
    isRowSelectable: { // 过滤选择
      type: Function,
      default: null
    },
    resCB: { // 请求成功回调
      type: Function,
      default: null
    },
    onRowSelected: { // 选中回调
      type: Function,
      default: null
    },
    onChildRowSelected: { // 选中回调
      type: Function,
      default: null
    },
    onRowDoubleClicked: { // 双击回调
      type: Function,
      default: null
    },
    onRowGroupOpened: { // 展开收起事件
      type: Function,
      default: null
    },
    isDetailLazy: { // 子表是否懒加载
      type: Boolean,
      default: false
    },
    otherParams: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      AllModules,
      gridApi: null,
      columnApi: null,
      gridOptions: null,
      _columnDefs: null,
      rowData: null,
      frameworkComponents: null,
      defaultColDef: null,
      pagination: {
        total: 0,
        pageNo: 0,
        pageSize: 20
      },
      searchInfo: {},
      detail: null,
      isExportSelRows: false,
      resData: null,
      isChildEvent: false,
      isParentEvent: false,
      selChilRow: null,
      detailLazyRow: []
    }
  },
  beforeMount() {
    this.initTableConfig()
  },
  methods: {
    initTableConfig() {
      let options = this.initBasicConfig()
      if (this.masterDetail) {
        options = {
          ...options,
          ...this.initDetailConfig()
        }
      }
      this.gridOptions = options
      const defs = this.columnDefs
      defs.map(item => {
        const cellName = `${item.headerName}`
        if (item.width && !cellName) {
          return item
        }
        const cellWidth = handleCellWidth(cellName)
        if (cellName && cellName !== 'checkbox') {
          // console.log('cellWidth', cellName, cellWidth)
        }

        if (cellWidth) {
          item.width = cellWidth
        }
      })
      this._columnDefs = defs
      if (this.defRowData) {
        this.rowData = this.defRowData
      } else {
        this.rowData = []
      }
      if (this.editType) {
        this.frameworkComponents = {
          dialogEditor: dialogEditor,
          dialogRenderer: dialogRenderer,
          inputEditor: inputEditor
        }
      }
      this.defaultColDef = {
        // flex: 1,
        minWidth: 40,
        resizable: true,
        editable: this.editType !== '' // 是否可编辑
      }
    },
    initBasicConfig() {
      const _this = this
      const options = {
        localeText: AG_GRID_LOCALE_CN,
        rowDeselection: true,
        suppressRowClickSelection: false,
        groupSelectsChildren: false,
        // *** 多选 start ***/
        rowMultiSelectWithClick: true,
        rowSelection: 'multiple',
        // *** 多选 end ***/
        onRowSelected(e) {
          _this.masterDetail && _this.onParentSelectRow(e, _this)
          _this.onRowSelected && _this.onRowSelected(e)
        },
        onRowDoubleClicked(e) {
          _this.onRowDoubleClicked && _this.onRowDoubleClicked(e)
        },
        // 给定某个条件判断是否可选
        isRowSelectable(rowNode) {
          if (_this.isRowSelectable) {
            return _this.isRowSelectable(rowNode)
          }
          return true
        }
      }
      if (this.editType === 'fullRow') {
        options['editType'] = 'fullRow'
      }
      if (this.excelStyles) {
        options['ExcelStyles'] = this.excelStyles
      }
      return options
    },
    initDetailConfig() {
      const _this = this
      const defs = this.childColumnDefs
      defs.map(item => {
        const cellName = `${item.headerName}`
        if (item.width && !cellName) {
          return item
        }
        const cellWidth = handleCellWidth(cellName)
        if (cellName && cellName !== 'checkbox') {
          // console.log('cellWidth', cellName, cellWidth)
        }

        if (cellWidth) {
          item.width = cellWidth
        }
      })
      const options = {
        masterDetail: true,
        keepDetailRows: true,
        groupSelectsChildren: false,
        isRowMaster(dataItem) {
          if (_this.isRowMaster) {
            const data = _this.getNodeData(dataItem)
            return _this.isRowMaster(data, _this)
          } else {
            return true
          }
        },
        detailCellRendererParams: {
          detailGridOptions: {
            columnDefs: defs,
            defaultColDef: {
              flex: 1,
              minWidth: 40,
              resizable: true,
              editable: this.editType !== '' // 是否可编辑
            },
            rowSelection: 'multiple',
            rowMultiSelectWithClick: true,
            onRowSelected(e) {
              if (!_this.isParentEvent) {
                _this.selChilRow = e.node
              }
              _this.onChildRowSelected && _this.onChildRowSelected(e)
            },
            onSelectionChanged(e) {
              if (!_this.isParentEvent) {
                const model = e.api.getModel()
                const count = model.rootNode.allChildrenCount
                const selRowCount = e.api.getSelectedRows().length
                _this.setParentSelectNodeSta(e, selRowCount, count, _this)
              } else {
                _this.isParentEvent = false
              }
            }
          },
          getDetailRowData(params) {
            const data = _this.getNodeData(params.data)
            params.successCallback(data)
          }
        },
        defaultExportParams: {
          getCustomContentBelowRow(params) {
            const header = []
            const keys = []
            _this.childColumnDefs.map(item => {
              if (item.field === 'checkbox') {
                header.push(_this.cell(''))
              } else {
                header.push(_this.cell(item.headerName, 'header'))
              }
              keys.push(item.field)
            })
            let exportData = _this.getNodeData(params.node.data)
            if (_this.isExportSelRows) {
              const detailNode = params.node.detailNode
              exportData = detailNode ? detailNode.detailGridInfo.api.getSelectedRows() : []
            }
            const data = exportData.map((record) => {
              const arr = []
              keys.map(k => {
                if (k === 'checkbox') {
                  arr.push(_this.cell(''))
                } else {
                  arr.push(_this.cell(record[k], 'body'))
                }
              })
              return arr
            })
            return [header].concat(data, [[]])
          }
        },
        onRowGroupOpened(params) {
          const node = params.node
          const selected = node.selected
          if (node.expanded) {
            const detailNode = node.detailNode
            const detailApi = detailNode.detailGridInfo.api
            if (selected) {
              detailApi.selectAll()
            }
          }
          if (_this.onRowGroupOpened) {
            if (_this.isDetailLazy) {
              const nodeRelationKey = node.data[RELATION_KEY]
              if (_this.detailLazyRow.indexOf(nodeRelationKey) === -1) {
                _this.onRowGroupOpened(params, (data) => {
                  if (node.expanded && data) {
                    const detailNode = node.detailNode
                    const detailApi = detailNode.detailGridInfo.api
                    data.map(item => {
                      item[RELATION_KEY] = nodeRelationKey
                    })
                    detailApi.setRowData(data)
                    _this.detailLazyRow.push(nodeRelationKey)
                  }
                })
              }
            } else {
              _this.onRowGroupOpened(params)
            }
          }
        }
      }
      return options
    },
    cell(text, styleId) {
      return {
        styleId: styleId,
        data: {
          type: /^\d+$/.test(text) ? 'Number' : 'String',
          value: String(text)
        }
      }
    },
    onParentSelectRow(e, _this) {
      const detailNode = e.node.detailNode
      if (detailNode) {
        if (!_this.isChildEvent) {
          const detailApi = detailNode.detailGridInfo.api
          this.isParentEvent = true
          if (e.node.selected) {
            detailApi.selectAll()
          } else {
            detailApi.deselectAll()
          }
        } else {
          _this.isChildEvent = false
        }
      }
    },
    setParentSelectNodeSta(e, sCount, dCount, _this) {
      if (!this.selChilRow) {
        return
      }
      let id = ''
      let parantNode = null
      let isTrue = false
      _this.gridApi.forEachNode((node) => {
        if (!isTrue) {
          const detailNode = node.detailNode
          if (detailNode) {
            detailNode.detailGridInfo.api.forEachNode(dNode => {
              if (_this.selChilRow.data[RELATION_KEY] === dNode.data[RELATION_KEY]) {
                id = node.id
                parantNode = node
                isTrue = true
              }
            })
          }
        } else {
          return true
        }
      })
      this.setCBSty(parantNode, id, sCount, dCount, _this)
    },
    setCBSty(parantNode, id, sCount, dCount, _this) {
      if (parantNode) {
        const nodeDom = parantNode.gridApi.gridPanel.eCenterContainer.querySelector(`div[row-id='${id}']`)
        const checkSta = nodeDom.querySelector('.ag-selection-checkbox').children
        const hideClass = 'ag-hidden'
        const parantNodeSelected = parantNode.selected
        let isEvent = false
        let cStaClass = ''
        if (sCount === dCount) {
          isEvent = !(parantNodeSelected === true)
          parantNode.setSelected(true)
          cStaClass = 'ag-icon-checkbox-checked'
        }
        if (sCount === 0) {
          isEvent = !(parantNodeSelected === false)
          cStaClass = 'ag-icon-checkbox-unchecked'
          parantNode.setSelected(false)
        }
        if (sCount > 0 && sCount !== dCount) {
          isEvent = !(parantNodeSelected === false)
          cStaClass = 'ag-icon-checkbox-indeterminate'
          parantNode.setSelected(false)
        }
        this.isChildEvent = isEvent
        for (let i = 0; i < checkSta.length; i++) {
          let dClass = checkSta[i].getAttribute('class')
          dClass = dClass.replace(hideClass, '')
          if (dClass.indexOf(cStaClass) !== -1) {
            checkSta[i].setAttribute('class', `${dClass}`)
          } else {
            checkSta[i].setAttribute('class', `${dClass} ${hideClass}`)
          }
        }
        _this.selChilRow = null
      }
    },
    // 获取数据
    getNodeData(_data) {
      let data = _data
      if (data) {
        const keys = this.childListKey.split(':')
        keys.map(k => {
          data = data[k] || []
        })
      } else {
        data = []
      }
      return data
    },
    // 表格初始化
    onGridReady(e) {
      this.gridApi = e.api
      this.columnApi = e.columnApi
      this.gridApi.sizeColumnsToFit()
      if (this.req) {
        this.query()
      }
    },
    // 页面改变
    pageChange(obj) {
      this.pagination.pageNo = obj.page
      this.pagination.pageSize = obj.limit
      this.query()
    },
    /**
     * @description: 搜索
     * @param {*} isSearch 是否查询
     * @return {*}
     */
    query(isSearch) {
      if (isSearch) {
        this.pagination.pageNo = 0
      }
      if (this.isDetailLazy) {
        this.detailLazyRow = []
      }
      const pagination = this.pagination
      let params = {
        ...this.otherParams ? this.otherParams : {},
        ...pagination
      }
      // 是否是列表页
      if (this.isList) {
        params = {
          ...params,
          ...this.searchInfo
        }
      } else {
        if (this.paramsIdKey) {
          params[this.paramsIdKey] = this.id
        }
      }
      this.send(params)
    },
    /**
     * @description: 发送请求
     * @param {*} params
     * @return {*}
     */
    send(params) {
      this.req(params).then((res) => {
        const keys = this.resListKey.split(':')
        let data = { ...res.data }
        this.resData = data
        keys.map(k => {
          data = data[k]
        })
        if (this.masterDetail && data && data.length > 0) {
          data.map((item, index) => {
            const str = `${RELATION_KEY}${index}_${new Date().getTime()}`
            item[RELATION_KEY] = str
            const keys = this.childListKey.split(':')
            let data = null
            keys.map(k => {
              item[k] = item[k] || []
              data = item[k]
            })
            data.map(ite => {
              ite[RELATION_KEY] = str
            })
          })
        }
        this.rowData = data
        const pKeys = this.resPagetionsKey.split(':')
        let pagetions = { ...res.data }
        pKeys.map(k => {
          pagetions = pagetions[k]
        })
        this.pagination.total = res.data.totalElements
        this.isRowCount && this.addRowCount()
        this.resCB && this.resCB(res)
      })
    },
    // 底部添加统计行
    addRowCount() {
      const tableData = this.rowData
      if (this.rowInfo && tableData.length > 0) {
        let count = 0
        tableData.map((item) => {
          count += item[this.rowInfo.numKey] || 0
        })
        this.gridApi.setPinnedBottomRowData([
          {
            [this.rowInfo.numKey]: count,
            [this.rowInfo.labelKey]: this.rowInfo.labelTxt,
            isRowCount: true
          }
        ])
      }
    },
    // 获取选中行数据
    getSelectedRows() {
      return this.gridApi.getSelectedRows()
    },
    /**
     * @description: 插入行
     * @param {*} form 行数据
     * @return {*} void
     */
    addEditRow(form) {
      this.rowData.push(form)
    },
    // 获取所有数据
    getRowData() {
      return this.$refs.agGrid.getRowData()
    },
    /**
     * @description: 搜索条件
     * @param {*} form 搜索条件表单
     * @return {*} void
     */
    setSearchInfo(form) {
      this.searchInfo = form
      this.query(true)
    },
    /**
     * @description: 导出数据
     * @param {*} onlySelected 只导出选择行
     * @return {*} void
     */
    exportSelData(obj) {
      const isTrue = obj.onlySelected || false
      this.isExportSelRows = isTrue
      this.gridApi.exportDataAsExcel({
        fileName: obj.fileName || 'export',
        onlySelected: isTrue,
        columnGroups: true
      })
    },
    resetRowData(rowData) {
      if (rowData) {
        this.rowData = rowData
      }
    }
  }
}
