<!--
 * @Description: 文本
-->
<template>
  <div>
    <cnd-input-number v-if="inputAttr.type === 'money'" ref="inputNum" v-model="val" :disabled="inputAttr.disabled" :placeholder="placeholder" @blur="handleBlur" />
    <el-input v-else ref="input" v-model="val" :type="inputAttr.type" :disabled="inputAttr.disabled" :placeholder="placeholder" @blur="handleBlur" />
  </div>
</template>

<script>
export default {
  name: 'InputEditor',
  data() {
    return {
      val: '',
      editType: '',
      inputAttr: {
        disabled: false,
        type: 'text',
        placeholder: this.$t('components.pleaseEnter')
      }
    }
  },
  created() {
    const meta = this.params.colDef.meta
    if (meta) {
      if (meta.type) {
        this.inputAttr.type = meta.type
      }
      if (meta.disabled) {
        this.inputAttr.disabled = meta.disabled
      }
      if (meta.placeholder) {
        this.inputAttr.placeholder = meta.placeholder
      }
    }
    this.editType = this.params.api.gridOptionsWrapper.gridOptions.editType
    if (this.editType !== 'fullRow') {
      this.$nextTick(() => {
        if (this.inputAttr.type === 'money') {
          this.$refs.inputNum.focus()
        } else {
          this.$refs.input.focus()
        }
      })
    }
    this.initData()
  },
  methods: {
    initData() {
      this.val = this.params.value
    },
    getValue() {
      return this.val
    },
    handleBlur() {
      this.editType !== 'fullRow' && this.params.api.stopEditing()
    }
  }
}
</script>

