<!--
 * @Description: 弹出层
-->
<template>
  <div>
    <cnd-input-dialog v-model="obj.name" :placeholder="$t('components.pleaseSelect')" @focus="handleFocus" />
    <!-- 人员弹出层 -->
    <horizon-staff-dialog v-if="dialog === 'staff'" title="人员查询" :append-to-body="true" org-type="100" :visible="dialogVisible.staff" @onSelect="onCloseDialog($event, 'staff')" />
    <!-- 核算组弹出层 -->
    <horizon-cost-dialog v-if="dialog === 'cost'" title="核算组查询" :append-to-body="true" :visible="dialogVisible.cost" @onSelect="onCloseDialog($event, 'cost')" />
    <!-- 商客 -->
    <horizon-customer-dialog v-if="dialog === 'customer'" title="商客查询" :append-to-body="true" :visible="dialogVisible.customer" @onSelect="onCloseDialog($event, 'customer')" />
    <!-- 公司弹出层 -->
    <horizon-company-dialog v-if="dialog === 'company'" title="公司查询" :append-to-body="true" :visible="dialogVisible.company" @onSelect="onCloseDialog($event, 'company')" />
    <!-- 部门弹出层 -->
    <horizon-depart-dialog v-if="dialog === 'depart'" title="部门查询" :append-to-body="true" :visible="dialogVisible.depart" @onSelect="onCloseDialog($event, 'depart')" />
    <!-- 财务组织弹出层 -->
    <horizon-finance-dialog v-if="dialog === 'finance'" title="财务组织查询" :append-to-body="true" :visible="dialogVisible.finance" @onSelect="onCloseDialog($event, 'finance')" />
    <!-- 仓库弹出层 -->
    <horizon-warehouse-dialog v-if="dialog === 'warehouse'" title="仓库查询" :append-to-body="true" :visible="dialogVisible.warehouse" @onSelect="onCloseDialog($event, 'warehouse')" />
    <!-- 项目列表弹出层 -->
    <cnd-dialog-table v-if="dialog === 'project'" :append-to-body="true" title="项目选择" :show-search="true" :visible="dialogVisible.project" :column-defs="projectColum" :req-config="reqProject" :show-pagination="false" @onSelect="onCloseDialog($event, 'project')" />
  </div>
</template>

<script>
export default {
  name: 'DialogEditor',
  data() {
    return {
      dialogVisible: {
        staff: false,
        cost: false,
        customer: false,
        company: false,
        depart: false,
        finance: false,
        project: false
      },
      obj: {
        id: '',
        name: ''
      },
      dialog: '',
      projectColum: [
        { field: 'sName', headerName: '项目名称', width: 100 },
        { field: 'sCode', headerName: this.$t('grid.others.itemNumberTag'), width: 100 }
      ],
      reqProject: {
        method: 'post',
        url: '/prj/prjproject/dialog/page/{pageNo}/{pageSize}',
        keyword: 'sName',
        params: {
          pageNo: 0,
          pageSize: 20
        },
        dataFormat: {
          key: 'content',
          total: 'totalElements'
        }
      }
    }
  },
  created() {
    this.dialog = this.params.colDef.meta.dialog
    this.initData()
  },
  methods: {
    initData() {
      this.obj.name = this.params.value
      this.obj.id = this.params.node.data[
        this.params.colDef.meta.relationIdFiled
      ]
    },
    getValue() {
      this.params.node.setDataValue(
        this.params.colDef.meta.relationIdFiled,
        this.obj.id
      )
      return this.obj.name
    },
    handleFocus() {
      this.dialogVisible[this.dialog] = true
    },
    /**
     * @description: 关闭弹窗
     * @param {*} e
     * @param {*} dioKey 弹窗key
     */
    onCloseDialog(e, type) {
      const data = e && e[0]
      this.dialogVisible[this.dialog] = false
      if (data) {
        this.setSelId(type, data)
      }
      const editType = this.params.api.gridOptionsWrapper.gridOptions.editType
      editType !== 'fullRow' && this.params.api.stopEditing()
    },
    // 设置选择的id
    setSelId(type, data) {
      let selIdKey = ''
      let _data = data
      if (type === 'customer' || type === 'warehouse' || type === 'project') {
        selIdKey = 'sId'
      } else if (type === 'company' || type === 'cost') {
        selIdKey = 'id'
      } else if (type === 'staff') {
        _data = _data.children[0]
        selIdKey = 'id'
      }
      this.setSelIdForName(selIdKey, type, _data)
    },
    // 设置选择id对应展示的文本
    setSelIdForName(selIdKey, type, data) {
      let nameKey = ''
      if (type === 'customer' || type === 'warehouse') {
        nameKey = 'sName'
      } else if (type === 'project') {
        nameKey = 'sCode'
      } else {
        nameKey = 'name'
      }
      this.obj = {
        id: data[selIdKey],
        name: data[nameKey]
      }
    }
  }
}
</script>
