<!--
 * @Description: 渲染组件
-->
<template>
  <div>
    {{ val }}
  </div>
</template>

<script>
export default {
  name: 'Dialog<PERSON><PERSON><PERSON>',
  data() {
    return {
      val: ''
    }
  },
  created() {
    this.initData()
  },
  methods: {
    initData() {
      this.val = this.params.value
    },
    setName(params) {
      const val = params.data[params.colDef.field]
      this.val = val
    },
    refresh(params) {
      this.params = params
      this.setName(params)
    }
  }
}
</script>
