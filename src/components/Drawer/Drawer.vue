<!--
 * @Description: 抽屉
-->
<template>
  <div>
    <div
      class="drawer-btn"
      @click="drawer = true"
    >
      <div
        v-for="(v, index) in label"
        :key="index"
      >{{ v }}</div>
      <div class="last-icon"><span class="drawer-btn-icon"><i class="el-icon-arrow-left" /></span></div>
    </div>
    <el-drawer
      class="drawer-main"
      :append-to-body="appendToBody"
      :show-close="showClose"
      :modal="modal"
      :with-header="withHeader"
      :title="title"
      :size="size"
      :visible.sync="drawer"
      :direction="direction"
    >
      <div
        class="drawer-main-btn"
        @click="drawer = false"
      >
        <div
          v-for="(v, index) in label"
          :key="index"
        >{{ v }}</div>
        <div class="last-icon"><span class="drawer-btn-icon"><i class="el-icon-arrow-right" /></span></div>
      </div>
      <div class="drawer-body">
        <slot name="drawerContent" />
      </div>
    </el-drawer>
  </div>
</template>

<script>
export default {
  name: 'Drawer',
  props: {
    label: {
      type: Array,
      default: () => []
    },
    appendToBody: {
      type: Boolean,
      default: true
    },
    showClose: {
      type: Boolean,
      default: false
    },
    modal: {
      type: Boolean,
      default: false
    },
    withHeader: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    },
    size: {
      type: [String, Number],
      default: '100%'
    },
    direction: {
      type: String,
      default: 'rtl'
    }
  },
  data() {
    return {
      drawer: false
    }
  }
}
</script>
<style lang="scss" scoped>
.drawer-btn {
  position: fixed;
  right: 0;
  bottom: 100px;
}
.drawer-main-btn {
  position: absolute;
  left: 0;
  bottom: 0;
}
.drawer-btn,
.drawer-main-btn {
  width: 34px;
  height: 270px;
  background: #409eff;
  color: #fff;
  padding-top: 20px;
  cursor: pointer;
  user-select: none;
  z-index: 10;
  border-radius: 5px 0 0 5px;
  div {
    text-align: center;
    padding-top: 5px;
    &.last-icon {
      position: absolute;
      bottom: 15px;
      left: 0;
      width: 100%;
    }
  }
}
.drawer-main {
  left: 20%;
  top: auto;
  height: 270px;
  bottom: 100px;
  border-radius: 5px 0 0 5px;
  box-shadow: 0 8px 10px -5px rgba(0, 0, 0, 0.2),
    0 16px 24px 2px rgba(0, 0, 0, 0.14), 0 6px 30px 5px rgba(0, 0, 0, 0.12);
}
.drawer-body {
  width: 100%;
  height: 100%;
  padding-left: 34px;
  overflow: hidden;
}
.drawer-btn-icon {
  width: 14px;
  line-height: 14px;
  background: #fff;
  color: #409eff;
  border-radius: 100%;
  text-align: center;
  display: inline-block;
}
</style>
