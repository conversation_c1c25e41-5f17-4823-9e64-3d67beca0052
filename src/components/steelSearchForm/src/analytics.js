/* eslint-disable */

import Analytics from '@cndinfo/cube-analytics-sdk'
const analytics = {
  getAnalyticsInstance(obj) {
    let params = {}
    try {
      if (obj.$router) {
        params = {
          routerObject: obj.$router
        }
      } else if (plus) {
        try {
          const uniPluginObject = uni.requireNativePlugin(
            'RangersAppLogUniPluginCN'
          )
          params = {
            uniPluginObject
          }
        } catch (e) {}
      }
    } catch (e) {}
    const analyticsInstance = new Analytics(params)
    analyticsInstance.start()
    return analyticsInstance
  }
}
export default analytics
