<template>
  <div>
    <el-input
      ref="input"
      :value="displayValue"
      :placeholder="placeholder"
      :disabled="inputNumberDisabled"
      :autofocus="autofocus"
      :size="inputNumberSize"
      :max="max"
      :min="min"
      :clearable="clearable"
      @blur="handleBlur"
      @focus="handleFocus"
      @input="handleInput"
      @change="handleInputChange"
      @@keyup.native="handleKeyup"
      @keyup.enter.native="handleKeydown"
    />
  </div>

</template>

<script>
import { Format } from 'cnd-horizon-utils'
import { Input as ElInput } from 'element-ui'
const TYPE_MAP = {
  PERCENT: 'percent',
  AMOUNT: 'amount',
  NUMBER: 'number'
}
const numExp = new RegExp(/^(\-|\+)?\d+(\.\d+)?$/)
export default {
  name: 'NumberValidator',
  components: {
    ElInput
  },
  inject: {
    elForm: {
      default: ''
    },
    elFormItem: {
      default: ''
    }
  },
  inheritAttrs: false,
  props: {
    max: {
      type: Number,
      default: Number.MAX_SAFE_INTEGER
    },
    min: {
      type: Number,
      default: Number.MIN_SAFE_INTEGER
    },
    disabled: Boolean,
    clearable: {
      type: Boolean,
      default: true
    },
    size: {
      type: String,
      default: 'mini'
    },
    placeholder: {
      type: String,
      default: ''
    },
    value: {
      type: [String, Number],
      default: ''
    },
    decimalDigit: {
      type: Number,
      default: void 0
    },
    // 输入的类型
    type: {
      type: String,
      default: 'number'
    },
    thousandthSign: {
      type: Boolean,
      default: false
    },
    // 是否自动聚焦
    autoFocus: {
      type: Boolean,
      default: false
    },
    // 聚焦是否选中内容
    focusSelect: {
      type: Boolean,
      default: false
    },
    isAllowedRange: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      currentValue: null,
      userInput: null,
      isFocus: false
    }
  },
  computed: {
    inputNumberSize() {
      return this.size
    },
    minDisabled() {
      return this._decrease(this.value, 1) < this.min
    },
    maxDisabled() {
      return this._increase(this.value, 1) > this.max
    },
    numPrecision() {
      const stepPrecision = this.getPrecision(1)
      if (this.decimalDigit !== void 0) {
        return this.decimalDigit
      } else {
        return Math.max(this.getPrecision(this.value), stepPrecision)
      }
    },
    inputNumberDisabled() {
      return this.disabled
    },
    displayValue() {
      if (this.userInput !== null) {
        return this.userInput
      }
      if (!this.currentValue) {
        return this.currentValue
      }
      return this.getDisplayValue(this.currentValue, this.isFocus)
    }
  },
  watch: {
    value: {
      immediate: true,
      handler(value) {
        if (this.currentValue !== value) {
          this.userInput = null
          this.currentValue = value
        }
      }
    }
  },
  mounted() {
    const innerInput = this.$refs.input.$refs.input
    innerInput.setAttribute('role', 'spinbutton')
    innerInput.setAttribute('aria-valuemax', this.max)
    innerInput.setAttribute('aria-valuemin', this.min)
    innerInput.setAttribute('aria-valuenow', this.currentValue)
    innerInput.setAttribute('aria-disabled', this.inputNumberDisabled)
    if (this.autoFocus) {
      this.$nextTick(() => {
        this.focus()
      })
    }
  },
  updated() {
    if (!this.$refs || !this.$refs.input) {
      return
    }
    const innerInput = this.$refs.input.$refs.input
    innerInput.setAttribute('aria-valuenow', this.currentValue)
  },
  methods: {
    getDisplayValue(val, isFocus) {
      let currentValue = val
      const decimalDigit = !this.decimalDigit ? 0 : this.decimalDigit
      if (!isFocus) {
        if (
          typeof currentValue === 'number' &&
          this.type !== TYPE_MAP.PERCENT
        ) {
          if (typeof this.decimalDigit === 'number') {
            currentValue = Format.keepDecimalPlaces(currentValue, decimalDigit)
          }
        }
        if (this.thousandthSign) {
          currentValue = Format.formatThousandthSign(currentValue, decimalDigit)
        }
        if (
          this.type === TYPE_MAP.PERCENT &&
          this.currentValue &&
          this.currentValue !== 0
        ) {
          currentValue = `${Format.accMul(currentValue, 100)}%`
        }
        if (this.type === TYPE_MAP.AMOUNT) {
          currentValue = Format.formatPrice(currentValue, this.decimalDigit)
        }
      }
      return currentValue
    },
    handleInputChange(value) {
      const newVal = value === '' ? void 0 : Number(value)
      this.userInput = null
      if (!Number.isNaN(newVal) || value === '') {
        this.isFocus = false
        this.triggerChange(newVal)
      }
    },
    select() {
      this.$refs.input.select()
    },
    handleKeyup(e) {
      this.$emit('keyup', e)
    },
    handleKeydown(e) {
      this.$emit('keydown', e)
    },
    triggerChange(value) {
      let newVal = value
      const oldVal = this.currentValue
      const decimalDigit = !this.decimalDigit ? 0 : this.decimalDigit
      if (typeof this.decimalDigit === 'number') {
        newVal = Format.keepDecimalPlaces(newVal, this.decimalDigit)
      }
      if (this.type === TYPE_MAP.PERCENT && newVal) {
        newVal = Format.accDiv(
          Format.keepDecimalPlaces(
            Format.accMul(Number(newVal), 100) + '',
            decimalDigit + 2
          ),
          1e4
        )
        if (this.userInput && this.decimalDigit >= 0) {
          const decimal = this.userInput.toString().split('.')[1]
          if ((decimal || '').toString().length > this.decimalDigit) {
            this.userInput = Format.keepDecimalPlaces(
              this.userInput,
              decimalDigit
            )
          }
        }
      }
      if (this.isAllowedRange && !this.isFocus && (newVal || newVal === 0)) {
        if (newVal >= this.max) {
          newVal = this.max
          this.userInput = null
        }
        if (newVal <= this.min) {
          newVal = this.min
          this.userInput = null
        }
      }
      if (newVal && this.type !== TYPE_MAP.PERCENT) {
        if (this.userInput && this.decimalDigit >= 0) {
          const decimal = this.userInput.toString().split('.')[1]
          if ((decimal || '').toString().length > this.decimalDigit) {
            this.userInput = newVal
          }
        }
      }
      if (oldVal === newVal) {
        return
      }
      if (newVal) {
        newVal = Number.isNaN(Number(newVal)) ? newVal : Number(newVal)
      }
      this.currentValue = newVal
      this.$emit('input', newVal)
      this.$emit('change', newVal, oldVal)
    },
    formatter(value) {
      if (this.type === TYPE_MAP.PERCENT && value) {
        return Format.accMul(value || 0, 100)
      }
      return value
    },
    parser(value) {
      if (this.type === TYPE_MAP.PERCENT) {
        return Format.accDiv(
          Format.keepDecimalPlaces(
            Format.accMul(Number(value || 0), 100),
            typeof this.decimalDigit === 'number' ? this.decimalDigit + 2 : 2
          ),
          1e4
        )
      }
      return value ? Number(value) : value
    },
    focus() {
      this.$refs.input.focus()
    },
    toPrecision(num, precision) {
      if (precision === void 0) {
        precision = this.numPrecision
      }
      return parseFloat(
        Math.round(num * Math.pow(10, precision)) / Math.pow(10, precision)
      )
    },
    getPrecision(value) {
      if (value === void 0) {
        return 0
      }
      const valueString = value.toString()
      const dotPosition = valueString.indexOf('.')
      let precision = 0
      if (dotPosition !== -1) {
        precision = valueString.length - dotPosition - 1
      }
      return precision
    },
    _increase(val, step) {
      if (typeof val !== 'number' && val !== void 0) {
        return this.currentValue
      }
      const precisionFactor = Math.pow(10, this.numPrecision)
      return this.toPrecision(
        (precisionFactor * val + precisionFactor * step) / precisionFactor
      )
    },
    _decrease(val, step) {
      if (typeof val !== 'number' && val !== void 0) {
        return this.currentValue
      }
      const precisionFactor = Math.pow(10, this.numPrecision)
      return this.toPrecision(
        (precisionFactor * val - precisionFactor * step) / precisionFactor
      )
    },
    increase() {
      if (this.inputNumberDisabled || this.maxDisabled) {
        return
      }
      const value = this.value || 0
      const newVal = this._increase(value, this.step)
      this.triggerChange(newVal)
    },
    decrease() {
      if (this.inputNumberDisabled || this.minDisabled) {
        return
      }
      const value = this.value || 0
      const newVal = this._decrease(value, this.step)
      this.triggerChange(newVal)
    },
    handleBlur(event) {
      this.isFocus = false
      this.userInput = this.getDisplayValue(this.currentValue, false)
      this.$emit('blur', event)
    },
    handleFocus(event) {
      this.isFocus = true
      this.userInput = this.formatter(this.currentValue)
      if (this.focusSelect) {
        this.$nextTick(() => {
          event.target.select()
        })
      }
      this.$emit('focus', event)
    },
    handleInput(value) {
      if (!value) {
        this.userInput = value
        this.triggerChange(value)
        return
      }
      const isNum = numExp.test(value)
      const isDot = value.endsWith('.') && numExp.test(`${value}0`)
      if (!(isNum || value === '-' || isDot)) {
        return
      }
      if (this.max === 0) {
        if (value.startsWith('-') && (isNum || value === '-' || isDot)) {
          this.userInput = value
          this.triggerChange(value)
        }
        return
      }
      if (value.startsWith('-') && this.min === 0) {
        return
      }
      this.userInput = value
      this.triggerChange(value)
    }
  }
}
</script>

<style scoped></style>
