import AgSearchSelect from './AgSearchSelect' // 全文检索下拉框【业务】
import CndInputNumber from './AgInputNumber' // 全文检索下拉框【业务】
import AgGridSelect from './AgGridSelect'
// 可用组件
const componentsObj = {
  AgSearchSelect: AgSearchSelect,
  CndInputNumber: CndInputNumber,
  AgGridSelect: AgGridSelect
}

// 已渲染组件对象
const renderComponent = {}

/**
 * 创建组件方法
 * @param {*渲染组件名称} name
 * @param {*传入的props} propsObj
 * @param {*一些特定触发事件} funObj
 *        {
 *           keyup: () => {} // 键盘弹起事件
 *           focus: () => {} // 聚焦触发事件
 *           blur: () => {} // 失焦触发事件
 *           change: () => {} // 值变化事件
 *           getOption: ()=> {} // 选择器时获取选项事件
 *        }
 */
function createComponent(name, propsObj = {}, funObj = {}) {
  return {
    data() {
      return {
        value: '',
        flagFirst: true,
        rendered: {},
        curCmpMark: ''
      }
    },
    methods: {
      // 组件被创建时调用一次
      // init(params) {
      //   console.log('组件init--', params)
      // },
      // // 供ag-grid调用，进入当前单元格时被调用(tab切换)
      focusIn() {
        // console.log('当前[]组件标识--', this.curCmpMark)
        // console.log('当前组件指向--', this.rendered[this.curCmpMark])
        this.rendered[this.curCmpMark].focus()
      },
      // 供ag-grid调用，获取最后value
      getValue() {
        // console.log('getValue[middleware]--', this.value)
        if (!funObj.focus) return this.value
      },
      // 保存组件内部传来的value
      saveVal(v) {
        this.value = v
        // console.log('saveVal[middleware]--', this.value)
      },
      // 键盘弹起事件
      emitKeyup(event) {
        if (funObj.keyup) {
          return funObj.keyup({
            event: event,
            rowData: this.params,
            middleware: this
          })
        }
      },
      // 键盘弹起事件
      emitKeydown(event) {
        if (funObj.keydown) {
          return funObj.keydown({
            event: event,
            rowData: this.params,
            middleware: this
          })
        }
      },
      // 聚焦触发事件
      emitFocus(event) {
        if (funObj.focus) {
          return funObj.focus({
            event: event,
            rowData: this.params,
            middleware: this
          })
        }
      },
      // 失焦触发事件
      emitBlur(event, v) {
        if (funObj.blur) {
          return funObj.blur({
            event: event,
            rowData: this.params,
            middleware: this,
            value: v
          })
        }
      },
      // 值变化触发事件
      emitChange(v) {
        if (funObj.change) {
          return funObj.change({
            value: v,
            rowData: this.params,
            middleware: this
          })
        }
      },
      // 选择器获取整个option
      getOption(option, type) {
        if (funObj.getOption) return funObj.getOption(option, this.params, this, type)
      },
      // 获取组件对象
      getComponent(cmp) {
        // console.log('组件指向[middleware]---', cmp)
        this.curCmpMark = cmp.mark
        renderComponent[cmp.mark] = cmp
        this.rendered = renderComponent
      }
      // setDialogVal(v) {
      //   // console.log('option------', option)
      //   // console.log('data----', complete_Data)
      //   if (funObj.setDialogVal) return funObj.setDialogVal({
      //     // event: event,
      //     value: v,
      //     rowData: this.params,
      //     middleware: this
      //   })
      //   // if (funObj.setDialogVal) return funObj.setDialogVal(option, data)
      // }
    },
    render(h) {
      // console.log('表格当前行数据--', this.params)
      // console.log('props--', propsObj)
      if (this.flagFirst) {
        this.value = this.params.value
        propsObj.value = this.value
        this.flagFirst = false
      }
      propsObj.forGrid = true
      propsObj.params = this.params
      return h(componentsObj[name], {
        props: propsObj,
        // 监听组件内部$emit
        on: {
          // setDialogVal: this.setDialogVal,
          getVal: this.saveVal,
          keyup: this.emitKeyup,
          keydown: this.emitKeydown,
          focus: this.emitFocus,
          blur: this.emitBlur,
          change: this.emitChange,
          getOption: this.getOption,
          getComponent: this.getComponent
        }
      })
    }
  }
}

export default {
  createComponent
}
