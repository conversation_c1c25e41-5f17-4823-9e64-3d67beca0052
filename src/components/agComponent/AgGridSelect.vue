<template>
  <div>
    <el-select
      ref="agSelect"
      v-model="tempV"
      :size="size"
      :clearable="clearable"
      :placeholder="isPlaceholder"
      :multiple="multiple"
      :reserve-keyword="reserveKeyword"
      :disabled="disabled"
      :filter-method="filterMethod"
      :filterable="filterable"
      :remote="remote"
      :allow-create="allowCreate"
      :default-first-option="defaultFirstOption"
      :remote-method="getOptions"
      :multiple-limit="multipleLimit"
      @change="change"
      @focus="focusAction"
      @blur="blurAction"
    >
      <el-option
        v-for="item in optionsComputed"
        :key="item[optionConfig.value]"
        :disabled="item.disabled"
        :label="item[optionConfig.label]"
        :value="optionConfig.value ? item[optionConfig.value] : item[optionConfig.label]"
      />
    </el-select>
  </div>
</template>

<script>
export default {
  name: 'AgGridSelect',
  props: {
    value: {
      type: String,
      default: ''
    },
    queryMethod: {
      type: Function,
      default: null
    },
    remoteMethod: {
      type: Function,
      default: null
    },
    remote: {
      type: Boolean,
      default: false
    },
    multipleLimit: {
      type: Number,
      default: 0
    },
    filterable: {
      type: Boolean,
      default: false
    },
    filterMethod: {
      type: Function,
      default: null
    },
    disabled: {
      type: Boolean,
      default: false
    },
    multiple: {
      type: Boolean,
      default: false
    },
    autoFocus: {
      type: Boolean,
      default: false
    },
    clearable: {
      type: Boolean,
      default: true
    },
    allowCreate: {
      type: Boolean,
      default: false
    },
    defaultFirstOption: {
      type: Boolean,
      default: false
    },
    placeholder: {
      type: String,
      default: ''
    },
    size: {
      type: String,
      default: 'mini'
    },
    config: {
      type: Object,
      default: () => {
        return {
          label: 'sCodeName',
          value: 'sCodeValue'
        }
      }
    },
    searchLimit: {
      type: Number,
      default: 4
    },
    options: {
      type: Array,
      default: () => {
        return []
      }
    },
    // 当前行数据（ag-grid中使用必须有）
    params: {
      type: Object,
      default: () => {
        return {}
      }
    },
    // 组件唯一标识
    mark: {
      type: String,
      default: ''
    },
    // 默认选项
    defaultOption: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      currOptions: this.options,
      tempV: '',
      defaultConfig: {
        label: 'sCodeName',
        value: 'sCodeValue'
      }
    }
  },
  computed: {
    isPlaceholder() {
      return this.placeholder || this.$t('components.pleaseSelect')
    },
    optionConfig() {
      Object.assign(this.defaultConfig, this.config)
      return this.defaultConfig
    },
    optionsComputed() {
      // console.log('拿到的options--', this.options)
      if (this.currOptions && this.currOptions.length > 0) {
        return this.currOptions
      } else {
        return []
      }
      // const { options } = this.params.colDef.filterList()
      // this.options = options
    },
    reserveKeyword() {
      return this.multiple
    }
  },
  watch: {
    tempV(newVal) {
      if (!newVal) {
        this.getOptions(null)
      }
    }
  },
  created() {
    // 返回当前组件指向
    // console.log('this.queryMethod--', this.queryMethod)
    this.mark && this.$emit('getComponent', this)
    if (this.queryMethod) {
      this.queryMethod(this.params).then(options => {
      // console.log('options--', options)
        this.currOptions = options
        // 设置当前选项
        const curOption = this.value || this.defaultOption
        this.$nextTick(() => {
          this.tempV = curOption
        })
        curOption && this.options.forEach(item => {
        // 当前为key/value情况
          if (item[this.optionConfig.value] === curOption || item[this.optionConfig.label] === curOption) {
          // this.tempV = item[this.optionConfig.label]
            this.$nextTick(() => {
              this.tempV = item[this.optionConfig.value]
              this.change(item[this.optionConfig.value], 'init')
            })
          }
        })
      })
    } else {
      this.currOptions = this.options
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.autoFocus && this.focus()
    })
  },
  // 销毁前触发失焦事件--解决ag-grid编辑状态回车计算问题
  beforeDestroy() {
    const pseudoEvent = {
      mark: 'pseudoEvent',
      target: {
        value: this.value
      }
    }
    this.blurAction(pseudoEvent)
  },
  methods: {
    // 设置值方法
    setValue(value) {
      this.tempV = value
      this.change(this.tempV)
    },
    // 重置方法
    reset() {
      this.tempV = ''
      this.change(this.tempV)
    },
    // 选择器聚焦方法
    focus() {
      // console.log('this.$refs.agSelect--', this.$refs.agSelect)
      this.$refs.agSelect.$el.children[0].children[0].focus()
    },
    // 聚焦事件触发
    focusAction(e) {
      this.$emit('focus', e)
    },
    // 失焦事件触发
    blurAction(e) {
      this.$emit('blur', e)
    },
    change(v, type) {
      console.log('change事件--', v)
      let curOption = ''
      if (this.multiple) {
        const obj = {}
        v.forEach(item => {
          obj[item] = true
        })
        curOption = this.currOptions.filter(item => {
          const key = this.optionConfig.value ? this.optionConfig.value : this.optionConfig.label
          return obj[item[key]]
        })
      } else {
        curOption = this.currOptions.find(item => {
          const key = this.optionConfig.value ? this.optionConfig.value : this.optionConfig.label
          return item[key] === v
        })
      }
      console.log('curOption--', curOption)
      this.$emit('getVal', v) // （ag-grid中使用必须有）
      this.$emit('getOption', curOption, type) // （返回整个对象，部分场景需要其他值）
    },
    getOptions(value) {
      if (this.remote && this.remoteMethod) {
        if (this.searchLimit !== 0 || (value && value.length < this.searchLimit)) {
          return
        }
        this.remoteMethod(value, this.params).then(options => {
          this.currOptions = options
        })
      }
    }
  }
}
</script>
