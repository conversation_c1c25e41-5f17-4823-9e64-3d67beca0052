<template>
  <el-popover
    v-model="visible1"
    popper-class="input-number-pop"
    placement="top-start"
    :content="msg"
    trigger="manual"
  >
    <el-input
      slot="reference"
      v-model="childValue"
      :class="['input-number', visible2 ? 'error-input' : '']"
      :size="size"
      :disabled="disabled"
      :placeholder="isPlaceholder"
      clearable
      @input="inputAction"
      @focus="focusAction"
      @blur="blurAction"
      @keyup.native="tabCell"
      @clear="clear"
    />
  </el-popover>
</template>

<script>
import { Format } from 'cnd-horizon-utils'
import { Validate } from 'cnd-utils'
import './AgInputNumber.scss'

export default {
  name: 'CndInputNumber',
  props: {
    // 组件唯一标识
    mark: {
      type: String,
      default: ''
    },
    // 是否在ag-grid中使用
    forGrid: {
      type: Boolean,
      default: false
    },
    // 绑定值（v-model）
    value: {
      type: String || Number,
      default: ''
    },
    type: {
      type: String,
      default: 'amount'
    },
    placeholder: {
      type: String,
      default: ''
    },
    size: {
      type: String,
      default: 'mini'
    },
    disabled: {
      type: Boolean,
      default: false
    },
    // 保留几位小数(输入时限制)
    decimalDigit: {
      type: Number,
      default: null
    },
    // 输入值的范围
    range: {
      type: Array,
      default: () => {
        return []
      }
    },
    // 是否展示千分符
    thousandthSign: {
      type: Boolean,
      default: false
    },
    // 是否可输入负值
    negative: {
      type: Boolean,
      default: false
    },
    // 只输入负值
    minus: {
      type: Boolean,
      default: false
    },
    // 是否自动聚焦
    autoFocus: {
      type: Boolean,
      default: false
    },
    // 聚焦是否选中内容
    focusSelect: {
      type: Boolean,
      default: false
    },
    // 当前行数据（ag-grid中使用必须有）
    params: {
      type: Object,
      default: () => {
        return {}
      }
    },
    // 必填
    required: {
      type: Boolean,
      default: false
    },
    // 输入规则
    rules: {
      type: Array,
      default: () => {
        return [{ max: 19, min: 6 }]
      }
    },
    // 是否返回空
    returnNull: {
      type: Boolean,
      default: false
    },
    negativeFn: {
      type: Function,
      default: null
    }
  },
  data() {
    return {
      visible1: false,
      visible2: false,
      unformatValue: '',
      childValue: this.value,
      // 当前是否聚焦
      flagFocus: false,
      msg: '',
      oldChildValue: this.value,
      localNegative: this.negative
    }
  },
  computed: {
    isPlaceholder() {
      return this.placeholder || this.$t('components.pleaseEnter')
    }
  },
  watch: {
    value(nv, ov) {
      this.childValue = nv
      this.formatNum(this.flagFocus)
    }
  },
  mounted() {
    if (this.params && Object.keys(this.params).length > 0) { this.childValue = this.params.value } // （ag-grid中使用必须有）
    this.formatNum()
    // 返回当前组件指向
    this.mark && this.$emit('getComponent', this)
    this.$nextTick(() => {
      this.autoFocus && this.focus()
    })
    if (this.negativeFn !== null) {
      const isTrue = this.negativeFn(this.params)
      if (isTrue) {
        this.localNegative = true
      } else {
        this.localNegative = false
      }
      this.setVal()
    }
  },
  // 销毁前触发失焦事件--解决ag-grid点击直接退出编辑状态情况
  beforeDestroy() {
    const pseudoEvent = {
      mark: 'pseudoEvent',
      target: {
        value: this.childValue
      }
    }
    this.flagFocus && this.blurAction(pseudoEvent)
  },
  methods: {
    // 监听键盘事件
    tabCell(e) {
      if (!this.mark) return
      // 解决ag-grid编辑状态回车赋值以及计算问题
      if (e.keyCode === 13) {
        const pseudoEvent = {
          mark: 'pseudoEvent',
          target: {
            value: this.childValue
          }
        }
        this.flagFocus && this.blurAction(pseudoEvent)
      }
      this.$emit('keyup', e)
    },
    // 设置值方法
    setValue(value) {
      this.childValue = value
      this.$emit('input', this.childValue)
      this.$emit('change', this.childValue)
      this.$emit('getVal', this.childValue) // （ag-grid中使用必须有）
      this.formatNum(false)
    },
    focus() {
      // this.$children[0].focus()
      this.$children[0].referenceElm.focus()
    },
    blur() {
      // this.$children[0].blur()
      this.$children[0].referenceElm.blur()
    },
    select() {
      this.$refs.inputNumber.focus()
      // this.$children[0].select()
    },
    // 清空事件
    clear() {
      this.focus()
      this.$emit('clear', this.childValue)
      this.$emit('input', this.childValue)
      this.$emit('change', this.childValue)
      this.$emit('getVal', this.childValue) // （ag-grid中使用必须有）
    },
    // 输入事件
    inputAction() {
      if (this.childValue === '' && this.required) {
        this.visible1 = this.visible2 = true
        this.msg = this.$t('grid.others.pleaseEnterTheDataValue')
      } else if (this.rules.length > 0) {
        this.rules.map((item) => {
          if (item.max || item.min) {
            const _max = this.childValue.toString().split('.')[0].length
            const _min =
              this.childValue.toString().indexOf('.') !== -1
                ? this.childValue.toString().split('.')[1].length
                : 0
            if (item.max < _max || item.min < _min) {
              this.visible1 = this.visible2 = true
              this.msg = item.msg
                ? item.msg
                : item.max && item.min
                  ? `整数位最多${item.max}位,小数位最多${
                    this.decimalDigit ? this.decimalDigit : item.min
                  }位。`
                  : item.max
                    ? `整数位最多输入${item.max}位。`
                    : `小数位最多输入${item.min}位。`
            } else {
              this.visible1 = this.visible2 = false
            }
          } else if (item.fn) {
            this.visible1 = this.visible2 = !item.fn(this.childValue)
            this.msg = item.msg
          }
        })
      } else {
        this.visible1 = this.visible2 = false
      }
      this.setVal()
    },
    setVal() {
      // let reg = /[^\d\.]/g
      // let reg2 = /[^+\d\.]/g
      // this.childValue = this.childValue.replace(/(?<!^)-/g, '')
      // this.negative && (reg = /[^-\d\.]/g) && (this.childValue = this.childValue.replace('-','$#$').replace(/\-/g,'').replace('$#$','-'))
      // this.minus && (reg = /[^-\d\.]/g) && (this.childValue = this.childValue.replace('-','$#$').replace(/\-/g,'').replace('$#$',''))
      // this.childValue = this.childValue.replace(reg, '')
      // this.childValue = this.childValue.replace('.','$#$').replace(/\./g,'').replace('$#$','.')
      // if(this.minus && !reg2.test(this.childValue) && this.childValue && this.childValue !== 0){
      //   this.childValue = '-' + this.childValue
      // }

      // replace(/(?!(-|\.|\d))./g, '') // 替换所有非 - . 数字 的字符
      this.childValue = this.childValue
        .replace(/(.*\..*)(\.)/g, '$1') // 不管第一个点的位置，只替换第二个点(.)
        .replace(/(.+)(-)/g, '$1') // 替换不在第一位的 -
      this.localNegative &&
        (this.childValue = this.childValue.replace(/(?!(-|\.|\d))./g, ''))
      if (this.minus) {
        this.childValue = this.childValue.replace(/(?!(\.|\d))./g, '')
        this.childValue = '-' + this.childValue
      }
      !this.localNegative &&
        !this.minus &&
        (this.childValue = this.childValue.replace(/(?!(\.|\d))./g, ''))

      // 如果小数超过限制截取限制数
      if (
        !Validate.isUndeFinedOrNullOrBlank(this.decimalDigit) &&
        !isNaN(Number(this.decimalDigit))
      ) {
        if (
          this.childValue.indexOf('.') > -1 &&
          this.childValue.split('.')[1].length > Number(this.decimalDigit)
        ) {
          const tempArr = this.childValue.split('.')
          if (Number(this.decimalDigit) === 0) {
            this.childValue = tempArr[0]
          } else if (Number(this.decimalDigit) > 0) {
            this.childValue =
              tempArr[0] + '.' + tempArr[1].substr(0, Number(this.decimalDigit))
          }
        }
      }
      if (this.type !== 'percent') {
        this.$emit('input', this.childValue)
        this.$emit('change', this.childValue)
        this.$emit(
          'getVal',
          this.visible2 ? this.oldChildValue : this.childValue
        ) // （ag-grid中使用必须有）
      } else if (!isNaN(Number(this.childValue))) {
        const tempVal =
          this.childValue === '' && this.returnNull
            ? ''
            : this.type === 'percent'
              ? Format.accDiv(
                Number(
                  Format.keepDecimalPlaces(this.childValue, this.decimalDigit)
                ),
                100
              )
              : Format.accDiv(Number(this.childValue), 100)
        // const tempVal  = Format.accDiv(Number(this.childValue), 100)
        this.$emit('getVal', this.visible2 ? this.oldChildValue : tempVal) // （ag-grid中使用必须有）
      }
      console.log('this.childValue', this.childValue)
    },
    // 聚焦事件
    focusAction(e) {
      if (this.visible2) this.visible1 = true
      // console.log('聚焦1--', e.target.value)
      // console.log('聚焦childValue--', this.childValue)
      this.flagFocus = true
      if (this.childValue) {
        // 百分比转为数字
        if (this.type === 'percent') {
          // e.target.value = this.childValue = Format.toPoint(this.childValue)
          this.childValue = e.target.value = this.childValue.replace('%', '')
          // console.log('聚焦事件后--', this.childValue)
        } else if (this.type === 'amount' || this.thousandthSign) {
          // 货币格式（千分符）转为数字
          e.target.value = this.childValue = Format.toNum(this.childValue)
        }
      }
      // console.log('聚焦2--', e.target.value)
      if (this.focusSelect) {
        this.$nextTick(() => {
          e.target.select()
        })
      }
      this.$emit('focus', e)
    },
    // 失焦事件
    blurAction(e) {
      this.childValue === '-' && (this.childValue = 0)
      this.visible1 = false
      // console.log('失焦--', e.target.value)
      // console.log('失焦事件--', this.childValue)
      if (this.childValue) {
        this.childValue = this.rangeControl(this.childValue)
        if (this.type === 'number') {
          this.childValue = parseFloat(this.childValue)
        }
      }
      // console.log('失焦控制位数--', this.childValue)
      if (this.type === 'percent') {
        // 如果不允许返回0 !this.returnNull  0-> '',数值->数值%
        // 允许返回0 this.returnNull  0-> 0,数值->数值%
        this.childValue = e.target.value =
          this.returnNull && !this.childValue
            ? this.childValue
            : Format.accDiv(this.childValue, 100)
      }
      !this.returnNull && this.$emit('getVal', this.childValue) // （ag-grid中使用必须有）
      this.$emit('input', this.childValue)
      this.$emit('change', this.childValue)
      this.$emit('blur', e, this.childValue)
      this.flagFocus = false
      this.formatNum(this.flagFocus)
      // console.log('失焦format--', this.childValue)
    },
    // 格式化内容
    formatNum(flag = false) {
      // console.log('格式化前--',  this.childValue)
      if (!flag && this.childValue) {
        this.unformatValue = this.childValue
        if (this.decimalDigit && this.type !== 'percent') {
          // 数字保留小数位数
          this.childValue = Format.keepDecimalPlaces(
            this.childValue,
            this.decimalDigit
          )
        }
        if (this.thousandthSign) {
          // 数字转为千分符
          this.childValue = Format.formatThousandthSign(
            this.childValue,
            this.decimalDigit
          )
        }
        console.log('this.childValue1', this.childValue)
        // 数字转为百分比
        if (this.type === 'percent') {
          this.childValue = `${Format.accMul(this.childValue, 100)}%`
        } else if (this.type === 'amount') {
          // 数字转为千分位金额-
          this.childValue = Format.formatPrice(
            this.childValue,
            this.decimalDigit
          )
        }
      }
      // console.log('格式化后--', this.childValue)
    },
    // 值范围控制
    rangeControl(value) {
      if (this.range && this.range.length > 0) {
        const multiple = this.type === 'percent' ? 100 : 1
        const _min = Number(this.range[0]) * multiple
        let _max = null
        this.range[1] && (_max = this.range[1] * multiple)
        // 小于最小值情况
        if (Number(value) < _min) {
          return Number(this.range[0])
        }
        if (_max && Number(value) > _max) {
          return _max
        }
        return value
      }
      return value
    }
  }
}
</script>
