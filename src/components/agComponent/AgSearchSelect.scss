.search-select {
    .el-scrollbar {
      position: static;
    }
    .el-select-dropdown__wrap {
      max-height: 342px;
      .el-scrollbar__view.el-select-dropdown__list {
        .el-select-dropdown__item {
          line-height: 32px !important;
          height: 32px !important;
          font-size: 12px !important;
        }
      }
    }
    .position-pagination {
      width: 100%;
      position: absolute;
      z-index: 9;
      bottom: 0;
      background-color: #ffffff;
      text-align: right;
      .el-pagination--small span:not([class*=suffix]), .el-pagination--small button {
        height: 26px !important;
        line-height: 26px !important;
      }
      .el-pagination__jump {
        margin-left: 0;
      }
      // .el-input__inner {
      //   height: 26px !important;
      //   line-height: 26px !important;
      //   padding: 0 2px !important;
      //   border: 1px solid #dfe4ed;
      // }
      .height-style {
        height: 26px !important;
        line-height: 26px !important;
        min-width: 20px !important;
      }
    }
  }
  .pd40 {
    padding-bottom: 42px;
  }