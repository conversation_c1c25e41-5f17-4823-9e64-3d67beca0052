<template>
  <div class="ag-cell-popover">
    <span>{{ formattedValue }}</span>
    <el-popover
      v-if="shouldShowPopover"
      v-bind="resolvedPopoverConfig"
      popper-class="ag-popover-popperclass"
    >
      <cnd-icon
        v-if="showQuestion"
        slot="reference"
        name="cnd-question-mark"
        color="#409EFF"
      />
      <span v-else slot="reference" style="color: red; font-size: 14px">*</span>
    </el-popover>
  </div>
</template>

<script>
const DEFAULT_POPOVER_CONFIG = {
  placement: 'top-start',
  trigger: 'hover'
}

export default {
  name: 'AgPopover',
  data() {
  },
  computed: {
    formattedValue() {
      const value = this.params?.value
      if (typeof value === 'function') {
        return value(this.params)
      }
      return value ?? ''
    },
    resolvedPopoverConfig() {
      const config = { ...DEFAULT_POPOVER_CONFIG, ...(this.params?.popoverConfig || {}) }
      const resolved = {}
      Object.entries(config).forEach(([key, value]) => {
        if (typeof value === 'function') {
          resolved[key] = value(this.params)
        } else {
          resolved[key] = value
        }
      })

      return resolved
    },
    shouldShowPopover() {
      const showPopover = this.params?.showPopover
      if (typeof showPopover === 'function') {
        return showPopover(this.params)
      }
      return showPopover ?? true
    },
    showQuestion() {
      const isQuestion = this.params?.isQuestion
      return isQuestion
    }
  }
}
</script>

<style lang="scss">
.ag-cell-popover {
  display: flex;
  align-items: center;
  gap: 5px;
}
.ag-popover-popperclass{
  font-size: 12px !important;
  padding: 10px;
}
</style>
