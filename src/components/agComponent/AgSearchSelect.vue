<template>
  <!--全文检索下拉框组件-->
  <el-select
    ref="agSearchSelect"
    v-model="selectValue"
    :popper-class="`search-select ${markId} ${pd40}`"
    :size="size"
    :clearable="clearable"
    :placeholder="isPlaceholder"
    :reserve-keyword="reserveKeyword"
    :multiple="multiple"
    :disabled="disabled"
    :filter-method="filterMethod"
    filterable
    remote
    :remote-method="getOptions"
    :loading-text="loadingText"
    :multiple-limit="multipleLimit"
    :loading="loading"
    @change="change"
    @focus="focusAction"
    @blur="blurAction"
    @visible-change="visibleChange"
  >
    <el-option
      v-for="(item, index) in optionsComputed"
      :key="index"
      :disabled="item.disabled"
      :label="
        item[optionConfig.seledLabel]
          ? item[optionConfig.seledLabel]
          : item[optionConfig.label]
      "
      :value="
        optionConfig.value
          ? item[optionConfig.value]
          : item[optionConfig.label]
            ? item[optionConfig.label]
            : item[optionConfig.seledLabel]
      "
    >
      <span>{{
        item[optionConfig.label]
          ? item[optionConfig.label]
          : item[optionConfig.seledLabel]
      }}</span>
    </el-option>
    <!-- 分页 -->
    <div
      v-show="totalPage > 1"
      class="position-pagination border-top"
      @click.stop
    >
      <cnd-pagination
        :total="pagination.total"
        :page="pagination.pageNo"
        :limit="pagination.pageSize"
        :event="pageChange"
        layout="prev,slot,next"
      >
        <span class="flexC">
          <el-input
            v-model="curPage"
            style="width: 30px"
            @blur="jumpPage"
            @keyup.native.stop.enter="jumpPage"
          />
          <span class="height-style">/{{ totalPage }}</span>
        </span>
      </cnd-pagination>
    </div>
  </el-select>
</template>

<script>
// import { postList } from '../../api/dict'
import { request } from 'cnd-horizon-utils'
// import { apiList } from 'cnd-horizon-utils'
import './AgSearchSelect.scss'

export default {
  name: 'AgSearchSelect',
  props: {
    // 接口地址
    defaultUrl: {
      type: String,
      default: ''
    },
    // 检索类型
    type: {
      type: String,
      default: 'staff'
    },
    // 客商类型字段
    customerType: {
      type: String,
      default: ''
    },
    // 货号类型字段
    artnoType: {
      type: String,
      default: ''
    },
    // 商品分类
    categories: {
      type: String,
      default: ''
    },
    // 检索字段
    searchKey: {
      type: String,
      default: 'sName'
    },
    value: {
      type: String || Array,
      default: ''
    },
    multipleLimit: {
      type: Number,
      default: 0
    },
    filterMethod: {
      type: Function,
      default: null
    },
    disabled: {
      type: Boolean,
      default: false
    },
    multiple: {
      type: Boolean,
      default: false
    },
    clearable: {
      type: Boolean,
      default: true
    },
    placeholder: {
      type: String,
      default: ''
    },
    size: {
      type: String,
      default: 'mini'
    },
    option: {
      type: Object,
      default: () => {
        return {
          seledLabel: 'sName',
          label: 'sPath',
          value: ''
        }
      }
    },
    autoFocus: {
      type: Boolean,
      default: false
    },
    // 组件唯一标识
    mark: {
      type: String,
      default: ''
    },
    // 当前行数据（ag-grid中使用必须有）
    params: {
      type: Object,
      default: () => {
        return {}
      }
    },
    otherOptions: {
      type: Object,
      default: () => {}
    },
    cancelPage: {
      type: Boolean,
      default: false
    },
    method: {
      type: String,
      default: 'post'
    },
    // 是否默认人民币
    currency: {
      type: String,
      default: '人民币'
    },
    labelDefault: {
      type: String,
      default: ''
    },
    transformSearchParams: {
      type: Function,
      default: (searchParams) => searchParams
    }
  },
  data() {
    return {
      loadingText: '加载中',
      defaultOption: {
        seledLabel: 'sName',
        label: 'sPath',
        value: ''
      },
      markId: '',
      pagination: {
        total: 0,
        pageNo: 0,
        pageSize: 20
      },
      loading: false,
      options: [],
      url: {
        company: '/org/dialog/corp/v2/page',
        depart: '/org/dialog/busi/v2/page',
        cost: '/org/dialog/cost/v2/page',
        area: '/area/dialog/v2/page',
        staff: '/staff/dialog/org/v2/page',
        customer: '/customer/dialog/v2/page',
        currency: '/currency/v2/page',
        artno: '/dialog/artno/v2/page',
        measurementunit: '/measurementunit/dialog/v2/query',
        warehouse: '/warehouse/info/dialog/v2/page-dialog',
        // creater: '/user/dialog/v2/page',
        creater: '/user/dialog/path/page',
        bank: '/bank/dialog/v2/page',
        tax: '/tax/type/page',
        goods: '/goods/dialog/detail/query/v2',
        applicant: '/user/dialog/path/page',
        portcustoms: '/portcustoms/dialog/name/like',
        groupcustomers: '/bda/groupCustomer/dialog/v2/page',
        bookingOrder: '/stock/booking/dialog/v2/page',
        company_depart: '/org/dialog/corporbus/page',
        staff_cost: '/staff/info/byorgid',
        sModifier: '/paper/homepage/notice/system/page',
        role: '/role/dialog/page',
        tenant: '/org/dialog/root/page',
        menu: '/sys/menu',
        bankCategory: '/bankCategory/page',
        hscode: '/hscode/bdahscode/dialog/page-dialog',
        root: '/org/root/page',
        paperCustomer: 'paper/customer/dialog/v2/page',
        pscmGoodsDetail: 'pscm/goods/detail/query/v2/page',
        pscmGoodsAttribute: 'pscm/goods/attribute/query/v2/page',
        paperStock: 'paper/stock/balance/summary/sum/query',
        cargoContainer: 'comm/container/dialog/v2/page',
        staffDialog: '/staff/dialog/org/v3/page',
        orgRoot: '/org/root/page',
        portcity: '/portcity/page',
        interaction: '/interaction/payconfig/page',
        factor: '/sys/datatableresource/dialog/page',
        affectField: '/sys/datafieldresource/dialog/page',
        QC: '/configure/liorgrelation/role-user/page',
        transfer: '/configure/liorgrelation/role-user/page',
        customerLevel: '/bda/user/customer/level/dialog/page',
        taxType: '/tax/type/dialog/v2/page',
        taxationtype: '/invoice/sale/tax-detail/taxationtype/page',
        pscmWarehouse: '/pscm/warehouse/info/dialog/v2/page-dialog',
        onlineUsers: '/user/dialog/path/page',
        escOrg: '/esc/org/dialog/page',
        bankRate: '/bank/rate/dialog/v2/page'
      },
      curPage: 1,
      flagInit: true,
      flagOptions: false,
      selectValue: this.value
    }
  },
  computed: {
    isPlaceholder() {
      return this.placeholder || this.$t('grid.others.pleaseEnterTheSearch')
    },
    optionConfig() {
      Object.assign(this.defaultOption, this.option)
      // console.log('defaultOption--', this.defaultOption)
      return this.defaultOption
    },
    optionsComputed() {
      // console.log('拿到的选项列表--', this.options)
      return this.options
    },
    reserveKeyword() {
      return this.multiple
    },
    // 总页数
    totalPage() {
      if (this.pagination.total === 0) return 0
      return Math.ceil(this.pagination.total / this.pagination.pageSize)
    },
    pd40() {
      if (this.totalPage > 1 && this.loadingText !== '无数据') return 'pd40'
      return ''
    }
  },
  watch: {
    curPage(nv, ov) {
      // console.log('当前页--', nv)
      this.pagination.pageNo = nv - 1
    },
    'pagination.pageNo'(nv, ov) {
      // console.log('分页配置--', nv)
      this.curPage = nv + 1
    }
  },
  created() {
    // 返回当前组件指向
    this.mark && this.$emit('getComponent', this)
    // 设置独有id--后面做滚动监听用
    this.markId =
      'select' + new Date().getTime() + Math.ceil(Math.random() * 10000)
    this.selectValue = this.value
    this.getOptions(this.selectValue)
  },
  mounted() {
    this.$nextTick(() => {
      this.autoFocus && this.focus()
    })
  },
  // 销毁前触发失焦事件--解决ag-grid编辑状态回车计算问题
  beforeDestroy() {
    const pseudoEvent = {
      mark: 'pseudoEvent',
      target: {
        value: this.value
      }
    }
    this.blurAction(pseudoEvent)
  },
  methods: {
    // 设置值方法
    setValue(value) {
      // console.log('value--', value)
      this.selectValue = value
      this.change(this.selectValue)
    },
    setUrl(params) {
      let lastUrl
      if (this.defaultUrl) {
        lastUrl = `${this.defaultUrl}`
      } else if (params.localId) {
        lastUrl =
          this.type === 'company' || this.type === 'depart'
            ? `${this.url['company_depart']}`
            : `${this.url['staff_cost']}`
      } else if (params.orgid) {
        lastUrl = `${this.url['staff_cost']}/${params.orgid}`
        delete params.orgid
      } else {
        lastUrl = `${this.url[this.type]}`
      }
      lastUrl = this.cancelPage
        ? lastUrl
        : `${lastUrl}/${this.pagination.pageNo}/${this.pagination.pageSize}`
      // console.log('lastUrl', lastUrl)
      return lastUrl
    },
    // 选择器聚焦方法
    focus() {
      // console.log('this.$refs.agSearchSelect--', this.$refs.agSearchSelect)
      this.$refs.agSearchSelect.$el.children[0].children[0].focus()
    },
    // 下拉框出现/隐藏时触发
    visibleChange(v) {
      // console.log('滚动--', v)
      // console.log('值--', this.value)
      this.flagOptions = v
      if (v && this.selectValue) {
        this.getOptions(this.selectValue, true, true)
      }
      // if (v) {
      //   const scrollbar = document.getElementsByClassName(this.markId)[0].getElementsByClassName('el-scrollbar')
      //   // el-scrollbar
      //   console.log('scrollbar--', scrollbar)
      //   scrollbar[0].getElementsByClassName('el-scrollbar__wrap')[0].addEventListener('scroll', (e) => {
      //     console.log('滚动事件--', e)
      //   })
      // }
    },
    // 跳转分页
    jumpPage() {
      this.getOptions(this.selectValue, false, false)
    },
    // 切换分页数据
    pageChange(obj) {
      this.pagination.pageNo = obj.page
      this.pagination.pageSize = obj.limit
      this.getOptions(this.selectValue, false, false)
    },
    // 聚焦事件
    focusAction(e) {
      !this.multiple && this.getOptions(this.selectValue, true, false)
      this.$emit('focus', e)
    },
    // 失焦事件触发
    blurAction(e) {
      this.$emit('blur', e)
    },
    // 选项change事件
    change(v) {
      console.log('已选值1--123', v)
      let curOption = ''
      if (this.multiple) {
        const obj = {}
        v.forEach((item) => {
          obj[item] = true
        })
        curOption = this.options.filter((item) => {
          const key = this.optionConfig.value
            ? this.optionConfig.value
            : this.optionConfig.label
          if (obj[item[key]]) return obj[item[key]]
          return obj[item[this.optionConfig.seledLabel]]
        })
      } else {
        curOption = this.options.find((item) => {
          const key = this.optionConfig.value
            ? this.optionConfig.value
            : this.optionConfig.label
          if (item[key]) return item[key] === v
          return item[this.optionConfig.seledLabel] === v
        })
      }
      console.log('curOption--', curOption)
      this.$emit('getVal', v) // （ag-grid中使用必须有）
      this.$emit('getOption', curOption) // （返回整个对象，部分场景需要其他值）
      this.$emit('change', curOption)
    },
    // 获取选项列表
    getOptions(value, loading = true, resetPageNo = true) {
      if (value !== null && value !== '' && value !== undefined) {
        this.selectValue = value
      }
      this.loading = loading
      resetPageNo && (this.pagination.pageNo = 0)
      setTimeout(() => {
        const params = {}
        params[this.searchKey] = Array.isArray(this.selectValue) ? '' : this.selectValue
        if (this.type === 'customer' && this.customerType) {
          params.supParam = this.customerType
        } else if (this.type === 'artno') {
          this.artnoType && (params.goodsDevCategory = this.artnoType)
          this.categories &&
            (params.sClassification = this.params.data[this.categories])
        } else {
          params.supParam = ''
        }
        Object.assign(params, this.otherOptions)
        // 拼接pageNo、pageSize
        // const lastUrl = `${this.url[this.type]}/${this.pagination.pageNo}/${this.pagination.pageSize}`
        const lastUrl = this.setUrl(params)
        const nextParams = typeof this.transformSearchParams === 'function' ? this.transformSearchParams(params) : params

        if (this.method === 'post') {
          request({
            url: lastUrl,
            method: 'POST',
            data: nextParams,
            noLoading: true
          })
            .then((res) => {
              const tempOptions = []
              res.data.content.forEach((item) => {
                if (item) {
                  tempOptions.push(item)
                }
              })
              this.options = tempOptions
              if (this.options.length === 0 && !value) {
                this.loadingText = '无数据'
              } else {
                this.loading = false
              }
              this.pagination.total = res.data.totalElements
              this.type === 'currency' && this.defaultCurrency(this.options)
              this.labelDefault && this.defaultCurrency(this.options)
              // if (this.flagInit && this.type === 'currency') {
              //   this.defaultCurrency(this.options)
              // }
              if (this.flagOptions) {
                // 触发resize事件
                setTimeout(() => {
                  const resizeE = new Event('resize')
                  window.dispatchEvent(resizeE)
                }, 10)
              }
            })
            .catch((error) => {
              console.log('请求失败--', error)
            })
            .finally(() => {
              // this.loading = false
            })
          // apiList.postList(params, lastUrl).then(res => {
          //   let tempOptions = []
          //   res.data.content.forEach(item => {
          //     if (item) {
          //       tempOptions.push(item)
          //     }
          //   })
          //   this.options = tempOptions
          //   if(this.options.length === 0 && !value) {
          //     this.loadingText = "无数据"
          //   } else {
          //     this.loading = false
          //   }
          //   this.pagination.total = res.data.totalElements
          //   if (this.flagInit && this.type === 'currency') {
          //     this.defaultCurrency(this.options)
          //   }
          //   if (this.flagOptions) {
          //     // 触发resize事件
          //     setTimeout(() => {
          //       const resizeE = new Event('resize')
          //       window.dispatchEvent(resizeE)
          //     }, 10)
          //   }
          // }).catch(error => {
          //   console.log('请求失败--', error)
          // }).finally(() => {
          //   // this.loading = false
          // })
        } else {
          request({
            url: lastUrl,
            method: 'GET',
            params,
            noLoading: true
          })
            .then((res) => {
              console.log('res', res)
              const tempOptions = []
              const data = res?.data?.content
                ? res.data.content
                : res.data.records
              data.forEach((item) => {
                if (item) {
                  tempOptions.push(item)
                }
              })
              this.options = tempOptions
              this.pagination.total = res?.data?.totalElements
                ? res.data.totalElements
                : res.data.total
              this.type === 'currency' && this.defaultCurrency(this.options)
              this.labelDefault && this.defaultCurrency(this.options)
              // if (this.flagInit) {
              //   this.type === 'currency' && this.defaultCurrency(this.options)
              // }
              if (this.flagOptions) {
                // 触发resize事件
                setTimeout(() => {
                  const resizeE = new Event('resize')
                  window.dispatchEvent(resizeE)
                }, 10)
              }
            })
            .catch((error) => {
              console.log('请求失败--', error)
            })
            .finally(() => {
              this.loading = false
            })
        }
      }, 200)
    },
    // 币种--币种时给默认人民币
    defaultCurrency(options) {
      if (this.selectValue) return
      this.flagInit = false
      options.forEach((item) => {
        if (item.sPath === this.currency) {
          this.selectValue = item.sPath
        }
      })
      this.change(this.selectValue)
    }
  }
}
</script>
