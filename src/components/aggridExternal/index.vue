<!--
    @component aggridExternal 公用布局组件
    @props
        {String} title    - 标题名称  ''
        {Boolean} defaultCollapse - 是否默认展开 true
        {Boolean} canCollapse - 是否可展开收起 true
    @slots
        title 标题slot
    @events
        change Boolean 展开收起回调
    <AUTHOR>
    @date 2020-12-09 09:10:51
    @update 2020-12-14 11:09:19 KYUUTA
-->
<template>
  <el-collapse
    :value="activeCollapse"
    class="aggrid-external"
    @change="changeCollapsed"
  >
    <el-collapse-item
      :title="title"
      name="1"
      :class="{'hide-arrow': !canCollapse }"
    >
      <div
        v-if="$slots.title"
        slot="title"
        class="title-wrapper"
      >
        <slot name="title" />
      </div>
      <slot v-if="$slots.default" />
    </el-collapse-item>
  </el-collapse>
</template>

<script>
export default {
  name: 'AggridExternal',
  props: {
    title: {
      type: String,
      default: ''
    },
    defaultCollapse: {
      type: Boolean,
      default: true
    },
    canCollapse: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      activeCollapse: this.defaultCollapse ? '1' : null
    }
  },
  methods: {
    changeCollapsed(collapsed) {
      if (!this.canCollapse) {
        this.activeCollapse = this.defaultCollapse ? ['1'] : []
      } else {
        this.$emit('change', !!collapsed.length)
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.aggrid-external{
  .el-collapse-item.is-active{
    border-bottom: 0!important;
  }
  .el-collapse-item__header {
    display: flex;
    height: 31px;
    font-size: 12px;
    line-height: unset;
    background: #FAFAFA;
    border-bottom: 0;
    border-left: 1px solid #e6e8eb;
    border-right: 1px solid #e6e8eb;
  }
  .el-collapse-item__header.is-active{
    border-bottom: 0!important;
  }
  .hide-arrow {
    .el-collapse-item__arrow {
      display: none;
    }
  }
  .el-collapse-item {
    margin-top: 0;
  }
  .title-wrapper {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .ag-ltr {
    border: 0;
  }
}
</style>
