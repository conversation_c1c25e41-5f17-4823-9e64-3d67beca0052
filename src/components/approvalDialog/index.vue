<template>
  <el-drawer
    :visible="visible"
    :with-header="false"
    :modal-append-to-body="false"
    :append-to-body="true"
    :direction="direction"
    size="40%"
    :before-close="handleClose"
    class="approvalDialog"
  >
    <div class="approval-container">
      <div class="approval-container-steps">
        <el-scrollbar>
          <el-steps class="mt-20" align-center>
            <el-step
              v-for="(item, index) in flowTree"
              :key="item.id"
              :title="item.name"
              :status="headerStatus(index)"
            />
          </el-steps>
        </el-scrollbar>
      </div>
      <div :style="`height: calc(100% - ${soltBtn ? '130px' : '0px'})`">
        <el-col :span="10" class="approval-opinion border-except-right">
          <div class="border-bottom approval-title">{{ $t('grid.others.approvalComments') }}</div>
          <div
            :class="
              flowList.length
                ? 'approval-content'
                : 'flex-center approval-content'
            "
          >
            <el-timeline v-if="flowList.length">
              <el-timeline-item
                v-for="(item, index) in flowList"
                :key="item.taskId"
                :color="contentStatus(item, index)"
                :style="procInstId === item.procInstId ? 'font-weight:bold' : ''"
                :timestamp="renderTime(item.sCreateTime)"
              >
                <div @click="changeName(item)">
                  <div>
                    <span class="mr-5" style="font-size: 16px">{{
                      item.vAssigneeName
                    }}</span>
                    <span style="font-size: 12px; color: #909399">{{
                      item.taskName
                    }}</span>
                  </div>
                  <div>
                    <p
                      :style="`color: ${
                        item.actionType === 'ROLLBACK' ? '#ef5f5f' : ''
                      }`"
                      class="approval-content-comment"
                    >
                      {{ item.comment }}
                    </p>
                  </div>
                </div>
              </el-timeline-item>
            </el-timeline>
            <div v-else style="color: #8c8c8c">{{ $t('grid.others.noApprovalCommentsAtThisTime') }}</div>
          </div>
        </el-col>
        <el-col :span="14" style="height: 100%" class="border">
          <div class="border-bottom approval-title">{{ $t('grid.others.approvalInstructions') }}</div>
          <div class="approval-content" v-html="explainData" />
        </el-col>
      </div>
      <div v-if="soltBtn" class="approval-btn">
        <slot />
      </div>
    </div>
  </el-drawer>
</template>

<script>
import { postList, processOri, getList } from '@/api/contract'
export default {
  props: {
    // 弹窗显隐
    visible: {
      type: Boolean,
      default: false
    },
    soltBtn: {
      type: Boolean,
      default: false
    },
    id: {
      type: String,
      default: ''
    },
    idKeys: {
      type: String,
      default: ''
    },
    url: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      direction: 'rtl',
      explainData: null,
      flowTree: [],
      flowList: [],
      statusIndex: 0,
      procInstId: null
    }
  },
  computed: {},
  watch: {
    visible(nv, ov) {
      if (nv) {
        console.log('approvalDialog')
        this.getProgress()
      } else {
        this.explainData = null
        this.statusIndex = 0
        this.flowTree = []
      }
    }
  },
  created() {},
  methods: {
    changeName(item) {
      if (item.procInstId !== this.procInstId) {
        this.procInstId = item.procInstId
        this.approveDescription(item.procInstId)
      }
    },
    renderTime(date) {
      if (!date) {
        return ''
      }
      const dateee = new Date(date).toJSON()
      return new Date(+new Date(dateee) + 8 * 3600 * 1000).toISOString().replace(/T/g, ' ').replace(/\.[\d]{3}Z/, '')
    },
    getProgress() {
      // 获取key
      postList(this.idKeys, `/sys/wfext/sheet/procdefdesc/List/businessids`)
        .then((res) => {
          const data = res.data || {}
          this.explainData = data[0].sDescription
          this.procInstId = data[0].sProcInsId
          const { sProcInsId = '', sProcDefId = '' } = data[0]
          if (!sProcDefId || !sProcInsId) return
          this.approveDescription(sProcInsId)
          this.getProcessComments(sProcInsId).then(() => {
            return this.getProcessDefinitions(sProcDefId)
          }).then(() => {
            this.getProgressInfo(sProcInsId)
          })
        })
        .catch((error) => {
          console.log(error)
        })
    },
    // 获取审批意见
    getProcessComments(sProcessInstanceId) {
      return new Promise((resolve, reject) => {
        processOri({ processInstanceBizKeys: this.idKeys }).then(res => {
          this.flowList = this.sortKey(res.data, 'sCreateTime')
          resolve()
        }).catch(err => {
          reject(err)
          this.flowList = null
        })
      })
    },
    // 获取审核流程
    getProcessDefinitions(sProcessDefinitionId) {
      return new Promise((resolve, reject) => {
        getList({}, `/wf/process-definitions/taskdef/${sProcessDefinitionId}`).then(res => {
          const flowTree = this.sortArr(res.data)
          flowTree.unshift({ name: this.$t('grid.others.submitApplication'), key: 'start' })
          flowTree.push({ name: this.$t('btns.finish'), key: 'end' })
          this.flowTree = flowTree
          resolve()
        }).catch(err => {
          reject(err)
          this.flowTree = []
        })
      })
    },
    // 获取进度
    getProgressInfo(sProcessInstanceId) {
      return new Promise((resolve, reject) => {
        getList({}, `/wf/tasks/${sProcessInstanceId}/list`).then(res => {
          const data = res.data
          const { currentTasks, process } = data
          if (currentTasks.length) {
            this.flowTree.map((item, index) => {
              if (currentTasks[0].taskDefinitionKey === item.id) {
                this.statusIndex = index
              }
            })
            return
          }
          if (process.status === 'COMPLETED') {
            this.statusIndex = this.flowTree.length - 1
          }
          resolve()
        }).catch(err => {
          reject(err)
          this.statusIndex = 0
        })
      })
    },
    headerStatus(index) {
      if (this.statusIndex === index) {
        return 'finish'
      }
      if (this.statusIndex > index) {
        return 'process'
      } else {
        return 'wait'
      }
    },
    contentStatus(item, index, type) {
      if (item.actionType === 'ROLLBACK' || item.actionType === 'DELETE') {
        return '#ef5f5f'
      }
      if (item.actionType === 'COMPLETED' || item.actionType === 'RESOLVED') {
        return '#7ac757'
      }
      return '#7ac757'
    },
    // 关闭/取消
    handleClose() {
      this.$emit('handleClose')
    },
    // 获取审批说明
    approveDescription(ids) {
      console.log('approveDescription')
      if (!ids) return
      const url = this.url ? `${this.url}/${ids}` : `/sys/wfext/sheet/procdefdesc/get/procinsid/${ids}`
      getList({}, url)
        .then((res) => {
          if (res.data.length > 0) {
            this.explainData = res.data[0].sDescription
          }
        })
        .catch(() => {
          this.explainData = null
        })
    },
    sortKey(array, key) {
      return array.sort(function(a, b) {
        const x = a[key]
        const y = b[key]
        return x < y ? 1 : x > y ? -1 : 0
      })
    },
    sortArr(arr) {
      const b = []
      for (let i = 0; i < arr.length; i++) {
        if (!arr[i].parentId.length) b.push(arr[i])
      }

      for (let i = 0; i < arr.length; i++) {
        for (let j = 0; j < b.length; j++) {
          if (arr[i].parentId[0] === b[j].id) b.push(arr[i])
        }
      }
      return b
    }
  }
}
</script>
<style lang="scss" scoped>
.approvalDialog {
  top: 0px!important;
  left: 0px!important;
  bottom: 0px!important;
  // z-index: 2088;
  .approval-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    .approval-container-steps {
      height: 90px;
    }
    .approval-content {
      height: calc(100% - 30px);
      padding: 10px;
      overflow: auto;
      .approval-content-comment {
        width: 100%;
        word-break: break-all;
        font-size: 12px;
        margin-top: 8px;
      }
    }
    .approval-opinion {
      height: 100%;
    }
    .approval-title {
      height: 30px;
      background: #f9f9f9;
      line-height: 30px;
      padding-left: 10px;
    }
    .approval-btn {
      background: #f9f9f9;
      padding: 10px;
      display: flex;
      justify-content: center;
      align-items: center;
      height: 40px;
    }
  }
  .flex-center {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  ::v-deep .el-timeline-item__tail {
    border-left: 2px solid #f5f5f5;
  }
  ::v-deep .el-timeline-item {
    padding-bottom: 24px;
  }
  ::v-deep .el-timeline-item__content{
    cursor: pointer;
  }
  ::v-deep .el-timeline-item__timestamp.is-bottom {
    margin-top: 8px;
  }
  ::v-deep .el-steps--horizontal {
    .el-step__title {
      font-weight: normal;
      white-space: nowrap;
    }
    .el-step__line {
      background-color: #fff;
      border: 1px dashed #cee5ff;
    }
    .is-finish {
      font-size: 14px;
      color: #3f8ce0 !important;
    }
    .is-process {
      font-size: 14px;
      color: #3f8ce2 !important;
    }
    .is-wait {
      font-size: 14px;
      color: #818181 !important;
    }
    .is-process {
      .el-step__icon {
        background: #cde6ff;
        border-color: #cde6ff;
        color: #fff;
      }
    }
    .is-wait {
      .el-step__icon {
        background: #dddddd;
        border-color: #dddddd;
        color: #fff;
      }
    }
    .is-finish {
      .el-step__icon {
        background: #3f8ce3;
        border-color: #3f8ce3;
        color: #fff;
      }
    }
    .is-rejected {
      .el-step__icon {
        background: #ef5f5f;
        border-color: #ef5f5f;
        color: #fff;
      }
    }
  }
}
</style>
