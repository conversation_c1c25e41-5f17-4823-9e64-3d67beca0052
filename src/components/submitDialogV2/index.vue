<template>
  <cnd-dialog
    v-if="visible"
    :visible="visible"
    :fullscreen="false"
    title="提交审批"
    width="60%"
    height="80px"
    @close="handleClose"
  >
    <template slot="content">
      <div class="submit-container" style="height: 100%">
        <div class="mt-10">
          <el-input
            v-model="remark"
            type="textarea"
            :rows="2"
            :maxlength="500"
            placeholder="请输入报审说明"
          />
        </div>
      </div>
    </template>
    <template slot="footer">
      <el-button size="mini" @click="handleClose">{{ $t('btns.cancel') }}</el-button>
      <el-button type="primary" size="mini" @click="onSure">{{ $t('btns.confirm') }}</el-button>
    </template>
  </cnd-dialog>
</template>
<script>
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      remark: null
    }
  },
  computed: {},
  watch: {
    visible(nv) {
      if (nv) {
        this.remark = null
      }
    }
  },
  methods: {
    handleClose() {
      this.explainData = null
      this.$emit('handleClose')
    },
    onSure() {
      const value = this.remark || ''
      this.$emit('onSure', value)
    }
  }
}
</script>
<style lang="scss" scoped>
  .submit-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
  }
</style>
