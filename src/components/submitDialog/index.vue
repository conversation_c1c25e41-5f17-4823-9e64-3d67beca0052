<template>
  <cnd-dialog
    v-if="visible"
    :visible="visible && explainData"
    :fullscreen="false"
    title="提交审批"
    width="60%"
    height="500px"
    @close="handleClose"
  >
    <template slot="content">
      <div class="submit-container" style="height: 100%">
        <!-- <div class="submit-container-steps">
          <el-scrollbar>
            <el-steps class="mt-20" align-center>
              <el-step
                v-for="(item, index) in flowTree"
                :key="item.name"
                :title="item.name"
                :status="headerStatus(index)"
              />
            </el-steps>
          </el-scrollbar>
        </div> -->
        <div :style="hiddenDesc? 'height: 100%' : 'height: calc(100% - 65px)'">
          <el-col class="border" style="height: 100%">
            <div class="border-bottom submit-title">{{ $t('grid.others.approvalInstructions') }}</div>
            <div v-if="explainData" class="submit-content" style="height: calc(100% - 30px)">
              <div v-html="explainData" />
              <div v-if="explainDataRemark" v-html="explainDataRemark" />
            </div>
            <div v-else class="submit-content-null" style="height: calc(100% - 30px)">
              暂无审批说明
            </div>
          </el-col>
        </div>
        <div v-show="!hiddenDesc" class="mt-10">
          <el-input
            v-model="remark"
            :disabled="disabledRemark"
            type="textarea"
            :rows="2"
            placeholder="请输入报审说明"
          />
        </div>
      </div>
    </template>
    <template slot="footer">
      <el-button size="mini" @click="handleClose">{{ $t('btns.cancel') }}</el-button>
      <el-button type="primary" size="mini" @click="onSure">{{ $t('btns.confirm') }}</el-button>
    </template>
  </cnd-dialog>
</template>
<script>
import {
  postList
  // processOri,
  // getList
} from '@/api/contract'
export default {
  props: {
    // 弹窗显隐
    visible: {
      type: Boolean,
      default: false
    },
    id: {
      type: String,
      default: ''
    },
    submitDesc: {
      type: String,
      default: null
    },
    apiUrl: {
      type: String,
      required: true
    },
    hiddenDesc: {
      type: Boolean,
      default: false
    },
    targetRemark: {
      type: String,
      default: ''
    },
    disabledRemark: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      flowTree: [
        { name: '提交申请' },
        { name: '完成' }
      ],
      explainData: null,
      statusIndex: 0,
      remark: null,
      explainDataRemark: null
    }
  },
  computed: {},
  watch: {
    visible(nv, ov) {
      if (nv) {
        this.getProgress()
      } else {
        this.explainData = null
        this.statusIndex = 0
        this.remark = this.targetRemark || null
        this.explainDataRemark = null
        // this.flowTree = []
      }
    }
  },
  methods: {
    // 关闭/取消
    handleClose() {
      this.explainData = null
      this.$emit('handleClose')
    },
    onSure() {
      const value = this.remark || ''
      this.$emit('onSure', value)
    },
    headerStatus(index) {
      if (this.statusIndex === index) {
        return 'finish'
      }
      if (this.statusIndex > index) {
        return 'process'
      } else {
        return 'wait'
      }
    },
    getProgress() {
      this.approveDescription()
      // // 获取key
      // getList({}, `sys/wfext/sheet/prodext/get/business/${this.id}`)
      //   .then((res) => {
      //     const data = res.data || {}
      //     const { sProcessInstanceId = '' } = data
      //     if (!sProcessInstanceId) return
      //     // 审批说明
      //     this.approveDescription(sProcessInstanceId)
      //   })
      //   .catch((error) => {
      //     console.log(error)
      //   })
    },
    // 获取审批说明
    approveDescription() {
      this.remark = this.targetRemark || null
      const url = this.apiUrl
      postList({}, url)
        .then((res) => {
          this.explainData = res.data
          if (this.submitDesc) {
            this.explainDataRemark = `<h3>***【运费付款提醒】${this.submitDesc}</h3>`
          }
        })
        .catch(() => {
          this.handleClose()
        })
    }
  }
}
</script>
<style lang="scss" scoped>
  .submit-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    .submit-container-steps {
      height: 90px;
    }
    .submit-content {
      // height: 310px;
      padding: 10px;
      overflow: auto;
      .submit-content-comment {
        width: 100%;
        word-break: break-all;
        font-size: 12px;
        margin-top: 8px;
      }
    }
    .submit-content-null{
        // height: 310px;
        padding: 10px;
        overflow: hidden;
        line-height: 300px;
        text-align: center;
      }
    .submit-opinion {
      height: 100%;
    }
    .submit-title {
      height: 30px;
      background: #f9f9f9;
      line-height: 30px;
      padding-left: 10px;
    }
    .submit-btn {
      background: #f9f9f9;
      padding: 10px;
      display: flex;
      justify-content: center;
      align-items: center;
      height: 40px;
    }
  }
  .flex-center {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  ::v-deep .el-timeline-item__tail {
    border-left: 2px solid #f5f5f5;
  }
  ::v-deep .el-timeline-item {
    padding-bottom: 24px;
  }
  ::v-deep .el-timeline-item__timestamp.is-bottom {
    margin-top: 8px;
  }
  ::v-deep .el-steps--horizontal {
    .el-step__title {
      font-weight: normal;
      white-space: nowrap;
    }
    .el-step__line {
      background-color: #fff;
      border: 1px dashed #cee5ff;
    }
    .is-finish {
      font-size: 14px;
      color: #3f8ce0 !important;
    }
    .is-process {
      font-size: 14px;
      color: #3f8ce2 !important;
    }
    .is-wait {
      font-size: 14px;
      color: #818181 !important;
    }
    .is-process {
      .el-step__icon {
        background: #cde6ff;
        border-color: #cde6ff;
        color: #fff;
      }
    }
    .is-wait {
      .el-step__icon {
        background: #dddddd;
        border-color: #dddddd;
        color: #fff;
      }
    }
    .is-finish {
      .el-step__icon {
        background: #3f8ce3;
        border-color: #3f8ce3;
        color: #fff;
      }
    }
    .is-rejected {
      .el-step__icon {
        background: #ef5f5f;
        border-color: #ef5f5f;
        color: #fff;
      }
    }
  }
</style>
