<template>
  <div>
    <el-select v-model="value" size="mini" clearable :placeholder="$t('components.pleaseSelect')">
      <el-option
        v-for="item in options"
        :key="item[skey]"
        :label="item[sName]"
        :value="item[skey]"
      />
    </el-select>
  </div>
</template>

<script>
export default {
  name: 'TableSelect',
  data() {
    return {
      value: null,
      aftKey: 'null',
      sName: 'sCodeName',
      skey: 'sCodeValue',
      options: [{
        value: '1',
        label: '张三'
      }, {
        value: '2',
        label: '李四'
      }]
    }
  },
  mounted() {
    this.$nextTick(() => {
      if (this.$refs.input) {
        this.$refs.input.focus()
      }
    })
  },
  beforeMount() {
    const value = this.params.value
    const { options, defaultVal, key, sName, skey } = this.params.colDef.filterList()
    this.aftKey = key
    if (sName) this.sName = sName
    if (skey) this.skey = skey
    this.options = options
    if (value === undefined || value === null) {
      this.value = defaultVal
    } else {
      this.value = value
    }
  },
  methods: {
    getValue() {
      // var name
      // for (var i in this.options) {
      //   if (this.options[i][this.skey] === this.value) {
      //     name = this.options[i][this.sName]
      //   }
      // }
      // this.params.data[this.aftKey] = this.value
      return this.value
    }
  }
}
</script>

<style scoped></style>
