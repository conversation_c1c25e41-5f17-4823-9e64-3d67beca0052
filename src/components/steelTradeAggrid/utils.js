function forEach(array, iteratee) {
  let index = -1
  const length = array.length
  while (++index < length) {
    iteratee(array[index], index)
  }
  return array
}

export function deepClone(target, map = new WeakMap()) {
  if (typeof target === 'object') {
    const isArray = Array.isArray(target)
    const cloneTarget = isArray ? [] : {}

    if (map.get(target) || !target) {
      return target
    }
    map.set(target, cloneTarget)

    const keys = isArray ? undefined : Object.keys(target)
    forEach(keys || target, (value, key) => {
      if (keys) {
        key = value
      }
      cloneTarget[key] = deepClone(target[key], map)
    })

    return cloneTarget
  } else {
    return target
  }
}

// 防抖
let timeout = null
export function debounce(fn, delay) {
  return function(e) {
    // 每当用户输入的时候把前一个 setTimeout clear 掉
    timeout && clearTimeout(timeout)
    // 然后又创建一个新的 setTimeout, 这样就能保证interval 间隔内如果时间持续触发，就不会执行 fn 函数
    timeout = setTimeout(() => {
      fn.apply(this, arguments)
    }, delay)
  }
}
