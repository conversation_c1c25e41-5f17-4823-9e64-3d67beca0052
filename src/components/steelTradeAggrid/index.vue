<!--
    @component steelTradeAggrid 钢贸aggrid表格组件
    @props
        {Array} columnDefs - 主表列配置  []
        {Array} rowData - 表格数据 []
        {String} tableSelection 选择模式 null
        {Boolean} autoLoadData 表格加载完成第一次自动加载数据
        {String} rowKey 行唯一键值(例如sId) 必填
        {Boolean} isSubtable 是否主子表 false
        {Array} childColumnDefs 子表列配置 []
        {String} childrenListKey 子表数据存在的key '_details'
        {String} childRowKey 子表键值 若拥有子表必填 ''
        {Function} loadDetail 懒加载子表方法 需要返回promise {}
        {Array} headerTotal 头部合计数组 具体参考本文件夹gridTotal的countList参数
        {Array} footerTotal 底部合计数组 具体参考本文件夹gridTotal的countList参数
        {Funciton} handleDetailCount 子表合计方法 入参{detail object}详情对象 需要返回 Array
          [{ count: 1,key:'count' },
          { title: '已到票未到货数量', count: 3, unit: this.$t('grid.others.ton') },
          { title: this.$t('grid.title.amount'), count: 3, unit: this.$t('grid.others.yuan') }]
        {String} fullRowType 全行编辑配置 null 可配置all parent child
        {Boolean} enableFilter 是否允许筛选
    @events
        {Object} rowClicked 主表点击行回调
            @param {Object} 行以及aggrid源生返回数据
        {Object} rowDoubleClicked 主表双击行回调
            @param {Object} 行以及aggrid源生返回数据
        {Object} childRowClicked 子表点击行回调
            @param {Object} 行以及aggrid源生返回数据
        {Object} childRowDoubleClicked 子表双击行回调
            @param {Object} 行以及aggrid源生返回数据
        {Object} selectedChange rowData watch
            @param {Array} 选中数据
    @methods
        {Function} clearSelection 清空选中
        {Function} refreshTable 刷新表格
        {Function} pageChange 调用传入{page: 0, limit: 20}即初始化分页重新获取
        {Function} getSelectedData 调用返回表格选中数据 若传入margeChild则返回所选所有子级的合集
        {Function} stopChildEditing 取消所有子表编辑状态
    <AUTHOR>
    @date 2020-12-15 11:00:25
    @update 2021-02-01 17:07:20
-->
<template>
  <div v-if="gridShow" ref="stGrid" class="steel-trade-aggrid" :style="gridStyle">
    <div
      v-if="$slots.header || headerTotal"
      class="steel-trade-aggrid-row steel-trade-aggrid-header"
    >
      <slot
        v-if="$slots.header"
        name="header"
      />
      <gridTotal
        v-else
        flag="header"
        :count-list="headerTotal"
      />
    </div>
    <ag-grid-vue
      :class="['table', 'ag-theme-balham', 'grid-class', 'master-detail', scrollStyle]"
      style="height: 100%"
      :grid-options="gridOptions"
      :column-defs="gridColumnDefs"
      :get-row-style="getRowStyle"
      :row-data="rowData"
      :modules="AllModules"
      :detail-row-auto-height="isSubtable"
      :row-group-panel-show="rowGroupPanelShow"
      @grid-ready="onGridReady"
      @selection-changed="handlSelectionChange"
      @paste-end="onPasteEnd"
      @sortChanged="handleSortChanged"
      @bodyScroll="agBodyScroll"
    />
    <div v-if="$slots.footer || footerTotal" class="steel-trade-aggrid-row steel-trade-aggrid-footer">
      <slot
        v-if="$slots.footer"
        name="footer"
      />
      <gridTotal
        v-else
        flag="footer"
        :count-list="footerTotal"
      />
    </div>
    <cnd-pagination
      v-if="paginationinif"
      :total="pagination.total"
      :page="pagination.page"
      :page-sizes="pageSizes"
      :limit="pagination.limit"
      :event="pageChange"
    />
  </div>
</template>

<script>
import Vue from 'vue'
import { AgGridVue } from '@ag-grid-community/vue'
import { AllModules } from '@ag-grid-enterprise/all-modules'
// import { AllModules } from 'cndinfo-ui/src/utils/agGridModules'

import { AG_GRID_LOCALE_CN } from 'cndinfo-ui/lib/lang/ag-grid.zh.js'
import childSelect from '@/components/aggridExtend/childSelect'
import headerSelect from '@/components/aggridExtend/headerSelect'
import parentSelect from '@/components/aggridExtend/parentSelect'
import headerDefSelect from '@/components/aggridExtend/headerDefSelect'
import defSelect from '@/components/aggridExtend/defSelect'
import customHeaderComponent from '@/components/aggridExtend/customHeaderComponent'
import TableInput from '@/components/TableInput.vue'
import TableTextarea from '@/components/TableTextarea.vue'
import AgPopover from '@/components/agComponent/AgPopover.vue'
import gridTotal from './gridTotal'

import { handleGridColumnDefs, handleGridChildColumnDefs } from './columnDefs'
const DEFAULT_PAGE_FIRST = 0
const DEFAULT_PAGE_SIZE = 30

import {
  deepClone,
  debounce
} from './utils.js'

export default {
  name: 'SteelTradeAggrid',
  components: { AgGridVue, gridTotal },
  props: {
    // 是否需要分页
    paginationinif: {
      type: Boolean,
      default: true
    },
    // heightinif 表格高度
    heightinif: {
      type: [Number, String],
      default: 0
    },
    autoHeight: {
      type: Boolean,
      default: false
    },
    // autoHeight为true时，minHeight生效
    minHeight: {
      type: [Number, String],
      default: 100
    },
    // 主标列配置
    columnDefs: {
      type: Array,
      default: () => []
    },
    // 加行样式
    getRowStyle: {
      type: Function,
      default: null
    },
    // 表格数据
    rowData: {
      type: Array,
      default: () => []
    },
    // 选择模式
    tableSelection: {
      type: String,
      default: null,
      validator: function(value) {
        return ['single', 'multiple', null].indexOf(value) !== -1
      }
    },
    // 自适应
    isSizeColumnsToFit: {
      type: Boolean,
      default: false
    },
    // 自动加载数据
    autoLoadData: {
      type: Boolean,
      default: true
    },
    // 行唯一键值
    rowKey: {
      type: String,
      required: true
    },
    // 加载事件用于翻页
    loadData: {
      type: Function,
      default: null
    },
    // 是否主子表
    isSubtable: {
      type: Boolean,
      default: false
    },
    // 子表列配置
    childColumnDefs: {
      type: Array,
      default: () => []
    },
    // 子表数据存放属性
    childrenListKey: {
      type: String,
      default: '_details'
    },
    // 子表键值
    childRowKey: {
      type: String,
      default: ''
    },
    // 懒加载子表方法
    loadDetail: {
      type: Function,
      default: null
    },
    handleDetailCount: {
      type: [Function, Object],
      default: null
    },

    // 头部合计
    headerTotal: {
      type: Array,
      default: null
    },
    // 底部合计
    footerTotal: {
      type: Array,
      default: null
    },
    // 是否全行编辑 all parent child
    fullRowType: {
      type: String,
      default: null
    },
    // 是否禁用黏贴 all parent child
    disablePaste: {
      type: String,
      default: null
    },
    showHeaderSelect: {
      type: Boolean,
      default: true
    },
    showChildSelect: {
      type: Boolean,
      default: true
    },
    openAfterFilter: {
      type: Boolean,
      default: false
    },
    additionalOptions: {
      type: Object,
      default: () => {}
    },
    pageSizes: {
      type: Array,
      default: () => [10, 20, 30, 50, 100, 200]
    },
    defaultPageSize: {
      type: Number,
      default: DEFAULT_PAGE_SIZE
    },
    // 是否可以进行拖拽分组
    rowGroupPanelShow: {
      type: String,
      default: 'never'
    },
    frameworkComponents: {
      type: Object,
      default: () => ({})
    },
    menuId: { // 用于存储列状态
      type: String,
      required: true
    },
    // 是否允许筛选
    enableFilter: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      AllModules,
      gridShow: true,
      gridApi: null,
      columnApi: null,
      gridOptions: null,
      gridColumnDefs: [],
      propsChildColumnDefs: this.childColumnDefs,
      childColumnDefs_: [],
      columnWidth: 0,
      gridHeight: 0,
      pagination: {
        total: 0,
        page: DEFAULT_PAGE_FIRST,
        limit: this.defaultPageSize
      },
      onHeaderDefSelect: (selected) => {
        this.$emit('headerSelected', selected)
      },
      onDefSelect: (params, selected) => {
        this.$emit('rowSelected', params, selected)
      },
      isEditCell: false,
      isEditChildCell: false,
      filterModel: null
    }
  },
  computed: {
    gridStyle() {
      const style = {}
      if (typeof this.gridHeight === 'string' && this.gridHeight.includes('vh')) {
        style.height = this.gridHeight
        return style
      }
      const gridHeight = +this.gridHeight
      if (gridHeight > 0) {
        style.height = `${gridHeight}px`
      }
      return style
    },
    scrollStyle() {
      let style
      const ua = navigator.userAgent.toLowerCase()
      const testUa = regexp => regexp.test(ua)
      if (testUa(/windows|win32|win64|wow32|wow64/g)) {
        style = 'scroll-style' // windows系统
      }
      return style
    }
  },
  watch: {
    fullRowType(newVal, oldVal) {
      this.gridShow = false
      this.initGridOptions()
      this.$nextTick(() => {
        this.gridShow = true
      })
    },
    rowData: {
      deep: true,
      handler(newValue) {
        this.$nextTick(() => {
          const filterData = this.openAfterFilter ? this.getAfterFilterData() : newValue
          const selectedData = filterData.filter(item => item._selected)
          this.$emit('selectedChange', filterData, selectedData)
          if (filterData.length <= 1) {
            this.handleGridHeight(filterData.length)
          }
          this.handlePinnedBottomRowData(filterData)
        })
      }
    }
  },
  created() {
    if (!this.autoHeight) {
      this.gridHeight = this.heightinif
    }
    this.initGridOptions()
  },
  beforeDestroy() {
    if (this.gridApi) {
      this.gridApi.removeEventListener('columnMoved', this.saveColumnState)
      this.gridApi.removeEventListener('columnPinned', this.saveColumnState)
      this.gridApi.removeEventListener('columnResized', this.saveColumnState)
    }
  },
  methods: {
    handleSortChanged() {},
    unique(arr) {
      const set = arr.filter((item, index) => {
        return arr.indexOf(item) === index
      })
      return set
    },
    initGridOptions() {
      let columnCount = 0
      let fillWidth = 0
      let _fill = 0
      const _this = this
      const fullRowType = this.fullRowType
      const disablePaste = this.disablePaste
      const hasSecMeter = this.columnDefs.some(item => item.children)
      const gridOptions = {
        rowSelection: this.tableSelection,
        suppressRowClickSelection: true,
        enableRangeSelection: true,
        // stopEditingWhenGridLosesFocus: true,
        enableFillHandle: true,
        suppressMultiRangeSelection: true,
        suppressClipboardPaste: !!((disablePaste === 'all' || disablePaste === 'parent')),
        embedFullWidthRows: true,
        frameworkComponents: {
          TableInput: Vue.extend(TableInput),
          TableTextarea: Vue.extend(TableTextarea),
          AgPopover: Vue.extend(AgPopover),
          headerSelect: Vue.extend(headerSelect),
          parentSelect: Vue.extend(parentSelect),
          headerDefSelect: Vue.extend(headerDefSelect),
          defSelect: Vue.extend(defSelect),
          customHeaderComponent: Vue.extend(customHeaderComponent),
          ...this.frameworkComponents
        },
        context: {
          componentParent: this,
          rowKey: this.rowKey,
          childRowKey: this.childRowKey,
          childrenListKey: this.childrenListKey,
          tableSelection: this.tableSelection,
          onHeaderDefSelect: this.onHeaderDefSelect,
          onDefSelect: this.onDefSelect,
          loadDetail: this.loadDetail,
          showHeaderSelect: this.showHeaderSelect
        },
        defaultColDef: {
          filter: this.enableFilter,
          sortable: true,
          resizable: true
        },
        editType: fullRowType === 'all' || fullRowType === 'parent' ? 'fullRow' : null,
        overlayLoadingTemplate: '<span class="ag-overlay-loading-center">数据加载中...</span>',
        overlayNoRowsTemplate: `<span class="ag-overlay-no-rows-wrapper">${this.$t('grid.others.noData')}</span>`,
        localeText: AG_GRID_LOCALE_CN,
        masterDetail: this.isSubtable,
        // * *************设置固定行样式**********
        // 公共样式已统一配置
        // getRowStyle: (params) => {
        //   if (params.node.rowPinned) {
        //     return { 'color': '#3e8ddc', 'border-top': '2px solid #3e8ddc !important', 'font-weight': '600', 'border-bottom': '0' }
        //   }
        // },
        isRowMaster: dataItem => {
          const hasLoadDetail = typeof this.loadDetail === 'function'
          return this.isSubtable && dataItem[this.childrenListKey] && dataItem[this.childrenListKey].length || hasLoadDetail
        },
        onRowClicked: params => { // 单击行
          this.stopChildEditing()
          params._isEditCell = this.isEditCell
          this.$emit('rowClicked', params)
        },
        onCellClicked: params => { // 单击单元格
          const isTrue = (value) => {
            try {
              if (typeof value === 'function') {
                return value(params)
              } else {
                return value
              }
            } catch (error) {
              return value
            }
          }
          this.isEditCell = isTrue(params?.colDef?.editable)
          this.$emit('cellClicked', params)
        },
        onRowDoubleClicked: params => { // 双击行
          params._isEditCell = this.isEditCell
          const el = params.event.path ? params.event.path[0] : null
          if (el && el.className.includes('ag-icon')) { return }
          this.$emit('rowDoubleClicked', params)
        },
        onCellValueChanged: params => { // 单元格编辑
          this.refreshTable()
          this.$emit('cellValueChanged', params)
        },
        onCellEditingStopped: params => { // 单元格编辑完成
          this.$emit('cellEditingStopped', params)
        },
        onRowValueChanged: params => { // 行编辑
          this.$emit('rowValueChanged', params)
        },
        onRowEditingStopped: params => { // 行编辑完成
          this.$emit('rowEditingStopped', params)
        },
        onFilterChanged: () => {
          // this.filterModel = this.gridApi.getFilterModel()
          const filterData = this.openAfterFilter ? this.getAfterFilterData() : this.rowData
          this.$emit('selectedChange', filterData, filterData.filter(item => item._selected))
        },
        onRowGroupOpened: params => { // 行组打开事件
          params.expanded ? columnCount++ : columnCount--
          if (_fill === 0) {
            fillWidth = params.columnApi.getColumnState()[params.columnApi.getColumnState().length - 1].width
            _fill = 1
          }
          columnCount === 0 && gridOptions.columnApi.setColumnWidth('fill', fillWidth, true)
          this.$emit('rowGroupOpened', params)
        },
        onColumnResized: params => {
          if (params.source === 'uiColumnDragged' && params.finished) {
            gridOptions.api.forEachNode(node => {
              const detailNode = node.detailNode
              detailNode && detailNode.detailGridInfo && detailNode.detailGridInfo.api.sizeColumnsToFit()
              if (detailNode && detailNode.detailGridInfo) {
                detailNode.detailGridInfo.columnApi.getColumnState().filter((item, index) => {
                  _this.propsChildColumnDefs[index].width = item.width
                })
              }
            })
          }
        },
        ...this.additionalOptions,
        onRangeSelectionChanged: debounce(params => {
          if (this.tableSelection === 'single') return
          const cellRanges = params.api.getCellRanges()
          if (cellRanges.length === 0) {
            return
          }
          const api = params.api
          const startIndex = cellRanges[0].startRow.rowIndex
          const endIndex = cellRanges[0].endRow.rowIndex
          const startRow = Math.min(startIndex, endIndex)
          const endRow = Math.max(startIndex, endIndex)
          // 是否正序拖动
          let startRowState = false
          api.forEachNode(node => {
            if (startIndex === node.rowIndex) {
              startRowState = node.data?._selected
            }
          })
          const { childrenListKey, childRowKey } = this
          if (startRow !== endRow && cellRanges[0].columns.length === 1 && cellRanges[0].columns[0].colDef.headerName === 'checkbox') {
            api.forEachNode(async(node) => {
              if (startRow <= node.rowIndex && node.rowIndex <= endRow) {
                node.data._selected = !startRowState
                // 开始滑动勾选之后，要不要加载子表
                if (typeof this.loadDetail === 'function' && !node.data[childrenListKey].length) {
                  node.data[childrenListKey] = await this.loadDetail(node.data)
                }
                if (node.data[childrenListKey]?.length > 0) {
                  node.data._selectedKeys = !startRowState ? node.data[childrenListKey].map(item => item[childRowKey]) : []
                  const rowNode = api.getDetailGridInfo(`detail_${node.id}`)
                  rowNode && rowNode.api.refreshCells({ force: true })
                }
              }
            })
            api.clearRangeSelection()
            api.refreshCells({ force: true })
          }
        }, 100)
      }
      this.gridColumnDefs = handleGridColumnDefs(this.isSubtable, hasSecMeter, this.tableSelection, this.columnDefs)
      if (this.isSubtable) {
        gridOptions.masterDetail = true
        gridOptions.detailCellRendererParams = {
          detailGridOptions: {
            rowSelection: 'multiple',
            suppressRowClickSelection: true,
            enableRangeSelection: true,
            groupSelectsChildren: true,
            // stopEditingWhenGridLosesFocus: true,
            enableFillHandle: true,
            suppressMultiRangeSelection: true,
            suppressClipboardPaste: !!(disablePaste === 'all' || disablePaste === 'child'),
            suppressHorizontalScroll: true,
            frameworkComponents: {
              TableInput: Vue.extend(TableInput),
              childSelect: Vue.extend(childSelect)
            },
            columnDefs: handleGridChildColumnDefs(hasSecMeter, this.tableSelection, this.showChildSelect, this.propsChildColumnDefs),
            context: gridOptions.context,
            defaultColDef: {
              filter: true,
              sortable: true,
              resizable: true
              // suppressMenu: true // 子表不显示头部菜单
            },
            editType: fullRowType === 'all' || fullRowType === 'child' ? 'fullRow' : null,
            overlayLoadingTemplate: '<span class="ag-overlay-loading-center">数据加载中...</span>',
            overlayNoRowsTemplate: `<span class="ag-overlay-no-rows-wrapper">${this.$t('grid.others.noData')}</span>`,
            localeText: AG_GRID_LOCALE_CN,
            onRowClicked: params => { // 单击行
              params._isEditCell = this.isEditChildCell
              this.$emit('childRowClicked', params)
            },
            onPasteEnd: params => {
              this.$emit('childPasteEnd', params)
            },
            onCellClicked: params => { // 单击单元格
              const isTrue = (value) => {
                try {
                  if (typeof value === 'function') {
                    return value(params)
                  } else {
                    return value
                  }
                } catch (error) {
                  return value
                }
              }
              this.isEditChildCell = isTrue(params?.colDef?.editable)
              this.$emit('childCellClicked', params)
            },
            onRowDoubleClicked: params => {
              this.$emit('childRowDoubleClicked', params)
            },
            onCellEditingStarted: params => {
              // 清空其他子级编辑 全行编辑 不执行
              if (!(fullRowType === 'all' || fullRowType === 'child')) {
                _this.stopChildEditing(params.data.parentId)
              }
            },
            onCellEditingStopped: params => {
              this.$emit('childCellEditingStopped', params)
            },
            onCellValueChanged: params => {
              this.refreshTable()
              this.$emit('childCellValueChanged', params)
            },
            onRowEditingStarted: params => {
              // 清空其他子级编辑
              _this.stopChildEditing(params.data.parentId)
            },
            onRowValueChanged: params => {
              this.$emit('childRowValueChanged', params)
            },
            onRowEditingStopped: params => {
              this.$emit('rowEditingStopped', params)
            },
            onGridReady: function(params) {
              columnCount === 1 && setTimeout(() => {
                _this.columnWidth = fillWidth + params.columnApi.columnController.bodyWidth - params.columnApi.columnController.scrollWidth
                gridOptions.columnApi.setColumnWidth('fill', _this.columnWidth, true)
                params.columnApi.getColumnState().filter((item, index) => {
                  _this.propsChildColumnDefs[index] && (_this.propsChildColumnDefs[index].width = params.columnApi.getColumnState()[index + 1].width)
                })
              }, 300)
            },
            onColumnResized: function(params) {
              if (params.source === 'uiColumnDragged' && params.finished) {
                _this.columnWidth = _this.columnWidth + params.columnApi.columnController.bodyWidth - params.columnApi.columnController.scrollWidth
                gridOptions.columnApi.setColumnWidth('fill', _this.columnWidth, true)
                gridOptions.api.forEachNode(node => {
                  const detailNode = node.detailNode
                  detailNode && detailNode.detailGridInfo && detailNode.detailGridInfo.columnApi.setColumnWidth(params.columns[0].colId, params.columns[0].actualWidth, true)
                })
                _this.childColumnDefs_.filter((item, index) => {
                  if (item.field === params.columns[0].colId) {
                    _this.childColumnDefs_[index].width = params.columns[0].actualWidth
                  }
                })
              }
            },
            onRangeSelectionChanged: debounce(params => {
              const cellRanges = params.api.getCellRanges()
              if (cellRanges.length === 0) {
                return
              }
              const api = params.api
              const startIndex = cellRanges[0].startRow.rowIndex
              const endIndex = cellRanges[0].endRow.rowIndex
              const startRow = Math.min(startIndex, endIndex)
              const endRow = Math.max(startIndex, endIndex)
              let startRowState = false
              api.forEachNode(node => {
                if (startIndex === node.rowIndex) {
                  startRowState = node.data._selected
                }
              })
              const { childrenListKey, childRowKey } = this
              if (startRow !== endRow && cellRanges[0].columns.length === 1 && cellRanges[0].columns[0].colId === 'checkbox') {
                api.forEachNode(node => {
                  if (startRow <= node.rowIndex && node.rowIndex <= endRow) {
                    const rowNode = gridOptions.api.getRowNode(node.data.parentId)
                    if (!startRowState) {
                      node.data._selected = true
                      rowNode.data._selectedKeys.push(node.data[childRowKey])
                      rowNode.data._selectedKeys = this.unique(rowNode.data._selectedKeys)
                      if (rowNode.data._selectedKeys.length === rowNode.data[childrenListKey].length) {
                        rowNode.data._selected = true
                      }
                    } else {
                      node.data._selected = false
                      rowNode.data._selectedKeys = rowNode.data._selectedKeys.filter(el => el !== node.data[childRowKey])
                    }
                  }
                })
                api.clearRangeSelection()
                api.refreshCells({ force: true })
                gridOptions.api.refreshCells({ force: true })
              }
            }, 100)
          },
          getDetailRowData: async(params) => { // 获取子级数据
            const { childrenListKey, childRowKey } = this
            if (typeof this.loadDetail === 'function' && !params.data[childrenListKey].length) {
              params.data[childrenListKey] = await this.loadDetail(params.data)
            }

            if (params.data[childrenListKey]) {
              const callRecords = params.data[childrenListKey].map((item, index) => {
                return {
                  ...item,
                  parentId: params.node.parent.id,
                  _index: index
                }
              })
              if (params.data._selected) {
                params.data._selectedKeys = params.data[childrenListKey].map(item => item[childRowKey])
              }
              params.successCallback(callRecords)
            } else {
              params.successCallback([])
            }
          }
        }
      }

      this.gridOptions = gridOptions
    },
    handlePinnedBottomRowData(newValue) {
      if (typeof this.handleDetailCount === 'function') {
        newValue.forEach((item, index) => {
          const rowNode = this.gridApi.getDetailGridInfo(`detail_${index}`)
          if (item._selectedKeys.length > 0) {
            const childSelectedList = []
            for (const childRowKey of item._selectedKeys) {
              const selectedItem = item[this.childrenListKey].find(el => {
                return el[this.childRowKey] === childRowKey
              })
              childSelectedList.push(selectedItem)
            }
            rowNode && rowNode.api.setPinnedBottomRowData([{ _childCount: this.handleDetailCount(childSelectedList) }])
          } else {
            rowNode && rowNode.api.setPinnedBottomRowData([])
          }
        })
      }
    },
    handlSelectionChange() {
      const selectedNodes = this.gridApi.getSelectedNodes()
      const selectedKeys = selectedNodes.map(item => item.data[this.rowKey])
      this.rowData.forEach(item => {
        if (selectedKeys.includes(item[this.rowKey])) {
          item._selected = true
        } else {
          item._selected = false
        }
      })
    },
    onPasteEnd(params) {
      this.$emit('onPasteEnd', params)
    },
    initDataSelectState(arr) {
      return arr.map(item => {
        item._selected = false
        item._selectedKeys = []
        return item
      })
    },
    getAfterFilterData() {
      const list = []
      this.gridApi.forEachNodeAfterFilterAndSort((node, index) => {
        if (node.data) {
          list.push(node.data)
        }
      })
      return list
    },
    getSelectedData(func, type = null) {
      setTimeout(() => {
        const filterData = this.openAfterFilter ? this.getAfterFilterData() : this.rowData
        const filterSelectedData = deepClone(filterData).filter(item => {
          return (item._selected || (item._selectedKeys ? item._selectedKeys.length : false)) &&
          (this.childRowKey ? !!(item[this.childrenListKey] && item[this.childrenListKey].length) : true)
        })

        if (this.childRowKey) {
          // 过滤子级
          const rowData = filterSelectedData.map(item => {
            item[this.childrenListKey] = item[this.childrenListKey]
              .filter(child => item._selectedKeys.includes(child[this.childRowKey]))
            return item
          })
          // 返回子级合集
          if (type === 'margeChild') {
            return func(this.initDataSelectState(rowData.reduce((prev, next) => {
              return prev.concat(next[this.childrenListKey])
            }, [])))
          }
          if (type === 'delete') {
            const masterData = []
            const childData = []

            deepClone(filterData).forEach(item => {
              if (item._selected) {
                masterData.push(item)
              } else if (item._selectedKeys ? item._selectedKeys.length : false) {
                childData.push(item)
              }
            })

            return func({
              masterData: masterData,
              childData: childData
            })
          }

          return func(this.initDataSelectState(rowData))
        }

        func(this.tableSelection === 'single'
          ? this.initDataSelectState(filterSelectedData)[0]
          : this.initDataSelectState(filterSelectedData))
      }, 0)
    },
    // 清空选中
    clearSelection() {
      this.rowData.forEach(item => {
        item._selected = false
        item._selectedKeys = []
      })
      this.refreshTable()
    },
    handleGridHeight(dataLength) {
      // autoHeight=true
      // 1、高度为自适应
      // 2、没数据100
      // 3、有高度 高于15条设为高度 heightinif
      const gridHeight = this.heightinif
      const gridMinHeight = this.minHeight
      const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
      if (this.autoHeight || isMobile) {
        if (+dataLength === 0) {
          const headerHeight = this.headerTotal ? 34 : 0
          const footerHeight = this.footerTotal ? 32 : 0
          const paginationHeight = this.paginationinif ? 34 : 0
          this.gridHeight = gridMinHeight + headerHeight + footerHeight + paginationHeight
          this.gridApi.setDomLayout()
        } else if (+dataLength >= 15 && gridHeight) {
          this.gridHeight = gridHeight
        } else {
          if (isMobile) {
            this.gridHeight = screen.height
            this.gridApi.setDomLayout()
          } else {
            this.gridHeight = 0
            this.$refs.stGrid.style.height = 'auto'
            this.gridApi.setDomLayout('autoHeight')
          }
        }
      }
    },
    // 加载表格数据 不传参数从第一页
    loadTableData(pagination = {
      page: DEFAULT_PAGE_FIRST,
      limit: this.pagination.limit
    }) {
      if (typeof this.loadData === 'function') {
        this.gridApi && this.gridApi.showLoadingOverlay()
        this.loadData(pagination).then((data, heightCount) => {
          let total = 0
          if (typeof (data) === 'number') {
            total = data
          } else if (data && data.totalElements) {
            total = data.totalElements
          } else if (data && data.length) {
            total = data.length
          }
          if (total === 0) {
            this.gridApi.showNoRowsOverlay()
          }
          this.handleGridHeight(heightCount || total)
          this.pagination.total = total
          this.pagination.page = pagination.page
          this.pagination.limit = pagination.limit

          /**
           * 当表格删除最后一页的数据，该页码的最后一条都已经被删除，导致该页码超过表格的最大页码，会使列表出现空数据的bug
           * 处理：将该页码改为当前表格的最大页码
           */
          // if (total > 0) {
          //   const maxPage = Math.ceil(total / this.pagination.limit)
          //   const currpage = (this.pagination.page + 1)
          //   if (currpage > maxPage) {
          //     this.pageChange({
          //       page: maxPage - 1 > 0 ? maxPage - 1 : 0,
          //       limit: this.pagination.limit
          //     })
          //   }
          // }

          // 再次查询之后，是否应用原有筛选条件
          // if (this.openAfterFilter && this.filterModel) {
          //   this.$nextTick(() => {
          //     this.gridApi.setFilterModel(this.filterModel)
          //   })
          // }
        }).catch(() => {
          this.handleGridHeight(0)
          this.gridApi.showNoRowsOverlay()
          console.error(`请检查loadData`)
        })
      }
    },
    // 重新加载
    reloadTableData() {
      this.loadTableData(this.pagination)
    },
    // 刷新aggrid表格
    refreshTable() {
      this.gridApi.forEachNode(node => {
        const detailNode = node.detailNode
        detailNode && detailNode.detailGridInfo && detailNode.detailGridInfo.api.refreshCells({ force: true })
      })
      this.gridApi.refreshCells({ force: true })
    },
    // 取消所有子表编辑状态
    stopChildEditing(parentId = null) {
      this.gridApi.forEachNode(node => {
        const detailNode = node.detailNode
        if (node.id !== parentId) {
          detailNode && detailNode.detailGridInfo && detailNode.detailGridInfo.api.stopEditing()
        }
      })
    },
    setColumnDefs(columnDefs) {
      this.gridColumnDefs = []
      const hasSecMeter = columnDefs.some(item => item.children)
      this.$nextTick(() => {
        this.gridColumnDefs = handleGridColumnDefs(this.isSubtable, hasSecMeter, this.tableSelection, columnDefs)
      })
      // this.gridApi.setColumnDefs(this.gridColumnDefs)
    },
    // 触发子级编辑，后马上关闭编辑(子级静态批量修改，页面上没有刷新。需要先触发过编辑，才能生效刷新)
    triggerChildEditing(index, colKey, parentId = null) {
      this.gridApi.forEachNode(node => {
        const detailNode = node.detailNode
        if (node.id !== parentId) {
          if (detailNode && detailNode.detailGridInfo && detailNode.data?.[this.childRowKey]?.length >= index + 1) {
            detailNode && detailNode.detailGridInfo && detailNode.detailGridInfo.api.startEditingCell({
              rowIndex: index,
              colKey: colKey
            })
          }
          detailNode && detailNode.detailGridInfo && detailNode.detailGridInfo.api.stopEditing()
        }
      })
    },
    onGridReady(params) {
      this.gridApi = params.api
      this.columnApi = params.columnApi

      this.restoreColumnState()
      this.gridApi.addEventListener('columnMoved', this.saveColumnState)
      this.gridApi.addEventListener('columnPinned', this.saveColumnState)
      this.gridApi.addEventListener('columnResized', this.saveColumnState)

      if (this.isSizeColumnsToFit) {
        this.gridApi.sizeColumnsToFit()
      }
      if (this.autoLoadData) {
        this.loadTableData()
      } else {
        this.handleGridHeight(0)
      }
    },
    pageChange(pagination) {
      this.pagination.page = pagination.page
      this.pagination.limit = pagination.limit
      this.$emit('pageChange', pagination)
      this.reloadTableData(pagination)
    },
    agBodyScroll(event) {
      this.$emit('bodyScroll', event)
    },
    // 保存列状态到本地存储
    saveColumnState() {
      if (this.columnApi && this.menuId) {
        const columnState = this.columnApi.getColumnState()
        const columnPinnedState = this.columnApi.getAllColumns().map(col => ({
          colId: col.getColId(),
          pinned: col.getPinned()
        }))
        localStorage.setItem(`agGridColumnState_${this.menuId}`, JSON.stringify(columnState))
        localStorage.setItem(`agGridPinnedState_${this.menuId}`, JSON.stringify(columnPinnedState))

        // 保存一个当前 columnDefs 结构的校验摘要
        const columnConfigSnapshot = this.columnDefs
          .map(col => ({ field: col.field, headerName: col.headerName }))
          .sort((a, b) => (a.field > b.field ? 1 : -1))
        localStorage.setItem(`agGridColumnConfigSnapshot_${this.menuId}`, JSON.stringify(columnConfigSnapshot))
      }
    },
    // 恢复列状态
    restoreColumnState() {
      if (this.menuId) {
        const savedColumnState = JSON.parse(localStorage.getItem(`agGridColumnState_${this.menuId}`) || '[]')
        const savedPinnedState = JSON.parse(localStorage.getItem(`agGridPinnedState_${this.menuId}`) || '[]')

        // 获取当前的 `columnDefs` 配置，仅保留关键字段
        const currentColumnConfig = this.columnDefs
          .map(col => ({ field: col.field, headerName: col.headerName }))
          .sort((a, b) => (a.field > b.field ? 1 : -1))

        // 从本地存储获取上一次保存的 `columnDefs` 结构快照
        const savedColumnConfigSnapshot = JSON.parse(localStorage.getItem(`agGridColumnConfigSnapshot_${this.menuId}`) || '[]')

        // 比较当前配置和存储的配置快照
        const columnsChanged = JSON.stringify(currentColumnConfig) !== JSON.stringify(savedColumnConfigSnapshot)

        if (columnsChanged) {
        // 清除本地存储的状态
          localStorage.removeItem(`agGridColumnState_${this.menuId}`)
          localStorage.removeItem(`agGridPinnedState_${this.menuId}`)
          localStorage.removeItem(`agGridColumnConfigSnapshot_${this.menuId}`)

          // 应用默认列状态
          this.columnApi.resetColumnState()
        } else {
        // 恢复列状态
          this.columnApi.applyColumnState({ state: savedColumnState, applyOrder: true })

          // 恢复冻结状态
          savedPinnedState.forEach(col => {
            this.columnApi.setColumnPinned(col.colId, col.pinned)
          })
        }
      }
    }

  }
}
</script>

<style lang="scss" scoped>
.steel-trade-aggrid {
  display: flex;
  flex-direction: column;
  // flex: 1;
  height: 100%;
  background-color: #FFF;
  &-row{
    line-height: 32px;
    padding: 0 11px;
    color: #333;
    font-size: 13px;
    border:1px solid rgba(203, 207, 216, 0.5);
  }
  &-header{
    height: 34px;
    line-height: 34px;
    border-bottom: 0;
  }
  &-footer {
    display: flex;
    align-items: center;
    border-top: 0;
  }
  .cnd-pagination {
    padding: 3px 0;
    border:1px solid rgba(203, 207, 216, 0.5);
    border-top: 0;
    display: table;
    width: 100%;
  }
  .ag-cell-last-left-pinned.ag-row.ag-row-no-focus.ag-row-odd.ag-full-width-row.ag-row-position-absolute{
      background-color: #fff !important;
  }
}
</style>
<style lang="scss">
.header-class-edit-color{
  color: #3E8DDC !important;
}
.scroll-style{
  .ag-center-cols-viewport{
    overflow-x:hidden
  }
}
.cnd-pagination{
  overflow: auto;
}
.is-fullscreen {
  .el-dialog__body {
    .dialog-body-position {
      .dialog-body {
        .el-tabs {
          .btn-group{
            border-left: 0;
            border-right: 0;
          }
          .steel-trade-aggrid-row{
            border:0;
            border-top:1px solid rgba(203, 207, 216, 0.5);
          }
          .cnd-pagination{
            border:0;
            border-top:1px solid rgba(203, 207, 216, 0.5);
          }
       }
      }
    }
  }
}
.steel-trade-aggrid-header{
  height: auto !important;
}
</style>
