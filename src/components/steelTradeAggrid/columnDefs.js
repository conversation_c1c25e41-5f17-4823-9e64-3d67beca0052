import { handleCellWidth } from './fieldDict'
import { deepClone } from '@/components/steelTradeAggrid/utils.js'
const defMeter = {
  field: '',
  pinned: 'left',
  headerName: 'checkbox',
  headerComponent: 'headerDefSelect',
  cellRenderer: 'defSelect',
  minWidth: 40,
  maxWidth: 40
}

const defSecMeter = {
  headerName: '',
  field: 'checkbox',
  headerClass: 'c-header_parent',
  children: [
    {
      field: 'checkbox',
      pinned: 'left',
      headerName: '',
      headerComponent: 'headerDefSelect',
      cellRenderer: 'defSelect',
      minWidth: 40,
      maxWidth: 40,
      headerClass: 'c-header_child'
    }
  ]
}
const lastDefMeter = {
  headerName: '',
  field: 'fill',
  flex: 1,
  minWidth: 0
}
const lastSecDefMeter = {
  headerName: '',
  field: 'last',
  headerClass: 'c-header_parent',
  children: [
    {
      headerName: '',
      field: 'fill',
      flex: 1,
      minWidth: 0,
      headerClass: 'c-header_child'
    }
  ]
}
// 默认表头
const parentMeter = {
  field: 'checkbox',
  pinned: 'left',
  headerName: 'checkbox',
  headerComponent: 'headerSelect',
  cellRenderer: 'parentSelect',
  minWidth: 40,
  maxWidth: 40
}

// 二级表头
const parentSecMeter = {
  headerName: '',
  field: 'checkbox',
  pinned: 'left',
  headerClass: 'c-header_parent',
  children: [
    {
      field: 'checkbox',
      headerName: 'checkbox',
      pinned: 'left',
      headerComponent: 'headerSelect',
      cellRenderer: 'parentSelect',
      minWidth: 40,
      maxWidth: 40,
      headerClass: 'c-header_child'
    }
  ]
}

const childMeter = {
  field: 'checkbox',
  pinned: (params) => {
    return params.data._childCount ? null : 'left'
  },
  headerName: '',
  cellRenderer: 'childSelect',
  minWidth: 40,
  maxWidth: 40,
  colSpan: (params) => {
    return params.data._childCount ? 99 : 1
  }
}
const childSecMeter = {
  headerName: '',
  headerClass: 'c-header_parent',
  children: [
    {
      field: 'checkbox',
      pinned: (params) => {
        return params.data._childCount ? null : 'left'
      },
      headerName: '',
      cellRenderer: 'childSelect',
      minWidth: 40,
      maxWidth: 40,
      headerClass: 'c-header_child',
      colSpan: (params) => {
        return params.data._childCount ? 99 : 1
      }
    }
  ]
}
// 父子表 展开列
const spreadMeter = {
  headerName: '',
  field: '_selected',
  pinned: 'left',
  cellRenderer: 'agGroupCellRenderer',
  minWidth: 40,
  maxWidth: 40
}

const spreadSecMeter = {
  headerName: '',
  field: '',
  headerClass: 'c-header_parent',
  children: [
    {
      headerName: '',
      field: '_selected',
      pinned: 'left',
      cellRenderer: 'agGroupCellRenderer',
      minWidth: 40,
      maxWidth: 40,
      headerClass: 'c-header_child'
    }
  ]
}

// 编辑单元格 标题为蓝色
const handleHeaderDefEdit = (item) => {
  const meter = deepClone(item)
  const addEditColorClass = (headerClass) =>
    headerClass ? `${headerClass} header-class-edit-color` : 'header-class-edit-color'

  if (meter.children?.length) { // 判断是否存在 children
    let childHasClass = false
    meter.children = meter.children.map((el) => {
      if (isTrue(el.editable)) {
        if (el.headerClass?.includes('c-header_child')) {
          childHasClass = true
        }
        return {
          ...el,
          headerClass: addEditColorClass(el.headerClass)
        }
      }
      return el
    })
    // 如果 children 中有满足条件的项，则在 meter.headerClass 中添加 header-class-edit-color
    if (childHasClass) {
      meter.headerClass = addEditColorClass(meter.headerClass)
    }
  } else if (isTrue(meter.editable)) { // 如果没有 children，则直接检查 meter 自身的 editable 属性
    meter.headerClass = addEditColorClass(meter.headerClass)
  }

  return meter
}

/*
 * isTrue
 * @Description: 用于处理editable返回值
 * 1.如果editable为function， ps: editable() => { return xxxxx }, 则返回结果
 * 2.其他情况则返回原值
 * 3.当editable(params)有传值时，会报错，try.catch 错误捕获，返回原值  ps: editable(params) => { return params... }
 */
const isTrue = (value) => {
  try {
    if (typeof value === 'function') {
      return value()
    } else {
      return value
    }
  } catch (error) {
    return value
  }
}
const handleItemWidth = (item, firHeaderName) => {
  if (item.width) {
    return item
  }
  const cellWidth = handleCellWidth(item.headerName, firHeaderName)
  let headerWidth = 0
  if (!firHeaderName) {
    headerWidth = (item.headerName.length + 2) * 14 + 22
  }

  if (cellWidth) {
    // const width = (+cellWidth > +headerWidth) ? cellWidth : headerWidth
    if (item.headerName !== 'checkbox') {
      // console.log(`${firHeaderName || ''}${item.headerName}`, item.field, `${width}px`)
    }
    return {
      ...item,
      width: (+cellWidth > +headerWidth) ? cellWidth : headerWidth
    }
  }
  return item
}
const handleCellRenderer = (columnDefs) => {
  return columnDefs.map(item => {
    if (!item.cellRenderer) { // && !isTrue(item.hideRender)
      const cellRenderer = params => {
        const showValue = params.valueFormatted ? params.valueFormatted : params.value
        return `<span title="${showValue ?? ''}">${showValue ?? ''}</span>`
      }
      if (item.children?.length > 0) {
        const children = handleCellRenderer(item.children)
        return {
          ...item,
          children
        }
      } else {
        return {
          ...item,
          cellRenderer
        }
      }
    } else {
      return item
    }
  })
}
const handleGridColumnDefs = (isSubtable, hasSecMeter, tableSelection, columnDefs) => {
  const middleColumnDefs = handleCellRenderer(columnDefs)
  let firstColumnDef
  let spreadColumnDef = null
  if (isSubtable) {
    firstColumnDef = hasSecMeter ? parentSecMeter : parentMeter
    spreadColumnDef = hasSecMeter ? spreadSecMeter : spreadMeter
  } else {
    firstColumnDef = hasSecMeter ? defSecMeter : defMeter
  }
  return [
    tableSelection ? firstColumnDef : null,
    spreadColumnDef,
    ...middleColumnDefs,
    hasSecMeter ? lastSecDefMeter : lastDefMeter
  ].filter(item => item).map(item => {
    if (hasSecMeter) {
      if (item.children && item.children.length > 0) {
        return handleHeaderDefEdit({
          ...item,
          children: item.children.map(el => {
            return handleItemWidth(el, item.headerName)
          })
        })
      }
      return handleHeaderDefEdit(item)
    } else {
      return handleHeaderDefEdit(handleItemWidth(item))
    }
  })
}
const handleGridChildColumnDefs = (hasSecMeter, tableSelection, showChildSelect, columnDefs) => {
  const childColumnDefs = handleCellRenderer(columnDefs)
  if (childColumnDefs[0].field !== 'checkbox' && showChildSelect) {
    const hasSecMeter = childColumnDefs.some(item => item.children)
    if (tableSelection) {
      childColumnDefs.unshift(hasSecMeter ? childSecMeter : childMeter)
    }
  }
  if (childColumnDefs[childColumnDefs.length - 1].field !== 'fill') {
    childColumnDefs.push({
      headerName: '',
      field: 'fill',
      minWidth: 0,
      flex: 1
    })
  }
  return childColumnDefs.map(item => {
    if (hasSecMeter) {
      if (item.children && item.children.length > 0) {
        return handleHeaderDefEdit({
          ...item,
          children: item.children.map(el => {
            return handleItemWidth(el, item.headerName)
          })
        })
      }
      return handleHeaderDefEdit(item)
    } else {
      return handleHeaderDefEdit(handleItemWidth(item))
    }
  })
}
export {
  handleGridColumnDefs,
  handleGridChildColumnDefs
}
