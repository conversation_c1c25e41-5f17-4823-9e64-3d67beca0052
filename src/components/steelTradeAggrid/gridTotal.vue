<!--
    @component aggridCount 公用合计业务组件
    @props
        {String} flag  - 「头部」 或 「底部」合计
        {Array} countList -
          demoList:
            [
              { count: 1 },
              { title: '已到票未到货数量', count: 3, unit: this.$t('grid.others.ton') },
              { title: this.$t('grid.title.amount'), count: 3, unit: this.$t('grid.others.yuan') }
            ]
    <AUTHOR>
    @date 2021-01-18 11:22:33
-->
<template>
  <span>
    <span v-for="(item,index) in countList" :key="index">
      <span v-if="item.key==='count'">
        {{ item.title?item.title:flag==='header'?$t('pagination.total'):$t('components.selected') }}
        <span class="count-color">{{ item.count }}</span>
        {{ $t('pagination.items') }}，
      </span>
      <span v-if="index===0">{{ $t('grid.others.amountTo') }}：</span>
      <span v-if="item.key!=='count'">
        {{ item.title }}
        <span class="sum-color">{{ item.count }}</span>
        {{ item.unit }}
        <span v-if="index!==countList.length-1">
          ，
        </span>
      </span>
    </span>
  </span>
</template>

<script>
export default {
  props: {
    flag: {
      type: String,
      default: 'header'
    },
    countList: {
      type: Array,
      default: () => []
    }
  }
}
</script>

<style lang="scss" >
  .count-color{
    color: #3E8DDC;
    font-weight: bold;
  }
  .sum-color{
    color: #EE0033;
    font-weight: bold;
  }
</style>
