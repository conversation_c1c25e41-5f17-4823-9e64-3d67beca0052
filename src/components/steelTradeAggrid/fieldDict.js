/*
 * @Author: 沈鹭荣
 * @Date: 2021-03-23 15:26:43
 * @LastEditors: 沈鹭荣
 * @LastEditTime: 2021-03-24 10:42:44
 * @Description:
 */
// 一级权重 直接匹配
import i18n from '@/lang'
// 162
const firLong = ['发票客户', '规格', '货物或服务名称', '系统物流费合计', '费用发票金额', '费用付款金额', '运输合同金额', '记账部门', '开票客户', '客户', '收款账号', '税收分类', '税收简称', '往来明细', '委托方', '增值税发票号', '身份证号码', '电话号码', '修改时间', '品名']
// 106
const firMid = ['部门', '材质', '采购货值', '单吨加价', '吨位', '发票代码', '发票号', '发票号码', '发票细类', '发票张数', '返利', '钢卷号', '港建单件', '加价', '交单', '结算单性质', '结算货值', '客户性质', '来款', '利率', '商品', '商品名称描述', '剩余货值', '实付', '违约金', '应收款', '用款', '运输合同额', '增值税率', '支付凭据', '审批意见']
// 78
const firShort = ['版本号', '备注', '币种', '产地', '车船号', '船名', '回签', '货前后', '凭证', '未认证', '序号', '件数', '单位', '创建人']

const firMini = ['人员', '排款', '开票', '交单', '回签', '实付', '凭证']
// 二级权重 模糊匹配
const secLong = ['据号', '单号', '登记号', '合同号', '数量/件数', '供应商', '客商', '仓库', '单位', '公司']
const secMid = ['到期日', '日期', '天数', '时间', '费用', '保证金', '单价', '金额', '余额', '税额', '税点', '税差', '数量', '占比', '息率', '比例', '方式', '件数', '时间', '是否', '项目', '人', '息', '截止', '类型', '核算组']
const secMini = ['状态']
// // 三等权重 模糊匹配
const thiLong = ['银行']

const handleCellWidth = (childName, parentName) => {
  const headerName = parentName ? `${parentName}${childName}` : childName
  if (!headerName) {
    return null
  }
  // 判断语言类型是否为中文
  if (i18n.locale === 'zh_CN') {
    const isFirLong = firLong.some(el => el === headerName)
    const isFirMid = firMid.some(el => el === headerName)
    const isFirShort = firShort.some(el => el === headerName)
    const isFirMini = firMini.some(el => el === headerName)
    const isSecLong = secLong.some(el => headerName.includes(el))
    const isSecMid = secMid.some(el => headerName.includes(el))
    const isSecMini = secMini.some(el => headerName.includes(el))
    const isThiLong = thiLong.some(el => headerName.includes(el))
    if (isFirLong) {
      return 162
    } else if (isFirMid) {
      return 106
    } else if (isFirShort) {
      return 78
    } else if (isFirMini) {
      return 64
    } else if (isSecLong) {
      return 162
    } else if (isSecMid) {
      return 106
    } else if (isSecMini) {
      return 64
    } else if (isThiLong) {
      return 162
    }
    return (childName.length + 2) * 14 + 22
  } else {
    return ''
  }
}
export {
  handleCellWidth
}
