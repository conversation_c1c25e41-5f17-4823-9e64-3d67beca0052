<template>
  <div>
    <el-input
      ref="input"
      v-model="value"
      type="textarea"
      size="mini"
      autosize
    />
  </div>
</template>

<script>
export default {
  name: 'TableTextarea',
  data() {
    return {
      value: null
    }
  },
  watch: {
    value() {
      this.$nextTick(() => {
        this.$refs.input.resizeTextarea()
      })
    }
  },
  mounted() {
    this.$nextTick(() => {
      if (this.$refs.input) {
        this.$refs.input.focus()
      }
    })
  },
  beforeMount() {
    this.value = this.params.value
  },
  methods: {
    getValue() {
      return this.value
    }
  }
}
</script>

<style scoped></style>
