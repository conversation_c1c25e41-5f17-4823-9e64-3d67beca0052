// qiankun 框架全局变量  使用方法参考   https://qiankun.umijs.org/zh/api#initglobalstatestate
import { initGlobalState } from 'qiankun'
import Cookies from 'js-cookie'

const initialState = {
  g6_sId: '',
  g6_sTechArtnoId: '',
  g6_visible: false,
  dependentData: '',
  g6_type: '',
  authenticationList: null,
  deleteCurrent: false,
  rePage: null,
  refreshTags: false,
  locale: Cookies.get('language') || 'zh_CN',
  bellType: ''
}
const actions = initGlobalState(initialState)
export default actions
