export default {
  btns: {
    add: '新增',
    agree: '同意',
    attachmentManagement: '附件管理',
    cancel: '取消',
    cancelApplication: '撤销申请',
    close: '关闭',
    resend: '重发',
    confirm: '确认',
    confirmKey: '确定',
    confirmSubmit: '确认提交',
    exportList: '导出列表',
    copy: '复制',
    previousStep: '上一步',
    delete: '删除',
    disable: '禁用',
    download: '下载',
    edit: '编辑',
    emptying: '清空',
    enable: '启用',
    finish: '完成',
    generate: '生成',
    match: '匹配',
    next: '下一步',
    no: '否',
    return: '返回',
    save: '保存',
    select: '选择',
    selectContract: '选择合同',
    selectItem: '选择项目',
    selectItemNumber: '选择货号',
    upload: '上传',
    versionBackup: '版本备份',
    versionComparison: '版本比对',
    viewApprovalStatus: '查看审批情况',
    viewAttachments: '查看附件',
    void: '作废',
    withdrawalOfOrder: '撤单'
  },
  router: {
    approvalManagement: '审批管理',
    arrivalManagement: '到货管理',
    attachmentManagement: '附件管理',
    attachmentQuery: '附件查询',
    bankClassification: '银行分类',
    bankInformation: '银行信息',
    basicCodeList: '基础码表',
    basicInformation: '基础资料',
    bookingAndLoadingStatusInquiry: '订舱装柜情况查询',
    bookingConfirmationRegistration: '订舱确认登记',
    bookingManagement: '订舱管理',
    bulkCargoArrivalNotice: '散货到货通知单',
    bulkCargoDeliveryManagement: '散货发货管理',
    bulkCargoDeliveryWorkbench: '散货发货工作台',
    bulkCargoLoading: '散货装柜',
    bulkCargoLoadingRegistration: '散货装柜登记',
    bulkShipmentNotificationForm: '散货发货通知单',
    businessDictionary: '业务字典',
    businessOrganization: '业务组织',
    businessRoleManagement: '业务角色管理',
    buttonManagement: '按钮管理',
    cityPort: '城市港口',
    codingRuleDefinitions: '编码规则定义',
    codingRulesManagement: '编码规则管理',
    codingVariablesDefinition: '编码变量定义',
    commodityCategory: '商品大类',
    commodityClassification: '商品分类',
    commodityProperties: '商品属性',
    commoditySearch: '商品查询',
    confirmationOfWholehipmentKey: '整柜发货确认单',
    contactDetailsInquiry: '往来明细查询',
    containerType: '集装箱型',
    contractManagement: '合同管理',
    correspondentAndExpnitionsKey: '往来和费用项目定义',
    correspondentRelatedConfiguration: '往来相关配置',
    costClearingHouseOrganization: '成本结算中心组织',
    costDetailInquiry: '成本明细查询',
    countryArea: '国别地区',
    currencyRegistration: '币种登记',
    customerApplication: '客商申请',
    customerInquiry: '客商查询',
    customerManagement: '客商管理',
    customerRegistration: '客商登记',
    developmentDictionary: '开发字典',
    dialogBoxCodeMatching: '对话框编码匹配',
    dictionaryManagement: '字典管理',
    documentManagement: '单据管理',
    documentProcessConfiguration: '单据流程配置',
    exceptionCodingManagement: '异常编码管理',
    exceptionManagement: '异常管理',
    exchangeRateMaintenance: '汇率维护',
    exchangeRateManagement: '汇率管理',
    exchangeRateNumberDefinition: '汇率编号定义',
    financialOrganization: '财务组织',
    fullContainerArrivalNotice: '整柜到货通知单',
    fullContainerShipmentManagement: '整柜发货管理',
    goodsNumberManagement: '货号管理',
    importTemplateMaintenance: '导入模板维护',
    inboundAndOutboundWInquiryKey: '进出仓明细查询',
    inboundReturnInquiry: '进仓回单查询',
    inboundReturnOrderAdjustmentQuery: '进仓回单调整单查询',
    inboundReturnOrderArkbenchKey: '进仓回单调整工作台',
    inquiryStatistics: '查询统计',
    inventoryIncomingOrders: '盘盈入仓单',
    inventoryManagement: '存货管理盘点管理',
    inventoryOutOrder: '盘亏出仓单',
    invoiceManagement: '发票管理',
    invoicingWorkbenchForShipping: '发货待开票工作台',
    legalOrganization: '法人组织',
    logisticsManagement: '物流管理',
    menuManagement: '菜单管理',
    mySubmittedRequests: '我提交的申请',
    organizationalUnit: '组织单元',
    parameterManagement: '参数管理',
    paymentRequestForm: '付款申请单',
    pendingApprovalWorkbench: '待审批工作台',
    permissionsDialog: '权限对话框',
    personnelMaintenance: '人员维护',
    personnelManagement: '人员管理',
    pointInTimeInventoryInquiryHistory: '时点库存查询（历史）',
    portOfCustomsDeclaration: '报关口岸',
    preSortingInventoryInquiry: '预拣配库存查询',
    processManagement: '流程管理',
    procurementPendingInvoiceWorkbench: '采购待开票工作台',
    purchaseContractProcessInquiry: '采购合同进程查询',
    purchaseInvoiceRegistrationForm: '采购发票登记单',
    purchaseInvoicing: '采购发票',
    realTimeInventoryBalanceInquiry: '实时库存余额查询',
    receiptAndPaymentManagement: '收付管理',
    receiptClaimForm: '收款认领单',
    resourceList: '资源列表',
    revenueDetailInquiry: '收入明细查询',
    reviewedRecordsInquiry: '已审记录查询',
    rootNodeManagement: '根节点管理',
    salesContractProcessInquiry: '销售合同进程查询',
    salesInvoice: '销售发票',
    salesInvoiceRegistrationForm: '销售发票登记单',
    systemConfiguration: '系统配置',
    systemParametersConfiguration: '系统参数配置',
    timedTaskDefinition: '定时任务定义',
    timedTaskManagement: '定时任务管理',
    tradeManagement: '贸易管理',
    transfer: '移仓调拨',
    transferOrder: '移仓调拨单',
    transferWorkbench: '移仓调拨工作台',
    triggerConfigurationManagement: '触发配置管理',
    uSPurchaseContracts: '美国采购合同',
    uSSalesContracts: '美国销售合同',
    underwritingConfiguration: '核销配置',
    underwritingConfigurationList: '核销配置列表',
    underwritingManagement: '核销管理',
    unitOfMeasurement: '计量单位',
    userManagement: '用户管理',
    warehouseManagement: '仓库管理',
    wholeContainerInventoryPreSorting: '整柜库存预拣配',
    wholeContainerShipmentWorkbench: '整柜发货工作台',
    workflowConfiguration: '工作流配置'
  },
  excel: {
    export: '导出',
    exportAll: '导出全部',
    exportAllDataAfterFiltering: '导出筛选后的全部数据',
    exportCsvFormat: '导出CSV格式',
    exportExcelFormatXlsx: '导出Excel格式 (.xlsx)',
    exportExcelFormatXml: '导出Excel格式 (.xml)',
    exportSelectedData: '导出已选',
    exportStatement: '导出对账单',
    exportStatus: '导出状态',
    exportTheCurrentPaglteringKey: '导出筛选后的当前页数据',
    exportThisPage: '导出本页',
    import: '导入'
  },
  navbar: {
    close: '关闭',
    closeAll: '关闭所有',
    closeLeftSide: '关闭左侧',
    closeOther: '关闭其它',
    closeRightSide: '关闭右侧',
    closeThisPage: '关闭本页',
    home: '首页',
    languageSwitching: '语言切换',
    refresh: '刷新',
    upTo15PagesCanBeOperaPagesKey: '最多可以打开15个页面，请关闭多余页面!'
  },
  login: {
    backToPreviousPage: '返回上一页',
    changePassword: '修改密码',
    fillInTheAccount: '填写账号',
    finishReset: '完成重置',
    forgotPassword: '忘记密码',
    getSmsVerificationCode: '获取短信验证码',
    login: '登录',
    viewAllInformation: '查看所有信息',
    pleaseEnterTheBoundMobilePhoneNumber: '请输入绑定的手机号',
    logout: '退出登录',
    pleaseEnterANewPassword: '请输入新密码',
    pleaseEnterTheNewPasswordAgain: '请再次输入新密码',
    accountPasswordLogin: '账号密码登录',
    pleaseEnterTheVerificationCode: '请输入验证码',
    pleaseEnterYourAccount: '请输入账号',
    pleaseEnterYourCellPhoneNumber: '请输入手机号',
    pleaseEnterYourPassword: '请输入密码',
    setNewPassword: '设置新密码',
    verifyYourIdentity: '验证身份'
  },
  pagination: {
    data: '数据',
    items: '条',
    page: '页',
    pages: '页',
    total: '共'
  },
  setting: {
    my: '我的',
    settings: '设置'
  },
  tips: {
    addSuccess: '添加成功',
    addedSuccessfully: '新增成功!',
    congratulationsOnTheModification: '恭喜你，修改成功',
    isIE: '本系统只支持非IE内核浏览器，IE浏览器可能无法正常使用全部功能，推荐使用chrome/firefox/safari进行访问！',
    confirmToRevokeRequest: '确认撤销申请？',
    confirmToRevoke: '确认撤销？',
    connectSuccessfully: '连接成功',
    deletedSuccessfully: '删除成功',
    doesItConfirmToSubmit: '是否确认提交？',
    downloadSuccessful: '下载成功',
    duplicated: '已复制',
    imageUploadFailure: '图片上传失败',
    isDeletionConfirmed: '是否确认删除?',
    isItContinued: '是否继续?',
    isItDeleted: '是否删除？',
    isItDisabled: '是否禁用',
    isItOkToDelete: '是否确定删除?',
    isItOkToSubmit: '是否确定提交?',
    isLocked: '是否锁定',
    isSubmissionConfirmed: '是否确认提交?',
    loadingApplication: '正在加载应用...',
    loadingFailedPleaseTryAgain: '加载失败，请重试！',
    loginInformationTimeout: '登录信息超时',
    networkError: '网络错误提示',
    operationSuccessful: '操作成功',
    operationFailure: '操作失败',
    pleaseTryAgain: '请重试',
    requestFailed: '请求失败',
    saveFailed: '保存失败',
    saveSuccess: '保存成功',
    serverRequestFailure: '服务器请求失败等',
    submitSuccess: '提交成功',
    submittedSuccessfully: '提交成功',
    tokenExpiration: 'token过期等',
    maximumPages: '最多可以打开15个页面，请关闭多余页面!',
    validateSuccess: '作废成功',
    voidFailed: '作废失败',
    withdrawalSuccessTag: '撤销成功'
  },
  grid: {
    others: {
      aThousandWordsAreBeicIdeasKey: '千言万语不如一张图，流程图是表示算法思路的好方法',
      aTotalOf0TotalHasAOf0YuanKey: '共0条，合计：已到货未到票数量0吨，金额0元',
      abbreviation: '简称',
      abbreviationKey: '英文简称',
      abbreviationCode: '缩写代码',
      abbreviationOfItemNumber: '货号简称',
      acceptanceAmount: '承兑金额',
      acceptanceDiscountRnterestKey: '承兑贴息率(月息)',
      acceptanceSingleTonDiscountInterest: '承兑单吨贴息',
      accessories: '辅料',
      accessoriesAnalysisBench: '辅料分析工作台',
      accessoriesBom: '辅料BOM',
      accessoriesRequirementsList: '辅料需求清单',
      accessoriesRequirementsSummary: '辅料需求汇总',
      accompanyingGoodsWigNumberKey: '随货同行单号',
      accordingToTheNumber: '据号',
      account: '账户',
      accountAlias: '账号别名',
      accountBank: '客商开户行',
      accountNumber: '账号',
      accountNumberNameEmcePhoneKey: '账号/姓名/邮件地址/移动电话/办公电话',
      accountNumberCannotBeEmpty: '账号不能为空',
      accountOpeningBank: '开户行',
      accountPeriodDays: '账期(天)',
      accountType: '账户类型',
      accountingFields: '核算字段',
      accountingFilter: '核销过滤',
      accountingGroupCode: '核销编码',
      accountingGroupId: '核算组id',
      accountingGroupName: '核算组名称',
      accountingGroupNameTag: '核销名称',
      accountingGroupQuery: '核算组查询',
      accountsReceivable: '应收账款',
      accountsReceivableInquiry: '应收账款查询',
      achievement: '成绩',
      achievementLevel: '成绩等级',
      acquiredSacco: '所获得萨科',
      actionCategoryParameter: '动作分类参数',
      actionCategorySelection: '动作分类挑选',
      actionClassification: '动作分类',
      actionClassificationCode: '动作分类编码',
      actionCodes: '动作代码',
      actionDescription: '动作描述',
      actionParameterConfiguration: '动作参数配置',
      actionParameterSelection: '动作参数挑选',
      actionParameterValueConfiguration: '动作参数值配置',
      actionParameters: '动作参数',
      actionType: '动作类型',
      actionTypeName: '动作类型名称',
      activated: '已启用',
      actualAmountOfDepositReceived: '实收保证金金额',
      actualContractDate: '实际签约日期',
      actualController: '实际控制人',
      actualInsuredUnitPriceExcludingTax: '实际投保单价（不含税）',
      actualIssue: '实发',
      actualPaid: '实付',
      actualQuantity: '实际数量',
      actualReceiptAndPayagementKey: '实收付调整单管理',
      actualReceiptAndPaymentDate: '实收付日期',
      actualReceiptAndPayustmentKey: '实收付往来调整',
      actualReceived: '实收',
      actualTimeSpentMinutes: '实际用时(分)',
      addAdjustment: '追加调整',
      addPrice: '加价',
      addProductAttribute: '新增商品属性值',
      address: '地址',
      addressTage: '客商地址',
      addressDetails: '详细地址',
      addressType: '地址类型',
      adjustItemDetails: '调整商品明细',
      adjustedPieceCount: '调整后件数',
      adjustedQuantity: '调整后数量',
      adjustedQuantityPiece: '调整后数量/件数',
      adjustedTaxAmount: '调整后含税金额',
      adjustedTaxAmountIsRequired: '调整后含税金额为必填',
      adjustedStandardQuantity: '调整后|标准数量',
      adjustedStandardQuaerThan0Key: '调整后|标准数量,调整后|库存数量 必须大于0',
      adjustmentAmount: '调整金额',
      adjustmentDate: '调整日期',
      adjustmentDateFrom: '调整日期从',
      adjustmentDateTo: '调整日期到',
      adjustmentDetails: '调整明细',
      adjustmentDifference: '调整差额',
      adjustmentInformation: '调整信息',
      adjustmentOrder: '调整单',
      adjustmentOrderNumber: '调整单号',
      adjustmentQuantity: '调整数量',
      adjustmentQuantityNumberOfPieces: '调整数量/件数',
      adjustmentReasonType: '调整原因类型',
      adjustmentRequestNumber: '调整申请单号',
      adjustmentType: '调整类型',
      advancePaymentDateTo: '预付付款日期到',
      affiliatedBusinessRole: '所属业务角色',
      affiliatedCommodityributesKey: '所属商品分类及要素属性',
      affiliatedCompany: '所属公司',
      affiliatedContractSearch: '关联合同查询',
      affiliatedRootOrganization: '所属根组织',
      affiliatedTaxOrganizationCode: '所属税务机构代码',
      affiliatedWorkshop: '所属车间',
      afterAdjustment: '调整后',
      age: '年龄',
      agedTo: '库龄到',
      agingDays: '库龄(天)',
      agreeAndForwardFloweviewerKey: '同意并转审（被转审人同意后流转下一节点）',
      agreementDeadline: '协议截止日期',
      agreementParty: '协议方',
      agreementTerms: '协议条款',
      aliCloud: '阿里云',
      all: '全部',
      allocationMethod: '配款方式',
      allotmentDetails: '配款明细',
      thisAmount: '本次金额',
      productAmount: '商品金额',
      amountExcludingTaxTag: '金额（不含税）',
      amountIncludingTax: '金额(含税)',
      amountTaxIncluded: '金额（含税）',
      amountExcludingTax: '不含税金额',
      amountInLocalCurrency: '折本位币金额',
      amountInLocalCurrencyWithoutTax: '折本位币不含税金额',
      amountInformation: '金额信息',
      amountInvoiced: '已开金额',
      amountInvoicedPayable: '应开票金额',
      amountNotReturned: '未回款金额',
      amountOfCustomsDeclaration: '报关金额',
      amountOfDepositReceived: '已收保证金金额',
      amountOfDepositUsed: '已动用保证金金额',
      amountOfDiscount: '贴息金额',
      amountOfInterestRecptancesKey: '承兑应收贴息金额',
      amountOfOffset: '冲抵金额',
      amountOfRedFlushThisMonth: '本月红冲数量',
      amountOfShipmentsToBeAudited: '待审发货金额',
      amountOfTransportation: '运输金额',
      amountOfTransportationContract: '运输合同金额',
      amountOfUndeclaredContracts: '未报关合同金额',
      amountPaid: '已付金额',
      amountPayable: '应付金额',
      amountProcessingMethod: '金额处理方式',
      amountReceivableSt: '应实收金额',
      amountReceivableNd: '应收款',
      amountReceivableRd: '应收款金额',
      amountReceivableTh: '应收金额',
      amountReceivableColcsCostsKey: '应收金额代收代付物流费用',
      amountReceivableForBreachOfContract: '应收金额违约金',
      amountReceivableInterestCharges: '应收金额利息费用',
      amountReceivableMarkup: '应收金额加价',
      amountReceivableOnecsCostsKey: '应收金额一票制物流费用',
      amountReceivablePurchaseValue: '应收金额采购货值',
      amountReceivableRebate: '应收金额返利',
      amountReceived: '收款金额',
      amountReceivedInAdvance: '预收金额',
      amountShippedThisMonth: '本月发货金额',
      amountTo: '合计',
      amountAllocated: '待配金额',
      amountToBeAllocated: '待配款金额',
      amountToBeBilled: '结算应开金额',
      amountToBeCollected: '待收款金额',
      amountToBeUsed: '本次使用金额',
      anInvalidResponseWaeServerKey: '从远程服务器接收到了一个无效的响应',
      analysisCompleted: '分析完成',
      analysisDetails: '分析详情',
      analysisInProgress: '分析中',
      analysisOrder: '分析单',
      analysisOrderNumber: '分析单号',
      analysisStatus: '分析情况',
      analyze: '分析',
      analyzeIe: '分析IE',
      analyzed: '已分析',
      annualSales: '全年销量',
      annualSalesAmount: '全年销售金额',
      applicableBusinessUnits: '适配经营单位',
      applicableConditions: '适用条件',
      applicableDocuments: '适配单据',
      applicableObjectType: '适用对象类型',
      applicableObjects: '适用对象',
      applicableProductCategories: '适配商品分类',
      applicant: '申请人',
      applicationAmount: '申请金额',
      applicationDateFrom: '申请日期从',
      applicationDateTo: '申请日期到',
      applicationFunctionAuthorization: '应用功能授权',
      applicationName: '应用名称',
      applicationTime: '申请时间',
      appliedQuantity: '已申请数量',
      applyForPurchase: '申请采购',
      apportionInvoiceAmountByQuantity: '按数量分摊发票金额',
      approval: '审批',
      approvalAttribution: '审批归属',
      approvalComments: '审批意见',
      approvalCommentsCanNotBeEmpty: '审批意见不能为空',
      approvalDate: '核准日期',
      approvalDescriptionScript: '审批描述脚本',
      approvalDetails: '审批详情',
      approvalInquiry: '审批查询',
      approvalInstructions: '审批说明',
      approvalManagement: '审批管理',
      approvalOrderNumber: '核准单号',
      approvalProcess: '审批流程',
      approvalStatus: '审批情况',
      area: '地区',
      areaChineseName: '地区中文名',
      areaCode: '地区编码',
      areaEnglish: '地区英文名',
      areaType: '地区类型',
      arrangementNumber: '排布编号',
      arrivalAmount: '到货金额',
      arrivalImport: '到货导入',
      arrivalOfGoods: '到货商品',
      arrivalOrderNumber: '到货单号',
      arrivalQuantity: '到货数量',
      arrivalShippingOrderNumber: '到/发货单号',
      arrived: '已到货数量',
      arrivedWithoutTickets: '已到货未到票',
      article: '条',
      articleDescription: '货号描述',
      articleEnglish: '货号英文名',
      articleEnglishKey: '款号英文名',
      articleNumberCategory: '货号类别',
      articleNumberCodeCoVatRateKey: '货号编码、合同数量、合同单位、合同单价、合同金额额、标准数量、标准换算比例、增值税税率',
      articleNumberCopy: '货号复制',
      articleNumberDetails: '货号详情',
      articleNumberInformation: '货号信息',
      articleNumberManagement: '货号管理',
      articleTitle: '条款标题',
      assetBroadCategory: '资产大类',
      assetChineseName: '资产中文名',
      assetClassification: '资产分类',
      assetCode: '资产编码',
      assetFile: '资产档案',
      assetName: '资产名称',
      assetNumber: '资产编号',
      assetPropertySelection: '资产属性挑选',
      assetStatus: '资产状态',
      assignManageableBusinessRoles: '分配可管理业务角色',
      assignmentDetails: '分配明细',
      associatedFields: '关联字段',
      attachment: '附件',
      attachmentTag: '附言',
      attachmentConfiguration: '附件配置',
      attachmentName: '附件名',
      attachmentSelection: '附件选择',
      attachmentType: '附件类型',
      attachmentTypeFileSizeUnitRequired: '附件类型、文件大小、单位必填',
      attachmentVideo: '附件视频',
      attribute: '属性',
      attributeCode: '属性编码',
      attributeCodeAttributeName: '属性编码，属性名称',
      attributeDetails: '属性明细',
      attributeInformation: '属性信息',
      attributeList: '属性列表',
      attributeName: '属性名称',
      attributeSelection: '属性选择',
      attributeType: '属性类型',
      sizeAttributeValue: '尺码属性值',
      attributeValue: '属性值',
      attributeValueCode: '属性值编码',
      attributeValueDetails: '属性值明细',
      attributeValueInformation: '属性值信息',
      attributeValueList: '属性值列表',
      attributeValueName: '属性值名称',
      attributeValueSelection: '属性值选择',
      attributeAttributeValue: '属性|属性值',
      attributesOfElements: '要素属性',
      attributor: '归属人',
      audit: '审核',
      auditProgress: '审核进度',
      auditSituation: '审核情况',
      auditTime: '审核时间',
      auditors: '审核人',
      automaticInterestUpdate: '自动更新利息',
      automaticMatching: '自动配款',
      automaticallyGeneraToRulesKey: '根据规则自动生成',
      automaticallyGeneratedByRule: '按照规则自动生成',
      available: '可选',
      availableAmount: '可用金额',
      availableDirectRelease: '可直放',
      availableMargin: '可用保证金',
      availableOrNot: '是否可用',
      availablePieces: '可用-件数',
      availableTons: '可用-吨数',
      averageCadence: '平均节奏',
      awardAndPunishmentInformation: '奖惩信息',
      awardAndPunishmentTime: '奖惩时间',
      awardInformation: '获奖信息',
      awardName: '获奖名称',
      awardTime: '获奖时间',
      awardingUnit: '发奖单位',
      back: '返回',
      backSideStructureDiagram: '背侧面结构图',
      backToList: '返回列表',
      realType: '应实类型',
      should: '应',
      actual: '实',
      commodityAttributeTypeCode: '商品属性类型编码',
      commodityAttributeTypeName: '商品属性类型名称',
      purchaseOffsettingTemporaryPayment: '采购冲抵暂付款',
      selectProvisionalPaymentTransaction: '选择暂付往来',
      salesOffsetAgainstProvisionalReceipts: '销售冲抵暂收款',
      selectReceivablesSt: '选择暂收往来',
      selectReceivablesNd: '暂付往来选择',
      selectReceivablesRd: '暂收往来选择',
      noContractDirectReleaseShippingOrder: '无合同直放发货单',
      noContractDirectReleaseShippingOrderAdded: '无合同直放发货单新增',
      noContractDeliveryOrderDetails: '无合同直放发货单详情',
      purchaseGoodsDetails: '采购商品明细',
      preOccupancyDetails: '预占用明细',
      preCreationScriptForNodes: '节点创建前脚本',
      nonContractualDirectReleaseReturnWorkbench: '无合同直放退货工作台',
      nonContractualDirectReleaseReturnOrder: '无合同直放退货单',
      generateANoContractDirectReleaseReturnNotice: '生成无合同直放退货通知单',
      addingANonContractualDirectReleaseReturn: '新增无合同直放退货单',
      creditRepayment: '信用还款',
      noContractDirectDebitReturns: '无合同直放退货单',
      amountOfCreditToBeRepaid: '待还信用额度',
      currentRepaymentAmount: '本次还款金额',
      redRush: '红冲',
      documentReplication: '单据复制',
      salesContractList: '销售合同列表',
      dateOfFirstReceipt: '首次收款日期',
      shippingNoticeDetails: '发货通知单明细',
      outboundType: '出仓类型',
      receiptNumber: '收款单号',
      quantityShipped: '出库数量',
      actionParameterList: '动作参数列表',
      actionParameterDetails: '动作参数详情',
      parameterValueDetails: '参数值明细',
      nameInChinese: '中文名称',
      englishNameKey: '英文名称',
      actionParameterValueConfigurationList: '动作参数值配置列表',
      actionParameterConfigurationDetails: '动作参数配置详情',
      add: '添加',
      actionParameterConfigurationList: '动作参数配置列表',
      actionCategoryList: '动作分类列表',
      codeKey: '代号',
      assetProfileDetails: '资产档案详情',
      assetPropertyDetails: '资产属性详情',
      equipmentParametersConfigurationList: '设备参数配置列表',
      equipmentParameterDetails: '设备参数详情',
      equipmentParametersList: '设备参数列表',
      assetCategoryList: '资产大类列表',
      sqlScript: 'SQL脚本',
      sqlParameterScript: 'SQL参数脚本',
      pleaseEnterChineseName: '请输入中文名称',
      productionLinePropertyDetails: '产线属性详情',
      productionLineConfigurationList: '产线配置列表',
      productionLineTypeDetail: '产线类型明细',
      productionTeamList: '生产小组列表',
      bomListMaintenance: 'BOM单维护',
      bomList: 'BOM单',
      confirmDelete: '确认删除',
      addANewBomOrder: '新增BOM单',
      editBomOrder: '编辑BOM单',
      bomScheme: 'BOM方案',
      bomOrderDetails: 'BOM单明细',
      bomRolePermissionConfiguration: 'BOM角色权限配置',
      clauseBaseLibraryList: '条款基础库列表',
      clauseTemplateDocumentReferenceConfigurationList: '条款模板单据引用配置列表',
      clauseTemplateList: '条款模板列表',
      clauseDetails: '条款明细',
      taxRate: '税率',
      pleaseSelectAProductCategory: '请选择商品大类',
      poNo: 'PO号',
      bottomProcessDetails: '底工艺明细',
      dragTheFileHereOrBrowse: '将文件拖到此处，或浏览',
      partName: '部位名称',
      cbmm3: 'CBM（立方米）',
      poCategory: 'PO类别',
      poQuantity: 'PO数量',
      bomStatusSummary: 'BOM状态汇总',
      versionBackupSuccess: '版本备份成功',
      processSheetNumber: '工艺单编号',
      processParameterDetailsKey: '工艺参数明细',
      processSequenceDetails: '工序明细',
      enterSectionNumber: '请输入款号',
      processFileList: '工艺档案列表',
      processActionDetails: '工序动作明细',
      processParameterList: '工艺参数列表',
      parameterValueConfigurationDetails: '参数值配置明细',
      processParameterValueConfigurationList: '工艺参数值配置列表',
      processParameterConfigurationList: '工艺参数配置列表',
      processCategoryList: '工艺类别列表',
      foreignExchangeRateDetails: '银行外汇牌价详情',
      foreignExchangeRateDetailsKey: '外汇牌价明细',
      bankForeignExchangeRateList: '银行外汇牌价列表',
      exchangeRateNumberList: '汇率编号列表',
      formulaConfigurationList: '公式配置列表',
      dailyProductionRegistrationListKey: '日产量登记列表',
      partClassification: '部位分类',
      partClassificationList: '部位分类列表',
      partsList: '部件列表',
      partInformation: '部位信息',
      partCodeKey: '部位编码',
      processFileListKey: '工序档案列表',
      processParameterDetails: '工序参数详情',
      listOfProcessParameters: '工序参数列表',
      listOfWorkSequenceParameterConfigurationValues: '工序参数配置值列表',
      listOfWorkSequenceParametersConfiguration: '工序参数配置列表',
      nodeDefinition: '节点定义',
      solutionTemplate: '方案模板',
      nodeDetails: '节点明细',
      nodeDependencyFlowChart: '节点依赖流程图',
      newItemsAlreadyExistPleaseSaveThemFirst: '已存在新增项,请先保存',
      scopeOfApplicationKey: '适用范围',
      tagDetails: '标签明细',
      fieldStatus: '字段状态',
      exceptionTypeDefinition: '异常类型定义',
      exceptionTypeCode: '异常类型编码',
      exceptionType: '异常类别',
      enterExceptionCategory: '请输入异常类别',
      enterName: '请输入名称',
      pleaseSelectFieldNameOrFieldStatus: '请选择字段名称或者字段状态',
      exceptionFeedbackDetails: '异常反馈详情',
      exceptionRegistrationNumber: '异常登记单号',
      exceptionTypeKey: '异常类型',
      degreeOfUrgency: '紧急程度',
      customerOrderNumber: '客户订单号',
      exceptionNode: '异常节点',
      exceptionHandler: '异常处理人',
      exceptionNotifier: '异常通知人',
      expectedResolutionTime: '期望解决时间',
      filterOoo: '筛选',
      equals: '等于',
      notEqual: '不等于',
      contains: '包含',
      notContains: '不包含',
      startsWith: '开始于',
      endsWith: '结束于',
      pivotMode: '枢轴模式',
      groups: '行组',
      values: '值',
      columns: '栏目',
      filters: '筛选',
      searchOoo: '搜索',
      currentHandler: '当前处理人',
      exceptionReporter: '异常报告人',
      exceptionReportTime: '异常报告时间',
      exceptionDescription: '异常描述',
      solution: '解决方案',
      assignmentDetailsKey: '指派明细',
      assignTheExceptionTo: '将异常指派给',
      selectExceptionType: '请选择异常类型',
      selectDegreeOfUrgency: '请选择紧急程度',
      selectCustomerOrderNumber: '请选择客户订单号',
      selectExceptionNode: '请选择异常节点',
      selectExceptionHandler: '请选择异常处理人',
      reportingPerson: '请选择报告人',
      pleaseSelectNode: '请选择节点',
      exceptionRegistrationDetails: '异常登记详情',
      exceptionRegistration: '异常登记',
      exceptionRegistrationList: '异常登记列表',
      exceptionLabel: '异常标签',
      exceptionStatus: '异常状态',
      handler: '处理人',
      attachmentInformation: '附件信息',
      exceptionWorkbench: '异常工作台',
      pending: '待处理',
      handled: '已处理',
      pleaseSelectAnExceptionTag: '请选择异常标签',
      exceptionWorkbenchList: '异常工作台列表',
      myFeedback: '我的反馈',
      myPending: '我的待处理',
      notifyMeOf: '通知我的',
      scheduleTaskDetails: '计划任务明细',
      pleaseEnterAPlanNumber: '请输入计划编号',
      pleaseSelectAPlanTemplate: '请选择计划模板',
      enterPlanName: '请输入计划名称',
      internalControlPlanDate: '内控计划日期',
      planDate: '计划日期',
      actualCompletionDate: '实际完成时间',
      taskFeedback: '任务反馈',
      makeAPlanList: '制定计划列表',
      fuzzySearch: '模糊搜索',
      developPlanListDetails: '制定计划列表详情',
      industryDefinition: '行业定义',
      processCategoryListKey: '工序类别列表',
      processOrderNumberKey: '工艺单编号',
      workProcessDependencyList: '工序依赖列表',
      sectionStationEquipmentDistributionMap: '款站位设备分布图',
      programmingEfficiency: '编程效率',
      numberOfPeopleInLine: '在排人数',
      percentageOfWorkHours: '工时占比',
      purchaseDetails: '采购明细',
      purchaseRequest: '采购申请',
      paymentRatio: '支付比例',
      analysisResults: '分析结果',
      specifySupplierSupplier: '【指定供应商】请选择供应商',
      inventoryWithholdingBatchProperties: '【库存预扣】请填写批次属性',
      countrySizeOrSize: '国别尺码&尺码',
      contractCurrency: '合同币种',
      vatRate: '增值税税率',
      selectContractQuantity: '请选择合同数量',
      selectContractUnitPrice: '请选择合同单价',
      selectTotalContractAmount: '请选择合同总额',
      selectExpectedDeliveryDate: '请选择预计交货日期',
      selectEarliestDeliveryDate: '请选择最早交货日期',
      shortLoadingRate: '短装率',
      overfillRate: '溢装率',
      backToLogin: '返回登录',
      backToTheOrderEffectiveTime: '回单生效时间',
      backupRepository: '备用仓库',
      backupSuccessful: '备份成功',
      balanceChart: '平衡图',
      balanceForThePeriod: '本期余额',
      balanceReceivableForThePeriod: '本期应收余额',
      bank: '银行',
      bankAccount: '银行账户',
      bankAccountAndDefauequiredKey: '银行账户和默认币种必填！',
      bankAccountNumber: '银行账号',
      bankAddress: '银行地址',
      bankClassification: '银行分类',
      bankClassificationCode: '银行分类编码',
      bankClassificationName: '银行分类名称',
      bankCnapsNumber: '银行CNAPS号',
      bankCode: '银行编码',
      bankDocumentNumber: '银行单据号',
      bankForeignExchangeRateMaintenance: '银行外汇牌价汇率维护',
      bankInformation: '银行信息',
      bankName: '银行名称',
      bankContractorCorrespondence: '银承往来',
      barCode: '条形码',
      barCodeDetails: '条形码明细',
      baseAmountOfCashRebate: '现款返息基数',
      basePrice: '基准价',
      basicAccount: '基本户',
      basicConfiguration: '基础配置',
      basicInformationComoFillInKey: '基础信息公司、部门、人员、核算组需填写',
      basicTmu: '基础TMU',
      batchAttributeEntryType: '批次属性录入类型',
      batchAttributeInputBox: '批次属性输入框',
      batchAttributeSelection: '批次属性选择',
      batchAttributes: '批次属性',
      batchDownload: '批量下载',
      batchElementProperties: '批次要素属性',
      batchExport: '批量导出',
      batchModify: '批量修改',
      batchNote: '批注',
      beforeAdjustment: '调整前',
      beforeAndAfterTheGoods: '货前后',
      bigGoodsDeliveryTo: '大货交期到',
      organizationName: '组织名',
      bill: '单据',
      billCode: '单据编码',
      billExpirationDate: '票据到期日',
      billNumber: '单据编号',
      billOfArrivalPrice: '到货单价',
      billOfExchangeExpiryDate: '汇票到期日',
      billOfExchangeOpeningDate: '汇票开票日期',
      billOfLadingDate: '提单日期',
      billOfLadingInformation: '提单信息',
      billOfLadingNumber: '提单号',
      billOfLadingType: '提单类型',
      billReceivableInformation: '应收票据信息',
      billRegistrationNumber: '票据登记号',
      billType: '票据类型',
      billingCurrency: '计费币种',
      billingPeriod: '账期',
      billingQuantity: '计费数量',
      billingUnit: '计费单位',
      billingUnitPrice: '计费单价',
      bloodType: '血型',
      bomDetailListOfBigGoodsStage: '大货阶段BOM明细列表',
      bookkeepingDepartment: '记账部门',
      boxRules: '箱规',
      brand: '品牌',
      breakdownOfCashbackInterest: '现款返息明细',
      browse: '浏览',
      buildSign: '建签',
      bulk: '散货',
      businessCategory: '业务分类',
      businessCompany: '业务公司',
      businessDaily: '经营日报',
      businessDateFrom: '业务日期-从',
      businessDateTo: '业务日期-到',
      businessDictionary: '业务字典',
      businessDictionaryDetails: '业务字典详情',
      businessElement: '业务要素',
      businessException: '业务异常',
      businessNature: '客商性质',
      businessOrganization: '业务组织',
      businessOrganizationRelationships: '业务组织关系',
      businessPlanIndicators: '经营计划指标',
      businessScope: '经营范围',
      businessSector: '业务部门',
      businessSupportReviewData: '业助复核数据',
      businessTime: '业务时间',
      buttonCoding: '按钮编码',
      buttonInformation: '按钮信息',
      buttonManagement: '按钮管理',
      buttonName: '按钮名称',
      buyingPrice: '买入价',
      byAttachmentName: '按附件名称',
      cDSteel: '建发钢贸管家',
      cDSteelTradeManagerHome: '建发钢贸管家—首页',
      cDSupplyChainSteelTradeManager: '建发供应链钢贸管家',
      cabinetNumber: '柜号',
      carNoLiftersNameNameAndId: '车号/提货人姓名（姓名及身份证）',
      carNumberAndIdCardOequiredKey: '行的车号和身份证必填一项',
      carNumberAndIdCardRequiredOne: '车号和身份证必填一项',
      carNumberOrPickupPedIdCardKey: '车号或提货人（姓名及身份证）',
      card: '卡',
      cargoOwner: '货主',
      cargoOwnerInquiry: '货主查询',
      carriageNumber: '车船号',
      carrier: '承运人',
      carrierIdNumber: '承运人证件号',
      carrierInformation: '承运信息',
      carrierSContactNumber: '承运人联系电话',
      caseNumberNonConformityRules: '箱号不符合规则',
      cash: '现钞',
      cashAmount: '现款金额',
      cashFlow: '现款往来',
      cashRebateMethod: '现款返息方式',
      cashRebateParameters: '现款返息参数',
      cashRefundRateMonthlyInterest: '现款返息率(月息)',
      cashRemittance: '现汇',
      cashbackRateMonthlyInterest: '现款返息率（月息）',
      category: '分类',
      categoryTag: '类别',
      categoryChineseName: '分类中文名',
      categorySelection: '类别选择',
      certificateInformation: '证书信息',
      certificateNumber: '证书号',
      certificateStatus: '证书状态',
      certificateType: '证件类型',
      changeAdjustment: '变更调整',
      checkDataExistsForDstomersKey: '勾选数据存在不同公司或客户',
      checkThatDataExistsppliersKey: '勾选数据存在不同公司或供应商',
      china: '中国',
      chinaDeskInvoiceDetail: '中台发票明细',
      middle: '中',
      chineseAbbreviation: '中文简称',
      chineseActionGroupName: '中文动作组名',
      chineseComment: '中文注释',
      chineseDescription: '中文描述',
      chineseGroupName: '中文组名',
      chineseNameKey: '中文名',
      chineseNameBeyond50Lengths: '中文名超出50个长度',
      chineseNameOfCommodicationKey: '商品分类中文名称',
      chineseNameOfCurrency: '币种中文名',
      chineseNameOfTheIndustry: '行业中文名',
      chineseNameOfThePlan: '计划中文名',
      citation: '引用',
      citationForm: '引用表',
      citationProcess: '引用工序',
      citationProcessCopy: '引用工序（复制）',
      citationSheet: '引用单据',
      cityPortCode: '城市港口编码',
      cityPortCodeLengthGLengthsKey: '城市港口编码长度大于16个长度',
      classificationChineseDescription: '分类中文描述',
      classificationCode: '分类编码',
      classificationCodeName: '分类编码名称',
      classificationEnglishDescription: '分类英文描述',
      classificationEnglishName: '分类英文名',
      classificationName: '分类名称',
      classificationPrefix: '分类前缀',
      productClassificationPrefix: '商品分类前缀',
      clauseBaseLibrary: '条款基础库',
      clauseContent: '条款内容',
      clauseLibrary: '条款库',
      clauseTemplateDetails: '条款模板明细',
      clauseTemplates: '条款模板',
      clear: '清空',
      clearDetails: '明细',
      clearScreening: '清除筛选',
      clickOk: '点击确认',
      clickToUpload: '点击上传',
      clientAccountBank: '客户开户行',
      clientConfiguration: '客户配置',
      clientName: '客户名称',
      clientPaymentNumber: '客户款号',
      clientPhone: '客户电话',
      closingTime: '结束时间 ',
      code: '编码',
      codeName: '编码/名称',
      codeNameTag: '码名',
      codeSegment: '码段',
      codeSegmentInformation: '码段信息',
      codeValue: '码值',
      codeVariableDefinition: '编码变量定义',
      codingVariableDefinitionDetails: '编码变量定义详情',
      coefficientName: '系数值名称',
      collectOnBehalfOfTheLogisticsCosts: '代收代付物流费用',
      collectionDeadline: '收款截止日期',
      collectionDetails: '收款明细',
      color: '颜色',
      colorCode: '色号',
      colorCodeDetails: '色码明细',
      colorCodeList: '色码列表',
      colorColorNumber: '颜色色号',
      colorGroup: '色组',
      colorGroupDescription: '色组描述',
      colorGroupName: '色组名称',
      colorGroupNumber: '色组编号',
      colorTotal: '颜色合计',
      columnFreeze: '列冻结',
      columnReset: '列重置',
      combineBySpecification: '按规格合并',
      commissionClients: '佣金客户',
      commissionCurrency: '佣金币种',
      commissionInformation: '佣金信息',
      commissionPaymentRatio: '付佣比例',
      commissioningProvider: '委托商',
      commissioningTime: '委托时间',
      commodityClassificationCode: '商品分类编码',
      commodityClassificationName: '商品分类名称',
      commodityDeclaration: '报关商品',
      commodityLevel: '商品级次',
      commodityName: '商品名称',
      commonName: '常用名',
      commonNameKey: '客商常用名',
      commonNameOfItemNumber: '货号常用名',
      companyAgreementNumber: '公司协议号',
      companyCanNotBeEmpty: '公司不能为空',
      companyName: '公司名称',
      companyQuery: '公司查询',
      completedQuantity: '已完成数量',
      completedSalesVolume: '完成销量',
      completion: '完结',
      completionExcludingTax: '完成额（不含税）',
      completionAmountIncludingTax: '完成额（含税）',
      completionRatio: '完成比',
      completionRatioExcludingTax: '完成比（不含税）',
      component: '成分',
      componentUse: '组件使用',
      compressedPackageZip: '压缩包.zip',
      conditionValue: '条件值',
      conditions: '条件',
      confidentialDataIsSsmittedKey: '机密数据严禁外传',
      configurationOfProcrValuesKey: '工序参数值配置',
      confirmDeletionOfCurrentFile: '确认删除当前文件？',
      confirmDeletionOfFolders: '确认删除文件夹？',
      confirmDeletion: '确定删除?',
      confirmLogout: '确定登出',
      confirmNumberOfPieces: '确认件数',
      confirmQuantity: '确认数量',
      confirmSubmission: '确认提交',
      confirmationOfInvalidation: '确认作废？',
      consigneeIdNumber: '提货人证件号',
      consigneeSName: '提货人姓名',
      constantValue: '常量值',
      consumption: '耗量',
      contactAddress: '联系地址',
      contactAddressKey: '联系人地址',
      contactInformation: '联系方式',
      contactInformationKey: '联系信息 ',
      contactName: '联系人名称',
      contactNumber: '联系电话',
      contactPersonTag: '客商联系人',
      contactPerson: '联系人',
      contactPhoneNumber: '联系人电话',
      contactType: '联系方式类型 ',
      contactTypeKey: '联系人类型',
      containerSpecificationManagement: '集装箱规格管理',
      containerTypeDetails: '集装箱型详情',
      contract: '合同',
      contractCopy: '合同复制',
      contractDetails: '合同明细',
      contractExecutionDate: '合同执行日期',
      contractGoodsDetails: '合同商品明细',
      contractId: '合同编号',
      contractMarginPercentage: '合同保证金比例',
      contractNumber: '合同号',
      contractOriginalCurrencyCurrency: '合同原币币种',
      contractPickingAndMatching: '合同拣配',
      contractPrintTemplate: '合同打印模板',
      contractQuantity: '合同数量',
      contractQuantityList: '合同数量清单',
      contractTemplate: '合同模板',
      contractTimeLimit: '合同时效',
      contractType: '合同类型',
      contractTypeTag: '签约类型',
      contractUnit: '合同单位',
      contractUnitPrice: '合同单价',
      contractValidityTo: '合同有效期(到)',
      contractOrAmount: '合同|金额',
      contractOrCurrency: '合同|币种',
      contractOrQuantity: '合同|数量',
      contractRemainingQuantity: '合同|剩余数量',
      contractOrUnit: '合同|单位',
      contractOrUnitPrice: '合同|单价',
      contractingStatus: '签约状态',
      contractingTime: '签约时间',
      controlDrop: '控制跌幅',
      controlFields: '控制字段',
      conversionRatioTag: '转换比率',
      conversionStandardUnitRatio: '折算标准单位比例',
      conversionUnit: '换算单位',
      convertedUsage: '换算用量',
      copiedSuccessfully: '复制成功',
      copyBusinessRole: '复制业务角色',
      copyCell: '复制单元格',
      copyFailure: '复制失败',
      copyInformation: '复制信息',
      copyOrderPlan: '复制订货计划',
      copySelection: '拷贝选择',
      copyToRootOrganization: '拷贝到(根组织)',
      copyToYear: '拷贝到（年）',
      copyToGoods: '拷贝到货商品',
      copyYearDataItem: '拷贝年度数据项',
      copyright: '版权所有@2020-2022 xxxxxx公司',
      copyrightTag: '版权所有©2016-2018厦门建发股份有限公司',
      copyrightCD: '版权所有 © 2021 厦门建发股份有限公司',
      correspondenceItemClassification: '往来项目分类',
      correspondentCreator: '往来创建人',
      correspondentSubTypes: '往来子类型',
      correspondentSubtypequiredKey: '往来子类型和作业类型必填',
      correspondingStartDate: '对应起息日',
      correspondingStatement: '对应结算单',
      costAdjustmentNumber: '成本调整号',
      costClearingHouseOrganization: '成本结算中心组织',
      costDate: '成本日期',
      costDateFrom: '成本日期从',
      costDateTo: '成本日期到',
      costDetailsTag: '成本明细',
      costItem: '成本项目',
      costItemInvoiceCodeequiredKey: '成本项目、发票代码、发票号、开票日期必填',
      costNature: '成本性质',
      costOrderNumber: '成本单号',
      costSharingPercentage: '成本分摊比例%',
      costType: '成本类型',
      costTypeItemNumberAequiredKey: '成本类型、货号、到货数量、单位、含税单价、金额、增值税税率必填',
      counterpartyBankAccountNumber: '对方银行账号',
      counterpartyCustomerBank: '对方客户银行',
      country: '国',
      countryTag: '适用国别',
      countryArea: '国别地区',
      countryOfOrigin: '原产国',
      countryOfOriginRegion: '起运国(地区)',
      countrySize: '国别尺码',
      countrySizeBreakdown: '国别尺码明细',
      countrySizeSize: '国别尺码|尺码',
      createdSuccessfully: '创建成功',
      creationDate: '创建时间',
      creationDateEnd: '创建时间 截止',
      creationTimeFrom: '创建时间从',
      creationTimeStart: '创建时间 开始',
      creationTimeTo: '创建时间到',
      creatorSearch: '创建人查询',
      creditLineApplicationAdd: '信用额度申请新增',
      creditTermType: '信用期限类型',
      crossMonthRedFlush: '跨月红冲',
      cubicMeters: '立方米',
      thisCurrencyTag: '本位币币种',
      currency: '币种',
      currencyCannotBeEmpty: '币种不能为空',
      currencyCode: '币种编码',
      currencyEnglish: '币种英文名',
      currencyOfCustomsDeclaration: '报关币种',
      currencyOfPurchase: '购入币别',
      currencyPayable: '应付币种',
      currencyRegistration: '币种登记',
      currentAdjustmentOrder: '往来调整单',
      currentAdjustmentOrderManagement: '往来调整单管理',
      currentApprover: '当前审批人',
      currentBalanceReceivable: '当前应收余额',
      currentBalanceReceivableInquiry: '应往来余额查询',
      currentCostQuantity: '本次成本数量',
      currentDataAfterFilteringEverywhere: '到处筛选后的当前数据',
      currentDateDateFrom: '往来日期日期从',
      currentDocumentNumber: '往来单据号',
      currentInventoryQuantity: '本次库存数量',
      currentInvoicedQuantity: '本次开票数量',
      currentReceivable: '当前应收',
      currentShipmentAmount: '本期发货金额',
      currentShipmentAmountTag: '本期发货数量',
      currentUnitPrice: '当前单价',
      currentUnitPriceOfPickup: '当前提货单价',
      currentUseQuantity: '本次使用数量',
      customer: '客户',
      customerSupplier: '客户/供应商',
      customerAccount: '客商账户',
      customerAccountNumber: '客户账号',
      customerAddress: '客户地址',
      customerBankAccount: '客商银行账户',
      customerCode: '客户编码',
      merchantsCode: '客商编码',
      customerDetails: '客商详情',
      customerInformation: '客户信息',
      customerInquiry: '客户查询',
      customerSAttributionConfiguration: '客商归属配置',
      customerSCurrentPaymentInquiry: '客商往来款查询',
      customerMaintenance: '客商维护',
      customerManagement: '客商管理',
      customerMaterialNumber: '客户物料编号',
      customerName: '客商名称',
      customerNature: '客户性质',
      customerNumber: '客户编号',
      customerOwner: '客户属主',
      customerPersonnelSize: '客户人员规模',
      customerProducts: '客户商品',
      customerProperties: '客户属性',
      customerReceiptDetails: '客户签收单详情',
      customerReceiptList: '客户签收单列表',
      customerReceiptOrderAdd: '客户签收单新增',
      customerSearch: '客商查询',
      customerShortName: '客商简称',
      customerSigningOrderNumber: '客户签收单号',
      customerSigningWorkbench: '客户签收工作台',
      customerType: '客商类型',
      customsBrokerExchangeDate: '报关行换单日',
      customsCodeInformation: '海关商编信息',
      customsCodeName: '海关商编名称',
      customsCommodityCode: '海关商品编码',
      customsCommodityName: '海关商品名称',
      customsDeclarationInformation: '报关信息',
      customsDeclarationPort: '报关口岸',
      customsEnterpriseCode: '海关企业代码',
      customsInformationCode: '海关信息编码',
      customsNumber: '海关编号',
      cutOffBoxNumber: '截止箱号',
      cutOffDays: '截止天数',
      cutOffTime: '截止时间',
      dailyProductionNumber: '日产量编号',
      dailyProductionRegistrationList: '日产量登记表',
      dailyYieldRegistrationDetails: '日产量登记详情',
      dangerButton: '危险按钮',
      dataCutoffTo: '数据截止至',
      dataImportingPleaseWait: '数据导入中，请稍等...',
      dataItem: '数据项',
      dataLoading: '数据加载中',
      dataMatchFailedPleaseReImport: '数据匹配失败，请重新导入',
      dataStatus: '数据状态',
      dataSubmissionInProquentlyKey: '数据提交中，请勿频繁操作',
      dataTableResources: '数据表资源',
      dataValue: '数据值',
      dateCreated: '创建日期',
      dateCreatedFrom: '创建日期从',
      dateCreatedTo: '创建日期到',
      dateMethod: '日期方式',
      dateMethodDescription: '日期方式描述',
      dateOfActualPayment: '实付日期',
      dateOfApproval: '通过日期',
      dateOfArrival: '到货日期',
      dateOfArrivalTag: '来款日期',
      dateOfArrivalFrom: '到货日期-从',
      dateOfArrivalTo: '到货日期-到',
      dateOfArrivalNoticeTag: '到货通知单日期',
      dateOfBillOfLadingTo: '到货单日期到',
      dateOfBirth: '出生日期',
      dateOfBirthTag: '出生时间',
      dateOfBusiness: '业务日期',
      dateOfCorrespondence: '往来日期日期',
      dateOfDispatchTo: '调拨日期-到',
      dateOfEntry: '入仓日期',
      dateOfEntryTag: '入库日期',
      dateOfEntryFrom: '入仓日期从',
      dateOfEntryTo: '入仓日期到',
      dateOfExitFrom: '出仓日期 从',
      dateOfFirstDisbursement: '首次付款日',
      dateOfFirstPayment: '首次付款日期',
      dateOfImport: '进口日期',
      dateOfOccurrence: '发生日期',
      dateOfPosition: '出仓日期',
      dateOfPositionFrom: '出仓日期从',
      dateOfReceipt: '签收日期',
      dateOfReceiptTag: '收票日期',
      dateOfReceiptByTheCustomsBroker: '报关行接单日',
      dateOfRevenueFrom: '收入日期-从',
      dateOfShipmentFrom: '发货日期 从',
      dateOfShipmentTo: '发货日期到',
      dateOfShippingNotification: '发货通知日期',
      dateOfSigningFrom: '签约日期从',
      dateOfSubmission: '提交日期',
      dateOfTheBillOfLading: '到货单日期',
      dateOfTheBillOfLadingFrom: '到货单日期从',
      dateOfTransaction: '往来日期',
      dateOfTransactionFrom: '往来日期从',
      dateOfTransfer: '调拨日期',
      dateOfTransferTag: '移仓日期',
      dateOfTransferFrom: '调拨日期-从',
      dateTo: '往来日期到',
      dayEndCreditDays: '日结信用天数',
      dayEndRecordCreditPrOfDaysKey: '日结记录信用期限（天数）',
      Days: '天数',
      daysOfDiscountReceivable: '应收贴息天数',
      daysOfTurnaround: '周转天数',
      daysPastDue: '逾期天数>=',
      deadWeight: '自重',
      deadline: '截止',
      deadlineTag: '截止日期',
      deadlineEffectiveDateFrom: '截止有效日期从',
      deadlineEffectiveDateTo: '截止有效日期到',
      deadweightUnit: '自重单位',
      debit: '借贷',
      decimalPlaces: '小数位',
      declarationInstructions: '报审说明',
      declaredQuantities: '已报关数量',
      defaultAccountAlreaAccountKey: '已存在默认账号，不可有多个默认账号',
      defaultAccountNumber: '默认账号',
      defaultAmount: '违约金',
      defaultButton: '默认按钮',
      defaultContactOrNot: '是否默认联系人',
      defaultCurrency: '默认币种',
      defaultHomePage: '默认首页',
      defaultTargetCurrency: '默认目标币种',
      defaultValue: '默认值',
      definitionCode: '定义编码',
      deleteEquipment: '删除设备',
      deletePersonnel: '删除人员',
      deleteStation: '删除站位',
      deleteStatus: '删除状态',
      deleteThisPage: '删除本页',
      deliveryAmount: '交货金额',
      deliveryDateTag: '交货日期',
      deliveryDateTagKey: '送货日期',
      deliveryDateFrom: '送货日期 从',
      deliveryDateTo: '送货日期 到',
      deliveryDays: '交期天数',
      deliveryDetails: '交货明细',
      deliveryOrder: '交单',
      deliveryPlan: '送货计划',
      deliveryPlanNumber: '送货计划单号',
      deliveryQuantity: '交货数量',
      deliveryTimeTag: '送货时间',
      deliveryTimeAndTranSummaryKey: '交期及运输概要',
      deliveryTimeOfLargeGoods: '大货交期',
      deliveryTimeOfLargeGoodsFrom: '大货交期从',
      demandAmount: '需求量',
      department: '部门',
      departmentSt: '部位',
      departmentNd: '所在部门',
      departmentCannotBeEmpty: '部门不能为空',
      departmentClassification: '字典分类名称',
      departmentCode: '部门编码',
      departmentCodeRequired: '部位代码必填',
      departmentLibrary: '部位库',
      departmentName: '部门名称',
      departmentOfCreditAllocation: '额度分配部门',
      departmentOfUse: '使用部门',
      departmentSearch: '部门查询',
      dependencyCode: '依赖编码',
      dependencyName: '依赖名称',
      depositReceivable: '应收保证金',
      depositReceivableAmount: '应收保证金金额',
      description: '描述',
      descriptionInformation: '描述信息',
      descriptionOfCommodityName: '商品名称描述',
      descriptionOfPrintingAndEmbroidery: '印绣花描述',
      descriptionOfTheEvent: '事迹描述',
      descriptionOfTheReturnRequest: '退货申请说明',
      designCode: '设计编码',
      designDiagram: '设计图',
      designMapDetails: '设计图明细',
      designatedSuppliers: '指定供应商',
      desktopNotification: '桌面通知',
      destination: '目的地',
      destinationDetails: '目的地详细地址',
      destinationPort: '目的口岸',
      destinationPortDirermationKey: '目的港直放信息',
      destinationPortTerminal: '目的港码头',
      destinationProvinceCity: '目的地省/市',
      detailPageMenu: '详情页菜单',
      detailedAddressOfDestination: '目的详细地址',
      detailedDescription: '细节描述',
      detailedInformation: '明细信息',
      detailedVoidMark: '明细作废标识',
      details: '详情',
      detailsKey: '大底明细',
      detailsOfAcceptanceDiscount: '承兑贴息明细',
      detailsOfProcessRequirements: '工艺要求明细',
      detailsOfRebateInterestReturn: '返利返息明细',
      detailsOfRedFlush: '红冲明细',
      detailsOfTheConfigurValuesKey: '工序参数值配置详情',
      developmentDictionaryDetails: '开发字典详情',
      deviceParameterConfiguration: '设备参数配置',
      deviceParameters: '设备参数',
      deviceTmw: '设备TMU',
      dialogBoxCode: '对话框编码',
      dialogBoxCodeMatch: '对话框编码匹配',
      dialogEntity: '对话框实体',
      dialogName: '对话框名称',
      dictionaryAlias: '字典别名',
      dictionaryCategoryName: '字典分类名',
      dictionaryClassificationCode: '字典分类编码',
      dictionaryName: '字典名称',
      dictionaryValue: '字典值',
      dictionaryValueName: '字典值名称',
      differenceBetweenEnInvoiceKey: '入库单与发票差额',
      dimensionUnit: '尺寸单位',
      directPickupLetterTemplate: '直提函模板',
      directRelease: '直放',
      directReleaseImport: '直放导入',
      directReleaseOutOfPntBenchKey: '直放出仓待调整工作台',
      directReleaseOutbountOrderKey: '直放出仓调整单',
      directReleaseOutbouDetailsKey: '直放出仓调整单详情',
      directReleaseOutbourNumberKeyTag: '直放出仓调整单号',
      directReleaseOutboursAddedKey: '直放出仓调整单新增',
      directReleaseOutbourNumberKey: '直放出仓回单号',
      directReleaseShippingOrderNumber: '直放发货单号',
      directionOfReceiptAndPayment: '收付方向',
      discountRate: '承兑贴息',
      discountRmbExchangeRate: '折人民币汇率',
      discountType: '折让类型',
      discountedRmbAmount: '折人民币金额',
      discountedUsdAmount: '折美元金额',
      discrepancyStatistics: '差异统计',
      discrepancyStatisticsDetail: '差异统计明细',
      dispatchName: '调度名称',
      dispatchType: '调拨类型',
      displayField: '显示字段',
      displayHasCorrespondedTo: '显示已对应',
      displayMethod: '显示方式',
      displayZeroInventory: '展示零库存',
      documentChineseTableName: '单据中文表名',
      documentDatabaseTableName: '单据数据库表名',
      documentDate: '单据日期',
      documentDateFrom: '单据日期从',
      documentDescription: '单据描述',
      DocumentDetails: '单据详情',
      documentName: '单据名称',
      documentNameKey: '单据名',
      documentNumber: '单据号',
      documentNumberKey: '单号',
      documentOperationScript: '单据操作脚本',
      documentProcessConfagementKey: '单据流程配置管理',
      documentSelection: '单据选择',
      documentSelectionKey: '单据挑选',
      documentStatus: '单据状态',
      documentType: '单据类型',
      domainNameCannotBeEmpty: '域名不能为空',
      domesticProcurementerAddedKey: '国内采购到货单新增',
      domesticProcurementArrivalWorkbench: '国内采购到货工作台',
      domesticProcurementGoods: '国内采购商品',
      domesticProcurementDetailsKey: '国内采购入仓回单详情',
      domesticProcurementReceiptKey: '国内采购入仓回单',
      domesticProcurementrkbenchKey: '国内采购待到货工作台',
      domesticProcurementrkbenchKeyTag: '国内采购待入仓工作台',
      domesticProcurementReceiptAdded: '国内采购入仓回单新增',
      domesticProcurementReturnOrder: '国内采购退货单',
      domesticProcurementReturnReceipt: '国内采购退货出仓回单',
      domesticProcurementReturnWorkbench: '国内采购退货工作台',
      domesticPurchase: '国内采购',
      eSignatureId: 'E建签ID',
      domesticPurchaseInbound: '国内采购入仓',
      domesticPurchaseOrderDetails: '国内采购到货单详情',
      domesticPurchaseReceipt: '国内采购到货单',
      domesticSales: '国内销售',
      domesticSalesDeliveryOrder: '国内销售发货单',
      domesticSalesDeliveryOrderAdded: '国内销售发货单新增',
      domesticSalesGoods: '国内销售商品',
      domesticSalesOrderDetails: '国内销售发货单详情',
      domesticSalesOutbound: '国内销售出仓',
      domesticSalesOutboundReturnDetails: '国内销售出仓回单详情',
      domesticSalesOutbourmAddedKey: '国内销售出仓回单新增',
      domesticSalesOutboundReturnReceipt: '国内销售出仓回单',
      domesticSalesOutboundWorkbench: '国内销售待出仓工作台',
      domesticSalesPendinrkbenchKey: '国内销售待发货工作台',
      domesticSalesReturnOrder: '国内销售退货单',
      domesticSalesReturnurnFormKey: '国内销售退货入仓回单',
      domesticSalesReturnWorkbench: '国内销售退货工作台',
      domesticTransportationContract: '国内运输合同',
      downloadMobileApp: '下载手机APP',
      downstreamIdxField: '下游IDX字段',
      downstreamTable: '下游表',
      downstreamTableName: '下游表名',
      downstreamUnderwritingFields: '下游核销字段',
      dragAndDropTheFileHere: '将文件拖到此处',
      dragHereToSetTheGrouping: '拖动到此处设置分组',
      dragTheFileHereOr: '将文件拖到此处，或',
      dragTheFileHereOrClickUpload: '将文件拖到此处，或点击上传',
      driver: '司机',
      duplicateRecord: '复制记录',
      earliestBusinessDate: '最早业务日期',
      earliestDeliveryDateFrom: '最早交期从',
      earliestDeliveryDateTo: '最早交期到',
      earliestInboundDate: '最早入库日期',
      earliestInboundDateTag: '最早入库时间',
      earlyInvoicingLogo: '提前开票标识',
      eContractCancellation: '电子签约撤单',
      editActionParameterConfiguration: '编辑动作参数配置',
      editAssetCategories: '编辑资产大类',
      editAssetPropertyDetails: '编辑资产属性详情',
      editEquipmentParameterDetails: '编辑设备参数详情',
      editPartInformation: '编辑部件信息',
      editProcessCategory: '编辑工序类别',
      editProcessCategoryTag: '编辑工艺类别',
      editProcessParameters: '编辑工艺参数',
      editSuccessfully: '编辑成功',
      editable: '可编辑',
      editingActionCodes: '编辑动作代码',
      editingProcessParameters: '编辑工序参数',
      educationHistory: '教育履历',
      educationTimeFrom: '教育时间从',
      educationTimeTo: '教育时间到',
      educationType: '教育类型',
      effectiveDate: '生效日期',
      effectiveDateFrom: '生效日期从',
      effectiveDateOfThisVersion: '本版本生效日期',
      effectiveDateTo: '生效日期到',
      effectiveTime: '生效时间',
      emailNotification: '邮件通知',
      emergencyContact: '紧急联系人',
      emergencyContactPhone: '紧急联系人电话',
      employeeSName: '员工姓名',
      employeeNumber: '员工编号',
      empty: '为空',
      emptyStationsAreNotDisplayed: '空站不显示',
      enableDate: '启用日期',
      enableStatus: '启用状态',
      enabledSuccessfully: '启用成功',
      encodingRuleConfiguration: '编码规则配置',
      encodingRuleDefinition: '编码规则定义',
      encodingRuleDefinitionDetails: '编码规则定义详情',
      endDate: '结束日期',
      endOfPeriodReceivableBalance: '期末应收余额',
      endTime: '结束时间',
      english: '英文',
      englishDescription: '英文描述',
      englishGroupName: '英文组名',
      englishName: '英文名',
      englishNameBeyond50Lengths: '英文名超出50个长度',
      englishNameOfTheIndustry: '行业英文名',
      enterContent: '输入内容',
      enterEntityAttributeFieldsKey: '输入实体属性，中文名称，表字段',
      enterQuantityTag: '入仓数量',
      enterResourceCode: '输入资源编码',
      enterSearchCriteria: '输入搜索条件',
      enterpriseType: '企业类型',
      entityName: '实体名称',
      entityProperties: '实体属性',
      entityPropertiesChieFieldsKey: '实体属性, 中文名称, 表字段',
      entrustedParty: '委托方',
      entrustedPartyInquiry: '委托方查询',
      entryDate: '入仓日期',
      entryDateFrom: '入仓日期从',
      entryDateFromTag: '入职日期从',
      entryDateTo: '入仓日期 到',
      entryMethod: '录入方式',
      entryPageCode: '入口页编码',
      entryQuantity: '入库数量',
      entryType: '入仓类型',
      entryTypeTag: '入库类型',
      entryExitType: '出入类型',
      equipmentBaseSam: '设备基础SAM',
      equipmentBaseSamSec: '设备基础SAM(秒)',
      equipmentChineseName: '设备中文名',
      equipmentClassification: '设备分类',
      equipmentConfirmation: '设备确认',
      equipmentDoesNotMeerementsKey: '设备不符合当前站位需求',
      equipmentEnglishName: '设备英文名',
      equipmentName: '设备名称',
      equipmentNameSelection: '设备名称挑选',
      equipmentParametersSelection: '设备参数挑选',
      equipmentPick: '设备挑选',
      equipmentStationDistributionMap: '设备站位分布图',
      erpSystemForPulpAndPaperIndustry: '浆纸行业ERP系统',
      establishedDate: '成立日期',
      estimatedArrivalDate: '预计到港日期',
      estimatedArrivalPercentage: '预计到货占比',
      estimatedDateOfDepartureTo: '预计出仓日期-到',
      estimatedDateOfShipment: '预计出仓日期',
      estimatedDateOfShipmentFrom: '预计出仓日期-从',
      estimatedPurchaseCostAddition: '预估采购成本新增',
      estimatedPurchaseCostDetails: '预估采购成本详情',
      estimatedRevenueAdded: '预估收入新增',
      estimatedRevenueDetails: '预估收入详情',
      estimatedRevenueRegistration: '预估收入登记',
      exampleMinD12345Wa4660225Key: '例：闽D12345,王某某,13066200632,33253112319660225;闽D123126 ,王某某,13066200632,33253119534660225',
      exceededQuantityPrompt: '超出数量提示',
      exception: '异常',
      exceptionCode: '异常编码',
      exceptionCodeManagementList: '异常编码管理列表',
      exceptionCodesExcepilledInKey: '异常编码、异常消息、多语言必须填写',
      exceptionMessage: '异常消息',
      ExceptionRequestDetails: '异常请求详情',
      exceptionRequestInformation: '异常请求信息',
      exceptionRequestWorkbench: '异常请求工作台',
      exceptionTypeSt: '异常类型',
      exchangeCurrency: '兑换币种',
      exchangeRateTag: '折美元汇率',
      exchangeRateIssuingBank: '汇率发布银行',
      exchangeRateNumber: '汇率编号',
      exchangeRateNumberSelection: '汇率编号挑选',
      exciseTax: '消费税',
      excludingTaxAmount: '不含税|金额',
      excludingTaxTotal: '不含税|总额',
      executionDays: '执行天数',
      executionMethod: '执行方法',
      executionTime: '执行时间',
      exemptions: '减免',
      existing: '已存在',
      existingBaseAccountAccountKey: '已存在基本户，不可有多个基本户',
      existingSizeSectionCountrySize: '已存在码段 国别尺码！',
      expectedArrivalDateTag: '预计抵港日',
      expectedDateOfDelivelectedKey: '预计交货日期、最早交货日期未选择',
      expectedDateOfEntryFrom: '预计入仓日期-从',
      expectedDateOfEntryTo: '预计入仓日期-到',
      expectedDateOfIssuance: '预计开证日期',
      expectedDateOfReceiptAndPayment: '预计收付日期',
      expectedDateOfShipment: '预计起运日期',
      expectedDeliveryDateTag: '预计交付日期',
      expectedInboundDate: '预计入仓日期',
      expectedPaymentDateTag: '预计付款日期',
      expectedPaymentDateFrom: '预计付款日期从',
      expectedReceiptDate: '预计收款日期',
      expenseBillNumber: '费用单号',
      expenseDetails: '费用明细',
      expenseInvoice: '费用发票',
      expenseInvoiceAmount: '费用发票金额',
      expenseInvoiceRegistration: '费用发票登记',
      expensePayment: '费用付款',
      expensePaymentAmount: '费用付款金额',
      expenseType: '费用类型',
      expirationDateTag: '截止有效日期',
      expirationDateFrom: '过期日期-从',
      expirationDateTo: '过期日期-到',
      expirationDateToTag: '失效日期到',
      expirationTime: '失效时间',
      expirationTimeSt: '到期时间',
      exportTheCurrentDataAfterFiltering: '导处筛选后的当前数据',
      exportSpecificInvoiceLimit: '出口专用发票限额',
      expressions: '表达式',
      extendedConfiguration: '扩展配置',
      fabric: '面料',
      fabricAnalysisWorkbench: '面料分析工作台',
      fabricAndAccessoriesDistribution: '面辅料分布图',
      fabricBom: '面料BOM',
      fabricDesign: '面辅料设计图',
      fabricRequirementsList: '面料需求清单',
      fabricRequirementsSummary: '面料需求汇总',
      factSheet: '情况说明',
      factoryData: '出厂数据',
      factoryDataWorkbench: '出厂数据工作台',
      factoryNameEnglish: '厂区名称(英文)',
      failedToDelete: '删除失败',
      failedToGetToken: '获取token失败',
      failedToSaveProcessDetails: '保存工序详情失败',
      failure: '失败',
      failureRequestLogRetransmission: '失败请求日志重发',
      failureRequestLogRetransmissionV2: '失败请求日志重发V2',
      fax: '传真',
      feeAdjustmentOrder: '费用调整单',
      feeAdjustmentOrderAdded: '费用调整单新增',
      feeConfiguration: '费用配置',
      feeDate: '费用日期',
      feeDateFrom: '费用日期-从',
      feeDateTo: '费用日期-到',
      feeDetailSelection: '费用明细选择',
      feeTime: '费用时间',
      feeTimeFrom: '费用时间 从',
      fees: '费用',
      fetchFunction: '取值函数',
      field: '字段',
      fieldCode: '字段编码',
      fieldDefinition: '字段定义',
      fieldName: '字段名',
      fieldNumber: '字段编号',
      fieldResource: '字段资源',
      fieldSelection: '字段选择',
      fieldType: '字段类型',
      fieldsMarkedInRedAreRequired: '标红字段必填！',
      fileDescription: '文件描述',
      fileName: '文件名',
      filePath: '文件路径',
      fileSize: '文件大小',
      fileUpload: '文件上传',
      filingDate: '申报日期',
      fillInTheAccountNumber: '填写账号',
      finalDeliveryPlace: '最终交货地',
      finalDestinationTag: '最终目的国',
      financialCostParameterDetails: '财务费用参数明细',
      financialOrganizationInquiry: '财务组织查询',
      fireTransportInformation: '火运信息',
      fireTransportUnitPriceYuan: '火运单价（元）',
      first: '第',
      fixedHeader: '固定 Header',
      flushColorMatchingChart: '齐色配色图',
      food: '食品',
      food2: '食品2',
      food1: '食品1',
      foreignProcurementIptAddedKey: '国外采购入仓回单新增',
      foreignPurchase: '国外采购',
      foreignPurchaseInboundReturnDetails: '国外采购入仓回单详情',
      foreignPurchaseInboundReturnReceipt: '国外采购入仓回单',
      foreignPurchaseOfGoods: '国外采购商品',
      foreignSales: '国外销售',
      foreignSalesTag: '国外销售商品',
      formatDoesNotMatch: '格式不符',
      formulaCode: '公式编码',
      formulaConfiguration: '公式配置',
      formulaName: '公式名称',
      formulaParameterDetails: '公式参数明细',
      formulas: '公式',
      forwardBillTermDays: '远期票据期限天数',
      freeze: '冻结',
      freightAmount: '运费金额',
      freightCarrier: '运费承担商',
      freightCharge: '运费承担',
      freightNumber: '货号',
      freightNumberCode: '货号编码',
      freightNumberCodeArequiredKey: '货号编码、到货数量、含税单价必填',
      freightPaymentMethod: '运费支付方式',
      freightServiceProvider: '货运服务商',
      freightUnitPrice: '运费单价',
      fromRootOrganization: '从(根组织)',
      fromYear: '从（年）',
      frontAndSideStructureDrawing: '正侧面结构图',
      fullContainerArrivanNumberKey: '整柜到货通知号',
      functionCode: '功能编码',
      functionName: '功能名称',
      gender: '性别',
      generalInvoiceLimit: '普通发票限额',
      generalProperties: '普通属性',
      generateArrival: '生成到货',
      generateArrivalOrder: '生成到货单',
      generateBillingStatement: '生成结算单',
      generateDirectReleantOrderKey: '生成直放出仓调整单',
      generateDirectReleaseReturnNotice: '生成直放退货通知单',
      generateDomesticPurchaseInvoice: '生成国内采购发票',
      generateFinalSettlement: '生成终结算',
      generateInboundReceipt: '生成入仓回单',
      generateInboundReturnAdjustment: '生成入仓回单调整单',
      generateReceiptAdjustment: '生成进仓调整单',
      genericCommodity: '通用商品',
      genericTemplate: '通用模版',
      goods: '货品',
      goodsInTransit: '在途货物',
      goodsTicketCorrespondence: '货票对应',
      goodsTicketCorrespondenceDetails: '货票对应明细',
      goodsValueInvoiceDetails: '货值发票明细',
      goodsValueInvoiceNumber: '货值发票号',
      grade: '等级',
      gramWeightG: '克重(g)',
      grammage: '克重',
      grossProfit: '毛利',
      grossProfitStatement: '毛利报表',
      grossWeightKg: '毛重(千克)',
      grossWeightSingleBox: '毛重（单箱）',
      groupByContract: '按合同分组',
      groupByMaterial: '按物料分组',
      groupBySection: '按款分组',
      groupCustomerCode: '群组客户编码',
      groupCustomerCreditApplicationAdded: '群组客户信用额度申请新增',
      groupCustomerCreditLineApplication: '群组客户信用额度申请',
      groupCustomerCreditLineInquiry: '群组客户信用额度查询',
      groupCustomerDefinition: '群组客户定义',
      groupCustomerLineOficationKey: '群组客户信用额度申请',
      groupCustomers: '群组客户',
      groupOwnerCustomer: '群主客户',
      groupOwnerCustomerCode: '群主客户编码',
      guarantee: '保证金',
      hangingDailyProduction: '吊挂日产量',
      hangingDailyProductionInquiry: '吊挂日产量查询',
      hasBeenDiscountedAmount: '已折让金额',
      haveSelected0Articlnt0YuanKey: '已选0条，合计：已到货未到票数量0吨，金额0元',
      healthStatus: '健康状况',
      helpCenter: '帮助中心',
      helper: '助记符',
      highestEducation: '最高学历',
      highestEfficiency: '最高效率',
      historyApprovalRecord: '历史审批记录',
      historyFactoryDetails: '历史出厂明细',
      historyVersion: '历史版本',
      hobbiesAndSpecialties: '爱好特长',
      homePage: '首页',
      huaweiCloud: '华为云',
      human: '人',
      iAmAUnit: '我是单位',
      iHaveNotReviewed: '我未审',
      iHaveReviewed: '我已审',
      iSubmittedForApproval: '我提交的审批',
      icon: '图标',
      id: '身份证',
      idCardAfterFillingInTheFirst: '身份证填写后第',
      idCardNumber: '身份证号',
      idCardNumberTag: '身份证号码',
      idCardToFillInAfterequiredKey: '身份证填写后提货人和联系电话必填',
      idNumber: '证件号',
      identification: '识别',
      ifTheRemainingQuanteUsedUpKey: '剩余数量用完，剩余件数也必须用完',
      ifYouChangeTheMaterClearedKey: '更换物料分类,要素属性将被清空',
      changepProductCtegoryCleared: '更换商品分类,码段信息、 批次要素属性将被清空',
      changepProductCtegory: '更换商品分类,要素属性将被清空',
      imageInformation: '图片信息',
      imageName: '图片名',
      imageSizeKb: '图片大小（KB）',
      imageUrl: '图片URL ',
      impactFactorTable: '影响因素表',
      impactFactors: '影响因素',
      impactField: '影响字段',
      importAndAdd: '导入并新增',
      importArrivalOrder: '进口到货单',
      importArrivalOrderAdd: '进口到货单新增',
      importArrivalOrderDetails: '进口到货单详情',
      importDateFrom: '进口日期从',
      importDateTo: '进口日期到',
      importDeclarationForm: '进口报关单',
      importEnterpriseCode: '进口企业代码',
      importFailed: '导入失败!',
      importMatch: '导入匹配',
      importPendingArrivalWorkbench: '进口待到货工作台',
      importPendingCustomrkbenchKey: '进口待报关工作台',
      importProcurementContract: '进口采购合同',
      importProcurementContractDetails: '进口采购合同详情',
      importProcurementContractNumber: '进口采购合同号',
      importProcurementGoodsValueInvoice: '进口采购货值发票',
      importProcurementPerkbenchKey: '进口采购待开票工作台',
      importStatementReceipt: '导入结算单回执',
      importSuccessful: '导入成功!',
      importVat: '进口增值税',
      inTransit: '在途',
      inTransitTons: '在途(吨)',
      inTransitOrderNumber: '在途单号',
      inOutDate: '出入仓日期',
      inOutDateTag: '进出仓日期',
      inOutReturnNumber: '进/出回单号',
      inOutTime: '进出仓时间',
      inOutTimeFrom: '进出仓时间-从',
      inOutTimeTo: '进出仓时间-到',
      inOutWarehouse: '进/出仓库',
      inOutWarehouseDetailInquiry: '进出仓明细查询',
      inOutWarehouseSchedonXlsxKey: '进出仓明细表信息_.xlsx',
      inboundTag: '入库',
      inboundAdjustmentOrder: '进仓调整单',
      inboundAdjustmentOrderNumber: '进仓调整单号',
      inboundAdjustmentWorkbench: '进仓调整工作台',
      inboundAndOutbound: '进出仓',
      inboundConsignee: '境内收货人',
      inboundCurrency: '入账币种',
      inboundDate: '入职日期',
      inboundDestination: '境内目的地',
      inboundOrderNumber: '入仓通知单号',
      inboundQuantityThisMonth: '本月入库量',
      inboundReturnAdjustmentOrder: '入仓回单调整单',
      inboundReturnAdjustmentOrderDetails: '入仓回单调整单详情',
      inboundReturnAdjustmentOrderNew: '入仓回单调整单新增',
      inboundReturnAdjustmentOrderNo: '入仓回单调整单号',
      inboundReturnAdjustmentWorkbench: '入仓回单调整工作台',
      inboundReturnNumberTag: '入仓回单号',
      inboundReturnOrderDetail: '入仓回单明细',
      inboundReturnOrderNumber: '入仓回单单号',
      inboundSubTable: '入仓子表',
      inboundWarehouseDetails: '进仓明细',
      inboundWarehousePenrkbenchKey: '入仓待成本登记工作台',
      inboundOutboundDateFrom: '入/出仓日期从',
      inboundOutboundDateTo: '入/出仓日期到',
      incomingModel: '来款',
      inconsistentPleaseSelectAgain: '不一致，请重新选择',
      indicator: '指标',
      indicatorQuantityAmount: '指标数量/金额',
      industryInterconnection: '产业互联',
      industryName: '行业名称',
      information: '息',
      informationButton: '信息按钮',
      informationOfTheConsignor: '提货人信息',
      inLine: '线内',
      inLineAverageRhythm: '线内平均节奏',
      inLineBalanceChart: '线内平衡图',
      inLineOutOfLine: '线内/线外',
      insertAboveTheLine: '行上方插入',
      insertBelowTheLine: '行下方插入',
      insertParameters: '插入参数',
      inspectionReport: '验货报告',
      inspectionStandard: '验货标准',
      insuranceFree: '是否免保险',
      insuredAmount: '投保金额',
      insuredParty: '投保方',
      integrateResourcesCreateValueAdded: '整合资源 创造增值',
      intelligentComparison: '智能比对',
      interestAccrualMethod: '计息方式',
      interestBase: '计息基数',
      interestBreakdown: '利息分段明细',
      interestRateTag: '利率',
      interestRateTagSt: '息率',
      interestRate: '利息',
      interestStartingStandard: '利息起算标准',
      interestBearingItems: '计息项目',
      interestFreeDays: '免息天数',
      interestFreeDaysOfAcceptance: '承兑免息天数',
      interestFreeExpirationDate: '免息到期日',
      interfaceAddress: '接口地址',
      internalCodeField: '内码字段',
      internalProfileStructureDiagram: '内剖结构图',
      internationalShippingInvoice: '国际运费发票',
      internationalShippirkbenchKey: '国际运费待开票工作台',
      inTransitTag: '在途单',
      inTransitDetails: '在途明细',
      inTransitImport: '在途导入',
      inTransitManagement: '在途单管理',
      inTransitOrderSavedSuccessfully: '在途单保存成功',
      inTransitQuantity: '在途数量',
      inTransitRegistrationBillNumber: '在途登记单号',
      inTransitStatus: '在职状态',
      invalidAccessToken: '无效的访问Token！',
      invalidate: '作废',
      inventoryAddress: '存货地址',
      inventoryAdjustment: '存货调整',
      inventoryAge: '库龄',
      inventoryAgeFrom: '库龄从',
      inventoryAmount: '库存金额',
      inventoryBalanceAgionXlsxKey: '库存余额账龄报表信息_.xlsx',
      inventoryContractAdagementKey: '存货合同调整管理',
      inventoryContractAdrNumberKey: '存货合同调整单号',
      inventoryContractAdssfullyKey: '存货合同调整单保存成功',
      inventoryContractAdrkbenchKey: '存货合同调整工作台',
      inventoryDataSelection: '盘盈数据选择',
      inventoryDetail: '库存明细',
      inventoryItems: '库存商品',
      inventoryMethod: '库存方式',
      inventoryPlanQuantity: '库存计划数量',
      inventoryPosition: '库位',
      inventoryPrepickAllocation: '库存预拣配',
      inventoryProfitEntryOrder: '盘盈入仓单',
      inventoryRatio: '库存比例',
      inventorySearch: '库存查询',
      inventorySummaryInquiry: '库存汇总查询',
      inventoryWithholding: '库存预扣',
      inventoryConversionRatio: '库存|换算比例',
      inventoryPickableQuantity: '库存|可拣配数量',
      inventoryOrQuantity: '库存|数量',
      inventoryUnits: '库存|单位',
      inventoryUsedQuantierThan0Key: '库存|使用数量需要大于0',
      inventoryPiece: '库存件数',
      inventoryTonnage: '库存-吨数',
      invoice: '发票',
      invoiceAfterTheGoods: '货后开票',
      invoiceAmount: '发票金额',
      invoiceAmountInformation: '发票金额信息',
      invoiceCategory: '发票大类',
      invoiceCategoryTag: '发票细类',
      invoiceCategoryCannotBeEmpty: '发票大类不能为空',
      invoiceCategoryCannotBeEmptyTag: '发票细类不能为空',
      invoiceCode: '发票代码',
      invoiceCodeCannotBeEmpty: '发票代码不可为空',
      invoiceCorrespondenceInformation: '发票对应信息',
      invoiceCustomer: '发票客户',
      invoiceCustomerTag: '发票客商',
      invoiceCustomerCannotBeEmpty: '发票客户不能为空',
      invoiceCustomeCustomer: '发票客户,客户',
      invoiceDate: '出票日期',
      invoiceDateTag: '开票日期',
      invoiceDateTagSt: '出票日期',
      invoiceDateKey: '发票日期',
      invoiceDateCannotBeEmpty: '发票日期不能为空',
      invoiceDateFrom: '发票日期-从',
      invoiceDateTo: '发票日期-到',
      invoiceInformation: '发票信息',
      invoiceIssuer: '出票人',
      invoiceIssuerAccountNumber: '出票人账号',
      invoiceList: '开票列表',
      invoiceNature: '发票性质',
      invoiceNumber: '发票号',
      invoiceNumberCannotBeEmpty: '发票号不可为空',
      invoicePayable: '应开票',
      invoiceProductDetails: '发票商品明细',
      invoiceQuantity: '发票数量',
      invoiceRegistrationNumber: '发票登记号',
      invoiceRequisitionNumber: '发票申开单号',
      invoiceStatus: '发票状态',
      invoiceUnit: '发票单位',
      invoiceUnitPrice: '发票单价',
      invoiceVoidedSuccessfully: '发票作废成功',
      invoicedAmount: '已开票金额',
      invoicedQuantity: '已开票数量',
      invoicedQuantityTag: '已开数量',
      invoicedQuantityMusuantityKey: '开票数量必须大于0且小于未开票数量',
      invoicedQuantityStaequiredKey: '开票数量、标准换算比例、单位、单价、金额必填',
      invoicedOrQuantity: '已开票|数量',
      invoicing: '开票',
      invoicingBankAccountNumber: '开票银行账号',
      invoicingBankName: '开票银行名称',
      invoicingCustomer: '开票客户',
      invoicingCustomerSBankAccount: '开票客户开户行',
      invoicingCustomerSBankAccountNumber: '开票客户银行账号',
      invoicingDateFrom: '开票日期-从',
      invoicingDateTo: '开票日期-到',
      invoicingUnit: '开票单位',
      invoicingUnitLogistupplierKey: '开票单位,物流供应商,供应商',
      inwardAndOutwardWarnNumberKey: '进出仓回单号',
      isThePositionAvailable: '是否可出仓',
      isTheSample: '是否样品',
      issuedBy: '发布人',
      issuedSuccessfully: '发起成功',
      issuingBank: '出票银行',
      issuingStatus: '发单状态',
      item: '品名',
      itemTag: '项目',
      itemDate: '项目日期',
      itemName: '项目名称',
      itemNumberTag: '项目号',
      itemNumberChineseName: '货号中文名',
      itemDescription: '款号描述',
      itemNumberId: '项目号id',
      itemNumberPicture: '货号图片',
      itemNumberItemName: '项目号/项目名称',
      jobSkillLevel: '工作技能等级',
      jobType: '作业类型',
      jump: '跳转',
      keyProperties: '键属性',
      keyword: '关键字',
      landTransportUnitPrice: '陆运单价',
      largeBase: '大底',
      largeBottomDesignDrawing: '大底设计图',
      largeCategoryCode: '大类编码',
      last: '楦头',
      lastApprovalStatus: '最近审批情况',
      lastDetails: '楦头明细',
      lastLevelProductCategory: '末级商品分类',
      lastModifiedDate: '最后修改日期',
      lastThreeMonths: '最近三个月',
      lastTriggerTime: '上次触发时间',
      lastWeek: '最近一周',
      latestDeliveryDateFrom: '最迟交期从',
      latestDeliveryDateTo: '最迟交期到',
      latestDispatchTime: '最新发送时间',
      latestNews: '最新消息',
      latestShipmentDate: '最迟装运日期',
      layoutSize: '布局大小',
      leftFrozen: '左侧冻结',
      legalOrganization: '法人组织',
      legalOrganizationCode: '法人组织编码',
      legalOrganizationName: '法人组织名称',
      legalPersonTaxControlInformation: '法人税控信息',
      legalRepresentative: '法人代表',
      legalRepresentativeIdCard: '法人代表身份证',
      length: '长度',
      lengthMm: '长度(mm)',
      level: '级别',
      liSi: '李四',
      limitMarginRatio: '限定保证金比例',
      limitRemainingMargiOfGoodsKey: '限定剩余保证金占剩余货值比例',
      lineOfCreditApplication: '信用额度申请',
      lineOfCreditApplicationFormNumber: '额度申请单号',
      lineOfCreditInquiry: '信用额度查询',
      link: '链接',
      list: '列表',
      listOfTheLayoutOfThtationsKey: '款加工方案站位布局清单',
      loadBearingUnit: '承重单位',
      loadingTag: '装货',
      loadingAndUnloadingCargoCarrier: '装卸货承担商',
      loading: '加载中...',
      localCurrency: '本位币种',
      localCurrencySt: '本位币',
      localCurrencyNd: '本位币币种',
      localCurrencyAmount: '本位币金额',
      localCurrencyExchangeRate: '本位币汇率',
      localNetPrice: '当地网价',
      localVideo: '本地视频',
      location: '所 在 地',
      lockLog: '锁定日志',
      lockPrice: '锁价',
      locked: '已锁定',
      locking: '锁定',
      loginNow: '立即登录 ',
      loginTimeoutPleaseLoginAgain: '登录超时，请重新登录！',
      logisticsContract: '物流运输合同',
      logisticsCostDetails: '物流费用明细',
      logisticsCostStatistics: '物流费用统计',
      logisticsCostTaxDifference: '物流费用税差',
      logisticsCostsReceiferenceKey: '应收物流费用（不含税差）',
      logisticsDate: '物流日期',
      logisticsFeesReceivferenceKey: '应收物流费用（含税差）',
      logisticsModeCanNotBeEmpty: '物流方式不能为空',
      logisticsOperationConsole: '物流操作总台',
      logisticsOperationDesk: '物流操作台',
      logisticsOrderNumber: '物流单号',
      logisticsStatus: '物流状态',
      logisticsSupplier: '物流供应商',
      logisticsUnitPrice: '物流单价',
      logisticsUnitPriceCanNotBeEmpty: '物流单价不能为空',
      logisticsUnitPriceIConfirmKey: '物流单价不等于税差拆分总和，请确认',
      longTermPickerDataSavedSuccessfully: '长期提货人数据保存成功!',
      longTermPickerSName: '长期提货人姓名',
      longTermPickerManagement: '长期提货人管理',
      longTermPickerManagementAdded: '长期提货人管理新增',
      longTermPickerNotSelected: '未选择长期提货人',
      longTermPickupPerson: '长期提货人',
      loss: '损耗',
      lossDataSelection: '盘亏数据选择',
      lossOfInventoryOrders: '盘亏出仓单',
      lossOutOrderInquiry: '盘亏出仓回单查询',
      lossType: '盘亏类型',
      mail: '邮件',
      mailAddress: '邮件地址',
      mailbox: '邮箱',
      mainButton: '主要按钮',
      mainChildTableDefault: '主子表（默认）',
      mainChildTableDemo: '主子表demo',
      mainChildTableShoppingCart: '主子表购物车',
      mainSubTableLazyLoading: '主子表(懒加载)',
      mainTableSelection: '主表单选',
      maintenanceOfStationEquipment: '维护站位设备',
      managementDepartment: '管理部门',
      manual: '手动',
      manualAllocation: '手动配款',
      manualBasicSam: '人工基础SAM',
      manualBasicSamSec: '人工基础SAM(秒)',
      manualTmu: '人工TMU',
      manufacturer: '生产厂家',
      marginAmount: '保证金金额',
      marginInterest: '保证金计息',
      marginRatio: '保证金比例',
      marginReceiptDate: '保证金收款日期',
      marginTransactions: '保证金往来',
      maritalStatus: '婚姻状况',
      markAsRead: '标记为已读',
      markAsUnread: '标记为未读',
      market: '市',
      marketPrice: '市场价',
      masterFormMultipleChoice: '主表多选',
      masterSubTableDefault: '主子表默认',
      masterSubTableLazyLoading: '主子表懒加载',
      matchingPickupTruckNumber: '匹配提货车号',
      material: '材质',
      materialAbbreviation: '物料简称',
      materialCategory: '物料分类',
      materialChineseName: '物料中文名称',
      materialCode: '物料编码',
      materialDescription: '物料描述',
      materialDiagram: '物料图',
      materialEnglishName: '物料英文名称',
      materialFunction: '物料功能',
      materialList: '物料列表',
      materialName: '物料名称',
      materialSelection: '物料挑选',
      materialSource: '物料来源',
      materialType: '物料类型',
      maturityDateOfAcceptance: '承兑到期日',
      maxCapacity: '最大承重',
      maximize: '最大化',
      maximumDaysLate: '最大逾期天数',
      maximumNumberOfExpoRecordsKey: '出口发票单张最大记录数',
      measurementMethod: '度量方式',
      measures: '采取措施',
      menu: '菜单',
      menuAddress: '菜单地址',
      menuApplicationRelation: '菜单应用关系',
      menuButtonPermissionConfiguration: '菜单按钮权限配置',
      menuUrlAddress: '菜单URL地址',
      merchant: '商客',
      merchantSearch: '商客查询',
      merchants: '客商',
      mergeCells: '合并单元格',
      mergeSuccess: '合并成功!',
      mergeSuccessfully: '合并成功',
      message: '消息',
      messageBrowse: '消息浏览',
      messageList: '消息列表',
      messageSubject: '消息主题',
      messageTemplate: '消息模板',
      messageTemplates: '消息模板',
      messageTemplatesAdded: '消息模板新增',
      messageType: '消息类型',
      metalCustomerLevel: '金属客户级别',
      method: '方式',
      methodName: '方法名称',
      methodOfCalculation: '计征方法',
      methodOfDiscounting: '承兑贴息方式',
      metricChart: '度量图',
      metricMethodRequired: '度量方式必填',
      mileage: '里程数',
      millionYuan: '万元',
      minIcpNo11111112Min5435353Key: '闽ICP备11111111号-2 闽公网安备 6465435353号',
      minIcpNo150070801Mi2033082Key: '闽ICP备15007080号-1 闽公网安备 35020302033082号',
      minIcpNo150070804: '闽ICP备15007080号-4',
      miscellaneousCharges: '杂费',
      mobilePhone: '移动电话',
      model: '型号',
      modelCategory: '款号类别',
      modelNumber: '款号',
      modelNumberAbbreviation: '款号简称',
      modificationWillCleModifyKey: '修改将会清空原先计息信息是否继续修改？',
      modify: '修改',
      modifyBusinessRole: '修改业务角色',
      modifyCustomerInvoicingInformation: '修改客户开票资料',
      modifyDate: '修改日期',
      modifyDefaultHomePage: '修改默认首页',
      modifyPassword: '修改密码',
      dataPermissionFieldLabelDefinition: '数据权限字段标签定义',
      dataPermissionFieldLabelDefinitionDetails: '数据权限字段标签定义明细',
      tagCode: '标签编码',
      labelDefinitionKeyValue: '标签定义键值',
      labelDefinitionKeyName: '标签定义键名',
      invoiceNumberKey: '发票号码',
      salesListToBeInvoiced: '销售待开票清单列表',
      generateInvoiceRequisition: '生成发票申开单',
      salesInvoiceRequisitionList: '销售发票申开单列表',
      settlementInvoicing: '是否结算开票',
      rebateInvoicing: '返利开票',
      generateTaxControlInvoicingDetails: '生成税控开票明细',
      deleteRecord: '删除记录',
      remainingInvoicedAmountOfTheContract: '合同剩余应开票金额',
      openSettlement: '未终结算',
      successfullyDeletedInvoiceCommodityDetailsMainTable: '删除发票商品明细主表成功',
      currentInvoicingAmount: '本次开票金额',
      adjustTailDifference: '调整尾差',
      mergeData: '合并数据',
      splitData: '拆分数据',
      invoiceLimitSplitting: '发票限额拆分',
      specificationMerge: '规格合并',
      cullMaterial: '剔除材质',
      modifyPickupInformation: '修改提货人信息',
      modifySuccessfully: '修改成功',
      modifyTime: '修改时间',
      modifyUnitPrice: '修改单价',
      modifyUser: '修改用户',
      month: '月',
      monthlyDiscountRate: '月贴息率',
      monthlyDiscountRateOfAcceptance: '承兑月贴息率',
      monthlyReturnInterestRate: '月返息率',
      monthlySettlementCreditMonths: '月结信用月数',
      monthlySettlementCreditTerm: '月结信用期限',
      monthlySettlementDate: '月结结算日',
      more: '更多',
      moreThan2ItemsNeedTrgeDataKey: '需勾选2条以上的数据才能合并数据',
      mostRecentMonth: '最近一个月',
      mould: '模具',
      mouldDetails: '模具明细',
      moveIn: '移入',
      moveInContractInformation: '移入合同信息',
      moveInInformation: '移入信息',
      moveInPersonnel: '移入人员',
      moveOutAccountingGroup: '移出核算组',
      moveOutCompany: '移出公司',
      moveOutInformation: '移出信息',
      moveOutOperator: '移出操作人',
      moveOutPersonnel: '移出人员',
      moveOutWarehouse: '移出仓库',
      moveToAccountingGroup: '移入核算组',
      moveToCompany: '移入公司',
      moveToDepartment: '移入部门',
      moveInOperator: '移入操作人',
      movingOutDepartment: '移出部门',
      multilingual: '多语言',
      multilingualSupport: '多语言支持',
      multiplePaidCorrespond: '多条实收待调整对应一条应收待调整',
      multiplePayablesAdjusted: '多条实付待调整对应一条应付待调整',
      nameTag: '名称',
      name: '姓名',
      nameOfEducationalInstitution: '教育机构名称',
      nameOfGoods: '货物名称',
      nameOfGoodsOrServices: '货物或服务名称',
      nameOfProductionLine: '产线名称',
      nameOfTaxOrganization: '所属税务机构名称',
      nameOfTransportationMeans: '运输工具名',
      nameSpecificationsAToMergeKey: '品名/规格不一致，请确认是否继续合并',
      nationality: '民族',
      nature: '性质',
      natureOfRevenue: '收入性质',
      natureOfWork: '工作性质',
      netWeightKg: '净重(千克)',
      netWeightSingleBox: '净重（单箱）',
      networkException: '网络异常',
      newAndOldPasswordsAseResetKey: '新、旧密码一样，请重新设置！',
      nextLimit: '下限',
      nextTriggerTime: '下次触发时间',
      nickname: '绰号',
      noApprovalCommentsAtThisTime: '暂无审批意见',
      noContractArrival: '无合同到货',
      noContractArrivalOrder: '无合同到货单',
      noContractArrivalOrderAdded: '无合同到货单新增',
      noContractArrivalOrderDetails: '无合同到货单详情',
      noData: '暂无数据',
      noFilterConditionsselectAndSave: '无筛选条件，请选择后保存',
      noGrouping: '不分组',
      noInformationAtThisTime: '暂无信息',
      noNewEndLevel: '末级不可新增',
      noOperationAuthority: '无操作权限',
      nodeAbbreviation: '节点简称',
      nodeChineseName: '节点中文名',
      nodeClassification: '节点分类',
      nodeCompletionPostScript: '节点完成后脚本',
      nodeConfiguration: '节点配置',
      nodeConfigurationInformation: '节点配置信息',
      nodeDefinitionDetails: '节点定义详情',
      nodeEnglishName: '节点英文名',
      nodeId: '节点ID',
      nodeName: '节点名称',
      nodeNumber: '节点编号',
      nodeScheme: '节点方案',
      nodeSchemeName: '节点方案名称',
      nodeSelection: '节点挑选',
      nodeSelectionTag: '节点选择',
      nomenclature: '款号名称',
      nonReadyStateOfTheDeSelectKey: '非准备状态的单据不能删除，请重新选择！',
      nonVirtual: '非虚拟',
      normal: '正常',
      notActivated: '未启用',
      notAudited: '未审',
      notCertified: '未认证',
      notLocked: '未锁定',
      notOpenAtTheMoment: '暂未开放',
      notRequested: '未申请',
      notSaved: '未保存',
      notYet: '暂无',
      noteDescription: '备注说明',
      noteNumber: '票据号',
      notePayableInformation: '应付票据信息',
      nothing: '无',
      noticeOfArrival: '到货通知单',
      noticeOfArrivalDetail: '到货通知单明细',
      noticeOnTheOfficialer12021Key: '关于行业ERP系统正式上线使用的通知：厦门市真合纸业有限公司于2021年9月1号正式上线使用行业ERP系统',
      notificationDate: '通知日期',
      notificationDateFrom: '通知日期 从',
      notificationDateTo: '通知日期 到',
      notificationNumber: '通知单号',
      notificationOfArrivalDateFrom: '到货通知日期从',
      notificationOfArrivalDateTo: '到货通知日期到',
      notificationOfArrivalNumber: '到货通知单号',
      number: '编号',
      numberAnalyzed: '已分析数量',
      numberList: '款号列表',
      numberOfActualIssue: '实发数量',
      numberOfAdjustedPieces: '调整件数',
      numberOfArrivalsWithoutTickets: '已到货未到票数量',
      numberOfConsignors: '委托数量',
      numberOfDays: '计息天数',
      numberOfDaysSt: '返息天数',
      numberOfDaysNd: '贴息天数',
      numberOfDaysInDefault: '违约天数',
      numberOfDaysOfForwardPeriod: '远期天数',
      numberOfDaysPastDue: '逾期天数',
      numberOfDaysUnderwritten: '保底天数',
      numberOfDeclarations: '本次报关数量',
      numberOfDeposited: '已入库数量',
      numberOfDiscrepancies: '差额数量',
      numberOfGeneratedPlans: '已生成计划数量',
      numberOfInvoices: '发票张数',
      numberOfInvoicesTag: '开票数量',
      numberOfInvoicesToBeSettled: '结算应开票数量',
      numberOfLoadedCars: '装车数量',
      numberOfOutgoingWarehouse: '已出仓数量',
      numberOfOutstandingIssues: '欠发数量',
      numberOfPickingParts: '拣配件数',
      numberOfPieces: '采购件数',
      numberOfPiecesTag: '件数',
      numberOfPiecesTagTag: '商品件数',
      numberOfPiecesConfifPiecesKey: '此次确认数量/件数',
      numberOfPiecesLoaded: '装车件数',
      numberOfPiecesOutOfWarehouse: '已出仓件数',
      numberOfPiecesReturned: '已退回件数',
      numberOfPiecesShipped: '发货件数',
      numberOfPiecesWrittenOff: '已核销数量/件数',
      numberOfRepetitions: '重复次数',
      numberOfRequests: '本次申请数量',
      numberOfRetries: '重试次数',
      numberOfReturnedPieces: '已回退件数',
      numberOfRowsSplit: '拆分行数',
      numberOfSheets: '张数',
      numberOfShipments: '发货数量',
      numberOfShipmentsPieces: '发货数量/件数',
      numberOfSignatures: '签收数量',
      numberOfSignedReceipts: '本次签收数量',
      numberOfTicketsNotYetArrived: '已到票未到货数量',
      numberShipped: '已发货数量',
      numberToBeInvoiced: '待开票数量',
      numberWrittenOff: '已核销数量',
      officePhone: '办公电话',
      offset: '冲抵比例%',
      offsetAdvancePayment: '冲抵预暂付款',
      offsetAdvanceReceipts: '冲抵预收款',
      offsetAmountMustBeGAdvanceKey: '冲抵金额必须大于0且不能大于预收金额',
      offsetRules: '冲抵规则',
      okToGenerateInvoicingRequest: '确定生成开票申请?',
      okToSubmit: '确定提交',
      oldAssetNumber: '旧资产编号',
      oldCode: '旧密码',
      onboardingDateTo: '入职日期到',
      onboardingTime: '入职时间',
      oneTicketLogisticsCosts: '一票制物流费用',
      onlyDataWithTheStatRevokedKey: '只能撤销状态为待审的数据',
      onlyNegativeNumbersterThemKey: '减免只能输入负数，请重新输入',
      onlyOneDataCanBeEdited: '只能编辑一条数据',
      onlyOneDataCanBeSelected: '只能选择一条数据',
      onlySingleDataCanBelittingKey: '只能勾选单条数据做拆分',
      openStockScheduleInformationXlsx: '敞口库存明细表信息_.xlsx',
      openTagsView: '开启 Tags-View',
      openingDate: '期初日期',
      openingDateFrom: '期初日期从',
      openingDateTo: '期初日期到',
      openingEntryTransactionAdditions: '期初录入往来新增',
      openingEntryTransactionDetails: '期初录入往来详情',
      openingPayable: '期初应付款',
      openingReceivable: '期初应收款',
      openingReceivableBalance: '期初应收余额',
      openingTransactionEntry: '期初往来录入',
      openingTransactionNumber: '期初往来单号',
      operatingResourceOccupancy: '营运资源占用',
      operatingUnit: '经营单位',
      operation: '操作',
      operationIsAllowedOckIsYesKey: '是否回签为是时才可以允许操作',
      operationManual: '操作手册',
      operationManualPdf: '操作手册.pdf',
      operationMethodTag: '操作方式',
      operationType: '操作类型',
      or: '或',
      order: '顺序',
      orderBySalesAdded: '以销定购新增',
      orderBySalesDetails: '以销定购详情',
      orderCreationTime: '制单时间',
      orderMethod: '出仓方式',
      orderNumber: '盘亏单号',
      orderNumberTag: '顺序号',
      orderOfArrivalAmount: '到货单金额',
      orderOfArrivalQuantity: '到货单数量',
      orderOfAttributes: '属性顺序',
      orderPlanNumber: '订货计划单号',
      orderPlanSavedSuccessfully: '订货计划单保存成功',
      orderPlanning: '订货计划',
      organization: '所属组织',
      organizationCode: '组织代码',
      organizationCodeCertificateNumber: '组织机构代码证号',
      organizationPath: '组织路径',
      organizationRootNodeCode: '组织根节点编码',
      organizationType: '组织类型',
      organizationUnitList: '组织单元列表',
      organizationalRootNode: '组织根节点',
      originalContractCustomer: '原合同客户',
      originalCurrencyTag: '原币',
      originalCurrencyAmount: '原币金额',
      originalCurrencyAmountIsEmpty: '原币金额为空',
      originalCurrencyCannotBeEmpty: '原币不能为空',
      originalCurrencyExcludingTaxAmount: '原币不含税金额',
      originalCurrencyToLngeRateKeyTag: '原币折本币汇率',
      originalCurrencyUnitPrice: '原币单价',
      originalCurrencyCurrency: '原币|币种',
      originalDateOfReceiptAndPayment: '原始收付款日期',
      originalDateOfTransaction: '原始往来日期',
      originalDocumentNumber: '原始单据号',
      originalLogisticsBillNumber: '原始物流单号',
      originalNumberOfPieces: '原始件数',
      originalQuantity: '原始数量',
      originalQuantityPiece: '原始数量/件数',
      other: '其它',
      otherInquiries: '其他查询',
      otherSupplementaryNotes: '其他补充说明',
      otherUnitPrice: '其他单价',
      otherUnitPriceYuan: '其他单价（元）',
      others: '其他',
      ourAcceptanceDueDate: '我司承兑到期日',
      outAdjustmentOrder: '出仓调整单',
      testList: '检测清单',
      outDateTo: '出仓日期到',
      outOfPositionDetails: '出仓明细',
      outOfPositionReturnrkbenchKey: '出仓回单调整工作台',
      outOfWarehouse: '出仓仓库',
      outOfWarehouseNotice: '出仓通知单',
      outOfWarehouseSubTable: '出仓子表',
      outOfWarehouseUnitPrice: '出仓单价',
      outboundAdjustmentOrderNumber: '出仓调整单号',
      outboundAdjustmentWorkbench: '出仓调整工作台',
      outboundGoods: '出库商品',
      OutboundImport: '出仓导入',
      outboundOrderNumber: '出仓回单单号',
      outboundOrderNumberTag: '出仓回单号',
      outboundPickingDetails: '出仓拣配明细',
      outboundReturnAdjustmentOrder: '出仓回单调整单',
      outboundReturnAdjustmentOrderNo: '出仓回单调整单号',
      outboundReturnDetails: '出仓回单明细',
      outboundReturnReceiptNumber: '出库回单号',
      outboundSelection: '出库选择',
      outerCodeField: '外码字段',
      outgoingWarehouseNumber: '出仓单号',
      outOfLine: '线外',
      outOfLineAverageRhythm: '线外平均节奏',
      outOfLineEquilibrium: '线外平衡图',
      outstandingAmount: '未完成金额',
      overdueAmount: '逾期金额',
      overdueArrivalTickeonXlsxKey: '逾期到货未到票信息_.xlsx',
      overdueDays: '逾期天数<=',
      overdueDescription: '逾期说明',
      overdueInventoryQuantity180: '逾期库存数量(180)',
      overdueInventoryQuantity90: '逾期库存数量(90)',
      overdueReceivableDetails: '逾期应收明细',
      overdueReceivableInquiry: '应收逾期查询',
      overdueReceivables: '逾期应收',
      overdueReceivablesSonXlsxKey: '逾期未收款报表信息_.xlsx',
      overdueRecordDescription: '逾期记录说明',
      overdueStartDate: '逾期起算日',
      overloadRate: '溢装率',
      overrunMode: '超出方式',
      overstockable: '可超值',
      packageAnalysisBench: '包材分析工作台',
      packageBom: '包材BOM',
      packageDesignChart: '包材设计图',
      packageDetails: '包装明细',
      packageMaterial: '包材',
      packageRequirementsList: '包材需求清单',
      packageRequirementsSummary: '包材需求汇总',
      packaging: '包装',
      packagingRequirements: '包装要求',
      packagingRequirementsTag: '包装要求明细',
      packagingType: '包装种类',
      paidAmount: '已付款金额',
      panelNumber: '小组编号',
      paragraphDrawing: '款图',
      paragraphType: '款项类型',
      parameter: '参数',
      parameterTag: '承兑贴息参数',
      parameterChineseName: '参数中文名称',
      parameterCode: '参数编码',
      parameterDescription: '参数描述',
      parameterDetails: '参数明细',
      parameterInformation: '参数信息',
      parameterInitialValue: '参数初始值',
      parameterName: '参数名称',
      parameterNameKey: '参数名',
      parameterScript: '参数脚本',
      parameterSelection: '参数选择',
      parameterType: '参数类型',
      parameterUnit: '参数单位',
      parameterValue: '参数值',
      parameterValueCode: '参数值编码',
      parameterValueName: '参数值名称',
      parentDependencies: '父级依赖',
      parentNode: '父级节点',
      parentTasks: '父级任务',
      part: '所属部位',
      partAbbreviation: '部件简称',
      partClassificationCode: '部位分类编码',
      partCode: '部件编码',
      partCodeTag: '部位代码',
      partDescription: '部件描述',
      partLibrary: '部件库',
      partNameRequired: '部位名称必填',
      password: '密码',
      passwordCanNotBeEmpty: '密码不能为空',
      passwordChangeSuccessfully: '密码修改成功',
      passwordConfirmation: '密码确认',
      passwordMustBeAny3CerThan8Key: '密码必须是大小写字母、数字、字符的任意3种组合，且长度大于8',
      passwordMustBeAnyTherThan8Key: '密码必须是大小写字母、数字、字符的任意三种组合，且长度大于8',
      paste: '粘贴',
      path: '路径',
      PathField: 'PATH字段',
      pathSelection: 'PATH选择',
      patrolWarehouseType: '巡库仓库类型',
      pay: '付',
      payable: '应付',
      payingParty: '付款方',
      paymentAdjustmentWorkbench: '付款调整工作台',
      paymentAmount: '付款金额',
      paymentAmountKey: '支付金额',
      paymentAmountOriginalCurrency: '支付金额(原币)',
      paymentApplication: '付款申请',
      paymentApplicationNumber: '付款申请单号',
      paymentDate: '付款日期',
      paymentInformation: '付款信息',
      paymentInformationKey: '支付信息',
      paymentLateArrivalFonXlsxKey: '付款逾期未到货表信息_.xlsx',
      paymentMethodTag: '支付方式',
      paymentMethodTagKey: '付款方式',
      paymentNumber: '付款号',
      paymentOrderNumber: '付款单号',
      paymentRequestFormDetails: '付款申请单明细',
      paymentRequestNumberTag: '付款申请号',
      paymentSelection: '付款选择往来',
      paymentStandard: '付佣标准',
      paymentType: '付款类型',
      paymentTypeTag: '支付类型',
      paymentUnit: '付款单位',
      paymentVoucher: '支付凭据',
      pendingApprovalTasks: '待审批任务',
      pendingAuditWorkbench: '待审工作台',
      pendingInTransitOrders: '待生成在途单',
      pendingMarginCollection: '待收保证金',
      pendingOrderPlanninerationKey: '待生成订货计划工作台',
      pendingPurchaseArrierationKey: '待生成采购到货单',
      pendingPurchaseOrderGeneration: '待生成采购入仓单',
      pendingPurchaseOrderGenerationTag: '待生成到货单',
      pendingPurchaseRequerationKey: '待生成采购申请单',
      pendingSalesOrderGeneration: '待生成销售发货单',
      percentage: '占比',
      permissionConfigurationDetails: '权限对话框配置详情',
      permissions: '权限',
      permissionsDialogCannotBeEmpty: '权限对话框不可为空',
      permissionsDialogConfiguration: '权限对话框配置',
      permissionsFieldSelection: '权限字段选择',
      permissionsView: '权限查看',
      personCannotBeEmpty: '人员不能为空',
      personCode: '人员编码',
      personId: '人员id',
      personnelAssignedReplaceOrNot: '已分配人员，是否替换?',
      personnelDetails: '人员明细',
      personnelInformationList: '人员信息列表',
      personnelLevel: '人员级别',
      personnelName: '人员名称',
      personnelQuery: '人员查询',
      personnelStatus: '人员在职状态',
      personnelType: '人员类型',
      phone: '电话',
      phoneNumber: '电话号码',
      physicalInvoiceDetails: '物理发票明细',
      physicalInvoiceNumber: '物理发票号',
      physicalInvoicingStatus: '物理开票状态',
      pickMatchStandardQuantity: '可拣配标准数量',
      pickMatchRedFlush: '拣配/红冲',
      pickPlaceRedFlushOutboundDetail: '拣配/红冲出仓明细',
      pickAndMatch: '挑选拣配',
      pickAndMatchForm: '挑拣配表',
      pickOutboundReturnDetails: '挑选出仓回单明细',
      pickOutboundReturnForm: '挑选出仓回单',
      pickOutgoingData: '挑选待出仓数据',
      pickPendingShipmentData: '挑选待发货数据',
      pickPrePick: '挑选预拣配',
      pickableQuantity: '可拣配数量',
      pickableStockQuantity: '可拣配库存数量',
      pickableInventoryQuantity: '可拣配|库存数量',
      pickableOrQuantity: '可拣配|数量',
      pickerSContactNumber: '提货人联系电话',
      pickingAndMatchingOingDataKey: '拣配原采购预拣配数据',
      pickingAndMatchingPctGoodsKey: '拣配采购合同商品',
      pickingItemDetails: '拣配商品明细',
      pickingQuantity: '拣配数量',
      pickingOrQuantity: '本次拣配|数量',
      pickupCustomer: '提货客户',
      pieceWeight: '件重',
      pieces: '件',
      placeOfDelivery: '送达地',
      placeOfLoading: '装货地',
      placeOfOrigin: '产地',
      placeOfOriginTag: '起运地',
      placeOfRegistration: '注册地',
      placeOrder: '下单',
      placeholder: '占位符',
      planAbbreviation: '计划简称',
      planClassification: '计划分类',
      planName: '计划名称',
      planNameInEnglish: '计划英文名',
      planNumber: '计划编号',
      planOrderNumber: '计划单号',
      planOutput: '计划产量',
      planQuantity: '计划数量',
      planQuantityMustBeLuantityKey: '计划数量需小于等于可拣配数量 并且 计划库存数量需小于等于可拣配库存数量',
      planType: '计划类型',
      planTypeKey: '计划类别',
      plannedQuantityMustBeGreaterThan0: '计划数量必须大于0',
      plannedStockQuantity: '计划库存数量',
      plannedWorkingTimeHours: '计划工作时间(小时)',
      plantCode: '厂区编码',
      plantConfiguration: '厂区配置',
      plantConfigurationDetails: '厂区配置详情',
      plantName: '厂区名称',
      plantNameChinese: '厂区名称(中文)',
      playback: '播放',
      pleaseAddADocumentReference: '请添加单据引用',
      pleaseAddPickupInformation: '请添加提货人信息',
      pleaseAddTheAttributesFirst: '请先添加属性',
      pleaseCheckAtLeastOneData: '请至少勾选一条数据!',
      pleaseCheckIfTheChirrectlyKey: '请检查childrenListKey是否配置正确!',
      pleaseCheckOneData: '请勾选一条数据！',
      pleaseCheckTheBox: '请勾选',
      pleaseCheckTheData: '请勾选数据',
      pleaseCheckTheDataFirst: '请先勾选数据',
      pleaseChooseTheWayOutOfTheWarehouse: '请选择出仓方式',
      pleaseChooseToExpandTheShare: '请选择扩大共享',
      pleaseCompleteAndSataFirstKey: '请先完成并保存基础信息数据',
      pleaseCompleteTheForm: '请补全表单',
      pleaseCompleteTheRequiredFields: '请补全必填项',
      pleaseCompleteTheRequiredForms: '请补全必填表单',
      pleaseConfirmTheNewPassword: '请确认新密码',
      pleaseConfirmThePasswordAgain: '请再次确认密码',
      pleaseEnterADictionaryAlias: '请输入字典别名',
      pleaseEnterAMnemonic: '请输入助记符',
      pleaseEnterAName: '请输入名称',
      pleaseEnterANote: '请输入备注',
      pleaseEnterANumber: '请输入数字',
      pleaseEnterATemplateName: '请输入模板名称',
      pleaseEnterApprovalComments: '请录入审批意见',
      pleaseEnterCustomer: '请输入客户',
      pleaseEnterDescriptionInformation: '请输入描述信息',
      pleaseEnterEmailAddress: '请输入邮件地址',
      pleaseEnterFax: '请输入传真',
      pleaseEnterInventoryUnit: '请输入库存单位',
      pleaseEnterIsTheMarketPrice: '请输入是市场价',
      pleaseEnterKeywords: '请输入关键词',
      pleaseEnterNewPassword: '请输入新密码',
      pleaseEnterPoNumber: '请输入PO号',
      pleaseEnterPriority: '请输入优先级',
      pleaseEnterTheAbbreviation: '请输入简称',
      enterAbbreviationOfTheItemNumber: '请输入货号简称',
      pleaseEnterTheAccountNumber: '请输入账号',
      pleaseEnterTheAccountingGroup: '请输入核算组',
      pleaseEnterTheAddress: '请输入地址',
      pleaseEnterTheAdjustedAmount: '请输入调整金额',
      pleaseEnterTheAdjustedQuantity: '请输入调整后数量',
      pleaseEnterTheAdjustmentOrderNumber: '请输入调整单号',
      pleaseEnterTheAmount: '请输入金额',
      enterApprovalDescriptionScript: '请输入审批描述脚本',
      enterApprovalRejectionComment: '请输入审批驳回意见',
      pleaseEnterTheArrivalOrderNumber: '请输入到货单号',
      pleaseEnterTheAttributeValueName: '请输入属性值名',
      enterBillingStatementNumber: '请输入结算单号',
      pleaseEnterTheBomList: '请输入BOM单',
      pleaseEnterTheBrand: '请输入品牌',
      pleaseEnterTheCategoryCode: '请输入分类编码',
      pleaseEnterTheCategoryName: '请输入分类名称',
      enterCategoryOfTheItemNumber: '请输入货号类别',
      enterCellPhoneVerificationCode: '请输入手机验证码',
      enterChineseCategoryDescription: '请输入中文分类描述',
      pleaseEnterTheChineseDescription: '请输入中文描述',
      pleaseEnterTheChineseName: '请输入中文名',
      enterChineseNameOfTrameterKey: '请输入动作参数中文名',
      enterChineseNameOfTheCurrency: '请输入币种中文名',
      enterChineseNameOfTheIndustry: '请输入行业中文名',
      enterChineseNameOfTheItemNumber: '请输入货号中文名',
      pleaseEnterTheChineseNameOfThePlan: '请输入计划中文名',
      enterChineseName: '请输入规格中文名',
      enterChineseNameToBeSearched: '请输入要搜索的中文名',
      enterClassificationCodeAndName: '请输入分类编码和名称',
      pleaseEnterTheClassificationPrefix: '请输入分类前缀',
      pleaseEnterTheCode: '请输入编码',
      pleaseEnterTheCommonName: '请输入常用名',
      pleaseEnterTheCompany: '请输入公司',
      enterContactPersonSPhoneNumber: '请输入联系人电话',
      pleaseEnterTheContent: '请输入内容',
      enterContentAccordingToTheFormat: '请按照格式输入内容',
      pleaseEnterTheContractAmount: '请输入合同金额 ',
      pleaseEnterTheContractNumber: '请输入合同号',
      enterContractOfCarriageNumber: '请输入运输合同号',
      pleaseEnterTheContractQuantity: '请输入合同数量 ',
      pleaseEnterTheContractUnitPrice: '请输入合同单价 ',
      pleaseEnterTheConversionAmount: '请输入换算用量',
      pleaseEnterTheConversionRatio: '请输入换算比例',
      pleaseEnterTheConversionUnit: '请输入换算单位',
      pleaseEnterTheCorrectCarNumber: '请输入正确的车号',
      enterCorrectCellPhoneNumber: '请输入正确的手机号',
      enterCorrectContactInformation: '请输入正确的联系方式',
      pleaseEnterTheCorrectContractAmount: '请输入正确的合同金额',
      enterCorrectContractQuantity: '请输入正确的合同数量',
      enterCorrectContractUnitPrice: '请输入正确的合同单价',
      pleaseEnterTheCorrectEmailAddress: '请输入正确的邮箱',
      pleaseEnterTheCorrectFormatOf: '请输入正确格式的部位！',
      pleaseEnterTheCorrectIdNumber: '请输入正确身份证号',
      pleaseEnterTheCorrectItemNumberCode: '请输入正确的货号编码',
      enterCorrectLicensePlateNumber: '请输入正确车牌号',
      enterCorrectOfficeTelephoneNumber: '请输入正确的办公电话',
      pleaseEnterTheCorrectPhoneNumber: '请输入正确的电话号码',
      pleaseEnterTheCorrectVatRate: '请输入正确的增值税税率',
      pleaseEnterTheCreator: '请输入创建人',
      pleaseEnterTheCurrency: '请输入币种',
      pleaseEnterTheCurrencyAbbreviation: '请输入币种简称',
      pleaseEnterTheCustomerCode: '请输入客商编码',
      pleaseEnterTheCustomerSAbbreviation: '请输入客商简称',
      pleaseEnterTheCustomerSName: '请输入客户名称',
      enterCustomerSPaymentNumber: '请输入客户款号',
      pleaseEnterTheDataItem: '请输入数据项',
      pleaseEnterTheDataValue: '请输入数据值',
      enterDayEndRecordCrrOfDaysKey: '请输入日结记录信用期限（天数）',
      pleaseEnterTheDcName: '请输入DC名',
      pleaseEnterTheDecimalPlaces: '请输入小数位',
      pleaseEnterTheDefinitionCode: '请输入定义编码',
      pleaseEnterTheDeliveryMethod: '请输入发送方式',
      enterDeliveryScheduleMethod: '请输入交期计划方式',
      pleaseEnterTheDeliveryTerms: '请输入交期条款',
      enterDescriptionOfTheReport: '请输入报审说明',
      enterDestinationPortTerminal: '请输入目的港码头',
      pleaseEnterTheDialogBoxCode: '请输入对话框编码',
      pleaseEnterTheDialogEntity: '请输入对话框实体',
      pleaseEnterTheDocument: '请输入单据',
      pleaseEnterTheDocumentActionScript: '请输入单据操作脚本',
      pleaseEnterTheDocumentName: '请输入单据名',
      pleaseEnterTheDocumentNumber: '请输入单据号',
      pleaseEnterTheEarliestDeliveryDate: '请输入最早交货期',
      pleaseEnterTheEffectiveDate: '请输入生效日期',
      enterEnglishNameOfTheCurrency: '请输入币种英文名',
      enterEnglishName: '请输入规格英文名',
      pleaseEnterTheEntryPageCode: '请输入入口页编码',
      pleaseEnterTheEntryReturnNumber: '请输入入仓回单号',
      pleaseEnterTheErpDocumentNumber: '请输入ERP单据号',
      pleaseEnterTheEstimatedDeliveryDate: '请输入预计交货日期',
      pleaseEnterTheExpirationDate: '请输入失效日期',
      pleaseEnterTheFilePath: '请输入文件路径',
      pleaseEnterTheFolderName: '请输入文件夹名',
      pleaseEnterTheFormula: '请输入公式',
      pleaseEnterTheFormulaCode: '请输入公式编码',
      pleaseEnterTheFormulaName: '请输入公式名称',
      pleaseEnterTheFunctionName: '请输入功能名称',
      pleaseEnterTheGramWeight: '请输入克重',
      pleaseEnterTheIconName: '请输入图标名称',
      enterIncomingAdjustmentOrderNumber: '请输入进仓调整单号',
      pleaseEnterTheIndustryName: '请输入行业名称',
      pleaseEnterTheInTransitBillNumber: '请输入在途单号',
      enterInventoryContrtNumberKey: '请输入存货合同调整单号',
      enterInvoiceApplicationNumber: '请输入发票申开单号',
      pleaseEnterTheInvoiceNumber: '请输入发票号',
      enterInvoiceRegistrationNumber: '请输入发票登记号',
      pleaseEnterTheIssuingUnit: '请输入发奖单位',
      pleaseEnterTheItemNumber: '请输入货号',
      pleaseEnterTheItemNumberTag: '请输入项目号',
      pleaseEnterTheItemNumberCode: '请输入货号编码',
      pleaseEnterTheLatestDeliveryDate: '请输入最迟交货期',
      enterLegalPersonOrganization: '请输入法人组织',
      enterLevelOfRewardOrPunishment: '请输入奖惩等级',
      enterLocalCurrencyExchangeRate: '请输入本位币汇率',
      pleaseEnterTheLocalNetworkPrice: '请输入当地网价',
      pleaseEnterTheLogisticBillNumber: '请输入物流单号',
      pleaseEnterTheLongTermConsignee: '请输入长期提货人',
      pleaseEnterTheMaterialCode: '请输入物料编码',
      pleaseEnterTheMaterialName: '请输入物料名称',
      pleaseEnterTheMenuAddress: '请输入菜单地址',
      pleaseEnterTheMenuUrlAddress: '请输入菜单URL地址',
      pleaseEnterTheMessageType: '请输入消息类型',
      enterMonthlyClosingCreditPeriod: '请输入月结信用期限',
      pleaseEnterTheMonthlyClosingDate: '请输入月结结算日',
      pleaseEnterTheNameOfTheAward: '请输入获奖名称',
      pleaseEnterTheNameOfTheDictionary: '请输入字典名称',
      pleaseEnterTheNameOfThePicker: '请输入提货人姓名',
      pleaseEnterTheNameOfThePlan: '请输入计划名称',
      pleaseEnterTheNameOfTheProduct: '请输入品名',
      enterNameOfTheProductionLine: '请输入产线名称',
      pleaseEnterTheNameOfTheShip: '请输入船名',
      pleaseEnterTheNameOfTheWarehouse: '请输入仓库名称',
      pleaseEnterTheNameOfTheWorkshop: '请输入车间名称',
      pleaseEnterTheNodeName: '请输入节点名称',
      enterNumberOfDaysOfDayEndCredit: '请输入日结信用天数',
      pleaseEnterTheNumberOfDeliveryDays: '请输入交期天数',
      enterNumberOfMonthsOfMonthlyCredit: '请输入月结信用月数',
      enterNumberOfPickingAndMatching: '请输入本次拣配|数量',
      pleaseEnterTheNumberOfPlans: '请输入计划数量',
      pleaseEnterTheNumberOfShipments: '请输入发货数量',
      pleaseEnterTheNumberOfSplitRows: '请输入拆分行数',
      pleaseEnterTheNumberOfTons: '请输入吨数',
      pleaseEnterTheNumberOfUses: '请输入使用使用数量',
      pleaseEnterTheOfficePhoneNumber: '请输入办公电话',
      pleaseEnterTheOffsetRatio: '请输入冲抵比例',
      pleaseEnterTheOldPassword: '请输入旧密码',
      pleaseEnterTheOrderPlanNumber: '请输入订货计划单号',
      enterOriginalDocumentNumber: '请输入原始单据号',
      enterOtherPartySAccountNumber: '请输入对方账号',
      enterOutboundAdjustmentOrderNumber: '请输入出仓调整单号',
      pleaseEnterTheOverflowRate: '请输入溢装率',
      pleaseEnterThePageAddress: '请输入页面地址',
      pleaseEnterTheParameterCode: '请输入参数编码',
      pleaseEnterTheParameterName: '请输入参数名',
      pleaseEnterTheParameterValueName: '请输入参数值名称',
      pleaseEnterThePart: '请输入部位',
      pleaseEnterThePassword: '请输入密码',
      pleaseEnterThePasswordToConfirm: '请输入密码确认',
      pleaseEnterThePaymentAmount: '请输入支付金额',
      pleaseEnterThePaymentOrderNumber: '请输入付款单号',
      pleaseEnterThePerson: '请输入人员',
      pleaseEnterThePersonCode: '请输入人员编码',
      enterPersonWhoPickedUpTheGoods: '请输入提货人',
      pleaseEnterThePhoneNumber: '请输入电话号码',
      pleaseEnterThePhysicalInvoiceNumber: '请输入物理发票号',
      enterPickupPersonSCeNumberKey: '请输入提货人联系电话',
      pleaseEnterThePlaceOfRegistration: '请输入注册地',
      enterPlannedInventoryQuantity: '请输入计划库存数量',
      pleaseEnterThePlannedQuantity: '请输入本次计划数量',
      enterPriceConfigurationOrderNumber: '请输入价格配置单号',
      pleaseEnterThePriceListCode: '请输入价格单编码',
      pleaseEnterThePriceListName: '请输入价格单名称',
      pleaseEnterThePriceTerms: '请输入价格条款',
      pleaseEnterThePriceType: '请输入价格类型',
      pleaseEnterThePriceTypeCode: '请输入价格类型编码',
      pleaseEnterThePriceTypeName: '请输入价格类型名称',
      pleaseEnterTheProcessDescription: '请输入工序描述',
      pleaseEnterTheProcessName: '请输入流程名称',
      pleaseEnterTheProcessOrderNumber: '请输入工艺单编号',
      enterProcessSelectionScript: '请输入流程选择脚本',
      pleaseEnterTheProductAttributeCode: '请输入商品属性编码',
      pleaseEnterTheProductAttributeName: '请输入商品属性名',
      pleaseEnterTheProductCategory: '请输入商品分类',
      pleaseEnterTheProductCategoryName: '请输入商品分类名称',
      pleaseEnterTheProductCategoryNumber: '请输入商品分类编号',
      pleaseEnterTheProgramCode: '请输入方案编码',
      pleaseEnterTheProgramName: '请输入方案名称',
      pleaseEnterThePropertyCode: '请输入属性编码',
      pleaseEnterThePropertyValueCode: '请输入属性值编码',
      pleaseEnterThePurchaseAmount: '请输入购入金额',
      enterPurchaseArrivalNoticeNumber: '请输入采购到货通知号',
      enterPurchaseContractNumber: '请输入采购合同号',
      pleaseEnterThePurchaseOrderNumber: '请输入采购到货单号',
      enterPurchaseRequisitionNumber: '请输入采购申请号',
      pleaseEnterThePurchaseUnitPrice: '请输入采购单价',
      pleaseEnterThePurchaseVatRate: '请输入采购增值税率',
      pleaseEnterTheQuantity: '请输入数量',
      pleaseEnterTheQuantityInStock: '请输入库存数量',
      enterRebateRegistrationNumber: '请输入返利登记号',
      pleaseEnterTheReceiptAccountNumber: '请输入收款账号',
      pleaseEnterTheReceiptClaimNumber: '请输入收款认领单号',
      enterReceiptNumberOOfGoodsKey: '请输入收货收据单号',
      pleaseEnterTheReceiver: '请输入接收人员',
      pleaseEnterTheRemittanceAmount: '请输入汇入金额',
      pleaseEnterTheRoleCode: '请输入角色编码',
      pleaseEnterTheRoleName: '请输入角色名称',
      enterRootOrganization: '请输入根组织名称',
      pleaseEnterTheRootOrganizationNode: '请输入根组织节点',
      pleaseEnterTheSalesContractNumber: '请输入销售合同号',
      pleaseEnterTheSalesOrderNumber: '请输入销售发货单号',
      enterSalesSalesContractNumber: '请输入销售销售合同号',
      pleaseEnterTheSalesUnitPrice: '请输入销售单价',
      pleaseEnterTheSalesVatRate: '请输入销售增值税率',
      pleaseEnterTheSearch: '请输入检索',
      pleaseEnterTheShortCode: '请输入简码',
      enterShortCodeOfTheWarehouse: '请输入仓库简码',
      pleaseEnterTheShortLoadingRate: '请输入短装率',
      enterSpecificationAbbreviation: '请输入规格简称',
      enterStandardConversionRatio: '请输入标准换算比例',
      enterStandardNumberOfThisUse: '请输入标准本次使用数量',
      pleaseEnterTheStandardQuantity: '请输入标准数量',
      enterSteelMillDirecrNumberKey: '请输入钢厂可直放单号',
      pleaseEnterTheStockConversionRatio: '请输入库存换算比例',
      pleaseEnterTheSubject: '请输入主题',
      pleaseEnterTheTaskGroup: '请输入任务分组',
      pleaseEnterTheTaskName: '请输入任务名称',
      pleaseEnterTheTaskType: '请输入任务类型',
      enterTaxpayerRegistrationNumber: '请输入纳税人登记号',
      pleaseEnterTheTermsAndConditions: '请输入条款内容',
      enterTicketRegistrationNumber: '请输入票据登记号',
      pleaseEnterTheTimeOfTheAward: '请输入获奖时间',
      pleaseEnterTheTotalAmount: '请输入总额',
      pleaseEnterTheTrainingName: '请输入培训名称',
      pleaseEnterTheTrainingType: '请输入培训类型',
      pleaseEnterTheTrainingUnit: '请输入培训单位',
      pleaseEnterTheTransferOrderNumber: '请输入移仓单号',
      pleaseEnterTheTypeOfAwardOrPenalty: '请输入奖惩类型',
      pleaseEnterTheUnderwritingCode: '请输入核销编码',
      pleaseEnterTheUnderwritingName: '请输入核销名称',
      enterUnifiedSocialCreditCode: '请输入统一社会信用代码',
      pleaseEnterTheUnitCode: '请输入单位编码',
      pleaseEnterTheUnitOfShipment: '请输入发货单位',
      pleaseEnterTheUnitPrice: '请输入单价',
      pleaseEnterTheUnitPriceIncludingTax: '请输入含税单价',
      pleaseEnterTheUpstreamTableName: '请输入上游表名',
      pleaseEnterTheUsePeriod: '请输入使用期限',
      pleaseEnterTheUserCode: '请输入用户编码',
      pleaseEnterTheVariableDeclaration: '请输入变量声明',
      pleaseEnterTheVariableName: '请输入变量名称',
      pleaseEnterTheVatRate: '请输入增值税税率',
      pleaseEnterTheWarehouseAbbreviation: '请输入仓库简称',
      enterUserAccount: '请输入用户账号',
      enterUserName: '请输入用户名称',
      pleaseEnterWhetherTheDefaultContact: '请输入是否默认联系人',
      pleaseEnterYourBankAccountNumber: '请输入银行账号',
      pleaseEnterYourCellPhoneNumberTag: '请输入移动电话',
      pleaseEnterYourContactInformation: '请输入联系方式',
      pleaseEnterYourContactPerson: '请输入联系人',
      pleaseEnterYourDepartment: '请输入所在部门',
      pleaseEnterYourEnglishName: '请输入英文名',
      pleaseEnterYourName: '请输入姓名',
      pleaseEnterYourStatus: '请输入状态',
      fillAttributeName: '请填写属性值名',
      beanTasks: 'Bean任务',
      beanName: 'Bean名称',
      bshScript: 'Bsh脚本',
      beanType: 'Bean类型',
      shellType: 'Shell类型',
      taskList: '任务列表',
      taskDetails: '任务详情',
      at: '于',
      sendToMe: '发给我',
      shortcutConfiguration: '快捷配置',
      tenantConfiguration: '租户配置',
      pleaseFillInTheBatchProperties: '请填写批次属性',
      pleaseFillInTheConversionAmount: '请填写换算用量',
      fillCustomerAccountNumber: '请填写客户账号',
      pleaseFillInTheDataCompletely: '请将数据填写完整',
      pleaseFillInTheDeliveryAddress: '请填写送货地址',
      pleaseFillInTheDemandAmount: '请填写需求量',
      fillInformationCompletelyFirst: '请先将信息填写完整',
      pleaseFillInTheNameOfTheSupplier: '请填写供应商名称',
      pleaseFillInTheParameterName: '请填写参数名',
      fillProcessParameterDetails: '请填写工序参数明细',
      fillProcessParameterValue: '请填写工序参数值',
      pleaseFillInTheQuantity: '请填写数量',
      pleaseFillInTheReasonForAdjustment: '请填写调整原因',
      fillThisApplicationQuantity: '请填写本次申请数量',
      pleaseFillInThisPurchaseQuantity: '请填写本次采购数量!',
      pleaseInputButtonName: '请输入按钮名称',
      pleaseInputFunctionCode: '请输入功能编码',
      pleaseInputTheCarAndBoatNumber: '请输入车船号',
      pleaseInputTheCarNumber: '请输入车号',
      pleaseInputTheCommonCellPhoneNumber: '请输入常用手机号',
      pleaseInputTheNameByAttachment: '请输入按附件名称',
      pleaseInputTheNameOfFactory: '请输入厂区名称',
      pleaseMakeSureThatTsAValueKey: '请确保码值码名有值',
      pleaseMakeSureThatTtExistsKey: '请确认父组件的componentParent指向的gridApi是否存在',
      pleaseMakeSureTheListHasAValue: '请确保列表有值',
      pleaseSaveTheBasicIEditingKey: '请先保存基础信息再编辑',
      pleaseSaveTheStatioonFirstKey: '请先保存站位信息',
      pleaseSelectA: '请选择一条',
      pleaseSelectAData: '请选择一条数据!',
      pleaseSelectALayoutCode: '请选择排布编码',
      pleaseSelectAPickerInformation: '请选择一条提货人信息',
      pleaseSelectAPricePlan: '请选择价格方案',
      pleaseSelectARecord: '请选择一条记录',
      selectSize: '请选择尺寸',
      pleaseSelectAWarehouse: '请选择仓库',
      pleaseSelectAccountingGroup: '请选择核算组',
      pleaseSelectActionParameters: '请选择动作参数',
      pleaseSelectAnalysisIe: '请选择分析IE',
      pleaseSelectApprovalDate: '请选择核准日期',
      pleaseSelectAssetClass: '请选择资产大类',
      pleaseSelectAssetClassTag: '请选择资产分类',
      pleaseSelectAssetStatus: '请选择资产状态',
      selectAtLeastOneActivityNature: '请至少选择一个活动性质',
      pleaseSelectAtLeastOneData: '请至少选择一条数据',
      pleaseSelectBigCargoDeliveryDate: '请选择大货交期',
      pleaseSelectBrand: '请选择品牌',
      selectCabinetType: '请选择柜型',
      pleaseSelectCompany: '请选择公司 ',
      pleaseSelectContactType: '请选择联系人类型',
      pleaseSelectCopyContract: '请选择复制合同',
      pleaseSelectCurrency: '请选择币种',
      pleaseSelectData: '请选择数据',
      pleaseSelectDeleteContract: '请选择删除合同',
      pleaseSelectDeleteData: '请选择删除数据',
      selectDimensionUnit: '请选择尺寸单位',
      pleaseSelectDocumentType: '请选择单据类型',
      pleaseSelectGender: '请选择性别',
      pleaseSelectInboundWarehouse: '请选择入仓仓库',
      pleaseSelectIncomingAdjustmentOrder: '请选择进仓调整单!',
      pleaseSelectInvoiceCustomer: '请选择发票客户',
      pleaseSelectLength: '请选择长度',
      pleaseSelectMarginInterestAccrual: '请选择保证金计息',
      pleaseSelectMonth: '请选择月',
      pleaseSelectMoveInPersonnel: '请选择移入人员',
      pleaseSelectMultiToOneOrOneToOne: '请选择多对一或者一对一',
      pleaseSelectNodeClassification: '请选择节点分类',
      pleaseSelectNodeRejection: '请选择节点驳回',
      pleaseSelectOperationData: '请选择操作数据',
      pleaseSelectOutgoingWarehouse: '请选择出仓仓库',
      selectOutgoingWarehntOrderKey: '请选择出仓调整单!',
      pleaseSelectPaymentPurpose: '请选择付款用途',
      pleaseSelectPersonnel: '请选择人员',
      pleaseSelectPoCategory: '请选择PO类别',
      pleaseSelectPortOfDischarge: '请选择卸货港',
      pleaseSelectPortOfLoading: '请选择装货港',
      pleaseSelectProcessOrder: '请选择工艺单',
      selectProcessParameterConfiguration: '请选择工序参数配置',
      pleaseSelectProcessParameters: '请选择工艺参数',
      selectProcessProcessDependency: '请选择工艺工序依赖',
      selectPurchaseInstallment: '请选择采购分批付款',
      pleaseSelectRemarks: '请选择备注',
      pleaseSelectSalesInvoice: '请选择销售发票',
      pleaseSelectShippingCost: '请选择运费承担',
      pleaseSelectShippingCurrency: '请选择运费币种',
      pleaseSelectShippingMethod: '请选择运输方式',
      pleaseSelectShippingOrderType: '请选择发货单类型',
      pleaseSelectSizeRange: '请选择尺码范围',
      pleaseSelectStatus: '请选择状态',
      pleaseSelectSupplier: '请选择供应商',
      selectTaxControlledPhysicalInvoice: '请选择税控物理发票',
      selectAccountingGroupToMoveOut: '请选择移出核算组',
      pleaseSelectTheActionCategory: '请选择动作分类',
      pleaseSelectTheActionType: '请选择动作类型',
      pleaseSelectTheAgreementParty: '请选择协议方',
      pleaseSelectTheAgreementType: '请选择协议类型',
      pleaseSelectTheAmountIncludingTax: '请选择含税金额',
      pleaseSelectTheBusinessElement: '请选择业务要素',
      pleaseSelectTheCategoryFirst: '请先选择分类',
      pleaseSelectTheCityFirst: '请先选择城市',
      pleaseSelectTheClauseTemplate: '请选择条款模板!',
      pleaseSelectTheCodeSegment: '请选择码段',
      pleaseSelectTheCollectionSituation: '请选择收款情况',
      pleaseSelectTheCompany: '请选择公司',
      pleaseSelectTheCompanyToMoveInto: '请选择移入公司',
      pleaseSelectTheConsignor: '请选择委托方',
      pleaseSelectTheContractUnit: '请选择合同单位 ',
      pleaseSelectTheContractingLocation: '请选择签约地点',
      pleaseSelectTheConversionUnit: '请选择换算单位',
      selectCorrespondingStatement: '请选择对应结算单',
      selectCorrespondingStatementNumber: '请选择对应结算单号',
      pleaseSelectTheCostDate: '请选择成本日期',
      pleaseSelectTheCountrySize: '请选择国别尺码!',
      pleaseSelectTheCreator: '请选择创建人',
      selectCreditAllocationDepartment: '请选择额度分配部门',
      pleaseSelectTheCreditCategory: '请选择额度类别 ',
      pleaseSelectTheCreditTermType: '请选择信用期限类型',
      pleaseSelectTheCurrencyOfPayment: '请选择支付币种',
      pleaseSelectTheCurrencyOfPurchase: '请选择购入币别',
      pleaseSelectTheCurrencyPayable: '请选择应付币种',
      pleaseSelectTheCustomer: '请选择客户',
      pleaseSelectTheCustomerTag: '请选择客商',
      pleaseSelectTheCustomerSAccountBank: '请选择客户开户行',
      pleaseSelectTheCustomsBroker: '请选择报关行',
      pleaseSelectTheDataToBeDeleted: '请选择需要删除的数据',
      pleaseSelectTheDataToBeExported: '请选择需要导出的数据',
      pleaseSelectTheDataToBeMerged: '请选择需要合并的数据',
      pleaseSelectTheDataToBeSplit: '请选择需要拆分的数据',
      pleaseSelectTheDate: '请选择日期',
      pleaseSelectTheDateOfAdjustment: '请选择调整日期',
      pleaseSelectTheDateOfArrival: '请选择到港日期',
      pleaseSelectTheDateOfArrivalTag: '请选择到货日期',
      selectDateOfArrivalNotification: '请选择到货通知日期',
      pleaseSelectTheDateOfReceipt: '请选择收款日期',
      pleaseSelectTheDateOfSplitting: '请选择拆分日期',
      pleaseSelectTheDateOfTransfer: '请选择移仓日期',
      pleaseSelectTheDeadline: '请选择截止时间',
      pleaseSelectTheDecimalPlaceOf: '请选择小数位的',
      pleaseSelectTheDepartment: '请选择部门',
      pleaseSelectTheDestinationPort: '请选择目的港码头',
      pleaseSelectTheDisplayMethod: '请选择显示方式',
      pleaseSelectTheDocument: '请选择单据',
      selectDocumentReferenceData: '请选择单据引用数据',
      pleaseSelectTheEffectiveDate: '请选择生效日期',
      pleaseSelectTheEffectiveDateTag: '请选择生效时间',
      pleaseSelectTheEquipmentName: '请选择设备名称',
      pleaseSelectTheExchangeRateNumber: '请选择汇率编号',
      pleaseSelectTheExpectedDateOfEntry: '请选择预计入仓日期',
      pleaseSelectTheExpirationDate: '请选择截止有效日期',
      pleaseSelectTheExpirationDateTag: '请选择失效日期',
      pleaseSelectTheFieldType: '请选择字段类型',
      pleaseSelectTheFileToBeDeleted: '请选择需要删除的文件！',
      selectFinalDeliveryLocation: '请选择最终交货地',
      pleaseSelectTheImpactFactorTable: '请选择影响因素表',
      pleaseSelectTheImpactField: '请选择影响字段',
      pleaseSelectTheIncomingCurrency: '请选择入账币种',
      pleaseSelectTheIndicator: '请选择指标',
      pleaseSelectTheInterestRate: '请选择利息起算标准',
      selectInTransitOrderToBeDeleted: '请选择要删除的在途单',
      pleaseSelectTheInventoryMethod: '请选择库存方式',
      pleaseSelectTheInvoiceCategory: '请选择发票细类',
      pleaseSelectTheInvoicingUnit: '请选择开票单位',
      selectItemDetailsOfDeletedKey: '请选择需要删除的拣配商品明细',
      selectItemDetailsToBeDeleted: '请选择要删除的商品明细',
      pleaseSelectTheItemNumber: '请选择货号',
      pleaseSelectTheItemNumberCode: '请选择货号编码',
      pleaseSelectTheLeftParentNodeFirst: '请先选择左侧父级节点',
      selectLeftPartCategAndSaveKey: '请先选择左侧的部位分类，再进行添加保存',
      pleaseSelectTheLeftTree: '请选择左树',
      selectLeftTreeActionCategory: '请选择左树动作分类',
      pleaseSelectTheLocalCurrency: '请选择本币币种',
      selectLogisticsServiceProvider: '请选择物流服务商',
      pleaseSelectTheManagementDepartment: '请选择管理部门',
      pleaseSelectTheMaterialSource: '请选择物料来源',
      selectMoveInAccountingGroup: '请选择移入核算组',
      pleaseSelectTheMovingDepartment: '请选择移出部门',
      pleaseSelectTheMovingOutCompany: '请选择移出公司',
      pleaseSelectTheMovingInDepartment: '请选择移入部门',
      selectNameOfTheShippingCarrier: '请选择运费承担商名称',
      pleaseSelectTheNatureOfTheCustomer: '请选择客户性质',
      pleaseSelectTheNatureOfTheCustomerTag: '请选择客商性质',
      pleaseSelectTheNatureOfTheInvoice: '请选择发票性质',
      pleaseSelectTheNotificationDate: '请选择通知日期',
      pleaseSelectTheNumberOfSheets: '请选择张数',
      pleaseSelectTheNumberOfShipments: '请选择发货数量',
      pleaseSelectTheNumberOfSplits: '请选择分拆数量',
      pleaseSelectTheOpeningDate: '请选择期初日期',
      pleaseSelectTheOperatingUnit: '请选择经营单位',
      pleaseSelectTheOperationCategory: '请选择操作分类',
      pleaseSelectTheOperationMethod: '请选择操作方式',
      pleaseSelectTheOperationMethodTag: '请选择经营方式',
      pleaseSelectTheOperationType: '请选择操作类型',
      pleaseSelectTheOperator: '请选择移出操作人',
      pleaseSelectTheOriginalCurrency: '请选择原币币种',
      pleaseSelectTheOtherBank: '请选择对方银行',
      pleaseSelectTheOutgoingDate: '请选择出仓日期',
      pleaseSelectTheOutgoingWarehouse: '请选择出库仓库',
      selectOutgoingWarehouseDetails: '请选择出仓明细!',
      pleaseSelectThePart: '请选择部位',
      pleaseSelectThePartToWhichItBelongs: '请选择所属部位',
      pleaseSelectThePayee: '请选择收款方',
      pleaseSelectThePayer: '请选择付款方',
      pleaseSelectThePaymentMethod: '请选择收款方式',
      pleaseSelectThePaymentSubtype: '请选择付款子类型',
      pleaseSelectThePaymentType: '请选择款项类型',
      pleaseSelectThePersonMovingOut: '请选择移出人员',
      pleaseSelectThePlanClassification: '请选择计划分类',
      pleaseSelectThePlanType: '请选择计划类型',
      selectPortOfCustomsDeclaration: '请选择报关口岸',
      pleaseSelectThePricingMethod: '请选择定价方式',
      pleaseSelectTheProcessCategory: '请选择工序类别',
      pleaseSelectTheProductCategory: '请选择商品分类',
      selectProductDetailDeletedKey: '请选择需要删除的商品明细',
      selectProductShipmentDetails: '请选择商品发货明细',
      pleaseSelectTheProductionDate: '请选择生产日期',
      pleaseSelectTheProductionLine: '请选择产线',
      pleaseSelectTheProductionTeam: '请选择生产小组',
      selectProgramClassification: '请选择方案分类',
      pleaseSelectTheProperty: '请选择属性',
      selectPurchaseAndSaleMethod: '请选择购销方式',
      selectPurchaseContractNumber: '请选择采购合同号',
      pleaseSelectThePurchaseDate: '请选择采购日期',
      pleaseSelectThePurchaseRequestForm: '请选择采购申请单',
      pleaseSelectTheReceivingBank: '请选择收款银行',
      pleaseSelectTheRecordWarehouse: '请选择备案仓库',
      selectRecordsThatNeedToBeDeleted: '请选择需要删除的记录',
      pleaseSelectTheReleaseDate: '请选择发布日期',
      pleaseSelectTheReturnCustomer: '请选择退货客户',
      pleaseSelectTheReturnDate: '请选择退货日期',
      pleaseSelectTheReturnSupplier: '请选择退货供应商',
      pleaseSelectTheReturnType: '请选择退货类型',
      pleaseSelectTheReturnWarehouse: '请选择退货仓库',
      selectRoleThatNeedsToBeCopied: '请选择需要复制的角色',
      pleaseSelectTheRootOrganization: '请选择根组织',
      pleaseSelectTheRowFirst: '请先选择行',
      pleaseSelectTheRowNumber: '请选择排布编号',
      pleaseSelectTheSalesContractNumber: '请选择销售合同号',
      pleaseSelectTheSameGroupOfData: '请选择同组数据!',
      selectSameSalesContractData: '请选择相同销售合同数据',
      pleaseSelectTheShipArrivalDate: '请选择船到日期',
      pleaseSelectTheShipName: '请选择船名',
      pleaseSelectTheShippingCutOffDate: '请选择发货截止日期',
      pleaseSelectTheShippingDate: '请选择发货日期',
      selectShippingProductRecord: '请选择发货商品记录!',
      pleaseSelectTheShippingStartDate: '请选择发货起始日期',
      pleaseSelectTheShippingWarehouse: '请选择发货仓库',
      pleaseSelectTheSizeRange: '请选择尺码范围!',
      pleaseSelectTheSplitRatio: '请选择分拆比例',
      pleaseSelectTheSplitUnit: '请选择分拆单位',
      pleaseSelectTheSplittingFee: '请选择分切费',
      pleaseSelectTheStartTime: '请选择开始时间',
      pleaseSelectTheStatisticsDate: '请选择统计日期',
      selectStatusOfThePersonInService: '请选择人员在职状态',
      pleaseSelectTheSystemPaymentNumber: '请选择系统款号',
      pleaseSelectTheTime: '请选择时间',
      pleaseSelectTheTransferDate: '请选调拨日期',
      pleaseSelectTheType: '请选择类型',
      pleaseSelectTheTypeOfBusiness: '请选择企业类型',
      pleaseSelectTheTypeOfContracting: '请选择签约类型',
      pleaseSelectTheTypeOfCustomer: '请选择客商类型',
      pleaseSelectTheTypeOfDiscount: '请选择折让类型',
      pleaseSelectTheTypeOfProductionLine: '请选择产线类型',
      pleaseSelectTheTypeOfSplit: '请选择分切类型',
      pleaseSelectTheTypeOfTransferIn: '请选择调入类型',
      pleaseSelectTheUnanalyzedData: '请选择未分析的数据!',
      pleaseSelectTheUnderwritingFilter: '请选择核销过滤',
      pleaseSelectTheUnitPriceWithTax: '请选择含税单价',
      pleaseSelectTheVehicleAndShipNumber: '请选择车船号',
      selectWarehouseToBeMovedOutOf: '请选择移出仓库',
      pleaseSelectTheWarehouseToMoveInto: '请选择移入仓库',
      pleaseSelectTheYearYouWantToCopy: '请选择要拷贝的年份',
      pleaseSelectTradeType: '请选择贸易类型',
      pleaseSelectUnit: '请选择单位',
      pleaseSelectUnitOfMeasure: '请选择计量单位',
      pleaseSelectUseDepartment: '请选择使用部门',
      pleaseSelectVatRate: '请选择增值税税率',
      pleaseSelectVideo: '请选择视频',
      pleaseSelectWarehouse: '请选择入仓',
      pleaseSelectWhetherPreInvoicing: '请选择是否预开票',
      pleaseSelectWhetherSinglePiece: '请选择是否单件',
      pleaseSelectWhetherTheLastLevel: '请选择是否末级别',
      selectWhetherToCollicationKey: '请选择是否集港通知',
      pleaseSelectWhetherToDefaultContact: '请选择是否默认联系人',
      pleaseSelectWhetherToDiscontinue: '请选择是否停业',
      pleaseSelectWhetherToEnable: '请选择是否启用',
      pleaseSelectWhetherToPrice: '请选择是否定价',
      selectWhetherToProhibitEditing: '请选择是否禁止编辑',
      pleaseSelectWhetherToRefund: '请选择是否退款',
      pleaseSelectWhetherToReturnTheVisa: '请选择是否回签',
      pleaseSelectWhetherToSample: '请选择是否样品',
      selectWhetherToSettrehouseKey: '请选择是否结算出仓',
      selectWhetherYouCanrehouseKey: '请选择是否可出仓',
      selectATradeBackground: '请选择是否有贸易背景',
      selectMadeAVoucher: '请选择是否已制凭证',
      pleaseSelectWhetherYouNeedAReviewer: '请选择是否需要复核人',
      selectAnAccountNumber: '请选择是否需要账号',
      selectTheNumberOfPieces: '请选择是否需要件数',
      pleaseSelectYear: '请选择年',
      pleaseSelectYearTag: '请选择年份',
      pleaseSupplementTheForm: '请补充表单',
      plural: '英文复数',
      policyPriceYuan: '投保单价（元）',
      politicalAppearance: '政治面貌',
      popUpWindow: '弹窗',
      port: '港口',
      portCode: '报关口岸编码',
      portOfDepartureTag: '起运口岸',
      portBuiltUnit: '港建单件',
      portBuiltUnitPrice: '港建单价',
      portBuiltUnitPriceYuan: '港建单价（元）',
      position: '职位',
      positionAmount: '出仓金额',
      positionDetailsInquiry: '出仓明细查询',
      positioningTime: '入仓时间',
      positiveAndNegativeTolerance: '正负公差',
      positiveAndNegativeequiredKey: '正负公差必填',
      postalCode: '邮政编码',
      postingDate: '发布日期',
      postingTime: '发布时间',
      postSettlement: '后结算',
      pre: '预',
      preExpenses: '预 费用',
      prePickingInformation: '预拣配信息',
      preProvisionalAmount: '预/暂付金额',
      preBoxingList: '预装箱清单',
      preCollectionDeposit: '预收保证金',
      preCollectionSuccessactionKey: '预收成功！可到资金往来查看详情',
      preEntryNumber: '预录入编号',
      preEstimated: '预估',
      preEstimatedPurchastrationKey: '预估采购成本登记',
      preInvoicedOrNot: '是否预开票',
      preliminary: '预 暂',
      preliminaryFees: '预 暂 费用',
      preLoadedBoxListDetails: '预装箱清单明细',
      premiumTag: '保险费',
      premiumInformation: '保费信息',
      premiumRate: '投保费率',
      premiumUnitPrice: '保费单价',
      preNodeCompletionScript: '节点完成前脚本',
      prepaymentAmount: '预付金额',
      prepaymentTransactionSelection: '预付往来选择',
      preReceiptOfPayment: '预收货款',
      preReceiptTransactionSelection: '预收往来选择',
      preservation: '保存',
      preview: '预览',
      previewDisplaySettings: '预览显示设置',
      previousOpeningBalance: '上期期初余额',
      previousSubmissionDate: '上级提交日期',
      previousSubmissionTime: '上级提交时间',
      priceConditionsConfiguration: '价格条件配置',
      priceConfigurationDetails: '价格配置明细',
      priceConfigurationSheet: '价格配置单',
      priceConfigurationSheetNumber: '价格配置单号',
      priceConfigurationTable: '价格配置表',
      priceEffectiveTime: '价格生效时间',
      priceOptionsBsh: '价格选项（BSH）',
      priceOrderCode: '价格单编码',
      priceOrderConfiguration: '价格单配置',
      priceOrderName: '价格单名称',
      pricePlan: '价格方案',
      priceSchemeConfiguration: '价格方案配置',
      priceType: '价格类型',
      priceTypeCode: '价格类型编码',
      priceTypeConfiguration: '价格类型配置',
      priceTypeName: '价格类型名称',
      priceVersionNumber: '价格版本号',
      pricingMethod: '定价方式',
      print: '打印',
      printWithAmount: '打印(含金额)',
      printWithoutAmount: '打印(不含金额)',
      printEmbroideryDetails: '印绣花明细',
      printPreview: '打印预览',
      printingAndEmbroideryColorScheme: '印绣花配色图',
      printingEmbroidery: '印绣花',
      priority: '优先级',
      prizeIssuingUnit: '发奖单位 ',
      processCategory: '工序类别',
      processCategoryTag: '工艺类别',
      processCategorySelection: '工序类别挑选',
      processCategorySelectionTag: '工艺类别挑选',
      processClassification: '工序分类',
      processCompletionDate: '流程完成日期',
      processCompletionDateFrom: '流程完成日期从',
      processCompletionDateTo: '流程完成日期到',
      processDependencyCode: '工序依赖编码',
      processDependencyScheme: '工序依赖方案',
      processDescription: '工序描述',
      processDescriptionDiagram: '工艺说明图',
      processDetails: '工艺明细',
      processDifficulty: '工序难易',
      processFile: '工艺档案',
      processFileDetails: '工艺档案详情',
      processInquiry: '进程查询',
      processLayoutOptions: '流程排布方案',
      processList: '流程列表',
      processName: '流程名称',
      processOrder: '工艺单',
      processOrderCode: '工艺单编码',
      processOrderNumber: '工艺单号',
      processOrderSelection: '工艺单挑选',
      processParameter: '工艺参数',
      processParameterConfiguration: '工序参数配置',
      processParameterConfigurationTag: '工艺参数配置',
      processParameterName: '工序参数名称',
      processParameterNameTag: '工艺参数名称',
      processParameterSelection: '工序参数挑选',
      processParameterSelectionTag: '工艺参数挑选',
      processParameterValueConfiguration: '工艺参数值配置',
      processParameterValDetailsKey: '工艺参数值配置详情',
      processProcessDependency: '工艺工序依赖',
      processProfile: '工序档案',
      processProfileDetails: '工序档案详情',
      processRequirements: '工艺要求',
      processSelection: '工艺挑选',
      processSelectionScript: '流程选择脚本',
      processVersion: '流程版本',
      processingScheme: '加工方案',
      processingSchemeStationLayoutList: '加工方案站位布局清单',
      processors: '处理者',
      procurementContractNumber: '采购合同号',
      procurementContractProcess: '采购合同进程',
      procurementContractQuantity: '采购合同数量',
      procurementContractWorkbench: '采购合同工作台',
      procurementInvoiceMatchingWorkbench: '采购发票匹配工作台',
      procurementStartingInterestDetails: '采购起息明细',
      procurementWorkbench: '采购工作台',
      product: '商品',
      productAttributeCode: '商品属性编码',
      productAttributeDetails: '商品属性详情',
      productAttributeName: '商品属性名',
      productAttributes: '商品属性',
      ProductBatchProperties: '商品批次属性',
      productCategories: '商品大类',
      productCategoryDetails: '商品大类详情',
      productCode: '商品编码',
      productDescription: '商品描述',
      productDetailsDeletedSuccessfully: '商品明细删除成功',
      productEnglishName: '商品分类英文名',
      productInformation: '商品信息',
      productOrService: '产品或服务',
      productPurchaseAndSaleContract: '产品购销合同',
      productSearch: '商品查询',
      productionLine: '产线',
      productionLineCode: '产线编码',
      productionLineConfiguration: '产线配置',
      productionLineEquipment: '产线设备',
      productionLineNameChinese: '产线名称(中文)',
      productionLineNameEnglish: '产线名称(英文)',
      productionLineNumber: '产线编号',
      productionLineNumberHanging: '产线号(吊挂)',
      productionLineProperties: '产线属性',
      productionLinePropertyList: '产线属性列表',
      productionLineType: '产线类型',
      productionTeam: '生产小组',
      productionTeamDetails: '生产小组详情',
      productionTeamName: '生产小组名称',
      productionTeamNameChinese: '生产小组名称(中文)',
      productionTeamNameEnglish: '生产小组名称(英文)',
      profitAndLossMeasururationKey: '盈亏测算配置',
      profitType: '盘盈类型',
      programCategory: '方案分类',
      programCode: '方案编码',
      programName: '方案名称',
      programNumber: '方案编号',
      programSavedSuccessfully: '方案保存成功！',
      programTemplate: '计划模板',
      programTemplateDetails: '方案模版详情',
      projectDateFrom: '项目日期从',
      projectDateTo: '项目日期到',
      projectDomesticProclectionKey: '项目国内采购商品选择',
      projectSelection: '项目选择',
      prompt: '提示',
      proportion: '比例',
      provinceCityOfOrigin: '起运地省/市',
      purchaseAgreementDetails: '采购协议详情',
      purchaseAgreementList: '采购协议列表',
      purchaseAgreementNew: '采购协议新增',
      purchaseAmount: '采购金额',
      purchaseAmountTag: '购入金额',
      purchaseAmountOfTheMonth: '本月采购量',
      purchaseAmountThisMonth: '本月采购额',
      purchaseAndSaleMethod: '购销方式',
      purchaseArrival: '采购到货',
      purchaseArrivalNoticeNumber: '采购到货通知单号',
      purchaseArrivalNotificationForm: '采购到货通知单',
      purchaseArrivalNotificationNumber: '采购到货通知号',
      purchaseArrivalOrder: '采购到货单',
      purchaseArrivalMethod: '采购到货付款方式',
      purchaseArrivalWorkbench: '采购到货工作台',
      purchaseBySale: '以销定购',
      purchaseConfirmation: '采购确认书',
      purchaseContractOrder: '采购合同/订单',
      purchaseContractDetails: '采购合同详情',
      purchaseContractProductDetails: '采购合同商品明细',
      purchaseContractProductSelection: '采购合同商品选择',
      purchaseContractWithTax: '采购合同含税',
      purchaseContractWithTaxAmount: '采购合同含税|金额',
      purchaseContractWithTaxUnitPrice: '采购合同含税|单价',
      purchaseCostAdjustmentOrder: '采购成本调整单',
      purchaseCostAdjustmentOrderAdd: '采购成本调整单新增',
      purchaseCostAdjustmentSheet: '采购成本调整单',
      purchaseCostLedger: '采购成本台账',
      purchaseCostSelection: '采购成本选择',
      purchaseDate: '采购日期',
      purchaseInstallmentPayment: '采购分批付款',
      purchaseInvoice: '采购发票',
      purchaseInvoiceInquiry: '采购发票查询',
      purchaseInvoiceInvoicing: '采购发票开票',
      purchaseInvoiceWorkbench: '采购发票工作台',
      purchaseOffsetAdvance: '采购冲抵预付款',
      purchaseOrder: '采购到货单',
      purchaseOrderManagement: '采购到货单管理',
      purchaseOrderNumber: '采购到货单号',
      purchaseOrderSavedSuccessfully: '采购到货单保存成功',
      purchasePrePicking: '采购预拣配',
      purchaseQuantity: '采购数量',
      purchaseRequestDetails: '采购申请详情',
      purchaseRequestForm: '采购申请单',
      purchaseRequestNumber: '采购申请号',
      purchaseReturn: '采购退货',
      purchaseReturnNoticeNumber: '采购退货通知单号',
      purchaseReturnReceiptNumber: '采购退货出仓回单号',
      purchaseSettlementMethod: '采购结算方式',
      purchaseTicketCorrespondence: '采购货票对应',
      purchaseType: '采购类型',
      purchaseValue: '采购货值',
      purchaseVatRate: '采购增值税率',
      purchaseVatTaxAmount: '采购增值税税额',
      purchaseSalesContractNumber: '采购/销售合同号',
      purchaseSalesListInformationXlsx: '采购/销售一览表信息_.xlsx',
      purchased: '已采购',
      purchasedQuantity: '已采购数量',
      purchasing: '采购',
      purchasingContractNumber: '采购合同编号',
      purchasingContractPreSorting: '采购合同预拣配',
      purchasingContracts: '采购合同',
      purchasingCostAdjustmentWorkbench: '采购成本调整工作台',
      purchasingOperationDesk: '采购操作台',
      purchasingUnitPrice: '采购单价',
      purchasingUnitPriceExcludingTax: '采购不含税单价',
      pushHanging: '推送吊挂',
      qualification: '资格证书',
      qualityManagement: '质量管理',
      qualityManagementDetails: '质量管理明细',
      quantityNumberOfPieces: '数量/件数',
      quantityArrivedAndQequiredKey: '到货数量和库存数量必填',
      quantityArrivedUnitequiredKey: '到货数量、含税单价必填',
      quantityCannotBe0: '数量不能<=0',
      quantityCannotBe0Key: '数量不能为0',
      quantityCannotBe0AnuantityKey: '数量不能为0且不能大于剩余数量',
      quantityCannotBeEmpty: '数量不能为空',
      quantityConfirmedThisTime: '此次确认数量',
      quantityDeliveredUnequiredKey: '到货数量、含税单价、关税税率必填',
      quantityDifferenceIs0: '数量差额为0',
      quantityOfThisPlan: '本次计划数量',
      quantityOut: '出仓数量',
      quantityShippedThisTime: '本次发货数量',
      outOfWarehouseGoods: '出仓商品',
      warehousedGoods: '入库商品',
      tailDifferenceAdjustment: '尾差调整',
      pleaseSelectInvoiceCategory: '请选择发票大类',
      creditCategory: '信用类别',
      quantityToBeSignedOff: '待签收数量',
      quantityToBeUsed: '使用数量',
      quantityTotal: '数量合计',
      quantityUsedAdjusontractKey: '使用数量<=出仓商品的调整后数量 且不能大于合同剩余数量',
      quantityUsedThisTime: '本次使用|数量',
      quayUnitPriceYuan: '码头单价（元）',
      query: '查询',
      queryConditions: '查询条件',
      queryDate: '查询日期',
      queryPersonnelInformationFailure: '查询人员信息失败',
      quickLaunch: '快速发起',
      rate: '返息率',
      rateTag: '现款返息率',
      rateNumberDefinition: '汇率编号定义',
      rateNumberMaintenance: '汇率编号维护',
      read: '已读',
      readOrNot: '是否阅读',
      ready: '准备',
      real: '真实',
      realTimeInventoryInquiry: '实时库存查询',
      reasonForAdjustment: '调整原因',
      rebate: '返息',
      rebateAmount: '返利金额',
      rebateDetails: '返利明细',
      rebateRegistration: '返利登记',
      rebateRegistrationDate: '返利登记日期',
      rebateRegistrationNumber: '返利登记号',
      rebates: '返利',
      receiptAccountNumber: '收款账号',
      receiptAdjustment: '收款调整',
      receiptAndInterestDetails: '收款及计息明细',
      receiptAndPaymentDetails: '收汇明细',
      receiptAndPaymentMethod: '收付方式',
      receiptAndPaymentSubType: '收付款子类型',
      receiptAndPaymentType: '收付类型',
      receiptAndPaymentTypeKey: '收付款类型',
      receiptBalance: '收款余额',
      receiptClaim: '收款认领',
      receiptClaimDetails: '收款认领明细',
      receiptClaimsAdded: '收款认领新增',
      receiptDate: '收款日期',
      receiptInformation: '收货信息',
      receiptInformationKey: '签收信息',
      receiptInformationTag: '收款信息',
      receiptManagement: '收货收据管理',
      receiptMethod: '收款方式',
      receiptNumberSt: '收款单据号',
      receiptNumberKey: '收货收据单号',
      receiptOfReceipt: '收货收据',
      receiptSelection: '收款选择往来',
      receiptStartDate: '收款起始日期',
      receiptStatus: '签收状态',
      receiptStatusTag: '收款情况',
      receiptStatusCannotBeEmpty: '收款情况不能为空',
      receiptType: '收款类型',
      receiptsForThePeriod: '本期收款',
      receivable: '应收',
      receivableKey: '应实收',
      receivableInquiry: '应收查询',
      receivableOverdueDetailsInquiry: '应收逾期明细查询',
      receivableType: '应收类型',
      receiveFrom: '收起',
      receiveSameMonth: '当月收款',
      receivePay: '收/付',
      received: '已收款',
      receivedAmount: '已收款金额',
      receivedDeposit: '已收保证金',
      receivedThisMonth: '本月收款',
      receiver: '接收人员',
      receiving: '收',
      receivingAddress: '收货地址',
      receivingBank: '收款银行',
      receivingContactPerson: '收货联系人',
      receivingContactPhone: '收货联系人电话',
      receivingCustomer: '收款客户',
      receivingParty: '收货方',
      receivingUnit: '收货单位',
      recognitionStatus: '认款状态',
      reconciliationDetailInquiry: '对账明细查询',
      reconciliationInquiry: '对账查询',
      recordDoesNotExist: '记录不存在',
      recordWarehouse: '备案仓库',
      redFlushSuccess: '红冲成功',
      reEnterPassword: '再次输入密码',
      referenceFigure: '参考图',
      referenceRootOrganization: '参照根组织',
      referenceSectionNumber: '参考款号',
      referrer: '转审人',
      refreshFailed: '刷新失败',
      refreshSuccess: '刷新成功',
      refundAgencyCode: '退税机构代码',
      refundAmount: '返息金额',
      refundInstitutionName: '退税机构名称',
      registeredCapitalMillionYuan: '注册资本(万元)',
      registrationCertificateNumber: '注册证号',
      registrationCurrency: '注册币种',
      registrationNumber: '登记号',
      reject: '驳回',
      rejectMaterialSuccess: '剔除材质成功',
      rejectedBy: '驳回提交人',
      rejectedComments: '驳回意见',
      rejectedStatusDataCannotBeDeleted: '驳回状态数据不可删除！',
      rejectionScript: '驳回脚本',
      relatedInOut: '相关出入库',
      relatedInOutSearch: '相关出入库查询',
      relation: '关系人',
      relationshipWithLegalEntity: '与法人的关系',
      relationshipWithPersonnel: '与人员关系',
      relogin: '重新登录',
      remainingDeposit: '剩余保证金',
      remainingDepositAsAdsValueKey: '剩余保证金占剩余货值比例',
      remainingDepositToBeReceived: '剩余待收保证金',
      remainingGoodsToBeReceived: '剩余待收货款',
      remainingGoodsValue: '剩余货值',
      remainingPieces: '剩余件数',
      remainingQuantity: '剩余数量',
      remainingQuantityNumberOfPieces: '剩余数量/件数',
      remainingQuantityRemainingPieces: '剩余数量/剩余件数',
      remainingNumber: '剩余|数量',
      remittanceAmount: '汇入金额',
      remittanceTotal: '汇总',
      rename: '重命名',
      repeatIntervalMilliseconds: '重复间隔(毫秒)',
      repetitionInterval: '重复间隔',
      replacementSuccessful: '替换成功',
      requestForUserAuthentication: '请求要求用户的身份认证',
      requestTimeout: '请求超时！',
      requestTimeoutPleaseTryAgain: '请求超时，请重试！',
      requested: '已申请',
      requiredField: '必填',
      requiredFieldsAreNotFilledIn: '必填项未填写',
      requiredPartyToUndertake: '需方承担',
      reservedCodeConditions: '预留编码条件',
      reservedCodeConfiguration: '预留编码配置',
      reservedCodeConfigurmationKey: '预留编码配置信息',
      resetRefresh: '重置刷新',
      ResourceDetails: '资源详情',
      resourceList: '资源列表',
      responsiblePerson: '负责人',
      retailCustomers: '零售客户',
      retailDirectDebitBackOrderInquiry: '零售直放回单查询',
      retailDirectReleaseDetailsKeyTag: '零售直放回单详情',
      retailDirectReleasedditionKey: '零售直放发货单新增',
      retailDirectReleaseDetailsKey: '零售直放发货单详情',
      retailDirectReleaseShippingOrders: '零售直放发货单',
      retailReturnOrderDetails: '零售回单详情',
      retailShippingOrderAdd: '零售发货单新增',
      retailShippingOrderDetails: '零售发货单详情',
      retailShippingOrderInquiry: '零售发货回单查询',
      retailShippingOrders: '零售发货单',
      retryAfterSeconds: '秒后重试',
      returnAfterTheTransnsfereeKey: '转审后返回（被转审人同意后返回转审人）',
      returnContract: '回签',
      returnContractTag: '退货合同',
      returnContractTagKey: '回退合同',
      returnDate: '退货日期',
      returnDateFrom: '退货日期从',
      returnDateTo: '退货日期到',
      returnDescription: '退货说明',
      returnDetails: '回退明细',
      returnInDateTo: '退货入仓日期到',
      returnNoticeNumber: '退货通知单号',
      returnOrderDetails: '回单明细',
      returnOrderDetailsTag: '退货单明细',
      returnOrderNumber: '回单号',
      returnOutDate: '退货出仓日期',
      returnOutDateFrom: '退货出仓日期从',
      returnOutDateTo: '退货出仓日期到',
      returnProductSelection: '退货商品选择',
      returnQuantity: '退货数量',
      returnRequestReason: '退货申请理由',
      returnToWarehouse: '退货入仓仓库',
      returnToWarehouseDate: '退货入仓日期',
      returnToWarehouseDateFrom: '退货入仓日期从',
      returnType: '退货类型',
      returnWarehouse: '退货仓库',
      returnableDiscountDays: '应返贴息天数',
      returnedGoodsOutOfTheWarehouse: '退货出仓仓库',
      returnedGoodsSupplier: '退货供应商',
      returnedPieces: '回退件数',
      returnedQty: '已退货数量',
      returnedQuantity: '已退回数量',
      returnedQuantitySt: '已回退数量',
      returnedQuantityNd: '回退数量',
      returnedOrQuantity: '已退货|数量',
      returningCustomer: '退货客户',
      revenueAdjustmentNumber: '收入调整号',
      revenueAdjustmentOrderNumber: '收入调整单号',
      revenueBillNumber: '收入单号',
      revenueDate: '收入日期',
      revenueDateFrom: '收入日期从',
      revenueDateTo: '收入日期到',
      revenueDetails: '收入明细',
      revenueType: '收入类型',
      revenueTypeItemNumbequiredKey: '收入类型、货号、发货数量、单位、含税单价、金额、增值税税率必填',
      revenueQuantity: '收入|数量',
      revenueUnit: '收入|单位',
      reviewed: '已审核',
      reviewedRecordsSearch: '已审记录查询',
      reviewedStatusDoesNotAllowOperation: '已审核状态不允许操作',
      reviewer: '复核人',
      revocationFailed: '撤销失败',
      revocationSubmitted: '撤销提交！',
      rewardAndPunishment: '奖惩情况',
      rewardAndPunishmentDecisionMaker: '奖惩决定者',
      rewardAndPunishmentLevels: '奖惩等级',
      rewardAndPunishmentType: '奖惩类型',
      rightFreeze: '右侧冻结',
      rmb: '人民币',
      role: '角色',
      roleCode: '角色编码',
      roleDocumentAuthorization: '角色单据授权',
      roleInformation: '角色信息',
      roleManagement: '角色管理',
      roleName: '角色名称',
      rootNodeCode: '根节点编码',
      rootNodeOrganization: '根节点组织',
      rootOrganization: '根组织',
      rootOrganizationCode: '根组织编码',
      organizationNameKey: '组织名称',
      rootOrganizationName: '根组织名称',
      roughingUp: '加粗',
      ruleDefinition: '规则定义',
      ruleDefinitionList: '规则定义列表',
      sales: '销售',
      salesAgreementAdditions: '销售协议新增',
      salesAgreementDetails: '销售协议详情',
      salesAgreementList: '销售协议列表',
      salesAmount: '销售金额',
      salesCompletionRatio: '销量完成比',
      salesContract: '销售合同',
      salesContractBillNumber: '销售合同单号',
      salesContractCode: '销售合同编码',
      salesContractCodeKey: '销售合同编号',
      salesContractDetails: '销售合同详情',
      salesContractProcess: '销售合同进程',
      salesContractQuantity: '销售合同数量',
      salesCustomer: '销售客户',
      salesDeliveryNoticeNumber: '销售发货通知单号',
      salesDirectReleaseReturnOrder: '销售直放退货单',
      salesDirectReleaseReturnWorkbench: '销售直放退货工作台',
      salesDiscountAdjustmentOrder: '销售折让调整单',
      salesDiscountAdjustmentOrderAdded: '销售折让调整单新增',
      salesExcludingTaxUnitPrice: '销售不含税单价',
      salesForTheMonth: '本月销售量',
      salesForTheMonthKey: '本月销售额',
      salesInvoiceInquiry: '销售发票申开单查询',
      salesInvoiceInvoicing: '销售发票开票',
      salesInvoiceInvoicingAdded: '销售发票开票新增',
      salesInvoiceInvoicingDetails: '销售发票开票详情',
      salesInvoiceInvoicissfullyKey: '销售发票申开单保存成功',
      salesInvoiceNumber: '销售发票号',
      salesInvoiceRegistrationNumber: '销售发票登记号',
      salesInvoiceWorkbench: '销售发票工作台',
      salesInvoicing: '销售开票',
      salesInvoicingIncludingTaxAmount: '销售开票含税金额',
      salesInvoicingInvoiceDate: '销售开票开票日期',
      salesInvoicingInvoiceQuantity: '销售开票发票数量',
      salesInvoicingWorkbench: '销售待开票工作台',
      salesOffsetAdvanceCollection: '销售冲抵预收款',
      salesOrder: '销售发货单',
      salesOrderAdd: '销售发货单新增',
      salesOrderDetails: '销售发货单详情',
      salesOrderNumber: '销售发货单号',
      salesOutboundReturnNumber: '销售出仓回单号',
      salesPendingShipmentDetail: '销售待发货明细',
      salesPendingShipmentWorkbench: '销售待发货工作台',
      salesReturn: '销售退货',
      salesReturnInboundReturnNumber: '销售退货入仓回单号',
      salesReturnNoticeNumber: '销售退货通知单号',
      salesReturnOrderNumber: '销售退货单号',
      salesRevenueAdjustmentOrder: '销售收入调整单',
      salesRevenueAdjustmentSheetAdd: '销售收入调整单新增',
      salesRevenueAdjustmentSlip: '销售收入调整单',
      salesRevenueAdjustmentWorkbench: '销售收入调整工作台',
      salesRevenueLedger: '销售收入台账',
      salesRevenueSelection: '销售收入选择',
      salesSettlementMethod: '销售结算方式',
      salesShipment: '销售发货',
      salesShipmentCollection: '销售发货收款方式',
      salesShipmentDetails: '销售发货明细',
      salesUnitPrice: '销售单价',
      salesVatRate: '销售增值税率',
      salesVatTaxAmount: '销售增值税税额',
      salesOrAmount: '销售|金额',
      salesExcludingTaxAmount: '销售|不含税金额',
      salesTaxRate: '销售|税率%',
      salesOrUnitPrice: '销售|单价',
      sameDayReceipt: '当天收款',
      sameDayShipping: '当天发货',
      sameDayWarehousing: '当天入库',
      sameMonthReceipt: '当月入库',
      sameMonthShipping: '当月发货',
      sampleCommentDetails: '样品评语明细',
      sampleComments: '样品评语',
      sampleType: '样品类型',
      saveCanvasDependenciesFailed: '保存画布依赖失败',
      saveCanvasFailed: '保存画布失败',
      saveFiltering: '保存筛选',
      saveRolePermissionsSuccessKey: '保存角色权限配置成功',
      saveSuccessfully: '保存成功',
      saveToExit: '保存退出',
      savedFilterConditions: '已保存筛选条件',
      scanQrCode: '扫描二维码',
      scheduling: '排款',
      schedulingDate: '排款日期',
      scopeOfApplication: '适应范围',
      scrappedSuccessfully: '作废成功!',
      script: '脚本',
      scriptAfterProcessCompletion: '流程完成后脚本',
      scriptBeforeProcessCompletion: '流程完成前脚本',
      scriptTask: '脚本任务',
      seaFreightUnitPrice: '海运单价',
      seaFreightUnitPriceYuan: '海运单价（元）',
      search: '搜索',
      searchByCategoryName: '按分类名搜索',
      selectAccountingGroup: '选择核算组',
      selectAdjustmentType: '选择调整类型',
      selectArea: '选择区域',
      selectAtLeastOneDocument: '至少选择一个单据',
      selectAtLeastOneRole: '至少选择一个角色',
      selectAtLeastOneUser: '至少选择一个用户',
      selectBomSheet: '选择BOM单',
      selectCategory: '选择分类',
      selectCommodity: '选择商品',
      selectCommodityCategory: '选择商品分类',
      selectCommodityCategoryKey: '选择商品大类',
      selectCompany: '选择公司',
      selectConsignor: '选择委托方',
      selectCreationTime: '选择创建时间',
      selectCustomer: '选择客户',
      selectDate: '选择日期',
      selectDateAndTime: '选择日期时间',
      selectDepartment: '选择部门',
      selectDialogCode: '选择对话框编码',
      selectDocument: '选择单据',
      selectEquipment: '选择设备',
      selectFinancialOrganization: '选择财务组织',
      selectIcon: '选择图标',
      selectImportInvoiceGoods: '选择进口发票商品',
      selectInboundCostRecord: '选择入仓成本记录',
      selectInboundReturnDetails: '挑选入仓回单明细',
      selectIncomingInformation: '选择调入信息',
      selectIncomingProductDetails: '挑选到货商品明细',
      selectInterestRate: '选择计息标准',
      selectInventoryCommodity: '选择库存商品',
      selectInvoiceDetails: '选择发票明细',
      selectInvoiceDetailsToVoid: '选择发票明细作废',
      selectItemGoods: '选择项目商品',
      selectLongTermPicker: '选择长期提货人',
      selectLossData: '选择盘亏数据',
      selectMatchingMethod: '选择配款方式',
      selectModificationTime: '选择修改时间',
      selectNodeClassification: '选择节点分类',
      selectPaidTransactionAdjustment: '选择实付往来调整',
      selectPaymentType: '选择款项类型',
      selectPendingArrivalData: '挑选待到货数据',
      selectPendingInboundData: '挑选待入仓数据',
      selectPerson: '选择人员',
      selectPlanCategory: '选择计划类别',
      selectPrepaidTransactions: '选择预付往来',
      selectPrepayment: '选择预暂收往来',
      selectPrepayments: '选择预暂付往来',
      selectProcess: '选择工序',
      selectProcessClassification: '选择工序分类',
      selectProcessParameters: '选择工艺参数',
      selectProcurementContract: '选择采购合同',
      selectProductDetails: '选择商品明细',
      selectProductionLineAttributes1: '选择产线属性1',
      selectProgramCategory: '选择方案分类',
      selectPurchasingCost: '选择采购成本',
      selectReceiptAdjustment: '选择实收往来调整',
      selectReceiptData: '选择实收往来数据',
      selectReturnDetails: '挑选退货明细',
      selectReturnedGoods: '选择退货商品',
      selectSalesContract: '选择销售合同',
      selectSalesContractNumber: '选择销售合同号',
      selectSalesDetail: '挑选销售明细',
      selectSalesRevenue: '选择销售收入',
      selectShippingData: '挑选发货商品明细',
      selectShippingGoodsRecord: '选择发货商品记录',
      selectSuccess: '选择成功',
      selectSupplier: '选择供应商',
      selectSurplusData: '选择盘盈数据',
      selectTable: '选择表',
      selectThePaidTransactionData: '选择实付往来数据',
      selectTheTransferOrderDetails: '选择移仓调拨单明细',
      selectTheWarehouseReceiptToBeSigned: '选择要签收的出仓回单',
      selectTime: '选择时间',
      selectTotal: '选中合计',
      selectUpToOnePieceOfData: '最多只能选择一条数据',
      selectVideo: '选择视频',
      selectZoneToBringInAutomatically: '选择区自动带入',
      selected0ArticlesTont0YuanKey: '已选0条，合计：数量0吨，不含税金额0元，税额0元，含税金额0元',
      sellingPrice: '卖出价',
      sendingMethod: '发送方式',
      series: '系列',
      serverInternalErrorRequestKey: '服务器内部错误，无法完成请求',
      serviceMayBeRestartingPleaseWait: '可能正在重启服务，请稍等！',
      serviceProviderQuery: '服务供应商查询',
      serviceProviders: '服务供应商',
      setAsLeft: '设置为已离职',
      setAsTheMainVideo: '设为主视频',
      setDecimalPlaces: '设置小数位',
      setTheDepartment: '设置所在部门',
      setUpSuccessfully: '设置成功',
      setUpTime: '成立时间',
      settlementAmount: '结算金额',
      settlementDate: '结算日期',
      settlementDays: '结算天数',
      settlementGrossProfit: '结算毛利',
      settlementInformationHasBeenUpdated: '结算信息已更新',
      settlementList: '结算清单',
      settlementMethodPayBeEmptyKey: '结算方式、支付方式、日期方式、预计支付日期、支付比例、支付金额不能为空',
      settlementOutOfPosition: '是否结算出仓',
      settlementPolicy: '结算政策',
      settlementQuantity: '结算数量',
      settlementSummaryInformation: '结算概要信息',
      settlementTemplateInformationXlsx: '结算模板信息_.xlsx',
      settlementTerms: '结算条款',
      settlementTermsDetail: '结算条款明细',
      settlementValue: '结算货值',
      sharedFieldName: '共享字段名',
      sharedType: '共享类型',
      shareholderBackground: '股东背景',
      shipArrivalDate: '船到日期',
      shipArrivalDirectPickupLetter: '船到码头直提函',
      shipConstructionYear: '船舶建造年',
      shipName: '船名',
      shipmentAmount: '发货金额',
      shipmentAndReceiptDetails: '发货与收款明细',
      shipmentDate: '装船日期',
      shipmentTime: '装运时间',
      shipped: '已发送',
      shippedOrders: '已发单',
      shippedQuantity: '已发货|数量',
      shipping: '发货',
      shippingAndReceivingSummary: '发货与收款汇总',
      shippingCharges: '运费',
      shippingDate: '发货日期',
      shippingDateFrom: '发货日期从',
      shippingDateTo: '发货日期 到',
      shippingDeadline: '发货截止日期',
      shippingDetailsTag: '起运详细地址',
      shippingDetailsSelection: '发货明细选择',
      shippingNotice: '发货通知单',
      shippingNoticeNumber: '发货通知单号',
      shippingOrderNumber: '发货单号',
      shippingOrderQuantity: '发货单数量',
      shippingOrderType: '发货单类型',
      shippingPaymentMethod: '运输支付方式',
      shippingSettlementMode: '运费结算模式',
      shippingStartDate: '发货起始日期',
      shippingType: '出库类型',
      shippingTypeTag: '发货类型',
      shippingUnit: '发货单位',
      shippingWarehouseAddress: '发货仓库地址',
      shippingPremiumInformation: '运/保费信息',
      shippingQuantity: '发货|数量',
      shippingOrUnit: '发货|单位',
      shoppingCart: '购物车',
      shoppingCartDetails: '购物车明细',
      shortCode: '简码',
      shortCodeExceeds16Lengths: '简码超出16个长度',
      shortOverflow: '短溢装',
      shortOverflowRate: '短溢装率',
      showAttributes: '显示属性',
      showName: '显示名称',
      sidebarLogo: '侧边栏 Logo',
      signedBy: '签收人',
      signedFor: '签收',
      signedForSingleNumber: '签收单单号',
      signedQuantity: '已签收数量',
      signingDateTo: '签约日期到',
      signingLocation: '签约地点',
      simpleDescription: '简述',
      simpleTriggerConfiguration: '简单触发配置',
      simpleType: '简单类型',
      simultaneousPlay: '同时播放',
      single: '单',
      singleDetail: '单明细',
      singleNumber: '单号',
      singlePrice: '单价',
      singleTonDiscount: '单吨贴息',
      singleTonInterestRebate: '单吨返息',
      singleTonMarkup: '单吨加价',
      siteIe: '现场IE',
      size: '大小',
      sizeSt: '尺码',
      sizeNd: '尺寸',
      sizeAttributeName: '尺码属性名称',
      sizeBaseLibraryList: '尺码基础库列表',
      sizeComparisonChart: '尺码对照表',
      sizeMeasurementChart: '尺码度量表',
      sizeRange: '尺码范围',
      sizeRequired: '尺码必填',
      sizeSpecification: '尺寸规格',
      sizeTable: '尺寸表',
      sizeTableDetails: '尺寸表明细',
      sizeQuantity: '尺码/数量',
      skillLevel: '技能等级',
      slice: '分切',
      slitGoods: '分切商品',
      slittingFee: '分切费',
      slittingType: '分切类型',
      slittingUnitPrice: '分切单价',
      sms: '短信',
      smsNotification: '短信通知',
      sortBy: '排序',
      sortLevel: '排序级别',
      sourceApplicationNumber: '来源申请单号',
      sourceDocument: '来源单据',
      sourceNumber: '来源单号',
      special: '特殊',
      specialty: '专业',
      specification: '规格',
      specificationLength: '规格（长）',
      specificationWidth: '规格（宽）',
      specificationDescription: '规格说明',
      specificationModel: '规格型号',
      specificationNameChinese: '规格名称（中文名）',
      specificationRequirements: '规格要求',
      split: '拆分',
      splitAmount: '拆分金额',
      splitCells: '拆分单元格',
      splitDate: '拆分日期',
      splitDateKey: '分拆日期',
      splitDateFrom: '分拆日期从',
      splitDateTo: '分拆日期到',
      splitOrderNumber: '分拆单号',
      splitProcessingInboundReturnInquiry: '分拆加工入仓回单查询',
      splitProcessingInboDetailsKey: '分拆加工入仓回单查询详情',
      splitProcessingOrder: '分拆加工单',
      splitProcessingOrderAdd: '分拆加工单新增',
      splitProcessingOrderDetails: '分拆加工单详情',
      splitProcessingOrderNumber: '分拆加工单号',
      splitProduct: '拆分商品',
      splitQuantity: '拆分数量',
      splitSuccess: '拆分成功!',
      splitUnits: '拆分单位',
      splittingProcessingInquiryKey: '分拆加工出仓回单查询',
      splittingProcessingDetailsKey: '分拆加工出仓回单查询详情',
      splittingSuccess: '分摊成功',
      spotCounterInterestRateMonthly: '现款反息率(月息)',
      spotSingleTonDiscountRate: '现款单吨贴息',
      spotSingleTonRebate: '现款单吨返息',
      staffing: '人员配置',
      standard: '标准',
      standardAvailableQuantity: '标准可用数量',
      standardConversionRatio: '标准换算比例',
      standardConversionRBeEmptyKey: '标准换算比例不可为空',
      standardInventory: '标准库存',
      standardMaterialCode: '标准物料编码',
      standardPickableQuantity: '标准可拣配数量',
      standardPrice: '标准价',
      standardQuantityCannotBeEmpty: '标准数量不可为空',
      standardQuantityOfArrivalOrder: '到货单标准数量',
      standardRatio: '标准比例',
      standardStockQuantity: '标准库存|数量',
      standardStockUnits: '标准库存|单位',
      standardWorkingHours: '标准工时',
      standardWorkingHoursS: '标准工时(s)',
      standardOrConversionRatio: '标准|换算比例',
      standardOrPickableQuantity: '标准|可拣配数量',
      standardOrQuantity: '标准|数量',
      standardRemainingQuantity: '标准|剩余数量',
      standardUnits: '标准|单位',
      standardUsedQuantiterThan0Key: '标准|使用数量需要大于0',
      startBoxNumber: '起始箱号',
      startDate: '开始日期',
      startDateTag: '起息日',
      startDateCannotBeEmpty: '起息日不能为空',
      startDays: '起始天数',
      startTimeRd: '开始时间',
      startTimeNd: '起始日期',
      startUpTime: '启动时间',
      statement: '报表',
      statementTag: '结算单',
      statementDetails: '结算单明细',
      statementManagement: '结算单管理',
      statementNature: '结算单性质',
      statementNumber: '结算单号',
      statementNumberKey: '对应结算单号',
      statementOverview: '结算单总览',
      statementPrice: '结算单价',
      statementWorkbench: '结算单工作台',
      stationIdHanging: '站位ID(吊挂)',
      stationInformation: '站位信息',
      stationNumber: '站位号',
      stationNumberCannotBeEmpty: '站位号不能为空',
      stationWorkHours: '站位工时',
      stationery: '文具',
      stationery1: '文具1',
      stationery2: '文具2',
      statisticsDate: '统计日期',
      statusIsVoidedBeforeItCanBeVoided: '状态为作废才能作废',
      statusSummary: '状态汇总',
      statusUpdate: '状态更新',
      statusUpdateSuccessful: '状态更新成功',
      steamshipUnitPriceYuan: '汽运单价（元）',
      steamshipmentInformation: '汽运信息',
      steelCoilNumber: '钢卷号',
      steelFactoryDirectReleaseList: '钢厂可直放清单',
      steelFactoryDirectRrNumberKey: '钢厂可直放单号',
      steelFactoryDirectReleaseWorkbench: '钢厂可直放工作台',
      steelMillDirectRelease: '钢厂直放',
      steelMillDirectReleagementKey: '钢厂可直放清单管理',
      steelMillInterestFreeDays: '钢厂免息天数',
      steelMillInterestFreeDaysKey: '钢厂免贴息天数',
      steelMillsCanBeDirectlyReleased: '钢厂可直放',
      stencilDownload: '模板下载',
      stillToCome: '还需来款',
      stitchLength: '车缝长度',
      stitchType: '车缝类型',
      stitchingEfficiency: '车缝工作效率',
      strong: '强',
      subject: '主题',
      submit: '提交',
      submitApplication: '提交申请',
      submitAttributeValue: '提交属性值',
      submitDateTo: '提交日期到',
      submitFailed: '提交失败',
      submitForApproval: '提交审批',
      submitForReview: '提交审核',
      submitForReviewSuccessfully: '提交审核成功！',
      submitTime: '提交时间',
      submittedBy: '提交人',
      submittedForReview: '已提交审核！',
      submittedFrom: '提交日期从',
      subtotal: '小计',
      subtotalColorDeck: '色组小计',
      subtotalSize: '尺码小计',
      success: '成功',
      successButton: '成功按钮',
      successfulCorrespondence: '货票对应成功',
      successfulDirectRelease: '直放成功',
      successfulOperation: '操作成功',
      successfulSplitting: '拆分成功',
      summaryOfOutgoingProductDetails: '出仓商品汇总明细',
      SuperiorSubmitter: '上级提交人',
      suppliedBy: '供方承担',
      supplier: '供应商',
      supplierKey: '供应方',
      supplierEditable: '供应商(可编辑)',
      supplierInquiry: '供应商查询',
      supplierMaterialCode: '供应商物料编码',
      sureToDelete: '确定要删除吗?',
      symbol: '符号',
      systemAccountingData: '系统核算数据',
      systemAnnouncement: '系统公告',
      systemCode: '系统编码',
      systemException: '系统异常',
      systemInsuredUnitPrice: '系统投保单价',
      systemInvoiceCommodityDetails: '系统发票商品明细',
      systemLayoutConfiguration: '系统布局配置',
      systemLogisticsCostTotal: '系统物流费合计',
      systemMaterialCode: '系统物料编码',
      systemNumber: '系统编号',
      systemOperationAdministrator: '系统运维通知：若在系统使用过程中，遇到疑难问题，请联系管理员，联系电话:',
      systemParameterConfiguration: '系统参数配置',
      systemSectionNumber: '系统款号',
      systemTaxDifference: '系统税差',
      tableExport: '表格导出',
      tableFields: '表字段',
      tableHeader1: '表头1',
      tableHeader2: '表头2',
      tableName: '表名',
      tableSelection: '表选择',
      tagSet: '标签集',
      takeProfitEntryReturnOrderInquiry: '盘盈入仓回单查询',
      targetAmountExcludingTax: '目标额（不含税）',
      targetCurrency: '目标币种',
      targetSales: '目标销量',
      targetUnit: '目标单位',
      targetUnitName: '目标单位名称',
      tariff: '关税',
      task: '任务',
      taskAssociatedNodeDetails: '任务关联节点明细',
      taskDependencyDetails: '任务依赖关系明细',
      taskDescription: '任务描述',
      taskGrouping: '任务分组',
      taskInformation: '任务信息',
      taskManagement: '任务管理',
      taskManagementDetails: '任务管理详情',
      taskName: '任务名称',
      taskNameGrouping: '任务名称分组',
      taskNumber: '任务编号',
      taskStartDate: '任务开始日期',
      taskStartDateFrom: '任务开始日期从',
      taskStartDateTo: '任务开始日期到',
      taskType: '任务类型',
      taxAbbreviation: '税收简称',
      taxAmount: '税额',
      taxClassification: '税收分类',
      taxCodeNumber: '税则号',
      taxCodeRegistrationNumber: '税则登记号',
      taxDedicatedAccountNumber: '税务专用账号',
      taxDifference: '税差',
      taxDifferenceReceivable: '应收税差',
      taxDifferenceSplit: '税差拆分',
      taxIncluded: '含税',
      taxIncludedUnitPrice: '含税|单价',
      taxName: '税种名称',
      taxNotIncluded: '不含税',
      taxNotIncludedUnitPrice: '不含税|单价',
      taxPoint: '税点',
      taxRateInformation: '税率信息',
      taxRateManagement: '税率管理',
      taxRegistrationNumber: '税务登记号',
      taxType: '税种',
      taxOrAmount: '含税|金额',
      taxAmountCannotBeEmpty: '含税|金额不可为空',
      taxTotal: '含税|总额',
      taxControlExport: '税控接口发票导出路径',
      taxControlImport: '税控接口发票导入路径',
      taxControlInvoicingDetails: '税控开票明细',
      taxControlInvoicingQuantity: '税控开票数量',
      taxControlledBase: '税控基数',
      taxControlledMachineControlNumber: '税控机控制数',
      taxControlledNonComortMarkKey: '税控不强制导出标识',
      taxControlledPhysicalInvoices: '税控物理发票',
      taxInclusiveUnitPrice2: '含税单价2',
      taxpayerRegistrationNumber: '纳税人登记号',
      telephoneNumber: '客商电话',
      templateNumber: '模板编号',
      templateType: '模板类型',
      temporary: '暂',
      temporaryCost: '暂 费用',
      tenantAdministrator: '租户管理员快捷配置',
      terminalUnitPrice: '码头单价',
      test: '测试',
      testCriteria: '测试标准',
      testImportButton: '测试导入按钮',
      testInformation: '测试信息',
      testReport: '测试报告',
      theAdjustmentAmountnAmountKey: '调整金额不能大于收款金额',
      theAmountOfInterestToBeReturned: '现款应返息金额',
      theAmountOfMarginUseMarginKey: '保证金使用金额不能大于可用保证金',
      theAmountOfThisAdjustmentCannotBe0: '本次调整金额不能为0',
      theAmountOfThisAdvanceReceipt: '本次预收金额',
      theAmountOfThisInvoice: '本次开票|金额',
      theAmountOfThisReceipt: '本次收款金额',
      theAmountReceivableferenceKey: '应收金额物流费用税差',
      theAmountToBeUsedCalocatedKey: '本次使用金额不能大于待配金额',
      theBusinessUnitCannotBeEmpty: '经营单位不能为空',
      theContactAddressCannotBeEmpty: '联系人地址不可为空',
      theContactNameCannotBeEmpty: '联系人名称不可为空',
      theContactPhoneNumberCannotBeEmpty: '联系人电话不可为空',
      theContactTypeCannotBeEmpty: '联系人类型不可为空',
      theCurrentAdjustmentAmount: '本次调整金额',
      theCurrentAmountOfIncome: '本次收入数量',
      theCurrentArrivalQuuantityKey: '本次到货数量大于0且小于等于未完成数量',
      theCurrentCorrespondingQuantity: '本次对应数量',
      theCurrentDepositReeceivedKey: '本次预收保证金不能大于待收保证金',
      theCurrentOffsettingAmount: '本次冲抵金额',
      theCurrentOperationntinueKey: '当前操作将覆盖更新当前尺寸表及度量表，是否确定继续？',
      theCurrentPlanQuantity: '本次计划|数量',
      theCurrentProcessHaAllowedKey: '当前工序已被其他工艺单引用,不允许编辑',
      theCurrentProcessIsssFirstKey: '当前工序为空，请先选择工序',
      theCurrentRecordDoeoAccessKey: '当前记录不存在或无权限访问！',
      theCurrentStatusIsIrrevocable: '当前状态不可撤销',
      theDataInThisStatusCannotBeDeleted: '这个状态的数据不能删除',
      theDocumentDetailsHryAgainKey: '该单据明细还未保存，请保存后重试',
      theDocumentIsNotReadyToBeDeleted: '单据非准备状态不能删除',
      theExistenceOfMultiwSavingKey: '存在多条相同的目标单位，不允许保存！',
      theExpirationDateIsFrom: '失效日期从',
      theFolderIsNotAllowedToBeDeleted: '该文件夹不允许删除',
      theIncomingAmountMuAllowedKey: '汇入金额必须等于收款金额才允许提交',
      theInterestBaseCannotBeEmpty: '计息基数不能为空',
      theInvoiceIsInvalid: '该发票已作废',
      theInvoicedQuantityCannotBeEmpty: '开票数量不可为空',
      theInvoicedQuantityuantityKey: '开票数量不能大于原始数量',
      theInvoicingDateCannotBeEmpty: '开票日期不可为空',
      theLocalCurrencyCannotBeEmpty: '本位币币种不能为空',
      theLocalCurrencyExcBeEmptyKey: '本位币汇率不能为空',
      theMaturityDateOfThountingKey: '承兑到期日不能晚于钢厂免贴息天数',
      theMethodInTheClienhibitedKey: '客户端请求中的方法被禁止',
      theNatureOfTheInvoiceCannotBeEmpty: '发票性质不能为空',
      theNumberOfConfirmedPieces: '此次确认件数',
      theNumberOfSignaturuantityKey: '签收数量不能为0且小于未签数量',
      theOffsetAmountMustdAmountKey: '冲抵金额必须大于0且不能大于预付金额',
      theParameterNameCannotBeEmpty: '参数名称不能为空',
      thePickupPersonAndCequiredKey: '行的提货人和联系电话必填',
      thePlannedQuantityCuantityKey: '计划数量不能大于标准可拣配数量',
      thePlannedStockQuanuantityKey: '计划库存数量必须小于等于可拣配库存数量',
      theProportionOfTheRheGoodsKey: '限定剩余保证金占比剩余货值比例',
      theQuantityOfTheCuruantityKey: '本次报关数量必须大于0且小于未报关数量',
      theQuantityOfTheCurrentRedRush: '本次红冲数量',
      theQuantityOfThisReturn: '本次退货数量',
      theQuantityOfThisShipmentCannotBe0: '本次发货数量不能为0',
      theQuantityToBeUseduantityKey: '本次使用数量必须大于0且小于剩余数量',
      theRemainingPiecesAeUsedUpKey: '剩余件数用完，剩余数量也必须用完',
      theShippingNotificaBeEmptyKey: '发货通知日期不能为空',
      theShippingWarehousBeEmptyKey: '发货仓库地址不能为空',
      theStatusOfThisDocuDepositKey: '该单据状态不允许预收保证金',
      theStatusOfThisDocuorGoodsKey: '该单据状态不允许预收货款',
      theSyntaxOfTheClienstandItKey: '客户端请求的语法错误，服务器无法理解',
      theUpdateWasSuccessful: '更新成功!',
      theVersionBackupWilrationKey: '将对当前PO及PO下的BOM单做版本备份，PO将可修改且下游BOM单需再重新录入及提交审核，是否确定继续操作？',
      theWayOutOfTheWarehBeEmptyKey: '出仓方式不能为空',
      themeColor: '主题色',
      themeColorModification: '主题颜色修改',
      themeName: '主题名称',
      thinkingGuide: '思维导图',
      thirdPartyLogin: '第三方登录',
      thirdPartyShipmentNumber: '第三方发货单号',
      thisActionDeletesThntinueKey: '此操作将删除该数据, 是否继续?',
      thisActionWillAddThntinueKey: '此操作将明细加入到此采购到货单中, 是否继续?',
      thisActionWillBeOutntinueKey: '此操作将出仓销售发货单, 是否继续?',
      thisActionWillClearrmationKey: '此操作将会清空色组信息',
      thisActionWillDeleteTheRoleDoYouWantToContinue: '此操作将删除该角色, 是否继续?',
      thisActionWillPlayTntinueKey: '此操作将直放此数据, 是否继续?',
      thisActionWillWithdntinueKey: '此操作将该单据撤回准备状态，是否继续？',
      thisArrivalQuantity: '本次到货数量',
      thisCurrency: '本币',
      thisDiscountAmount: '本次折让金额',
      thisDiscountAmountNegative: '本次折让金额(负数)',
      thisInvoiceIsAnAdvasRecordKey: '此发票为提前开票，无法选择发货商品记录',
      thisInvoiceIsNotAnAontractKey: '此发票不是提前开票，无法选择销售合同',
      thisInvoiceQuantity: '本次开票|数量',
      thisIs: '这是',
      thisIsAMessage: '这是一段信息',
      thisIsCndGrid: '这是cnd-grid',
      thisIsCndUpload: '这是cnd-upload',
      operationVoidDocumentKey: '此操作将作废该单据，是否继续?',
      thisPurchaseQuantity: '本次采购数量',
      thisReturnQuantity: '本次退货|数量',
      time: '时间',
      timePassed: '通过时间 ',
      title: '标题',
      to: '到',
      toBeAnalyzed: '待分析',
      toBeAudited: '待审',
      toBeGeneratedForPlannedQuantity: '待生成计划数量',
      toBePurchased: '待采购',
      toShippingNoticeNumber: '到/发货通知单号',
      todaySYield: '今日产量',
      ton: '吨',
      tonnage: '吨数',
      tonnageDecimal: '吨数小位数',
      topicNumber: '主题编号',
      total0ArticlesTotalnt0YuanKey: '共0条，合计：数量0吨，不含税金额0元，税额0元，含税金额0元',
      total326ArticlesTot544YuanKey: '共326条，合计：收款金额164544元',
      totalAmount: '总额',
      totalAmountKey: '总金额',
      totalAmountExcludingTaxKey: '不含税总额',
      totalAmountOfProcurement: '采购总额',
      totalAmountPayable: '应付总额',
      totalAmountReceivedDuringThePeriod: '本期收款总额',
      totalAmountShippedDuringThePeriod: '本期发货总额',
      totalAmountShippedDuringThePeriodTag: '本期发货总量',
      totalAmountWithTax: '含税总额',
      totalContractAmount: '合同总额',
      totalInventory: '库存总量',
      totalNumber: '总数',
      totalPlatform: '总平台',
      totalPrice: '总价',
      totalPrice$: '总价（元）',
      totalTax: '总税额',
      totalUnitPriceYuan: '合计单价（元）',
      totalUnitPriceOfTransportation: '运输单价合计',
      totalView: '总览',
      tradeArea: '贸易区域',
      tradeType: '贸易类型',
      traditionalChinese: '繁体',
      trainingInformation: '培训信息',
      trainingName: '培训名称',
      trainingType: '培训类型',
      trainingUnit: '培训单位',
      transactionAndExpennitionsKey: '往来和费用项目定义',
      transactionDetails: '往来明细',
      transactionMethod: '成交方式',
      transactionNumber: '往来单号',
      transactionType: '往来类型',
      transfer: '货转',
      transferAmount: '调入金额',
      transferDetails: '移仓调拨明细',
      transferImport: '货转导入',
      transferOfGoodsRightWay: '货权转移方式',
      transferOfWarehouse: '移仓',
      transferOrderManagement: '移仓调拨单管理',
      transferOrderNo: '调拨单号',
      transferOrderNumber: '移仓单号',
      transferOrderSavedSuccessfully: '移仓调拨单保存成功',
      transferOrderWorkbench: '移仓调拨单工作台',
      transferOut: '移出',
      transferReview: '转审',
      transferReviewOpinion: '转审意见',
      transferTime: '转正时间',
      transferToPreSettlement: '转为预结算',
      transferTrialNode: '转审节点',
      transferUsage: '移仓用途',
      transitPort: '中转港',
      transportCommissionOrder: '运输委托单',
      transportContractAmount: '运输合同额',
      transportContractNumber: '运输合同号',
      transportModeCategory: '运输方式类别',
      transportUnitPriceYuan: '运输单价（元）',
      transportationContract: '运输合同',
      transportationMode: '运输方式',
      treeTitle: '树形标题',
      triggerGrouping: '触发器分组',
      triggerName: '触发器名称',
      triggerStatus: '触发器状态',
      triggerTime: '触发时间',
      triggerType: '触发器类型',
      tripartiteAgreementDetails: '三方协议详情',
      tripartiteAgreementList: '三方协议列表',
      triPartyAgreementAdded: '三方协议新增',
      twoDifferentPasswordsPleaseReEnter: '两次密码输入不同，请重新输入！',
      twoTimesPasswordInputIsNotTheSame: '两次密码输入不一致',
      twoTimesThePasswordTheSameKey: '两次输入密码不一致!',
      type: '类型',
      typeCode: '类型编码',
      typeName: '类型名称',
      typeOfCredit: '额度类别',
      typeOfFreightCharge: '运费承担类型',
      typeOfReferral: '转审类型',
      typeOfRevenueAndExpenditure: '收支方式',
      typeOfShipment: '装运类别',
      dateType: '日期类型',
      startDateSt: '起运日期',
      usd: '美元',
      billingAgeDay: '帐龄（天）',
      paymentWithin3DaysAfterShipment: '货后3天内付款',
      paymentWithinDays: '天内付款',
      typeOfShipping: '船运类型',
      typeOfTransfer: '调入类型',
      typeOfTransportationCommission: '运输委托类型',
      unanalyzed: '未分析',
      unarrivedQuantity: '未到货数量',
      uncompletedQuantity: '未完成数量',
      undeclaredQuantity: '未报关数量',
      undelivered: '未发送',
      undeliveredQuantity: '未发货数量',
      underline: '下划线',
      underwritingConditionsScript: '核销条件脚本',
      underwritingStopDate: '保底止息日',
      underwritingSubcategories: '核销子类',
      uniformPurchaseDistributionAdded: '统购分销新增',
      uniformPurchaseDistributionDetails: '统购分销详情',
      uniformPurchaseOfDistribution: '统购分销',
      uniformSocialCreditCode: '统一社会信用代码',
      uninvoicedQuantity: '未开票数量',
      uninvoicedAmount: '未开票|金额',
      uninvoicedOrQuantity: '未开票|数量',
      unitCode: '单位编码',
      unitIsEmptySubmit: '单位为空，是否继续提交?',
      unitName: '单位名称',
      unitOfArrival: '到货单位',
      unitOfMeasure: '计量单位',
      unitPriceOfArrivalOrder: '到货单单价',
      unlockLog: '解锁日志',
      unprocessed: '未处理',
      unread: '未读',
      unrequestedQuantity: '未申请数量',
      unreturnedQuantity: '未退货数量',
      unreturnedOrQuantity: '未退货|数量',
      unsignedQuantity: '未签收数量',
      up: '上升',
      update: '更新',
      updateExpirationDate: '更新失效日期',
      updateReceiptInterest: '更新收款利息',
      updateStatus: '更新状态',
      updateSuccessful: '更新成功',
      updateUnitPrice: '更新单价',
      uploadAttachment: '上传附件',
      uploadAvatarImageCagFormatKey: '上传头像图片只能是 JPG /PNG格式!',
      uploadAvatarImageSied500KbKey: '上传头像图片大小不能超过 500kb!',
      uploadImage: '上传图片',
      uploadTime: '上传时间',
      uploadedSuccessfully: '上传成功！',
      uploader: '上传人员',
      upperLimit: '上限',
      upstreamIdxField: '上游IDX字段',
      upstreamTable: '上游表',
      upstreamTableName: '上游表名',
      upstreamUnderwritingFields: '上游核销字段',
      urlLink: 'URL链接',
      arrivalType: '到货类型',
      numberOfContainers: '柜数',
      quantityToBeWrittenOff: '待核销数量',
      quantityToBeWrittenOffThisTime: '本次核销数量',
      shipmentUnit: '装运单位',
      numberOfShipmentsKey: '装运数量',
      unitOfWeight: '重量单位',
      outOfWarehouseStatus: '出仓状态',
      totalContainerVolume: '总柜量',
      dateOfCustomsDeclaration: '报关日期',
      returnDateKey: '还柜日期',
      loadingListNumber: '装柜清单号',
      dateOfLoading: '装柜日期',
      totalNumberOfContainers: '总柜数',
      containerSize: '集装箱规格',
      statistics: '统计',
      numberOfWarehouseReconciliation: '仓库核对数',
      bulkPickingWarehouse: '散货拣配仓库',
      fullContainerWarehouse: '整柜入仓仓库',
      containerLoading: '装柜',
      notificationType: '通知单类型',
      whetherTLeaveWarehouse: '是否自动出仓',
      quantityPicked: '本次拣配数量',
      listOfGoodsShipped: '发货商品清单',
      startTime: '起运时间',
      estimatedNumberOfContainers: '预计柜数',
      purchaseLotNumber: '采购批次号',
      productionBatch: '生产批次',
      pointInTimeDate: '时点日期',
      whetherToCompleteTheCheckout: '是否完成核销',
      receivingPointPickupDate: '接收点取货日期',
      loadingStation: '装载站',
      dateOfLoadingRegistration: '装柜登记日期',
      pickupLocation: '提柜地点',
      pickupLocationKey: '取货地点',
      requiredStockQuantity: '所需库存数量',
      pickedStockQuantity: '已拣配库存数量',
      quantityToBePicked: '待拣配数量',
      adjustmentOfStockType: '调整库存类型',
      usage: '用量',
      useGroup: '使用群体',
      useOfMoney: '用款',
      usePeriodMonths: '使用期限(月)',
      usedSecurityDeposit: '已动用保证金',
      userAuthorization: '用户授权',
      userCode: '用户编码',
      userInformationAcquReLoginKey: '用户信息获取失败请重新登陆！',
      userNameEmailMobilePhoneNumber: '用户名/邮箱/手机号',
      userPasswordChange: '用户密码修改',
      userSelectDocument: '用户选择单据',
      validMark: '有效标记',
      validPeriod: '有效期间',
      validPeriodBeginning: '有效期(始)',
      validPeriodEnd: '有效期(终)',
      validTimeEnd: '有效时间(终)',
      validTimeStart: '有效时间(始)',
      valueAddedTaxVat: '增值税',
      valueOfGoods: '货值',
      variableDeclaration: '变量声明',
      variableName: '变量名称',
      vatInvoiceLimit: '增值税发票限额',
      vatInvoiceNumber: '增值税发票号',
      vatType: '增值税类型',
      vatOrAmount: '增值税|金额',
      vatOrAmountTag: '增值税|税额',
      vatOrRate: '增值税|税率',
      vatRateCannotBeEmpty: '增值税|税率不可为空',
      verificationOfIdentity: '验证身份',
      versionBackupSuccessful: '版本备份成功！',
      versionEffectiveDate: '版本生效时间',
      versionHistory: '版本历史',
      versionMaintenance: '版本维护',
      versionUpdateDate: '版本更新日期',
      vesselType: '船舶类型',
      toGrantAuthorization: '授权',
      selectAUser: '请选择用户',
      offsetRuleConfiguration: '冲抵规则配置',
      maximumBearingUnit: '最大承重单位',
      selectAReferenceRootOrganization: '请选择参照根组织',
      refund: '退款',
      procurementImplementation: '采购执行情况',
      salesExecution: '销售执行情况',
      totalAmountIncludingTax: '含税总金额',
      totalAmountExcludingTax: '不含税总金额',
      selectPaymentMethod: '请选择付款方式',
      selectAtLeastOnePieceOfData: '请选择至少一条数据',
      listOfPurchaseContracts: '采购合同列表',
      levelKey: '级次',
      revenueAndExpenditureDirection: '收支方向',
      selectFunctionalCurrency: '请选择本位币种',
      grantor: '授予方',
      grantee: '被授予方',
      authorizationStartDate: '授权开始日期',
      authorizationEndDate: '授权结束日期',
      applicationNo: '申请单号',
      balance: '余款',
      unfreeze: '解冻',
      isAClosedOrvirtualized: '勾选数据存在已完结/已作废合同,请重新选择',
      basicInformationConfiguration: '基础信息配置',
      personnelUserConfiguration: '人员用户配置',
      enterCompanyName: '请输入公司名称',
      enterDepartmentName: '请输入部门名称',
      enterNameOfTheAccountingGroup: '请输入核算组名称',
      enterNameOfTheWarehouse: '请输入仓库名称',
      initializationFilter: '初始化过滤',
      creditDefinition: '信用定义',
      creditLimitApplicationDetails: '信用额度申请详情',
      monthsOfCreditSettlement: '信用月结月数',
      reasonsForApplication: '申请理由',
      selectApplicant: '请选择申请人',
      selectTheApplicationDate: '请选择申请日期',
      selectACreditDefinition: '请选择信用定义',
      selectTheGrantor: '请选择授予方',
      selectTheGrantee: '请选择被授予方',
      selectAmount: '请选择金额',
      selectTheAuthorizationStartDate: '请选择授权开始日期',
      selectTheAuthorizationEndDate: '请选择授权结束日期',
      creditLimitDefinition: '信用额度定义',
      quotaName: '额度名称',
      creditLimitConfiguration: '信用额度配置',
      enableCreditLimit: '是否启用信用额度',
      result: '结果',
      generateInternationalPremiumInvoice: '生成国际保费发票',
      enterOverdueDescription: '录入逾期说明',
      entityNameKey: '实体名',
      chineseName: '中文名称',
      video: '视频',
      videoComparison: '视频比对',
      videoList: '视频列表',
      videoPlayback: '视频播放',
      thisNodeIsFullyExpanded: '该节点全部展开',
      thisNodeIsAllCollapsed: '该节点全部收起',
      userRoleSearch: '用户角色查询',
      videoUpload: '视频上传',
      view: '查看',
      viewAccessories: '查看附件',
      viewApprovalHistory: '查看历史审批记录',
      viewHistoryVersion: '查看历史版本',
      viewLogisticsCostStatistics: '查看物流费用统计',
      viewProductionLineInformation: '查看产线信息',
      viewPurchaseExecution: '查看采购执行情况',
      viewSalesExecution: '查看销售执行情况',
      virtual: '虚拟',
      virtualEntityType: '虚拟实体类型',
      virtualOrganization: '虚拟组织',
      volumeRatio: '量比',
      volumeUnits: '体积单位',
      voucher: '凭证',
      warehouse: '仓库',
      warehouseAbbreviation: '仓库简称',
      warehouseArea: '仓库面积',
      warehouseCode: '仓库编码',
      warehouseDetails: '仓库详情',
      warehouseName: '仓库名称',
      warehouseNameTag: '出仓仓库名称',
      warehouseQuery: '仓库查询',
      warehouseReturnAdjustmentOrderAdded: '出仓回单调整单新增',
      warehouseReturnNumber: '仓库回仓号',
      warehouseReturnNumberTag: '仓库回单号',
      warehouseShortCode: '仓库简码',
      warehouseType: '仓库类型',
      warehouseVolume: '仓库容积',
      warningButton: '警告按钮',
      waterTransportInformation: '水运信息',
      wayOfDelivery: '提货方式',
      waybillWorkbench: '在途单工作台',
      weak: '弱',
      webSite: '网址',
      website2: '网址2',
      website3: '网址3',
      welcomeLogin: '欢迎登录',
      whetherEarlyInvoicingLogo: '是否提前开票标识',
      whetherExternalOrganization: '是否外部组织',
      whetherItHasBeenCorresponded: '是否已对应',
      whetherItHasBeenSettled: '是否已结算',
      whetherItHasBeenSignedFor: '是否已签收',
      whetherItIsAnAnnualAgreement: '是否为年协',
      whetherItIsInvalid: '是否失效',
      whetherItIsTheMainVideo: '是否是主视频',
      whetherProjectItem: '是否工程项目',
      whetherRedFlush: '是否红冲',
      whetherRiskTransfer: '是否风险转移',
      whetherSinglePiece: '是否单件',
      whetherTheAdministrator: '是否管理员',
      whetherTheBasicAccount: '是否基本户',
      whetherTheDefaultContactIsNotEmpty: '是否默认联系人不可为空',
      whetherTheLastLevel: '是否末级',
      whetherTheMainVideo: '是否主视频',
      whetherThePortOfEntryNotice: '是否集港通知',
      whetherTheProjectCanNotBeEmpty: '是否工程项目不能为空',
      whetherThePurchaseIllmentsKey: '采购是否分批付款',
      whetherTheRebateRetstOrNotKey: '返利是否返息',
      whetherTheReviewerCanNotBeEmpty: '是否需要复核人不能为空',
      whetherTheReviewerIsRequired: '是否需要复核人',
      whetherTheVoucherHasBeenMade: '是否已制凭证',
      whetherThereIsATradeBackground: '是否有贸易背景 ',
      whetherToAddNotesInformation: '是否追加备注信息?',
      whetherToAutoComplete: '是否自动完成',
      whetherToAutomaticanterestKey: '是否自动更新利息',
      whetherToContinueToSubmit: '是否继续提交',
      whetherToDelete: '是否删除',
      whetherToEnable: '是否启用',
      whetherToExcludeInventory: '是否剔除库存',
      whetherToExempt: '是否免赔',
      whetherToExpandSharing: '是否扩大共享',
      whetherToForceCheck: '是否强制校验',
      whetherToGenerateInvoicing: '是否生成开票',
      whetherToGeneratePayment: '是否生成付款',
      whetherToGroup: '是否分组',
      whetherToMakeVouchers: '是否制凭证',
      whetherToOffset: '是否冲抵',
      whetherToOperate: '是否经营',
      whetherToPass: '是否通过',
      whetherToPrice: '是否定价',
      whetherToReturnTheGoods: '是否退货',
      whetherToSchedulePayment: '是否排款',
      whetherToShow: '是否显示',
      whetherToSignBack: '是否回签',
      whetherToStopBusiness: '是否停业',
      whetherUnlimited: '是否无限期',
      whetherWePayTheShippingCost: '是否我司支付运费',
      wholeContainer: '整柜',
      wholeCut: '整切',
      width: '幅宽',
      widthCm: '幅宽(cm)',
      widthMm: '宽度(mm)',
      withExportInvoiceInterface: '有出口发票接口',
      withTableHeaderCopy: '带表头复制',
      withTaxControlInterface: '有税控接口',
      withdraw: '撤销',
      withdrawOrder: '撤单',
      withdrawRequest: '撤销申请',
      withdrawSubmit: '撤销提交',
      withdrawSubmitKey: '撤回提交',
      withdrawalFee: '预提费用',
      withdrawalRequest: '撤单申请',
      withdrawalRequestSuccessful: '撤销申请成功',
      withdrawalScript: '撤回脚本',
      withdrawalSuccess: '撤单成功',
      withdrawalSuccessful: '撤销成功！',
      workHistory: '工作履历',
      workHours: '工时',
      workNumber: '工号',
      workSequenceCode: '工序编码',
      workSequenceNumber: '工序编号',
      workSequenceParameter: '工序参数',
      workSequenceSam: '工序SAM',
      workSequenceSamSeconds: '工序SAM(秒)',
      bulkShippingOrder: '生成散货发货单',
      fullContainerShipmentOrder: '生成整柜发货单',
      AuthorizationList: '授权列表',
      AuthorizedDocuments: '授权单据',
      AuthorizedBy: '授权人',
      AuthorizedByKey: '被授权人',
      DateOfTermination: '终止日期',
      ReasonForAuthorization: '授权原因',
      enterAuthorizer: '请输入授权人',
      enterAuthorizedPerson: '请输入被授权人',
      enterDateOfActivation: '请输入启用日期',
      enterExpirationDate: '请输入终止日期',
      enterReasonForAuthorization: '请输入授权原因',
      workUnitPrice: '工价单价',
      workUnitPriceYuan: '工价单价(元)',
      workshop: '车间',
      workshopCode: '车间编码',
      workshopConfiguration: '车间配置',
      workshopConfigurationDetails: '车间配置详情',
      workshopName: '车间名称',
      workshopNameChinese: '车间名称(中文)',
      workshopNameEnglish: '车间名称(英文)',
      workshopNumber: '车间编号',
      writeOffConditions: '核销条件',
      writeOffConfigurationList: '核销配置列表',
      wrongNumberOfSignatures: '签收数量有误',
      wrongTouchInstruction: '误触指令',
      yarnStructure: '纱织结构',
      year: '年',
      inventoryContractAdjustmentOrder: '存货合同调整单',
      actualReceiptPaymentInquiry: '实收付余额查询',
      support: '支',
      cronExpressions: 'cron表达式',
      yearAgreementNumber: '年协议号',
      yes: '是',
      yuan: '元',
      zeroCut: '零切',
      zhangSan: '张三'
    },
    tabs: {
      arrivalDetails: '到货明细',
      arrivalList: '到单列表',
      arrivedGoodsDetails: '到货商品明细',
      basicInformation: '基础信息',
      cargoInformation: '货运信息',
      cityPortList: '城市港口列表',
      claimDetails: '认领明细',
      commodityDetails: '商品明细',
      containerTypeAndQuantityInformation: '柜型柜量信息',
      contractList: '合同列表',
      costBreakdown: '费用明细',
      costDetails: '费用明细',
      deliveryAndShippingrmationKey: '交期及运输概要信息',
      inventoryContractAdjustmentList: '存货合同调整单列表',
      goodsNumberList: '货号列表',
      inboundDetails: '入仓明细',
      invoiceDetails: '发票明细',
      invoicingDetails: '开票明细',
      listData: '列表数据',
      bookingInformation: '订舱信息',
      bookingRegistrationInformation: '订舱登记信息',
      loadingStatus: '装柜使用情况',
      salesContractDetails: '销售合同商品明细',
      invoiceMatching: '发票匹配入仓明细',
      shipmentPickingDetails: '发货拣配明细',
      containerInquiry: '货柜查询',
      newCargoGoodsDetails: '新增货运商品明细',
      shippingGoodsSummary: '发货商品汇总',
      prePickingByContainer: '按柜挑选预拣配',
      loadingDetails: '装柜明细',
      movingDetails: '移仓明细',
      outboundDetails: '出仓明细',
      paymentDetails: '付款明细',
      pickingAndDistributionDetails: '拣配明细',
      productDetails: '商品明细',
      functionGuide: '功能导图',
      loadingContainerArrivalListRegistration: '装柜到货清单登记',
      wholeContainerShipmentNotificationForm: '整柜发货通知单',
      pickingAndMatchingTable: '挑拣配表',
      actualWriteOffDetails: '应实核销明细',
      newPurchaseInvoice: '新增采购发票',
      purchaseInvoiceDetails: '采购发票详情',
      newBookingRegistration: '新增订舱登记',
      bookingRegistrationDetails: '订舱登记详情',
      newLoadingListRegistration: '新增装柜清单登记',
      loadingListRegistrationDetails: '装柜清单登记详情',
      newWarehouseReceipt: '新增进仓回单',
      inboundReturnDetails: '进仓回单详情',
      newBulkCargoArrivalNote: '新增散货到货通知单',
      bulkArrivalNoticeDetails: '散货到货通知单详情',
      newBulkLoadingRegistrationForm: '新增散货装柜登记单',
      bulkLoadingRegistrationDetails: '散货装柜登记详情',
      newFullContainerArrivalNotice: '新增整柜到货通知单',
      fullContainerArrivalNoticeDetails: '整柜到货通知单详情',
      bulkShipmentNotificationDetails: '散货发货通知单详情',
      newBulkShipmentNotificationForm: '新增散货发货通知单',
      fullContainerShipmentNoticeDetails: '整柜发货通知单详情',
      newFullContainerShipmentNotice: '新增整柜发货通知单',
      newWarehouseReturnAdjustmentOrder: '新增进仓回单调整单',
      inboundReturnOrderAdjustmentDetails: '进仓回单调整单详情',
      outboundReturnOrderAdjustmentDetails: '出仓回单调整单详情',
      newOutboundReturnOrderAdjustmentOrder: '新增出仓回单调整单',
      queryCriteria: '查询条件',
      receiptIssueDetails: '出入库明细',
      prePickingDetails: '预拣配明细',
      occupiedQuotaDetails: '占用额度明细',
      salesAllowanceApprovalDetails: '销售折让核准明细',
      salesRevenueDetails: '销售收入明细',
      outOfWarehouseCommodityDetails: '出仓商品明细',
      contractPrePickingDetails: '合同预拣配明细',
      noticeDetails: '通知明细',
      relatedContractInquiry: '关联合同查询',
      settlementInformation: '结算信息',
      shippingDetails: '发货明细',
      shippingGoodsDetails: '发货商品明细',
      systemInformation: '系统信息'
    },
    title: {
      AccessLogQuery: '访问日志查询',
      accessTime: '访问时间',
      accountAliases: '账户别名',
      accountingGroup: '核算组',
      addSettlementCenter: '新增成本结算中心',
      fieldName: '字段名称',
      conversionType: '转实类型',
      balance: '余款',
      revenueAndExpenditureDirection: '收支方向',
      inventoryExceptionReview: '库存异常复核',
      standardQuantityExceptions: '标准数量异常',
      inventoryQuantityExceptions: '库存数量异常',
      prePickedQuantity: '预拣配数量',
      agreeAndReturnToReviewer: '同意后返回转审人',
      staticReferrer: '静态转审人',
      dynamicReferrer: '动态转审人',
      domesticReceiptRegistration: '国内收款登记',
      newDomesticReceiptRegistration: '国内收款登记新增',
      payingCustomer: '付款客户',
      claims: '认领',
      outboundRevenueRegistrationWorkbench: '出仓待收入登记工作台',
      confirmClaims: '确认认领',
      cancelClaimSubmission: '取消提交认领',
      claimingDepartment: '认领部门',
      claimsAccountingTeam: '认领核算组',
      claimedPersonnel: '认领人员',
      receiveAndPay: '收付',
      generateSalesRevenueRegistrationForm: '生成销售收入登记单',
      customerLevelConfiguration: '客户等级配置',
      levelCode: '等级编码',
      levelPicture: '等级图片',
      superiorName: '上级名称',
      priceClassification: '价格分类',
      priceClassificationCode: '价格分类编码',
      priceClassificationName: '价格分类名称',
      priceConfiguration: '价格配置',
      priceConfigurationAdd: '价格配置新增',
      retailPrice: '零售价',
      customerClassPrice: '客户等级价格',
      customerLevel: '客户等级',
      effective: '生效',
      submitAClaim: '提交认领',
      priceInformation: '价格信息',
      specifyTierPrice: '指定等级价格',
      setStepPrice: '设置阶梯价',
      stepPrice: '阶梯价',
      dateOfReceipt: '到账日期',
      amountClaimed: '认领金额',
      domesticCollectionClaims: '国内收款认领',
      receiptImport: '收款导入',
      submitConfirmation: '提交确认',
      cancelConfirmation: '取消确认',
      cancellation: '取消认领',
      incomingCustomerDescription: '来款客户描述',
      billingCustomer: '做账客户',
      currency: '认领币种',
      receiptRegistrationNumber: '收款登记号',
      effectiveSetup: '生效设置',
      price: '价格',
      order: '序',
      numberOfOrders: '订货数',
      taxControlledDetails: '税控明细',
      taxControlledProductDetails: '税控商品明细',
      generateInvoicingData: '生成开票数据',
      taxClassificationName: '税收分类名称',
      daysToPayback: '回款天数',
      accountPeriodQuery: '账期查询',
      taxCodeName: '税收编码名称',
      timeOfArrival: '到货时间',
      tagCode: '标签编码',
      labelDefinitionDescription: '标签定义说明',
      salesQuantity: '销售数量',
      permissionQuery: '权限查询',
      addExpenseInvoiceRegistration: '新增费用发票登记',
      addNewConsignee: '新增收货人',
      priceListDetails: '价格单详情',
      pricePlanDetails: '价格方案详情',
      actualReceiptAndPaymentTransactionDetails: '实收付往来明细',
      newCostInvoiceRegistration: '新增费用发票登记',
      priceListAdditions: '价格单新增',
      pricePlanAdded: '价格方案新增',
      priceConfigurationAdded: '价格配置新增',
      newLegalOrganization: '新增法人组织',
      costSettlementCenter: '成本结算中心',
      loadingSituation: '装货情况',
      quotaName: '额度名称',
      rootNode: '根节点',
      creditLimit: '信用额度',
      attributeName: '属性名',
      agreementNumber: '协议号',
      agreementType: '协议类型',
      amount: '金额',
      entryAmount: '入账金额',
      amountHandling: '金额处理方式',
      amountWithTax: '含税金额',
      amountWithoutTax: '不含税金额',
      applicationDate: '申请日期',
      arrival: '到货',
      arrivalDate: '到港日期',
      arrivalWarehouse: '到货仓库',
      articleNumberCode: '货号编码',
      balanceKey: '余额',
      beforeAndAfter: '货前后',
      beginningOfPeriod: '期初',
      billReceivingBank: '收票银行',
      bookingDate: '订舱日期',
      bookingNumberActual: '订舱单号（实际）',
      bookingNumberSystem: '订舱单号（系统）',
      bulkArrival: '散货到货',
      bulkDelivery: '散货发货',
      businessElements: '业务要素',
      carrierShipowner: '承运人/船东',
      changmaoArea: '常贸地区',
      city: '市',
      claimStatus: '认领状态',
      commissionCustomer: '付佣客户',
      commissionPaymentStandard: '付佣标准',
      commissionRate: '付佣比例',
      commodityAttributeValue: '商品属性值',
      commodityClassification: '商品分类',
      company: '公司',
      consignee: '收货人',
      consigneeCellPhoneNumber: '收件人手机号',
      consigneeSPhoneNumber: '收货人电话',
      consignor: '委托方',
      consumptionTaxAmount: '消费税税额',
      consumptionTaxRate: '消费税税率',
      contractAmount: '合同金额',
      contractAmountInformation: '合同金额信息',
      contractDeclarationDate: '合同申报日期',
      contractExpirationDate: '合同到期日期',
      conversionRatio: '换算比例',
      corporateInformation: '法人控税信息',
      cost: '成本',
      counterpartyAccountNumber: '对方账号',
      counterpartyAgreementNumber: '对方协议号',
      counterpartyBank: '对方银行',
      counterpartyContractNumber: '对方合同号',
      createdAt: '创建时间',
      createdBy: '创建人',
      creationMethod: '创建方式',
      needCurrency: '应付币种',
      entryCurrency: '入账币种',
      current: '往来',
      currentBalance: '往来余额',
      customers: '客户',
      customsBroker: '报关行',
      customsClearanceTime: '截关时间',
      customsDeclarationDate: '报关申报日',
      customsInspectionDate: '海关检查日',
      dangerousGoodsOrNot: '是否危险品',
      date: '日期',
      dateAndMethod: '日期方式',
      dateOfArrivalNotice: '到货通知日期',
      dateOfChangeOfOrderAtCustomsBroker: '报关行换单日',
      dateOfNotificationOfShipment: '发货通知日期',
      dateOfSigning: '签约日期',
      daysOfDelivery: '交期天数',
      deliveryAddress: '送货地址',
      deliveryPlanMethod: '交期计划方式',
      deliveryTerms: '交期条款',
      deliveryTime: '配送时间',
      departureDate: '开船日期',
      descriptionOfLoadingPort: '装货港描述',
      descriptionOfShippingMethod: '运输方式描述',
      descriptionOfTransshipmentPort: '中转港描述',
      destinationDescription: '目的地描述',
      detailedAddress: '详细地址',
      dictionaryList: '字典列表',
      directReleaseMethod: '直放方式',
      districtCounty: '区/县',
      domesticProcurementContract: '国内采购合同',
      domesticSalesContract: '国内销售合同',
      earliestDeliveryDate: '最早交货日期',
      customerSupplierPermission: '客商部门权限',
      generatePurchaseInvoice: '生成采购发票',
      guaranteedBase: '保底基数',
      earliestDeliveryDateSt: '最早交期',
      earliestDeliveryDateNd: '最早交货日期',
      earliestDeliveryDateRd: '最早交货期',
      earliestPickupDate: '最早提柜日',
      effectiveTimeOfThisVersion: '本版本生效时间',
      essentialInformation: '基本信息',
      estimatedArrivalTime: '预计到达时间',
      exchangeRate: '汇率',
      excludingTaxUnitPrice: '不含税单价',
      expectedArrivalDate: '预计到港日',
      expectedCompletionDateOfShipment: '预计装运完成日',
      expectedDateOfDeparture: '预计起运日',
      expectedPaymentDate: '预计支付日期',
      exportDutyTaxRate: '出口关税|税率',
      exportProvisionalTariffTaxRate: '出口暂定税|税率',
      filingDeadline: '申报截止时间',
      finalDeliveryLocation: '最终交货地点',
      finalDestination: '最终目的地',
      finalReviewTime: '终审时间',
      foreignPurchaseContract: '国外采购合同',
      foreignSalesContract: '国外销售合同',
      freightBearingType: '运费承担类型',
      freightForwarder: '货代公司',
      fromTo: '从...到',
      certificateType: '证书类型',
      idNumber: '证件号码',
      organizationName: '组织名',
      organizationPath: '组织路径',
      cronType: 'cron类型',
      fullContainerArrival: '整柜到货',
      importInvoiceNumber: '进口发票号',
      importMostFavoredNationTariffRate: '进口最惠国关税|税率',
      importOrdinaryTariffRate: '进口普通关税|税率',
      importTariffTaxRate: '进口暂定税|税率',
      inbound: '入仓',
      inboundReturnNumber: '进仓回单号',
      inboundWarehouse: '入仓仓库',
      insuranceCompany: '保险公司',
      inventory: '库存',
      inventoryBalance: '库存余额',
      inventoryNumber: '盘点单号',
      inventoryQuantity: '库存数量',
      inventoryUnit: '库存单位',
      invoiceNotYetDueForReminder: '发票未到提醒日',
      inwardAmount: '汇入金额',
      inwardReturnOrderAdjustmentNumber: '进仓回单调整单号',
      itemNumber: '货号',
      itemNumberName: '货号名称',
      latestDateOfShipment: '最迟装运日期',
      latestDeliveryDate: '最迟交货日期',
      latestDeliveryDateSt: '最迟交货期',
      latestDeliveryDateNd: '最迟交期',
      contractEffectiveDate: '合同生效日',
      discountRate: '贴息率',
      warehouseAddress: '仓库地址',
      warehouseContact: '仓库联系人',
      configureTenant: '配置租户',
      sendTime: '发送时间',
      recipientName: '收件人名',
      senderName: '发件人名',
      recipient: '收件人',
      recipientAddress: '收件人地址',
      sender: '发件人',
      latestDeliveryDateRd: '最迟送达时间',
      latestDeliveryDateTh: '最晚交货期',
      listOfCountriesRgions: '国别地区列表',
      localCurrencyOrAmount: '本币|金额',
      localCurrencyCurrency: '本币|币种',
      logisticsServiceProvider: '物流服务商',
      logisticsSourceType: '物流来源类型',
      longTermCargoPickupPerson: '长期提货人',
      lossOrderNumber: '盘亏单号',
      menuName: '菜单名',
      mobileTerminal: '手机端',
      modeOfTransport: '运输方式',
      modifiedAt: '修改时间',
      modifiedBy: '修改人',
      moveOutOfWarehouse: '移出仓库',
      moveToWarehouse: '移入仓库',
      nameOfShip: '船名',
      notifiedPerson: '被通知人',
      numberOfShip: '船次',
      operatedBy: '操作人',
      operationMethod: '经营方式',
      expirationTimeTag: '操作时间',
      origin: '原产地',
      originCurrency: '原币|币种',
      originalCurrency: '原币币种',
      originalCurrencyOfCommissionPayment: '付佣原币币种',
      originalCurrencyToLngeRateKey: '原币折本币|汇率',
      originalOrCurrencyAmount: '原币|金额',
      originalVersionEffectiveTime: '原始版本生效时间',
      systemOrderNumber: '系统单号',
      earliestReturnedDate: '最早还柜日',
      expectedCutDate: '最晚还柜日',
      otherAdditionalNotes: '其他补充说明',
      outbound: '出仓',
      outboundMethod: '出仓方式',
      outboundPicking: '出仓拣配',
      outboundReturnOrderNumber: '出仓回单号',
      outgoingWarehouse: '出仓仓库',
      outwardReturnAdjustmentNumber: '出仓回单调整单号',
      overloadingRate: '溢装率',
      packagingUnit: '包装单位',
      packingQuantity: '包装数量',
      parentProjectName: '父项目名称',
      parentProjectNumber: '父项目号',
      payer: '付款方',
      paymentCurrency: '支付币种',
      paymentMethod: '收款方式',
      paymentPurpose: '付款用途',
      paymentRatio: '支付比例',
      paymentRequestNumber: '付款申请单号',
      personnel: '人员',
      pickerContact: '提货人联系方式',
      pickupPerson: '提货人',
      pickUpPoint: '提柜点',
      placeOfContracting: '签约地点',
      selectInterestCalculationMethod: '请选择计息方式',
      portOfArrival: '抵港港区',
      portOfDeparture: '起运港',
      portOfDischarge: '卸货港',
      portOfLoading: '装货港',
      PosCard: 'POS卡',
      premiumAmount: '保费金额',
      premiumCurrency: '保费币种',
      priceTerms: '价格条款',
      productCategory: '商品分类',
      productName: '商品名称',
      profitPerTonSettlement: '结算单吨利润',
      projectDate: '项目日期',
      projectName: '项目名称',
      projectType: '项目类型',
      province: '省',
      purchaseAndSalesMethod: '购销方式',
      purchaseContractNumber: '采购合同号',
      purchaseInvoiceNumber: '采购发票号',
      purchaseReturnNo: '采购退货单号',
      quantity: '数量',
      receiptClaimNumber: '收款认领单号',
      receiptCurrency: '收款币种',
      receiptRegistrationNumber: '收款登记单号',
      receiptSubtype: '收款子类型',
      receivedByCustomsBroker: '报关行接单日',
      recipientSt: '收款方',
      recipientKey: '收款人',
      remarks: '备注',
      returnPoint: '还柜点',
      revenue: '收入',
      salesContractNumber: '销售合同号',
      salesInvoiceNo: '销售发票号',
      salesReturnNo: '销售退货单号',
      serialNumber: '序号',
      serviceProvider: '服务供应商',
      settlementIncrement: '结算增值',
      settlementMethod: '结算方式',
      settlementProfit: '结算利润',
      shipment: '发货',
      shipper: '托运人',
      shippingAmount: '运费金额',
      shippingCurrency: '运费币种',
      shippingWarehouse: '发货仓库',
      shortShipmentRate: '短装率',
      source: '来源',
      sourceDocumentCode: '来源单据编码',
      sourceDocumentNumber: '来源单据号',
      sourceDocumentType: '来源单据类型',
      specificationAbbreviation: '规格简称',
      specificationChineseName: '规格中文名',
      specificationCode: '规格编码',
      specificationEnglishName: '规格英文名',
      standardQuantity: '标准数量',
      standardUnit: '标准单位',
      statisticalQuantity: '统计数量',
      statisticalUnit: '统计单位',
      status: '状态',
      stockMethod: '库存方式',
      stockPickingDetails: '库存拣配明细',
      suppliers: '供应商',
      takeProfitOrderNumber: '盘盈单号',
      tariffAmount: '关税金额',
      tariffRate: '关税税率',
      tax: '税',
      taxRate: '税率',
      tenant: '租户',
      tenantAdministratorConfiguration: '租户管理员账号配置',
      tenantCode: '租户编码',
      tenantName: '租户名称',
      terminalName: '码头名称',
      totalAmountOfPayment: '支付总金额',
      totalCargoName: '货物总品名',
      totalGrossWeight: '总毛重',
      totalNetWeight: '总净重',
      totalNumberOfPieces: '总件数',
      totalQuantity: '总数量',
      totalVolume: '总体积',
      transferOfCargoRights: '货权转移方式',
      transshipmentPort: '中转港',
      typeOfTrade: '贸易类型',
      unit: '单位',
      unitPrice: '单价',
      unitPriceWithTax: '含税单价',
      units: '单位',
      unloadingPortDescription: '卸货港描述',
      user: '用户',
      userAccount: '用户账号',
      userAttribute: '用户属性',
      userName: '用户名称',
      vatAmountKey: '增值税税额',
      vatAmount: '增值税额',
      vatRate: '增值税率',
      vatRateKey: '增值税税率',
      versionNumber: '版本号',
      warehouseTransferOrderNo: '移仓调拨单号',
      webSide: 'web端',
      whetherBankAcceptanceIsRequired: '是否需要银行承兑',
      whetherTheAccountNumberIsRequired: '是否需要账号',
      wholeContainerDelivery: '整柜发货',
      winningBidNumber: '中标号',
      zipCode: '邮编'
    },
    btns: {
      addANewDocument: '新增单据',
      addANewInventoryIncomingOrder: '新增盘盈入仓单',
      addANewProductCategory: '新增商品大类',
      addAdjustmentOrder: '新增调整单',
      addArea: '新增地区',
      addAssetPropertyDetails: '新增资产属性详情',
      addBankInformation: '新增银行信息',
      addBusinessDictionary: '新增业务字典',
      addBusinesOrganization: '新增业务组织',
      addButton: '添加按钮',
      addCollectionClaim: '新增收款认领',
      addContainerType: '新增集装箱型',
      addContractDetailsSuccessfully: '新增合同明细成功',
      addCorporateInformation: '新增法人控税信息',
      addCustomerConfiguration: '新增客户配置',
      addDeliverySchedule: '新增送货计划',
      addDevice: '添加设备',
      copyUserDocument: '复制用户单据',
      addDictionary: '新增字典',
      addExchangeRateNumber: '新增汇率编号',
      addFinancialOrganization: '新增财务组织',
      addFolder: '新增文件夹',
      addItemNumber: '新增货号',
      addItemNumberKey: '添加货号',
      addMaterialFile: '新增物料档案',
      addNewAssetCategories: '新增资产大类',
      addNewAttributes: '新增属性',
      addNewBusinessRole: '新增业务角色',
      addNewCategory: '新增分类',
      addNewLine: '新增行',
      addNewModelNumberFile: '新增款号档案',
      addNewPaymentApplication: '新增付款申请',
      addNewPersonnel: '新增人员',
      addNewProductAttributes: '新增商品属性',
      addNewProductionLineProperties: '新增产线属性',
      addNewTask: '新增任务',
      addNewUser: '新增用户',
      addOrderPlan: '新增订货计划',
      addOrderPlanWorkbenchList: '新增订货计划工作台列表',
      addPickerInformation: '新增提货人信息',
      addPlanDetails: '新增计划明细',
      addProductAttributeValue: '新增商品属性值',
      addProductDetails: '新增商品明细',
      addPropertyValue: '添加属性值',
      addRootNodeOrganization: '新增根节点组织',
      addRootOrganization: '新增所属根组织',
      addSalesContract: '新增销售合同',
      addSalesInvoice: '新增销售发票',
      addSalesOrder: '新增销售发货单',
      addSalesShipmentDetails: '新增销售发货明细',
      addSalesShipmentGenerated: '加入待生成销售发货单',
      addSchedulingSolution: '新增排布方案',
      addSettlementDetails: '新增结算明细',
      addSettlementStatement: '新增结算单',
      addTaskManagement: '新增任务管理',
      addTenant: '新增租户',
      addToArrival: '加入到货',
      addToCart: '加入购物车',
      addToLogisticsConsole: '加入物流操作台',
      addToPendingInTranserationKey: '加入待生成在途单',
      addToToBeGeneratedArrivalOrder: '加入待生成到货单',
      addToWarehouse: '加入入仓',
      addUnderwritingConfiguration: '新增核销配置',
      addUOM: '新增计量单位',
      addWarehouse: '新增仓库',
      addBankCategory: '新增银行分类',
      expandAll: '全部展开',
      putAllAway: '全部收起',
      addWorkshopConfiguration: '新增车间配置',
      addManual: '新增(手工)',
      addedLineBelowDisableInsertion: '新增行下方禁止插入',
      addedPageImportPurchaseContract: '新增页进口采购合同',
      addedPageSignatureDetails: '新增页签版详情',
      additionalAdjustmentOptions: '追加调整选择',
      adjustedInventoryQuantity: '调整后|库存数量',
      cancelCancel: '取消作废',
      cancelClose: '取消完结',
      cancelCorrespondence: '取消对应',
      cancelEnable: '取消启用',
      cancelFreeze: '取消冻结',
      confirmationOfClaim: '确认认领',
      generateInTransit: '生成在途',
      generateInTransitOrder: '生成在途单',
      generateInTransitOrderDetails: '生成在途单明细',
      generateOrderPlanner: '生成订货计划单',
      generateOutboundAdjustmentOrder: '生成出仓调整单',
      generateProcurementContract: '生成采购合同',
      generatePurchaseArrivalNotice: '生成采购到货通知单',
      generatePurchaseArrivalOrder: '生成采购到货单',
      generatePurchaseCostAdjustmentOrder: '生成采购成本调整单',
      generatePurchaseCosionFormKey: '生成采购成本登记单',
      generatePurchaseReceipt: '生成采购入仓单',
      generatePurchaseRequisition: '生成采购申请单',
      generatePurchaseReturnNotice: '生成采购退货通知单',
      generateSalesInvoice: '生成销售发票',
      generateSalesReturnNotice: '生成销售退货通知单',
      generateSalesRevenueAdjustmentForm: '生成销售收入调整单',
      generateSettlementAdjustment: '生成结算调整单',
      generateShippingOrders: '生成发货单',
      generateSignOnSheet: '生成签收单',
      generateSteelFactoraseListKey: '生成钢厂可直放清单',
      generateTransactionAdjustmentForm: '生成往来调整单',
      generateTransferOrder: '生成移仓单',
      generateTransferTransferOrder: '生成移仓/调拨单',
      generateWarehouseReceipt: '生成出仓回单',
      generateWarehouseRentOrderKey: '生成出仓回单调整单',
      initiateESignature: '发起电子签约',
      initiateReceipt: '发起签收',
      matchIncomingData: '匹配入仓数据',
      newClauseLibrary: '新增(条款库)',
      newAccountingField: '新增核算字段',
      newActionCode: '新增动作代码',
      newActionParameterConfiguration: '新增动作参数配置',
      newActionParameterVurationKey: '新增动作参数值配置',
      newCodingRuleDefinition: '新增编码规则定义',
      generateImportDeclaration: '生成进口报关单',
      newCodingVariableDefinition: '新增编码变量定义',
      newCostInvoicing: '新增费用发票开票',
      newCreationSuccess: '新建成功',
      newCustomer: '新增客商',
      newDailyProductionRegistration: '新增日产量登记',
      newDependencyScheme: '新建依赖方案',
      newDevelopmentDictionary: '新增开发字典',
      newDomesticPurchaseReturnForm: '新增国内采购退货单',
      newDomesticSalesReturnForm: '新增国内销售退货单',
      newFolder: '新建文件夹',
      newFormulaConfiguration: '新增公式配置',
      newGroup: '新建分组',
      newImportDeclarationForm: '新增进口报关单',
      newInternationalFreightInvoice: '新增国际运费发票',
      newInTransitOrder: '新增在途单',
      newLegalRelationship: '新增法人关系',
      newListOfSteelMillsReleaseKey: '新增钢厂可直放清单',
      newLossOutOrder: '新增盘亏出仓单',
      newNode: '新建节点',
      newNodeDefinition: '新增节点定义',
      newOverdueAmountForTheMonth: '本月新增逾期金额',
      newPagePurchaseContract: '新增页采购合同',
      newPartInformation: '新增部件信息',
      newPassword: '新密码',
      newPermissionDialogConfiguration: '新增权限对话框配置',
      newPlanTemplate: '新增计划模板',
      newPlantConfiguration: '新增厂区配置',
      newProcess: '新建工序',
      newProcessCategory: '新增工序类别',
      manualPicking: '手动拣配',
      assignment: '指派',
      resolve: '解决',
      exceptionHandling: '异常处理',
      viewDetails: '查看详情',
      change: '变更',
      viewHistory: '查看历史',
      automaticPicking: '自动拣配',
      confirmationOfPicking: '确认拣配',
      newProcessCategoryTag: '新增工艺类别',
      newProcessFile: '新增工艺档案',
      newProcessParameters: '新增工序参数',
      newProcessParametersTag: '新增工艺参数',
      newProductionLineConfiguration: '新增产线配置',
      newProductionTeam: '新增生产小组',
      generatingInventoryAdjustment: '生成存货合同调整单',
      newPurchaseArrivalOrder: '新增采购到货单',
      newPurchaseInvoiceInvoicing: '新增采购发票开票',
      newPurchaseRequest: '新增采购申请',
      newReferenceConfiguration: '新增引用配置',
      newSalesDirectReleaseReturnForm: '新增销售直放退货单',
      newSchemeTemplate: '新增方案模版',
      newSettlementClauseDetails: '新增结算条款明细',
      newSettlementTerms: '新增结算条款',
      newStartNewJourneyHeartService: '新起点 新征程 心服务',
      newTransferOrder: '新增移仓调拨单',
      newWarehouseManagement: '新增仓库管理',
      paymentSelectionTransaction: '付款选择往来',
      selectContractDetails: '选择合同明细',
      selectTransaction: '选择往来'
    },
    columns: {
      appendixSalesContract: '销售合同附件',
      availableQuantity: '可拣配|数量',
      batchNumber: '批次号',
      batchProperties: '批次属性',
      batchProperty: '批次属性',
      buttonList: '按钮列表',
      cabinetType: '柜型',
      cabinetVolume: '柜量',
      carNumber: '车号',
      carNumberKey: '车牌号',
      clauseTemplateNumber: '条款模板编号',
      clauseTitle: '条款标题',
      collectionAdjustmentWorkbench: '收款调整工作台',
      companyCode: '公司代码',
      consumptionTaxCalculationMethod: '消费税计征方法',
      containerNumber: '柜号',
      containerSize: '柜型尺寸',
      containerWeightUnit: '柜重单位',
      contractRecordWarehouse: '合同备案仓库',
      currentPickingQuantity: '本次拣配|数量',
      customsCode: '海关商编',
      dateOfPickup: '提柜日期',
      dateOfProduction: '生产日期',
      deliveryDate: '交付日期',
      deliveryTerminal: '交货码头',
      dialogPermissionList: '对话框权限列表分类列表',
      difference: '差额',
      distributionByPurchase: '统购分销',
      earliestReturnDate: '最早返回日期',
      emptyContainerContainerWeight: '空柜柜重',
      exceptionList: '异常列表',
      exceptionRequestList: '异常请求列表',
      expectedDeliveryDate: '预计交货日期',
      expirationDate: '失效日期',
      expirationDateSt: '到期日',
      expirationDateNd: '过期日期',
      expirationDateRd: '过期时间',
      fixedMarkUp: '固定加价',
      freight: '运费',
      functionList: '功能列表',
      grossWeight: '毛重',
      optionalDictionariesList: '可选字典列表',
      netWeight: '净重',
      paymentSubtype: '付款子类型',
      pickupDate: '取货日期',
      pickupLocation: '提货地点',
      pickUpMethod: '提货方式',
      planDetails: '计划明细',
      premium: '保费',
      productionDate: '生产日期',
      purchaseBySales: '以销定购',
      purchaseToSell: '以购定销',
      quantityBeforeAdjustment: '调整前数量',
      quantityToBeArrived: '待到货数量',
      quantityToBeShipped: '待发货数量',
      receivingPoint: '接收点',
      receivingPointContactNumber: '接收点联系电话',
      sealNumber: '封签号',
      shipmentList: '发货单列表',
      shipmentPointPickupDate: '装运点取货日期',
      classificationList: '分类列表',
      parameterList: '参数列表',
      userDocumentAuthorization: '用户单据授权',
      userList: '用户列表',
      triggerConfigurationList: '触发配置列表',
      tenantUserManagement: '租户用户管理',
      granteeClassification: '被授予方分类',
      grantorClassification: '授予方分类',
      listOfRetailOutboundReceipts: '零售出仓回单列表',
      tariffCalculationMethod: '关税计征方法',
      templateCode: '模板编码',
      templateName: '模板名称',
      tenantList: '租户列表',
      termsAndConditions: '条款内容',
      towTruckDriver: '拖车司机',
      towTruckDriverPhoneNumber: '拖车司机电话',
      towingCompany: '拖车公司',
      variableList: '变量列表',
      vgm: 'VGM',
      volume: '体积',
      warehouseList: '仓库列表',
      whetherSamples: '是否样品',
      whetherToRefund: '是否退款'
    },
    tips: {
      accountingGroupCannotBeEmpty: '核算组不能为空',
      allocationSuccess: '配款成功',
      amountIsEmpty: '金额为空',
      canTDeleteSubTableDinTableKey: '不可单独删除子表数据，请选择主表！',
      canOnlyMultiSelectSotExistKey: '只能多选销售合同号存在的或者销售合同号都不存在的',
      cancelReview: '取消审核',
      cancellationSuccessful: '作废成功',
      cancelledSuccessfully: '取消对应成功',
      cannotBeEmpty: '不能为空',
      cannotSelectDifferentWarehouses: '不能选择不同仓库！',
      ConfirmOrNotToCancel: '是否确认作废?',
      generateSettlementAdjustment: '是否确认生成结算调整单？',
      addNecessaryContentsBasicInformation: '基础信息必要内容不能为空，请补充！',
      operationCopyData: '此操作将复制该数据, 是否继续?',
      preparationStatusCannotBeChecked: '准备状态无法查询',
      currentStatusCannotBeWithdrawn: '当前状态不可撤单',
      pickingIsComplete: '拣配完成！',
      selectTypeOfArrival: '请选择到货类型',
      currentDataIsProcessedNoEditingAllowed: '当前数据已处理，不允许编辑!',
      enterCorrectNumberOfContainers: '请输入正确的柜数',
      enterCorrectLatestDeliveryDate: '请输入正确的最迟交货日期',
      selectDeliveryMethod: '请选择交期方式',
      selectEarliestDeliveryDate: '请选择最早交货期',
      selectLatestDeliveryDate: '请选择最迟交货期',
      selectShortLoadingRate: '请选择短装率',
      selectOverfillRate: '请选择溢装率',
      selectCountryAndRegion: '请选择国别地区',
      selectContractPeriod: '请选择合同有效期',
      selectDocument: '请选择文件',
      selectInvoiceCategory: '请选择发票大类',
      enterCustomerSBankAccountNumber: '请输入客户银行账号',
      enterNumberOfVoyages: '请输入航次',
      selectPortOfLoading: '请选择装货港',
      selectPortOfOrigin: '请选择起运港',
      selectPortOfDischarge: '请选择卸货港',
      selectCarrier: '请选择承运人',
      enterBookingNumber: '请输入订舱单号',
      selectFinalDestination: '请选择最终目的地',
      selectExpectedArrivalDate: '请选择预计到达日期',
      selectExpectedDateOfDeparture: '请选择预计起运日期',
      selectUnitOfShipment: '请选择装运单位',
      enterShipmentQuantity: '请输入装运数量',
      selectArrivalWarehouse: '请选择到货仓库',
      enterLoadingOrderNumber: '请输入装柜单号',
      selectDateOfLoadingRegistration: '请选择装柜登记日期',
      selectDateOfLoading: '请选择装柜日期',
      selectShipOwner: '请选择船东',
      selectTowingCompany: '请选择拖车公司',
      selectDestination: '请选择目的地',
      selectBookingNumber: '请选择订舱单号',
      selectPickUpDate: '请选择提柜日期',
      selectBulkCargoPickingWarehouse: '请选择散货拣配仓库',
      selectAFullContainerWarehouse: '请选择整柜入仓仓库',
      enterLoadingListNumber: '请输入装柜清单号',
      selectPickupMethod: '请选择提货方式',
      whetherAutomaticallyLeaveWarehouse: '请选择是否自动出仓',
      selectSettlementMethod: '请选择结算方式',
      selectPaymentMethod: '请选择支付方式',
      enterPaymentPercentage: '请输入支付比例',
      selectExpectedPaymentDate: '请选择预计支付日期',
      selectDateMethod: '请选择日期方式',
      enterPaymentAmount: '请输入支付金额',
      enterInvoicedQuantity: '请输入开票数量',
      theContainerTypeAndQuantityInformation: '柜型柜量信息不能为空',
      theDetailedInformationOfGoods: '货运商品明细不能为空',
      containerTypeCanNotBeEmpty: '集装箱型不能为空',
      containerQuantityCannotBeEmpty: '柜量不能为空',
      selectCostType: '请选择费用类型',
      selectDateOfCharge: '请选择费用日期',
      enterOriginalCurrencyAmount: '请输入原币金额',
      containerSizeCanNotBeEmpty: '集装箱规格不能为空',
      standardUnitCanNotBeEmpty: '标准单位不能为空',
      numberOfStatisticsCannotBeEmpty: '统计数量不能为空',
      statisticalUnitCannotBeEmpty: '统计单位不能为空',
      costAmountCannotBeEmpty: '费用金额不能为空',
      quantityUsedCannotBeEmpty: '使用数量不能为空',
      warehouseCannotBeEmpty: '仓库不能为空',
      warehouseReconciliation: '仓库核对数不能为空',
      quantityArrivedCannotBeEmpty: '到货数量不能为空',
      dateOfEntryCannotBeEmpty: '入仓日期不能为空',
      loadingDetailsCannotBeEmpty: '装柜明细不能为空',
      bookingNumberCannotBeEmpty: '订舱单号不能为空',
      containerTypeCannotBeEmptyKey: '柜型不能为空',
      inventoryUnitCannotBeEmpty: '库存单位不能为空',
      inventoryQuantityCannotBeEmpty: '库存数量不能为空',
      dateOfLoadingCannotBeEmpty: '装柜日期不能为空',
      pickupDateCanNotBeEmpty: '提货日期不能为空',
      pickBeEarlierThanDateOfLoading: '提柜日期不能早于装柜日期，请重新选择',
      containerNumberCanNotBeEmpty: '柜号不能为空',
      sealNumberCanNotBeEmpty: '封签号不能为空',
      emptyContainerWeightCanNotBeEmpty: '空柜柜重不能为空',
      contractDetailsDeletedSuccessfully: '合同明细删除成功',
      copyOrderPlanSuccess: '复制订货计划成功',
      currentBrowserIsNotSupported: '当前浏览器不支持！',
      currentRecordDoesNotExist: '当前记录不存在!',
      currentStatusCannotBeBackedUp: '当前状态不能备份',
      currentStatusCannotBeDeleted: '当前状态不可删除',
      currentStatusCannotBeSubmitted: '当前状态不可提交',
      currentStatusCannotBeVoided: '当前状态不能作废',
      deleteFrom: '中删除？',
      deletingInvoiceProdssfullyKey: '删除发票商品明细子表成功',
      deletingOriginalCansFailedKey: '删除原画布依赖失败',
      deletionSuccess: '删除成功!',
      disableEditing: '禁止编辑',
      disableSuccess: '禁用成功',
      displayPropertiesDiBeEmptyKey: '显示属性，显示名称，属性类型，是否显示不能为空',
      doYouConfirmTheConvlementKey: '是否确认转为预结算？',
      doesItConfirmTheGenccountKey: '是否确认生成终结算？',
      doesItConfirmTheSetrationKey: '是否确认设置为已离职?',
      doesItInvolveCashBackInterest: '是否涉及现款返息',
      doesItInvolveThePromissoryNote: '是否涉及承兑贴息',
      doesItSaveTheCurrentDetails: '是否保存当前详情？',
      domesticPurchaseInvssfullyKey: '国内采购发票登记单保存成功',
      generateInTransitOrderSuccess: '生成在途单成功',
      generateIncomingAdjssfullyKey: '生成进仓调整单成功!',
      generateInventoryCossfullyKey: '生成存货合同调整单成功',
      generateOutboundAdjssfullyKey: '生成出仓调整单成功!',
      generateSteelFactorssfullyKey: '生成钢厂可直放清单成功',
      isPickingConfirmed: '是否确认拣配?',
      cancelled: '已取消',
      thisOrderCanBeCancelledIsItConfirmed: '该单据可撤单，是否确认',
      generateSuccess: '生成成功',
      generateTransferTrassfullyKey: '生成移仓/调拨单成功',
      generateVersionMainssfullyKey: '生成版本维护单成功',
      initiateReceiptSuccessfully: '发起签收成功',
      InventoryBeblank: '库存方式不能为空',
      isItAListedCompany: '是否上市公司',
      isItAnElectronicContract: '是否电子签约？',
      isItAnESignatureWithdrawal: '是否电子签约撤单？',
      isItConfirmedThatThcelledKey: '是否确认撤销申请？',
      isTheAccountNumberRequired: '是否需要账号',
      currencyCannotBeBlank: '币种不可为空',
      isTheClearingOutPosBeEmptyKey: '是否结算出仓不能为空',
      isTheCurrentProcessOrderSaved: '是否保存当前工艺单？',
      isTheDeletionConfirmed: '是否确认删除？',
      isTheStatusOfTheOrdToSentKey: '是否将发单状态改为已发送?',
      isVariable: '是否变量',
      isVersionMaintenanceConfirmed: '是否确认版本维护？',
      isVoidingConfirmed: '是否确定作废?',
      preparationModificyRejectionKey: '只能删除状态为准备、修改、驳回的数据',
      removeSelectionFrom: '是否将选择项从',
      selectAtLeastOne: '至少选择一个',
      noDetailedData: '对不起，无明细数据，请重新选择!',
      executeVersionModified: '当前动作将对楦头执行版本备份操作，单据将可修改并需重新提交审批申请，是否确定继续操作？',
      performVersionRwquirement: '当前动作将对包装要求执行版本备份操作，单据将可修改并需重新提交审批申请，是否确定继续操作？',
      performVersionFabricBom: '当前动作将对面料BOM执行版本备份操作，单据将可修改并需重新提交审批申请，是否确定继续操作？',
      performVersionModified: '当前动作将对模具执行版本备份操作，单据将可修改并需重新提交审批申请，是否确定继续操作？',
      performVersionOutsole: '当前动作将对大底执行版本备份操作，单据将可修改并需重新提交审批申请，是否确定继续操作？',
      performVersionPackageBom: '当前动作将对包材BOM执行版本备份操作，单据将可修改并需重新提交审批申请，是否确定继续操作？',
      performVersionPreAssembly: '当前动作将对预装箱清单执行版本备份操作，单据将可修改并需重新提交审批申请，是否确定继续操作？',
      performVersionSize: '当前动作将对尺寸表执行版本备份操作，单据将可修改并需重新提交审批申请，是否确定继续操作？',
      performVersionBomMaterials: '当前动作将对辅料BOM执行版本备份操作，单据将可修改并需重新提交审批申请，是否确定继续操作？',
      performVersionPrinting: '当前动作将对印绣花执行版本备份操作，单据将可修改并需重新提交审批申请，是否确定继续操作？',
      performVersionProcess: '当前动作将对工艺要求执行版本备份操作，单据将可修改并需重新提交审批申请，是否确定继续操作？',
      performVersionManagement: '当前动作将对质量管理执行版本备份操作，单据将可修改并需重新提交审批申请，是否确定继续操作？',
      performVersionSample: '当前动作将对样品评语执行版本备份操作，单据将可修改并需重新提交审批申请，是否确定继续操作？',
      performVersionDesign: '当前动作将对设计图执行版本备份操作，单据将可修改并需重新提交审批申请，是否确定继续操作？',
      updatePoColorCode: '当前动作将更新PO色码数据状态以及删除下游BOM单，是否确定继续操作？',
      pickingQuantityLess: '本次拣配数量必须大于0且小于等于剩余数量',
      pickingOrQuantityLess: '本次拣配|数量必须大于0且小于等于可拣配数量',
      plannedQuantityEqual: '本次计划数量需大于等于0且小于等于未完成数量',
      plannedQuantityLess: '计划数量必须小于等于可拣配数量',
      deleteDocumentTip: '此操作将删除该单据, 是否继续?',
      deleteInformation: '此操作将删除该信息, 是否继续?',
      deleteInformationTip: '此操作将删除该信息, 是否继续?',
      deleteRoleAccountTip: '此操作将删除该账号的角色, 是否继续?',
      deleteSelectedTip: '此操作将删除选中数据, 是否继续?',
      thisOperationWillDintinueKey: '此操作将直放销售发货单, 是否继续?',
      thisOperationWillGentinueKey: '此操作将生成到货单, 是否继续?',
      thisOperationWillTrntinueKey: '此操作将出仓/货转此数据, 是否继续?',
      thisPickingQuantityuantityKey: '本次拣配数量应小等于调整数量',
      thisPurchaseQuantityIsNotEnough: '本次采购数量不足!',
      whetherToRevokeTheApplication: '是否撤销申请?',
      whetherToRevoke: '是否撤销？',
      whetherToWaiveModification: '是否放弃修改?',
      youCanOnlyDeleteDateModifyKey: '只能删除状态为准备/修改的数据',
      youCanOnlyDeleteDatOfReadyKey: '只能删除状态为准备的数据',
      youHaveBeenLoggedOunAgainKey: '你已被登出，可以取消继续留在该页面，或者重新登录?',
      youHaveNotSelectedTontractKey: '您当前未选择商品明细，无法回退合同',
      minimumBaseCannotBeEmpty: '保底基数不能为空',
      guaranteedBaseCannotLessBase: '保底基数不能小于计息基数',
      invoicedQuantityBeInvoiced: '本次开票数量不能大于待开票数量',
      theInvoiceAmountCannotBeLessThanTheAmountIncludingTax: '本次开票金额不能小于含税金额',
      theInvoicingAmountMustBeLessThan0: '本次开票金额需小于0',
      canNotSelectDuplicateCustomerLevel: '不能选择重复的客户等级',
      canOnlyDeleteDataWhoseStatusIsNotEffective: '只能删除状态为未生效的数据',
      onlyPreparedModifiedAndRejectedDataCanBeSubmitted: '只能提交准备、修改、驳回的的数据',
      enterTheCodeNameAbbreviationOfTheItemNumber: '输入货号编码/名称/简称',
      withdrawReadyState: '此操作将单据撤回准备状态，是否继续？',
      currentStatusCannotBeCancelled: '当前状态无法取消作废！',
      detailsCannotBeEmpty: '商品明细不能为空！',
      adjustedTailDifferenceSuccessfully: '调整尾差成功',
      yourBrowserDoesNotSNotWorkKey: '您的浏览器不支持socket，消息通知功能将不能使用！'
    }
  },
  components: {
    adaptiveAllColumnWidths: '自适应所有列宽',
    adaptiveCurrentColumnWidth: '自适应当前列宽',
    collapse: '收起',
    copyWithTableHeader: '带表头复制',
    dragHereToSetGrouping: '拖动到此处设置分组',
    expand: '展开',
    format: '格式',
    clearTheSelected: '清空已选',
    grouping: '分组',
    inquiry: '查询',
    noListDataTipsForNow: '列表暂无数据提示',
    pleaseEnter: '请输入',
    pleaseSelect: '请选择',
    reset: '重置',
    saveParameters: '保存参数',
    selectParameters: '选择参数',
    selected: '已选',
    viewMore: '查看更多'
  }
}
