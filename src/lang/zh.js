export default {
  route: {
    menuManager: '菜单管理',
    roleManager: '角色管理',
    userList: '用户管理',
    system: '系统管理',
    contract: '合同管理',
    createOrEdit: '新增/编辑',
    createOrEditWithTab: '带tab新增/编辑',
    searchListDemo: '列表查询页面Demo',
    searchListWithEdit: '带编辑列表查询',
    demo: '案例',
    leftTreeSearch: '左树查询',
    documentInquiry: '单据查询页',
    documentModify: '单据编辑页',
    leftRightDoubleGrid: '左右双Grid',
    topBottomDoubleGrid: '上下双Grid',
    gridGroup: 'grid分组查询',
    business: '业务布局',
    othersDrawer: 'Drawer 抽屉',
    othersInfiniteScroll: 'InfiniteScroll 无限滚动',
    othersBacktop: '回到顶部',
    othersImage: 'Image 图片',
    othersCalendar: 'Calendar 日历',
    othersDivider: '分割线',
    othersTimeline: '时间线',
    othersCollapse: '折叠面板',
    othersCarousel: '走马灯',
    othersCard: '卡片',
    othersPopconfirm: '气泡确认框',
    othersPopover: '弹出框',
    othersTooltip: '文字提示',
    othersDialog: '对话框',
    others: '其他',
    avatar: '头像',
    badge: '标记',
    pagination: '分页',
    tree: '树形控件',
    progress: '进度条',
    agGrid: 'ag-grid表格',
    data: '数据',
    navigation: '导航',
    navMenu: '导航菜单',
    navTabs: '标签页',
    navBreadcrumb: '面包屑',
    pageHeader: '页头',
    navDropdown: '下拉菜单',
    navSteps: '步骤条',
    noticeAlert: '警告',
    noticeLoading: '加载',
    noticeMessage: '消息提示',
    messageBox: '弹框',
    notification: '通知',
    notice: '提示',
    formVerify: '表单验证',
    formLayout: '表单布局',
    baseForm: '基本表单',
    formInput: '输入框',
    inputNumber: '计数器',
    formSelect: '选择器',
    formCascader: '级联选择器',
    fromSwitch: '开关',
    fromSlider: '滑块',
    datePicker: '日期选择器',
    timePicker: '时间选择器',
    dateTimePicker: '日期时间选择器',
    formUpload: '上传',
    formRate: '评分',
    colorPicker: '颜色选择器',
    formTransfer: '穿梭框',
    layout: '布局',
    layoutContainer: '布局容器',
    base: '基础',
    button: '按钮',
    font: '字体',
    fontLink: '文字链接',
    formRadio: '单选框',
    checkBox: '多选框',
    dashboard: '首页',
    introduction: '简述',
    documentation: '文档',
    guide: '引导页',
    permission: '权限测试页',
    rolePermission: '角色权限',
    pagePermission: '页面权限',
    directivePermission: '指令权限',
    icons: '图标',
    components: '组件',
    componentIndex: '介绍',
    tinymce: '富文本编辑器',
    markdown: 'Markdown',
    jsonEditor: 'JSON编辑器',
    dndList: '列表拖拽',
    splitPane: 'Splitpane',
    avatarUpload: '头像上传',
    dropzone: 'Dropzone',
    sticky: 'Sticky',
    countTo: 'CountTo',
    componentMixin: '小组件',
    backToTop: '返回顶部',
    dragDialog: '拖拽 Dialog',
    dragSelect: '拖拽 Select',
    dragKanban: '可拖拽看板',
    charts: '图表',
    keyboardChart: '键盘图表',
    lineChart: '折线图',
    mixChart: '混合图表',
    example: '综合实例',
    nested: '路由嵌套',
    menu1: '菜单1',
    'menu1-1': '菜单1-1',
    'menu1-2': '菜单1-2',
    'menu1-2-1': '菜单1-2-1',
    'menu1-2-2': '菜单1-2-2',
    'menu1-3': '菜单1-3',
    menu2: '菜单2',
    Table: 'Table',
    dynamicTable: '动态Table',
    dragTable: '拖拽Table',
    inlineEditTable: 'Table内编辑',
    complexTable: '综合Table',
    tab: 'Tab',
    form: '表单',
    createArticle: '创建文章',
    editArticle: '编辑文章',
    articleList: '文章列表',
    errorPages: '错误页面',
    page401: '401',
    page404: '404',
    excel: 'Excel',
    exportExcel: '导出 Excel',
    selectExcel: '导出 已选择项',
    mergeHeader: '导出 多级表头',
    uploadExcel: '上传 Excel',
    zip: 'Zip',
    pdf: 'PDF',
    exportZip: 'Export Zip',
    theme: '换肤',
    clipboardDemo: 'Clipboard',
    i18n: '国际化',
    externalLink: '外链',
    table: '表格',
    formTable: '复杂Grid'
  },
  navbar: {
    logOut: '退出登录',
    mdfPassword: '密码修改',
    dashboard: '首页',
    github: '项目地址',
    theme: '换肤',
    size: '布局大小'
  },
  login: {
    title: '账号密码登录',
    logIn: '登录',
    forget: '忘记密码',
    username: '账号',
    password: '密码',
    any: '随便填',
    thirdparty: '第三方登录',
    thirdpartyTips: '本地不能模拟，请结合自己业务进行模拟！！！'
  },
  documentation: {
    documentation: '文档',
    github: 'Github 地址'
  },
  permission: {
    addRole: '新增角色',
    editPermission: '编辑权限',
    roles: '你的权限',
    switchRoles: '切换权限',
    tips: '在某些情况下，不适合使用 v-permission。例如：Element-UI 的 Tab 组件或 el-table-column 以及其它动态渲染 dom 的场景。你只能通过手动设置 v-if 来实现。',
    delete: '删除',
    confirm: '确定',
    cancel: '取消'
  },
  guide: {
    description: '引导页对于一些第一次进入项目的人很有用，你可以简单介绍下项目的功能。本 Demo 是基于',
    button: '打开引导'
  },
  components: {
    documentation: '文档',
    tinymceTips: '富文本是管理后台一个核心的功能，但同时又是一个有很多坑的地方。在选择富文本的过程中我也走了不少的弯路，市面上常见的富文本都基本用过了，最终权衡了一下选择了Tinymce。更详细的富文本比较和介绍见',
    dropzoneTips: '由于我司业务有特殊需求，而且要传七牛 所以没用第三方，选择了自己封装。代码非常的简单，具体代码你可以在这里看到 @/components/Dropzone',
    stickyTips: '当页面滚动到预设的位置会吸附在顶部',
    backToTopTips1: '页面滚动到指定位置会在右下角出现返回顶部按钮',
    backToTopTips2: '可自定义按钮的样式、show/hide、出现的高度、返回的位置 如需文字提示，可在外部使用Element的el-tooltip元素',
    imageUploadTips: '由于我在使用时它只有vue@1版本，而且和mockjs不兼容，所以自己改造了一下，如果大家要使用的话，优先还是使用官方版本。'
  },
  table: {
    dynamicTips1: '固定表头, 按照表头顺序排序',
    dynamicTips2: '不固定表头, 按照点击顺序排序',
    dragTips1: '默认顺序',
    dragTips2: '拖拽后顺序',
    title: '标题',
    importance: '重要性',
    type: '类型',
    remark: '点评',
    search: '搜索',
    add: '添加',
    export: '导出',
    reviewer: '审核人',
    id: '序号',
    date: '时间',
    author: '作者',
    readings: '阅读数',
    status: '状态',
    actions: '操作',
    edit: '编辑',
    publish: '发布',
    draft: '草稿',
    delete: '删除',
    cancel: '取 消',
    confirm: '确 定',
    reset: '重置'
  },
  excel: {
    export: '导出',
    selectedExport: '导出已选择项',
    placeholder: '请输入文件名(默认excel-list)'
  },
  zip: {
    export: '导出',
    placeholder: '请输入文件名(默认file)'
  },
  pdf: {
    tips: '这里使用   window.print() 来实现下载pdf的功能'
  },
  theme: {
    change: '换肤',
    documentation: '换肤文档',
    tips: 'Tips: 它区别于 navbar 上的 theme-pick, 是两种不同的换肤方法，各自有不同的应用场景，具体请参考文档。'
  },
  tagsView: {
    refresh: '刷新',
    close: '关闭',
    closeOthers: '关闭其它',
    closeAll: '关闭所有',
    closeRight: '关闭右侧',
    closeLeft: '关闭左侧'
  },
  settings: {
    title: '系统布局配置',
    theme: '主题色',
    tagsView: '开启 Tags-View',
    fixedHeader: '固定 Header',
    sidebarLogo: '侧边栏 Logo'
  }
}
