import Vue from 'vue'
import VueI18n from 'vue-i18n'
import Cookies from 'js-cookie'
import elementEnLocale from 'element-ui/lib/locale/lang/en' // element-ui lang
import elementZhLocale from 'element-ui/lib/locale/lang/zh-CN'// element-ui lang
import elementEsLocale from 'element-ui/lib/locale/lang/es'// element-ui lang
import esLocale from './es'
import { scriptJoin } from '@/utils/index'

if (!window.enLocale) {
  scriptJoin('enLocale', 'https://front-end-huawei-cdn.devops.cndinfo.com/lang/en.min.js')
  scriptJoin('zhLocale', 'https://front-end-huawei-cdn.devops.cndinfo.com/lang/zh.min.js')
  setTimeout(() => {
    i18n.setLocaleMessage('en', { ...window.enLocale, ...elementEnLocale })
    i18n.setLocaleMessage('zh_CN', { ...window.zhLocale, ...elementZhLocale })
  }, 300)
}

// function tInt(key) {
//   const value = this.$t(key)
//   if (value === key) {
//     scriptJoin('enLocale', 'https://front-end-huawei-cdn.devops.cndinfo.com/lang/en.min.js')
//     scriptJoin('zhLocale', 'https://front-end-huawei-cdn.devops.cndinfo.com/lang/zh.min.js')
//     setTimeout(() => {
//       i18n.setLocaleMessage('en', {...window.enLocale, ...elementEnLocale})
//       i18n.setLocaleMessage('zh', {...window.zhLocale, ...elementZhLocale})
//     }, 150)
//     return value
//   } else {
//     return value
//   }
// }

// Vue.prototype.$tInt = tInt

const zhLocale = window.zhLocale
const enLocale = window.enLocale

const messages = {
  en: {
    ...enLocale,
    ...elementEnLocale
  },
  zh_CN: {
    ...zhLocale,
    ...elementZhLocale
  },
  es: {
    ...esLocale,
    ...elementEsLocale
  }
}
// 默认语言
const i18n = new VueI18n({
  // set locale
  // options: en | zh | es
  locale: Cookies.get('language') || 'zh_CN',
  // set locale messages
  messages
})

export default i18n
