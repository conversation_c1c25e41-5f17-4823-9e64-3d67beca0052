$bg: #fff;
$light_gray: #262626;
$cursor: #262626;
$dark_gray: #889aa4;

@supports (-webkit-mask: none) and (not (cater-color: $cursor)) {
  .login-container .el-input input {
    color: $cursor;
  }
}

.login-container {
  // background-image: url('~@/assets/login/login_bg.png');
  // background-image: url('~@/assets/login/newbg.jpg');
  background-size: cover;
  position: relative;
  .logo-title {
    position: absolute;
    top: 21px;
    left: 25px;
  }
  .logo-title > img {
    width: 280px;
    height: auto;
  }
  .layout-class {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 0 16vw 0 6vw;
    height: 100%;
    .logo-desc img {
      width: 40vw;
      height: 67vh;
    }
  }
  .company {
    font-size: 12px;
    color: #000;
    position: absolute;
    left: 46px;
    bottom: 30px;
    backdrop-filter: blur(10px);
    //text-shadow: 2px 2px 4px #fff;
    background-color: rgba(255, 255, 255, 0.5);
    /*半透明的黑色背景*/
    padding: 5px 10px 10px 10px;
    /*文字周围的间距*/
    border-radius: 8px;
    -webkit-border-radius: 8px;
    -moz-border-radius: 8px;
    -ms-border-radius: 8px;
    -o-border-radius: 8px;
  }
  .el-input {
    display: inline-block;
    height: 40px;
    // width: 85%;

    input {
      background: transparent;
      border: 0px;
      -webkit-appearance: none;
      border-radius: 0px;
      padding: 12px 5px 12px 15px;
      color: $light_gray;
      height: 40px;
      caret-color: $cursor;

      &:-webkit-autofill {
        box-shadow: 0 0 0px 1000px $bg inset !important;
        -webkit-text-fill-color: $cursor !important;
      }
    }
  }

  .el-form-item {
    // width: 320px;
    // height: 40px;
    background: rgba(255, 255, 255, 1);
    border: 1px solid rgba(223, 226, 229, 1);
    border-radius: 2px;
    color: #454545;
    margin-bottom: 20px;
  }
}

.login-container {
  min-width: 900px;
  height: 100%;
  width: 100%;
  background-color: $bg;
  overflow: hidden;

  .login-form {
    //margin-top: 80px;
    position: relative;
    width: 320px;
    height: 320px;
    padding: 50px 40px 40px 40px;
    margin-left: 83px;
    overflow: hidden;
    background: rgba(255, 255, 255, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);

    &.login-form-special {
      background: rgba(0, 0, 0, 0.3);
      border: none;
      box-shadow: 0 4px 6px rgba(255, 255, 255, 0.5);
    }
    .login-forget {
      color: $light_gray;
      margin: 0 0 0 auto;
      display: block;
      width: max-content;
    }
    .forget-pass {
      font-size: 12px;
      color: rgba(62, 141, 220, 1);
      text-align: right;
      margin-bottom: 42px;
    }
  }

  .tips {
    font-size: 14px;
    color: #fff;
    margin-bottom: 10px;

    span {
      &:first-of-type {
        margin-right: 16px;
      }
    }
  }

  .svg-container {
    padding: 6px 5px 6px 15px;
    color: $dark_gray;
    vertical-align: middle;
    width: 30px;
    display: inline-block;
  }

  .title-container {
    position: relative;
    margin-left: -40px;
    margin-bottom: 40px;
    display: flex;
    align-items: center;
    .small-rect {
      display: inline-block;
      width: 8px;
      height: 32px;
      background: rgba(62, 141, 220, 1);
    }
    .title {
      font-size: 26px;
      color: $light_gray;
      margin: 0 0 0 33px;
      font-weight: bold;
    }
    .loadtitle {
      padding-left: 40px;
      height: 40px;
      display: block;
      margin: 0 auto;
    }

    .set-language {
      color: #fff;
      position: absolute;
      top: 3px;
      font-size: 18px;
      right: 0px;
      cursor: pointer;
    }
  }

  .show-pwd {
    position: absolute;
    right: 10px;
    top: 2px;
    font-size: 20px;
    color: $dark_gray;
    cursor: pointer;
    user-select: none;
  }

  .thirdparty-button {
    position: absolute;
    right: 0;
    bottom: 6px;
  }

  @media only screen and (max-width: 470px) {
    .thirdparty-button {
      display: none;
    }
  }
  .form-footer {
    font-size: 11px;
    text-align: center;
    color: #454545;
  }
  .beian {
    line-height: 14px;
  }
  .beian-img {
    vertical-align: top;
    opacity: 1;
    height: 14px;
    width: 14px;
  }
  .tolink {
    cursor: pointer;
    margin-right: 4px;
    margin-left: 4px;
  }
  .tolink:hover {
    color: #3e8ddc;
  }
  .copyright {
    margin-right: 2px;
  }

  .login-form-special {
    .login-forget {
      color: #fff;
    }
    .login-forget:hover {
      color: #3e8ddc;
    }
    .tolink {
      color: #fff;
    }
    .tolink:hover {
      color: #3e8ddc;
    }
    .copyright {
      color: #fff;
    }
  }
}

.ie-tips {
  .el-message-box__header {
    display: none;
  }
  .el-message-box__btns {
    padding-top: 0px;
  }
}
