#nav-container {
  .sidebarLogoFade-enter-active {
    transition: opacity 1.5s;
  }

  .sidebarLogoFade-enter,
  .sidebarLogoFade-leave-to {
    opacity: 0;
  }

  .sidebar-logo-container {
    position: relative;
    width: 100%;
    height: $navbarHeight;
    line-height: $navbarHeight;
    background: #FFF;
    text-align: center;
    overflow: hidden;
    // border-bottom: 1px solid #f3f4f6;

    & .sidebar-logo-link {
      height: 100%;
      width: 100%;
      background-color: #fff;

      & .sidebar-logo-shrink {
        width: 57%;
        height: auto;
        margin-left: 0;
        vertical-align: middle;
      }

      & .sidebar-logo-unfold {
        width: 17%;
        height: auto;
        margin-left: 12px;
        margin-right: 5px;
        vertical-align: middle;
      }

      & .sidebar-title {
        margin: 0 auto;
        width: 80%;
        height: auto;
      }
    }

    &.collapse {
      .sidebar-logo {
        margin-right: 0px;
      }
    }
  }
}
