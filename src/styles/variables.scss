// base color
$blue: #324157;
$light-blue: #3a71a8;
$red: #c03639;
$pink: #e65d6e;
$green: #30b08f;
$tiffany: #4ab7bd;
$yellow: #fec171;
$panGreen: #30b08f;
$tagsColor: #3e8ddc;
$tagsHeight: 24px;
$tagsLineHeight: 24px;
$borderColor: #f3f4f6;

$--border-color-light: #dfe4ed;
$--border-color-lighter: #e6ebf5;

//SvgSize
$SvgSize: 20px;
//navbar 导航栏高度
$navbarHeight: 46px;
//SlidBar 左边菜单
$sideBarWidth: 180px; //侧边菜单宽度
$menuItemHeight: 32px; //菜单item的高度/行高
$menuText: #ffff; //默认字体颜色
$menuActiveText: #5aaafd; // 选中字体颜色
$subMenuActiveText: #3e8ddc; // 子菜单选中后一级菜单的字体颜色 //https://github.com/ElemeFE/element/issues/12951
$menuActive: #3e8ddc;

$menuBg: #135091;
$menuHover: rgba(200, 200, 200, 0.2);

$subMenuBg: #135091;
$subMenuHover: rgba(200, 200, 200, 0.2);

// Appmain
$mainTop: 45px;

// the :export directive is the magic sauce for webpack
// https://www.bluematador.com/blog/how-to-share-variables-between-js-and-sass
:export {
  menuText: $menuText;
  menuActiveText: $menuActiveText;
  subMenuActiveText: $subMenuActiveText;
  menuBg: $menuBg;
  menuHover: $menuHover;
  subMenuBg: $subMenuBg;
  subMenuHover: $subMenuHover;
  sideBarWidth: $sideBarWidth;
  menuActive: $menuActive;
}
