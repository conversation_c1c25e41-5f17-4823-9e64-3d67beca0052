@import './variables.scss';
@import './mixin.scss';
@import './sidebar.scss';
@import './logo.scss';
@import './svg.scss';
@import './toolTip.scss';
@import './dropdownMenu.scss';
@import './password-strong.scss';
@import './login.scss';
@import './collapse-menu.scss';
@import './popover.scss';

html {height: 100%;}

body {
  padding: 0;
  height: 100%;
  margin: initial;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
  font-size: 12px;
  background: #F8F8FA;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

// 白色背景
.bg-white {
  background-color: #ffffff;
}

.el-button--primary {
  background-color: #3E8DDC;
  border-color: #3E8DDC;
}

ul, li {
  list-style: none;
  margin: 0;
  padding: 0;
}

.pd-10 {
  padding-bottom: 10px;
}

.border-bottom {
  border-bottom: 1px solid #dfe4ed;
}

.border-top {
  border-top: 1px solid #dfe4ed;
}
.w100 {
  width: 100%;
}
.p-10 {
  padding: 10px;
}
.text-c {
  text-align: center;
}
