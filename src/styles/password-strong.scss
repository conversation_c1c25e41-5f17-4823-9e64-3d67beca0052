.strong-level {
  height: 28px;
  line-height: 28px;
  span {
    // margin-top: 3px;
    display: inline-block;
    width: 33.333333%;
    height: 100%;
    text-align: center;
    font-size: 12px;
    background-color: rgb(237, 237, 237);
  }
  .bg-red {
    background-color: red;
    color: #ffffff;
  }
  .bg-yellow {
    background-color: orange;
    color: #ffffff;
  }
  .bg-green {
    background-color: green;
    color: #ffffff;
  }
}

// 横向
.flex {
  display: flex;
}
.flexC {
  display: flex;
  align-items: center;
}
.flexCC {
  display: flex;
  align-items: center;
  justify-content: center;
}
.flexCW {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
.flexCA {
  display: flex;
  align-items: center;
  justify-content: space-around;
}
.flexCB {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

// 纵向
.flexV {
  display: flex;
  flex-direction: column;
}
.flexVC {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.flexVCC {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.flexVCL {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: left;
}