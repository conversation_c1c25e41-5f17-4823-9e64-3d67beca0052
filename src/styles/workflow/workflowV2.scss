::v-deep .el-tabs__content {
  height: calc(100% - 40px);
}
.border-none {
  border: none;
}
.is-border-none {
  border: none;
}

::v-deep .el-card__body {
  height: 100%;
}

.is-transparent-tabs-header {
  ::v-deep {
    .el-tabs__header {
      background-color: transparent;
    }
  }
}

.bg-gray {
  background-color: #F8F8FA;
}

.overflow-y-auto {
  overflow-y: auto;
}

.flex-1 {
  flex: 1;
}
.searchInput {
  width: 300px;
  margin: 0px 10px 1px 0px;
}
.flexInputButton {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  padding: 8px;
}
::v-deep .el-tabs__content  {
  overflow: auto;
}
.buttonBox {
  display: flex;
  flex-direction: row-reverse;
  padding: 10px;
}