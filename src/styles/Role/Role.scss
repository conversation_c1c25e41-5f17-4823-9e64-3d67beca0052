::v-deep .el-tabs__content {
  height: calc(100% - 40px);
}
.application-container {
  width: 100%;
  height: 100%;
  .layout-content {
    padding: 0 !important;
  }
}
.right-tab2-container {
  width: 100%;
  height: 100%;
}
.right-tab3-bottom-container {
  width: 100%;
  .flexButton {
    display: flex;
    width: 386px;
  }
  .searchBox {
    width: 200px;
    margin-right: 10px;
  }
}
.right-tab3-top-container {
  width: 100%;
  height: 100%;
  border-bottom: 1px solid rgb(240, 240, 240);
  .flexButton {
    display: flex;
    width: 386px;
  }
  .searchBox {
    width: 200px;
    margin-right: 10px;
  }
}
.position-absolute {
  z-index: 0 !important;
}
.grid-flex {
  height: 135px;
  flex: auto;
}
.docfield-config-dialog {
  height: calc(100vh - 50px);
  overflow: auto;
  margin-top: 10px;
  margin-bottom: 10px;
}
.hp100 {
  height: 100%;
}
.mr-top10 {
  margin-top: 10px;
}
.mr-bottom10 {
  margin-bottom: 10px;
}
.rolebill-grid-dialog {
  height: 300px;
  overflow: auto;
  border-radius: 2px;
  margin-top: 10px;
  margin-bottom: 10px;
}
.role-grid-dialog {
  height: 300px;
  overflow: auto;
  border-radius: 2px;
  margin-top: 10px;
  margin-bottom: 10px;
}
.user-doc-grid {
  height: 300px;
  flex: auto;
}
.user-list-grid {
  height: 200px;
  flex: auto;
}
.role-left-content {
  padding: 5px;
  height: 41px;
}
.role-left-content-input {
  width: 49%;
}
.wp100 {
  width: 100%;
}
