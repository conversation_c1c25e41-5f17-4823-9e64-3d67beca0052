// @import '../../node_modules/@ag-grid-enterprise/all-modules/dist/styles/ag-grid.scss';

// @import '../../node_modules/@ag-grid-enterprise/all-modules/dist/styles/ag-theme-balham/sass/legacy/ag-theme-balham-v22-compat';
// Set the colors to blue and amber
$ag-active: #E91E63; // pink-500 from https://www.materialui.co/colors
// Import the ag-Grid blue theme

@import 'https://front-end-huawei-cdn.devops.cndinfo.com/npm/@ag-grid-community/all-modules@25.3.0/dist/styles/ag-theme-balham.min.css';
@import 'https://front-end-huawei-cdn.devops.cndinfo.com/npm/@ag-grid-community/all-modules@25.3.0/dist/styles/ag-grid.min.css';

.grid-cell-centered {
  text-align: center;
  justify-content: center;
}

/* Agrid */
.ag-row-hover {
  /* putting in !important so it overrides the theme's styling as it hovers the row also */
  // background-color: rgba(62, 141, 220, 0.35) !important;
  background-color: none;
}
.ag-column-hover {
  // background-color: rgba(153, 204, 102, 0.32);
  background-color: none;
}

.ag-body-horizontal-scroll-viewport {
  overflow-x: auto !important;
}
