#nav-container {
  height: 100%;
  .menu-limit {
    overflow: hidden;
    text-overflow: ellipsis;
    -o-text-overflow: ellipsis;
    white-space: nowrap;
    width: 100px;
    display: inline-block;
  }
  .menu-icon-level-0 {
    width: 110px;
  }
  .menu-level-0 {
    width: 132px;
  }
  .menu-icon-level-1 {
    width: 98px;
  }
  .menu-level-1 {
    width: 120px;
  }
  .menu-icon-level-2 {
    width: 86px;
  }
  .menu-level-2 {
    width: 108px;
  }
  .menu-icon-level-3 {
    width: 74px;
  }
  .menu-level-3 {
    width: 96px;
  }
  .menu-icon-level-4 {
    width: 62px;
  }
  .menu-level-4 {
    width: 84px;
  }
  .menu-icon-level-4 {
    width: 50px;
  }
  .menu-level-4 {
    width: 72px;
  }
  .menu-icon-level-5 {
    width: 38px;
  }
  .menu-level-5 {
    width: 60px;
  }
  // 主体区域 Main container
  .main-container {
    min-height: 100%;
    transition: margin-left 0.28s;
    margin-left: $sideBarWidth;
    position: relative;
  }

  // 侧边栏 Sidebar container
  .sidebar-container {
    transition: width 0.28s;
    width: $sideBarWidth !important;
    height: 100%;
    position: fixed;
    font-size: 0px;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 2;
    background-color: rgb(19, 80, 145);
    // overflow: hidden;
    //reset element-ui css
    .horizontal-collapse-transition {
      transition: 0s width ease-in-out, 0s padding-left ease-in-out,
        0s padding-right ease-in-out;
    }

    .scrollbar-wrapper {
      overflow-x: hidden !important;

      .el-scrollbar__view {
        height: 100%;
      }
    }

    .el-scrollbar__bar.is-vertical {
      right: 0px;
    }

    .el-scrollbar {
      height: 100%;
    }

    &.has-logo {
      .el-scrollbar {
        height: calc(100% - 47px);
      }
    }

    .is-horizontal {
      display: none;
    }

    a {
      display: inline-block;
      width: 100%;
      overflow: hidden;
    }

    .cnd-icon {
      margin-right: 10px;
    }

    .el-menu {
      border: none;
      height: 100%;
      width: 100% !important;
      // border-right: 1px solid $borderColor;
      > .menu-wrapper:first-child {
        //border-top: 1px solid $borderColor;
        // border-bottom: 1px solid $borderColor;
      }
      .el-menu-item,
      .el-submenu__title {
        height: $menuItemHeight;
        line-height: $menuItemHeight;
        font-size: 12px;
        color: #fff;
      }
      .el-submenu__icon-arrow {
        right: 10px;
        color: #fff;
      }
      .menu-wrapper {
        .customize-submenu-1 > .el-submenu__title {
          padding-left: 32px !important;
        }
        .customize-submenu-2 > .el-submenu__title {
          padding-left: 44px !important;
        }
        .customize-submenu-3 > .el-submenu__title {
          padding-left: 56px !important;
        }
        .customize-submenu-4 > .el-submenu__title {
          padding-left: 68px !important;
        }
        .customize-submenu-5 > .el-submenu__title {
          padding-left: 80px !important;
        }
        .customize-submenu-6 > .el-submenu__title {
          padding-left: 92px !important;
        }
      }
    }
    .el-menu-item.is-active {
      border-left: 3px solid #e7110f;
      // background: #10457E !important;
    }

    // menu hover
    .submenu-title-noDropdown,
    .el-submenu__title {
      &:hover {
        background-color: $menuHover !important;
      }
    }
    .is-opened.customize-submenu-0 > .el-submenu__title {
      // color: $subMenuActiveText !important;
      color: #fff !important;
      background-color: #3e8ddc !important;
    }
    // .is-opened .el-menu--inline .router-link-active {
    //   border-left: 2px solid #E7110F;
    //   color: $subMenuActiveText !important;
    //   background-color: #10457E !important;
    // }
    // .is-opened .el-menu--inline .router-link-active .menu-limit {
    //   color: #65B2FF !important;
    // }

    // & .nest-menu .el-submenu>.el-submenu__title,
    // & .el-submenu .el-menu-item {
    //   // padding-left: 50px !important;
    //   min-width: $sideBarWidth !important;
    //   background-color: $subMenuBg !important;
    //   &:hover {
    //     background-color: $subMenuHover !important;
    //   }
    // }
  }

  .hideSidebar {
    .sidebar-container {
      width: 54px !important;
    }

    .main-container {
      margin-left: 54px;
    }

    .cnd-icon {
      margin-right: 0px;
    }

    .submenu-title-noDropdown {
      padding: 0 !important;
      position: relative;

      .el-tooltip {
        padding: 0 !important;

        .cnd-icon {
          margin-left: 20px;
        }
      }
    }

    .el-submenu {
      overflow: hidden;

      & > .el-submenu__title {
        padding: 0 !important;

        .cnd-icon {
          margin-left: 20px;
        }

        .el-submenu__icon-arrow {
          display: none;
        }
      }
    }

    .el-menu--collapse {
      .el-submenu {
        & > .el-submenu__title {
          & > span {
            height: 0;
            width: 0;
            overflow: hidden;
            visibility: hidden;
            display: inline-block;
          }
        }
      }
    }
  }

  .el-menu--collapse .el-menu .el-submenu {
    min-width: $sideBarWidth !important;
  }

  // 适配移动端, Mobile responsive
  .mobile {
    .main-container {
      margin-left: 0px;
    }

    .sidebar-container {
      transition: transform 0.28s;
      width: $sideBarWidth !important;
    }

    &.hideSidebar {
      .sidebar-container {
        pointer-events: none;
        transition-duration: 0.3s;
        transform: translate3d(-$sideBarWidth, 0, 0);
      }
    }
  }

  .withoutAnimation {
    .main-container,
    .sidebar-container {
      transition: none;
    }
  }
}

// when menu collapsed
.el-menu--vertical {
  & > .el-menu {
    .cnd-icon {
      margin-right: 16px;
    }
  }

  .nest-menu .el-submenu > .el-submenu__title,
  .el-menu-item {
    &:hover {
      // you can use $subMenuHover
      background-color: $menuHover !important;
    }
  }

  // the scroll bar appears when the subMenu is too long
  > .el-menu--popup {
    max-height: 100vh;
    overflow-y: auto;
    &::-webkit-scrollbar-track-piece {
      background: #d3dce6;
    }

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background: #99a9bf;
      border-radius: 20px;
    }
  }
}
