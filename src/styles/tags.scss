@import './variables.scss';

.tags-view-container {
  overflow: hidden;
  height: 41px;
  width: max-content;
  background: #fff;
  .tags-view-wrapper {
    display: flex;
    align-items: center;
    height: 100%;
    >a{
      display: flex;
      overflow: hidden;
      // display: inline-block;
      border-radius: 2px;
      border: 1px solid #9CA7B0;
      margin-right: 5px;
      height: 24px;
      position: relative;
      &.active {
        color: $tagsColor;
        border-color: $tagsColor;
        .el-icon-close {
          color:$tagsColor;
        }
        .el-popover__reference{
          color: $tagsColor;
        }
      }
      .tags-view-item {
        padding: 0;
        color: #8C8C8C;
        border: none;
        border-radius:0 ;
        font-size: 12px;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        display: inline-block;
        position: relative;
        cursor: pointer;
        height: $tagsHeight;
        line-height: $tagsLineHeight;
        background: #fff;
        font-size: 12px;
        width: 100%;
        height: 100%;
        &:hover{
          background: #fff;
        }
        &:focus{
          background: #fff;
        }
      }
      .close-icon{
        position: absolute;
        right: 0;
        top: 0;
        bottom: 0;
        margin: auto;
        width: 14px;
        background: #fff;
      }
      .el-icon-close {
        background: #fff;
        height: 100%;
        color: #8C8C8C;
      }
    }
  }
  .contextmenu {
    margin: 0;
    background: #fff;
    z-index: 100;
    position: absolute;
    list-style-type: none;
    padding: 5px 0;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 400;
    color: #333;
    box-shadow: 2px 2px 3px 0 rgba(0, 0, 0, .3);
    li {
      margin: 0;
      padding: 7px 16px;
      cursor: pointer;
      &:hover {
        background: #eee;
      }
    }
  }
}