import router from './router'
import otherRoutes from './router/detail'
import store from './store'
import NProgress from 'nprogress' // progress bar
import 'nprogress/nprogress.css' // progress bar style
import { getMenuList } from '@/api/menu'
import { filterAsyncRouter } from '@/store/modules/permission'
import { getSessionId, getToken } from 'cnd-horizon-utils/src/utils/auth'
import actions from '@/shared/action'
import { MessageUtil } from 'cnd-horizon-utils'
// import { configSubApp } from './subapp.config' // 子应用配置
// import { loadMicroApp, start } from 'qiankun'

NProgress.configure({ showSpinner: false }) // NProgress Configuration

const whiteList = ['/login', '/forget', '/share'] // 重定向白名单
const authWhiteList = ['/404', '/401', '/navbar', '/share'] // 校验白名单
let timeInterval = null

router.beforeEach(async(to, from, next) => {
  console.log('门户-to', to)
  console.log('门户-from', from)
  document.title = to.query.name || to.meta.title || '首页'
  localStorage.setItem('menuId', to.meta.id || to.query.activeId || '') // 存储当前访问菜单id

  NProgress.start()
  let hasToken = ''
  if (store.getters.authenticationMethod === 'token') {
    hasToken = getToken()
  } else {
    hasToken = getSessionId()
  }
  console.log('href')
  if (to.path === '/login') {
    if (to.query.ticket || to.query.code) {
      await store.dispatch('user/login', { code: to.query.ticket || to.query.code, type: 'mysso' }).then(res => {
        if (res.token) {
          next('/')
        }
      }).catch(res => {
      })
      NProgress.done()
    } else if (to.query.client_id) {
      const href = `https://login.cndpassport.com/oauth2.0/authorize?client_id=${to.query.client_id}&redirect_uri=${encodeURIComponent(`${window.location.protocol}//${window.location.host}/login?client_id=${to.query.client_id}`)}&response_type=code`
      console.log('href', href)
      window.location.href = href
    }
  }
  if (hasToken) {
    if (sessionStorage.getItem('navbar') && (to.path === '/' || to.path === '/navbar')) {
      next({ path: sessionStorage.getItem('navbar') })
      NProgress.done()
    } else if (to.path === '/login') {
      next({ path: '/' })
      NProgress.done()
    } else {
      const visitedViews = JSON.parse(sessionStorage.getItem('visitedViews'))
      let openShow = true
      visitedViews &&
        visitedViews.filter(item => {
          if (item.fullPath === to.fullPath) {
            openShow = false
          }
        })
      if (visitedViews && openShow && visitedViews.length > 14) {
        MessageUtil.warning('最多可以打开15个页面，请关闭多余页面!')
        from.path === '/' && next({ path: '/navbar' })
        NProgress.done()
        next(false)
        return false
      }
      // 判断是否有用户id
      const hasUserId = store.getters.userId
      console.log('门户-用户', hasUserId)
      // const permission_routes = store.getters.permission_routes
      const flagLoadMenu = store.getters.flagLoadMenu
      // console.log('hasUserId--', hasUserId)
      console.log('门户-flagLoadMenu', flagLoadMenu)
      if (hasUserId) {
        // console.log('有用户id')
        // console.log('store--', store)
        // 按钮权限
        if (flagLoadMenu) {
          // 白名单菜单不校验
          if (authWhiteList.indexOf(to.path) === -1) {
            if (to.path.indexOf('/redirect') === -1) {
              // 判断当前页面是否授权
              if (to.meta && (to.meta.id || to.meta.key)) {
                if (authWhiteList.indexOf(to.path) === -1) {
                  // store.dispatch('menu/asyncLogMenu', to.meta.title)
                  await store.dispatch('menu/asyncGetBtnList', to.meta.id ? to.meta.id : to.query.activeId).then(authenticationList => {
                    actions.setGlobalState({
                      authenticationList: authenticationList
                    })
                  })
                }
                judgeAuth(next, to)
              } else {
                console.log(to, '无id')
                next({ path: '/404' })
              }
            } else {
              next()
            }
          } else {
            next()
          }
        } else {
          // console.log('菜单未下载')
          loadMenus(next, to, hasUserId)
        }
      } else {
        console.log('无用户id')
        try {
          // 获取用户信息
          await store.dispatch('user/getInfo')
          next({ ...to, replace: true })
        } catch (error) {
          // 移除token/session-cookie重新登录
          await store.dispatch('user/resetToken')
          // next(`/login?redirect=${to.fullPath}`)
          next({ path: '/login', query: { redirect: to.fullPath }})
          NProgress.done()
        }
      }
    }
  } else {
    if (whiteList.indexOf(to.path) !== -1) {
      next()
    } else {
      sessionStorage.removeItem('navbar')
      next({ path: '/login', query: { redirect: to.fullPath }})
      NProgress.done()
    }
  }
  // 触发resize事件
  const myEvent = new CustomEvent('menuResize', {
    detail: {
      isOpened: store.getters.sidebar.opened,
      visitedViews: store.getters.visitedViews
    },
    bubbles: true,
    cancelable: false
  })
  window.dispatchEvent(myEvent)
})

// 判断当前跳转路由是否授权
const judgeAuth = (next, to) => {
  timeInterval = setTimeout(() => {
    const hrefList = location.href.split('/')
    const newHref = `${hrefList[0]}//${hrefList[2]}${to.fullPath}`
    const reg = RegExp('redirect')
    if (reg.test(location.href)) {
      next({ path: to.fullPath })
    } else {
      location.replace(newHref) // 避免qiankun报错导致卡顿问题
    }
  }, 3000)
  let flagAuth = false
  const unformattedMenu = store.getters.unformattedMenu
  flagAuth = to.meta.id ? matchRoute(unformattedMenu, to.meta.id) : !!to.meta.key
  setTimeout(() => {
    if (flagAuth) {
      next()
    } else {
      console.log('无匹配菜单')
      next({ path: '/404' })
    }
  }, 10)
}
// 遍历是否存在匹配id
const matchRoute = (arr, matchId) => {
  for (const item of arr) {
    if (item.id === matchId) {
      return true
    }
    if (item.children && item.children.length) {
      const temp = matchRoute(item.children, matchId)
      if (temp) {
        return true
      }
    }
  }
}

// 获取用户授权菜单
export const loadMenus = (next, to, userId) => {
  getMenuList({
    userId: userId,
    applicationId: 'c91d2e57-ad84-4451-9340-8aecfc56fd8c'
  }).then((response) => {
    const { data } = response
    // configSubApp.forEach(item => {
    //   !store.state.app.microSystem[item.name] && loadApp(item.name)
    // })
    store.dispatch('menu/asyncSetUnformattedMenu', data)
    const temp_routes = formatMenu(data)
    const asyncRouter = filterAsyncRouter(temp_routes).concat(otherRoutes)

    console.log('asyncRouter------', asyncRouter)

    store.dispatch('permission/GenerateRoutes', asyncRouter).then(() => {
      // 存储路由
      router.addRoutes(asyncRouter) // 动态添加可访问路由表

      console.log('router-----', router)
      next({ ...to })
      // next({ ...to, replace: true })
    })
  }).catch((error) => {
    console.log('error--', error)
  })
}

// 规范菜单数据格式
const formatMenu = (arr) => {
  // console.log('格式化前菜单--', arr)
  const temp_menu = []
  arr.forEach(item => {
    const obj = {
      path: item.url,
      name: item.url,
      meta: {
        titleT: item.url === sessionStorage.getItem('navbar') ? '首页' : item.title,
        title: item.title,
        icon: item.icon,
        orderNo: item.orderNo,
        id: item.id,
        activeId: item.id,
        categoryId: item.categoryId,
        parentId: item.parentId,
        affix: item.url === sessionStorage.getItem('navbar')
      }
    }
    if (item.children && item.children.length) {
      obj.children = formatMenu(item.children)
    }
    if (sessionStorage.getItem('navbar') === obj.path && obj.fullPath) {
      store.dispatch('tagsView/addView', obj)
    }

    temp_menu.push(obj)
  })
  // console.log('格式化后菜单--', temp_menu)
  return temp_menu
}

// const loadApp = (appName) => {
//   console.log('加载了应用--', appName)
//   const appInfo = configSubApp.filter(item => {
//     return item.name === appName
//   })
//   console.log('应用信息--', appInfo)
//   if (appInfo && appInfo.length > 0) {
//     setTimeout(() => {
//       const tempObj = {}
//       tempObj[appName] = loadMicroApp(appInfo[0], { excludeAssetFilter: false })
//       store.dispatch('app/setSystemMicro', tempObj)
//       Promise.all([store.state.app.microSystem[appName].mountPromise]).then(data => {
//       }).catch(err => {
//         console.log('err', err)
//         // MessageUtil.error('加载失败，请重试！', err)
//       })
//     }, 10)
//   }
// }

router.afterEach(() => {
  // console.log('路由结束')
  clearInterval(timeInterval)
  // finish progress bar
  NProgress.done()
})

// start()
