import Es6Promise from 'es6-promise'
require('es6-promise').polyfill()
Es6Promise.polyfill()
import 'whatwg-fetch'
import 'custom-event-polyfill'
import 'core-js/stable/promise'
import 'core-js/stable/symbol'
import 'core-js/stable/string/starts-with'
import 'core-js/web/url'

import { getToken } from 'cnd-horizon-utils/src/utils/auth'
import Vue from 'vue'
import App from './App.vue'
// import router from './router'
import VueRouter from 'vue-router'
import { routes } from './router'
import store from './store'
import './components/index.js'
import './public-path'
// 指令导入及注册
import directives from './components/directives'
Object.keys(directives).forEach(key => {
  Vue.directive(key, directives[key])
})
// 主子应用通信
import actions from './shared/action'

// 国际化
import i18n from './lang' // Internationalization
// 引入项目相关组件包
import cndHUI from 'cnd-horizon-ui'
Vue.use(cndHUI)
// 引入图标库
import CndIcon from 'cnd-icon'
Vue.use(CndIcon)
// 引入项目全局样式
import './styles/index.scss' // global css
// import element from './element'
// Vue.use(element)
// ag-grid
import { LicenseManager } from '@ag-grid-enterprise/core'
LicenseManager.setLicenseKey(
  'Using_this_{AG_Grid}_Enterprise_key_{AG-051956}_in_excess_of_the_licence_granted_is_not_permitted___Please_report_misuse_to_legal@ag-grid.com___For_help_with_changing_this_key_please_contact_info@ag-grid.com___{Xiamen_C_&_D_Corporation_Ltd.}_is_granted_a_{Multiple_Applications}_Developer_License_for_{1}_Front-End_JavaScript_developer___All_Front-End_JavaScript_developers_need_to_be_licensed_in_addition_to_the_ones_working_with_{AG_Grid}_Enterprise___This_key_has_not_been_granted_a_Deployment_License_Add-on___This_key_works_with_{AG_Grid}_Enterprise_versions_released_before_{8_February_2025}____[v3]_[01]_MTczODk3MjgwMDAwMA==59c26be1ffd05aace4c89191a16db6c0'
)

const originalPush = VueRouter.prototype.push
VueRouter.prototype.push = function push(location) {
  return originalPush.call(this, location).catch((err) => err)
}

Vue.config.silent = true

Vue.config.productionTip = false
let router = null
let instance = null
let timeout = null // 防抖计时器
let visitedViews = [] // tab页数组

// console.log('VueRouter', VueRouter)
Vue.use(VueRouter)

function toUp(str) {
  // 下划线字母转驼峰
  let newStr = ''
  const arr = str.split('_')
  arr.forEach((item, index) => {
    if (index > 0) {
      newStr += item.replace(item[0], item[0].toUpperCase())
      return newStr
    } else {
      newStr += item
      return newStr
    }
  })
  return newStr
}

function render(props = {}, el = '#app-egl') {
  if (props) {
    // 注入 actions 实例
    actions.setActions(props)
  }
  router = new VueRouter({
    base: window.__POWERED_BY_QIANKUN__ ? '/egl/' : '/',
    mode: 'history',
    routes,
    scrollBehavior: (to, from, savedPosition) => {
      // 监听菜单展开/收起事件
      window.addEventListener('menuResize', debounce(ev => {
        visitedViews = ev.detail.visitedViews
        // console.log('ev--', ev)
        // 监听刷新默认缓存当前组件
        store.commit('resetcachePageName', '')
        visitedViews?.length > 0 && visitedViews.forEach((item, index) => {
          if (index !== 0) {
            let cachePage = item.path.split('/')[2]
            cachePage = toUp(cachePage.replace(cachePage[0], cachePage[0].toUpperCase()))
            // 把tab触发的页面加入到要缓存的组件中
            store.commit('addcachePageName', cachePage)
          }
        })
      }, 10), true)
      setTimeout(() => {
        // 把tab触发的页面加入到要缓存的组件中
        store.commit('addcachePageName', to.name)
      }, 10)
    }
  })
  router.beforeEach((to, from, next) => {
    // console.log('钢贸-to', to)
    // console.log('钢贸-from', from)
    // 监听qinakun全局变量
    actions.onGlobalStateChange((state, prevState) => {
      // console.log('state', state)
      // state: 变更后的状态; prevState: 变更前的状态
      // console.log('子应用观察者：改变前的值为 ', prevState)
      // console.log('子应用观察者：登录状态发生改变，改变后的的值为 ', state)
      store.dispatch('setBtnAuth', state.authenticationList)
      // 小铃铛展示类型
      store.dispatch('bellType', state.bellType)
    }, true)
    const reg = RegExp(to.path.split('/')[1])
    const redirect = new RegExp(redirect)
    const pathS = actions.actions.subApp ? reg.test(actions.actions.subApp.join(',')) : true
    if (Object.keys(to.query).length > 0 && !to.query.random && !pathS && getToken()) {
      next({ path: encodeURI(to.path), query: { ...to.query, rePage: to.query.fromPath ? to.query.fromPath : '/egl' + from.path, random: Math.random() }})
    } else {
      next()
    }
  })

  instance = new Vue({
    el: el,
    router,
    store,
    i18n,
    render: (h) => h(App)
  })
  actions.onGlobalStateChange?.((state, prev) => {
    instance.$i18n.locale = state.locale
  }, true)
}

function tabDelete(rePage = null) {
  actions.setGlobalState({
    deleteCurrent: true,
    rePage: rePage || this.$route.query.rePage
  })
}
Vue.prototype.$tabDelete = tabDelete
// 非主应用访问情况
if (!window.__POWERED_BY_QIANKUN__) {
  if (process.env.NODE_ENV === 'development') {
    render(false)
  }
}

// 防抖debounce代码：
function debounce(fn, delay) {
  timeout = null // 创建一个标记用来存放定时器的返回值
  return function(e) {
    // 每当用户输入的时候把前一个 setTimeout clear 掉
    timeout && clearTimeout(timeout)
    // 然后又创建一个新的 setTimeout, 这样就能保证interval 间隔内如果时间持续触发，就不会执行 fn 函数
    timeout = setTimeout(() => {
      fn.apply(this, arguments)
    }, delay)
  }
}

export async function bootstrap() {
  // console.log('vue app bootstraped');
}
export async function mount(props) {
  // console.log('props--', props)
  Vue.prototype.$parentRouter = props.parentRouter
  render(props)
}

Vue.mixin({
  beforeRouteLeave: function(to, from, next) {
    actions.onGlobalStateChange((state, prev) => {
      if (state.refreshTags) {
        actions.setGlobalState({ refreshTags: false })
        if (this.$vnode && this.$vnode.data.keepAlive) {
          if (this.$vnode.parent && this.$vnode.parent.componentInstance && this.$vnode.parent.componentInstance.cache) {
            if (this.$vnode.componentOptions) {
              const key = this.$vnode.key == null
                ? this.$vnode.componentOptions.Ctor.cid + (this.$vnode.componentOptions.tag ? `::${this.$vnode.componentOptions.tag}` : '')
                : this.$vnode.key
              const cache = this.$vnode.parent.componentInstance.cache
              const keys = this.$vnode.parent.componentInstance.keys
              if (cache[key]) {
                if (keys.length) {
                  const index = keys.indexOf(key)
                  if (index > -1) {
                    keys.splice(index, 1)
                  }
                }
                delete cache[key]
              }
            }
          }
        }
        this.$destroy()
      }
      next()
      return
    }, true)
    next()
  }
})

export async function unmount() {
  instance.$destroy()
  instance = null
  router = null
}

// 增加 update 钩子以便主应用手动更新微应用
export async function update() {
  // console.log('更新钩子')

}

// 重写4舍5入
Vue.prototype.$toFixed = function(num, decimal = 2) {
  var numbers = ''
  // 保留几位小数后面添加几个0
  for (var i = 0; i < decimal; i++) {
    numbers += '0'
  }
  var s = 1 + numbers
  // 如果是整数需要添加后面的0
  var spot = '.' + numbers
  // Math.round四舍五入
  //  parseFloat() 函数可解析一个字符串，并返回一个浮点数。
  var value = Math.round(parseFloat(num) * s) / s
  // 从小数点后面进行分割
  var d = value.toString().split('.')
  if (d.length === 1) {
    value = value.toString() + spot
    return value
  }
  if (d.length > 1) {
    if (d[1].length < 2) {
      value = value.toString() + '0'
    }
    return value
  }
}
