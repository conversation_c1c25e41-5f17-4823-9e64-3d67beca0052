import Es6Promise from 'es6-promise'
require('es6-promise').polyfill()
Es6Promise.polyfill()
import 'whatwg-fetch'
import 'custom-event-polyfill'
import 'core-js/stable/promise'
import 'core-js/stable/symbol'
import 'core-js/stable/string/starts-with'
import 'core-js/web/url'

import Vue from 'vue'
import App from './App.vue'
import router from './router'
// import Cookies from 'js-cookie'
import store from './store'
import { defineReadOnly } from './utils/index'
import 'normalize.css/normalize.css' // A modern alternative to CSS resets
import './permission'
// import { scriptJoin } from './utils/index'
Vue.config.productionTip = false

// 国际化
import i18n from './lang' // Internationalization
// // 引入ElementUI
// import element from './element'
// // 使用ElementUI
// Vue.use(element, {
//   size: Cookies.get('size') || 'mini', // set element-ui default size
//   i18n: (key, value) => i18n.t(key, value)
// })

// 引入图标库
import CndIcon from 'cnd-icon'
// 使用图标库
Vue.use(CndIcon)

import FixSelectDropdown from './utils/fixSelectDropdown'
Vue.use(FixSelectDropdown)
// import CndinfoHorizonUtils from 'cnd-horizon-utils'
// Vue.use(CndinfoHorizonUtils)

// global css
import '@/styles/index.scss'

// 微应用加载逻辑(手动加载)
import './qiankun'

// if ((navigator.language || navigator.browserLanguage).toLowerCase().includes('zh-cn')) {
//   Cookies.set('language', 'zh', 60 * 60 * 24 * 30)
//   i18n.locale = Cookies.get('language')
// } else {
//   Cookies.set('language', 'en', 60 * 60 * 24 * 30)
//   i18n.locale = Cookies.get('language')
// }

// import Vconsole from 'vconsole'
// const vConsole = new Vconsole()
// Vue.use(vConsole)
window.configs = {}

const configs = {
  'VUE_APP_AUTO_QUERY': false,
  'VUE_APP_SEARCH_COLLAPSE': true,
  'VUE_APP_SEARCH_ROWS': 2
}

Object.entries(configs).forEach(([key, value]) => {
  defineReadOnly(window.configs, key, value)
})
new Vue({
  i18n,
  router,
  store,
  render: (h) => h(App)
}).$mount('#app')

