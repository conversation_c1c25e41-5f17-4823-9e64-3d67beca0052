<!--
 * @Author: 沈鹭荣
 * @Date: 2021-01-26 15:44:46
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2021-02-06 12:04:13
 * @Description:
-->
<template>
  <div id="app-egl">
    <transition>
      <keep-alive :include="cachePageName">
        <router-view v-if="$route.meta.keepAlive" :key="$route.path + getRouteKey($route)" />
      </keep-alive>
    </transition>
    <transition>
      <router-view v-if="!$route.meta.keepAlive" :key="$route.path + getRouteKey($route)" />
    </transition>
  </div>
</template>
<script>
export default {
  name: 'App',
  data() {
    return {}
  },
  computed: {
    cachePageName() {
      return this.$store.state.cachePageName
    }
  },
  created() {
    // console.log('this.$route--', this.$route)
    // 把tab触发的页面加入到要缓存的组件中
    this.$store.commit('addcachePageName', this.$route.name)
    console.log('App cachePageName--', this.$store.state.cachePageName)
    // 监听页面刷新
    window.addEventListener('beforeunload', () => {
      // 监听刷新默认缓存当前组件
      this.$store.commit('resetcachePageName', this.$route.name)
    })
  },
  methods: {
    getRouteKey(route) {
      // console.log('this.$vnode', this.$vnode)
      if (route.query && route.query.random) {
        const key = route.query.random
        if (typeof (key) === 'function') {
          return key(route)
        }
        return key
      } else {
        return ''
      }
    }
  }
}
</script>

<style>
.test-list > div {
  display: inline-block;
  padding: 5px 15px;
  background: #ccc;
  border: 1px solid #ddd;
  cursor: pointer;
}
</style>
