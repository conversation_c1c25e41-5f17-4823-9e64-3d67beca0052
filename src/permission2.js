import router from './router'
import store from './store'
import NProgress from 'nprogress' // progress bar
import 'nprogress/nprogress.css' // progress bar style
import { getSessionId, getToken } from 'cnd-horizon-utils/src/utils/auth'
import { start } from 'qiankun'
import { menuResize, menuOperation, MaximumPages, judgeAuth, loadApp, getRedirect } from '@/utils/index'
import otherRoutes from '@/router/detail'
import { filterAsyncRouter } from '@/store/modules/permission'
import { MessageUtil } from 'cnd-horizon-utils'
NProgress.configure({ showSpinner: false }) // NProgress Configuration
var timeInterval
const whiteList = ['/login', '/forget'] // 重定向白名单
const authWhiteList = ['/404', '/401', '/navbar', '/share'] // 校验白名单
router.beforeEach(async(to, from, next) => {
  console.log('to-----', to)
  console.log('from-----', from)
  localStorage.setItem('menuId', to.meta.id || to.query.activeId || '') // 存储当前访问菜单id
  NProgress.start()
  const hasToken = store.getters.authenticationMethod === 'token' ? getToken() : getSessionId()

  if (hasToken && whiteList.indexOf(to.path) === -1) {
    const hasUserId = store.getters.userId // 用户id
    const flagLoadMenu = store.getters.flagLoadMenu // 是否生成菜单
    if (hasUserId) {
      if (flagLoadMenu) { // 白名单菜单不校验
        if (sessionStorage.getItem('navbar') && (to.path === '/' || to.path === '/navbar')) {
          next({ path: sessionStorage.getItem('navbar') })
          NProgress.done()
        } else if (authWhiteList.indexOf(to.path) === -1 && to.path.indexOf('/redirect') === -1 && !judgeAuth(to)) {
          next({ path: '/404' })
        } else { // 白名单不需要校验
          if (!MaximumPages(to) && to.path.indexOf('/redirect') === -1) {
            if (from.path === '/' || from.path === '/login') {
              next({ path: '/navbar' })
            } else {
              MessageUtil.warning('最多可以打开15个页面，请关闭多余页面!')
            }
            NProgress.done()
            return false
          }
          hrefTimeInterval(next, to)
          if (to.meta.id || to.query.activeId) {
            store.dispatch('menu/asyncGetBtnList', to.meta.id || to.query.activeId).then(authenticationList => {
              actions.setGlobalState({
                authenticationList: authenticationList // 按钮权限
              })
            })
          }
          loadApp()
          setTimeout(() => {
            next()
          }, 0)
        }
      } else {
        store.dispatch('menu/asyncGetMenuList', hasUserId).then(res => {
          const asyncRouter = filterAsyncRouter(res).concat(otherRoutes)
          store.dispatch('permission/GenerateRoutes', res).then(() => {
            router.addRoutes(asyncRouter) // 动态添加可访问路由表
            next({ ...to, replace: true })
          })
        })
      }
    } else { // 无用户id
      try {
        await store.dispatch('user/getInfo') // 获取用户信息
        next({ ...to, replace: true })
      } catch (error) {
        await store.dispatch('user/resetToken')
        next({ path: '/login', query: { redirect: getRedirect(to.fullPath) }})
      }
    }
  } else {
    if (whiteList.indexOf(to.path) !== -1) {
      hasToken ? next('/') : next()
    } else {
      await store.dispatch('user/resetToken')
      next({ path: '/login', query: { redirect: getRedirect(to.fullPath) }})
    }
  }
  menuResize() // 触发resize事件
})

function hrefTimeInterval(next, to) {
  timeInterval = setTimeout(() => {
    const hrefList = location.href.split('/')
    const fullPath = getRedirect(to.fullPath)
    const newHref = `${hrefList[0]}//${hrefList[2]}${fullPath}`
    const reg = RegExp('redirect')
    if (reg.test(location.href)) {
      next({ path: fullPath })
    } else {
      location.replace(newHref) // 避免 qiankun 报错导致卡顿问题
    }
  }, 300)
}

router.afterEach((to) => {
  document.title = to.query.name || to.meta.title || '首页'
  clearInterval(timeInterval)
  NProgress.done()
})

router.onError((error) => {
  const pattern = /Loading chunk (\d)+ failed/g
  const isChunkLoadFailed = error.message.match(pattern)
  const targetPath = router.history.pending.fullPath
  if (isChunkLoadFailed) {
    router.replace(targetPath)
  }
})

start({
  prefetch: true,
  sandbox: {
    experimentalStyleIsolation: true
  }
})
