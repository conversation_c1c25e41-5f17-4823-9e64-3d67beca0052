/* eslint-disable */
import { saveAs } from 'file-saver'
import XLSX from 'xlsx-color'
import axios from 'axios'
// import clientApi from '@/api/clientNumber'
const ExcelJS = require('exceljs')

function generateArray(table) {
  var out = [];
  var rows = table.querySelectorAll('tr');
  var ranges = [];
  for (var R = 0; R < rows.length; ++R) {
    var outRow = [];
    var row = rows[R];
    var columns = row.querySelectorAll('td');
    for (var C = 0; C < columns.length; ++C) {
      var cell = columns[C];
      var colspan = cell.getAttribute('colspan');
      var rowspan = cell.getAttribute('rowspan');
      var cellValue = cell.innerText;
      if (cellValue !== "" && cellValue == +cellValue) cellValue = +cellValue;

      //Skip ranges
      ranges.forEach(function (range) {
        if (R >= range.s.r && R <= range.e.r && outRow.length >= range.s.c && outRow.length <= range.e.c) {
          for (var i = 0; i <= range.e.c - range.s.c; ++i) outRow.push(null);
        }
      });

      //Handle Row Span
      if (rowspan || colspan) {
        rowspan = rowspan || 1;
        colspan = colspan || 1;
        ranges.push({
          s: {
            r: R,
            c: outRow.length
          },
          e: {
            r: R + rowspan - 1,
            c: outRow.length + colspan - 1
          }
        });
      };

      //Handle Value
      outRow.push(cellValue !== "" ? cellValue : null);

      //Handle Colspan
      if (colspan)
        for (var k = 0; k < colspan - 1; ++k) outRow.push(null);
    }
    out.push(outRow);
  }
  return [out, ranges];
};

function datenum(v, date1904) {
  if (date1904) v += 1462;
  var epoch = Date.parse(v);
  return (epoch - new Date(Date.UTC(1899, 11, 30))) / (24 * 60 * 60 * 1000);
}

function sheet_from_array_of_arrays(data,headerColors=[], opts) {
  var ws = {};
  var range = {
    s: {
      c: 10000000,
      r: 10000000
    },
    e: {
      c: 0,
      r: 0
    }
  };
  for (var R = 0; R != data.length; ++R) {
    for (var C = 0; C != data[R].length; ++C) {
      if (range.s.r > R) range.s.r = R;
      if (range.s.c > C) range.s.c = C;
      if (range.e.r < R) range.e.r = R;
      if (range.e.c < C) range.e.c = C;
      var cell = {
        v: data[R][C]
      };
      if(headerColors.length>0&&R === 0){
        if(headerColors[C]){
          cell.s = {
            font: {
              color: {
                rgb: headerColors[C]
              }
            }
          }
        }
      }
      if (cell.v == null) continue;
      var cell_ref = XLSX.utils.encode_cell({
        c: C,
        r: R
      });

      if (typeof cell.v === 'number') cell.t = 'n';
      else if (typeof cell.v === 'boolean') cell.t = 'b';
      else if (cell.v instanceof Date) {
        cell.t = 'n';
        cell.z = XLSX.SSF._table[14];
        cell.v = datenum(cell.v);
      } else cell.t = 's';

      ws[cell_ref] = cell;
    }
  }
  if (range.s.c < 10000000) ws['!ref'] = XLSX.utils.encode_range(range);
  return ws;
}

function Workbook() {
  if (!(this instanceof Workbook)) return new Workbook();
  this.SheetNames = [];
  this.Sheets = {};
}

function s2ab(s) {
  var buf = new ArrayBuffer(s.length);
  var view = new Uint8Array(buf);
  for (var i = 0; i != s.length; ++i) view[i] = s.charCodeAt(i) & 0xFF;
  return buf;
}

export function export_table_to_excel(id) {
  var theTable = document.getElementById(id);
  var oo = generateArray(theTable);
  var ranges = oo[1];

  /* original data */
  var data = oo[0];
  var ws_name = "SheetJS";

  var wb = new Workbook(),
    ws = sheet_from_array_of_arrays(data);

  /* add ranges to worksheet */
  // ws['!cols'] = ['apple', 'banan'];
  ws['!merges'] = ranges;

  /* add worksheet to workbook */
  wb.SheetNames.push(ws_name);
  wb.Sheets[ws_name] = ws;

  var wbout = XLSX.write(wb, {
    bookType: 'xlsx',
    bookSST: false,
    type: 'binary'
  });

  saveAs(new Blob([s2ab(wbout)], {
    type: "application/octet-stream"
  }), "test.xlsx")
}

export function export_json_to_excel({
  multiHeader = [],
  header,
  headerColors,
  data,
  filename,
  merges = [],
  autoWidth = true,
  bookType = 'xlsx'
} = {}) {
  /* original data */
  filename = filename || 'excel-list'
  data = [...data]
  data.unshift(header);

  for (let i = multiHeader.length - 1; i > -1; i--) {
    data.unshift(multiHeader[i])
  }

  var ws_name = "SheetJS";
  var wb = new Workbook(),
    ws = sheet_from_array_of_arrays(data,headerColors);
  if (merges.length > 0) {
    if (!ws['!merges']) ws['!merges'] = [];
    merges.forEach(item => {
      ws['!merges'].push(XLSX.utils.decode_range(item))
    })
  }

  if (autoWidth) {
    /*设置worksheet每列的最大宽度*/
    const colWidth = data.map(row => row.map(val => {
      /*先判断是否为null/undefined*/
      if (val == null) {
        return {
          'wch': 10
        };
      }
      /*再判断是否为中文*/
      else if (val.toString().charCodeAt(0) > 255) {
        return {
          'wch': val.toString().length * 2
        };
      } else {
        return {
          'wch': val.toString().length
        };
      }
    }))
    /*以第一行为初始值*/
    let result = colWidth[0];
    for (let i = 1; i < colWidth.length; i++) {
      for (let j = 0; j < colWidth[i].length; j++) {
        if (result[j]['wch'] < colWidth[i][j]['wch']) {
          result[j]['wch'] = colWidth[i][j]['wch'];
        }
      }
    }
    ws['!cols'] = result;
  }
  /* add worksheet to workbook */
  wb.SheetNames.push(ws_name);
  wb.Sheets[ws_name] = ws;

  var wbout = XLSX.write(wb, {
    bookType: bookType,
    bookSST: false,
    type: 'binary',
    cellStyles:true
  });
  saveAs(new Blob([s2ab(wbout)], {
    type: "application/octet-stream"
  }), `${filename}.${bookType}`);
}
//
export function export_sheet_to_excel({
  data,
  filename,
  bookType = 'xlsx'
}={}){
  var ws_name = "SheetJS";
  var wb = new Workbook()
  const ws = {}
  ws['!merges'] = [
    {s: {r: 0, c: 0}, e: {r: 2, c: 1}},
    {s: {r: 0, c: 2}, e: {r: 0, c: 17}}
  ]
  var A1 = XLSX.utils.encode_cell({
    c: 0,
    r: 0
  });
  ws['A1'] = {
    r:'<div><p>asdasd</p><p>alskdjasldkj</p></div>',
  }
  ws['C1'] = {
    t:'s',
    v:'XIAMEN C&D LIGHT INDUSTRY CO.， LTD.   厦门建发轻工有限公司',
    s : {
      alignment:{
        horizontal:'center'
      },
      font: {
        sz:18,
        bold:true
      }
    }
  }
  ws['!ref'] = "A1:C18"
  /* add worksheet to workbook */
  wb.SheetNames.push(ws_name);
  wb.Sheets[ws_name] = ws;

  var wbout = XLSX.write(wb, {
    bookType: bookType,
    bookSST: false,
    type: 'binary',
    cellStyles:true
  });
  saveAs(new Blob([s2ab(wbout)], {
    type: "application/octet-stream"
  }), `${filename}.${bookType}`);
}
// 华为云地址转base64
function ossUrlToBase64(fileName) {
  // return new Promise((resolve, reject) => {
  //   clientApi.getOssUrl({
  //     'httpMethodEnum': 'get',
  //     'objectName': fileName,
  //     'vOri':1
  //   }).then(res => {
  //     axios.request({
  //       url: res.data.url,
  //       method: 'GET',
  //       responseType: 'arraybuffer'
  //     }).then(res => {
  //       if(res.status === 200){
  //         const unicodeList = new Uint8Array(res.data)
  //         let str = ''
  //         const forNum = Math.ceil(unicodeList.length/100000)
  //         for(let i=0;i<forNum;i++){
  //           const unicode = unicodeList.slice(i*100000,(i+1)*100000)
  //           const character = String.fromCharCode(...unicode);
  //           str+=character
  //         }
  //         // const str = String.fromCharCode(...new Uint8Array(res.data));
  //         const base64 = `data:image/jpeg;base64,${window.btoa(str)}`
  //         resolve(base64)
  //       }else{
  //         reject(false)
  //       }
  //     }).catch(()=>{
  //       resolve(false)
  //     })
  //   }).catch(() => {
  //     reject(false)
  //   })
  // })
}
function netUrlToBase64(value){
  return new Promise((resolve, reject) => {
    const image = new Image();
    image.src = value;
    // 解决跨域 Canvas 污染问题
    image.setAttribute("crossOrigin", "anonymous");
    image.onload = function() {
      const canvas = document.createElement("canvas");
      canvas.width = image.width;
      canvas.height = image.height;
      const context = canvas.getContext("2d");
      context.drawImage(image, 0, 0, image.width, image.height);
      const imageBase64 = canvas.toDataURL("image/png"); //得到图片的base64编码数据
      resolve(imageBase64)
    }
  })
}
// 本地地址转转base64
function getBase64Image(img) {
  var canvas = document.createElement("canvas");
  canvas.width = img.width;
  canvas.height = img.height;
  var ctx = canvas.getContext("2d");
  ctx.drawImage(img, 0, 0, img.width, img.height);
  var ext = img.src.substring(img.src.lastIndexOf(".")+1).toLowerCase();
  var dataURL = canvas.toDataURL("image/"+ext);
  return dataURL;
}
export const export_excel_of_exceljs = async({
  data = [],
  filename,
  sheetname
})=>{
  // 声明Excel
  const workbook = new ExcelJS.Workbook()
  workbook.views = [
    {
      x: 0, y: 0, width: 10000, height: 20000,
      firstSheet: 0, activeTab: 1, visibility: 'visible'
    }
  ]
  // 声明工作表
  const sheet = workbook.addWorksheet(sheetname||'工作表')
  for(let item of data){
    const {cell,mergeCells,style,type,value} = item
    if(type === 'text'){
      const newCell = sheet.getCell(cell)
      newCell.value = value
      if(mergeCells){
        sheet.mergeCells(mergeCells);
      }
      if(style){
        const fontDict = ['size','color','bold','italic']
        const borderDict =['borderTop','borderRight','borderBottom','borderLeft']
        const alignDict =['horizontal','vertical','wrapText']
        const [font,alignment] = [{},{}]
        let border = {}
        for(let key in style){
          if(fontDict.includes(key)){
            font[key] = key === 'color'?{argb:`FF${style[key]}`}: style[key]
          }
          if(style.border){
            border = {
              top: {style:'thin', color: {argb:`FF${style[border]}`}},
              left: {style:'thin', color: {argb:`FF${style[border]}`}},
              bottom: {style:'thin', color: {argb:`FF${style[border]}`}},
              right: {style:'thin', color: {argb:`FF${style[border]}`}}
            }
          }else if(borderDict.includes(key)){
            const borderKey = key.substring(6,key.length).toLowerCase()
            border[borderKey] =  {style:'thin', color: {argb:`FF${style[border]}`}}
          }
          if(alignDict.includes(key)){
            alignment[key] = style[key]
          }
        }
        newCell.font = font
        newCell.border = border
        newCell.alignment = alignment
      }
    }
    // 插入图片 1、ossurl 华为云/网络图片 2、本地图片
    if(type === 'image'){
      const fileName =  value.split('?')[0]
      if(fileName){
        const imageBase64 = await ossUrlToBase64(fileName)
        if(imageBase64) {
          const newImage = workbook.addImage({
            base64: imageBase64,
            extension: fileName.split('.')[1],
          });
          sheet.addImage(newImage, mergeCells);
        }
      }
    }
    if(type === 'network'){
      const imageBase64 = await netUrlToBase64(value)
      if(imageBase64) {
        const newImage = workbook.addImage({
          base64: imageBase64,
          extension:  value.split('?')[0].split('.')[1],
        });
        sheet.addImage(newImage, mergeCells);
      }
    }
    if(type === 'require'){
      const handleRequireImg = ()=>{
      return  new Promise((resolve,reject)=>{
          const requireImg = require('@/assets/logo.png');
          let image = new Image();
          image.src = requireImg
          image.crossOrigin='Anonymous'
          image.onload = ()=>{
           resolve(getBase64Image(image))
          }
        })
      }

     const imageBase64 = await handleRequireImg()
        const newImage = workbook.addImage({
          base64: imageBase64,
          extension: value.split('.')[1],
        });
        sheet.addImage(newImage, mergeCells);
    }
  }
  // 下载excel
  const downloadExcel = async()=>{
    const buffer = await workbook.xlsx.writeBuffer();
    const link = document.createElement('a')

    // application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
    const blob = new Blob([buffer], { type: 'application/octet-stream' })
    link.style.display = 'none'
    link.href = URL.createObjectURL(blob)
    link.download = `${filename}.xlsx`
    document.body.appendChild(link)
    link.click()
  }
  downloadExcel()
}

export const export_excel_of_sheet_list = async({
  data = [],
  filename
})=>{
  // 声明Excel
  const workbook = new ExcelJS.Workbook()
  workbook.views = [
    {
      x: 0, y: 0, width: 10000, height: 20000,
      firstSheet: 0, activeTab: 1, visibility: 'visible'
    }
  ]
  // 声明工作表
  for(let el of data){
    const sheet = workbook.addWorksheet(el.title)
    if(el.columns?.length){
      el.columns.forEach((item)=>{
        const newColumn = sheet.getColumn(item.index);
        if(item.width){
          newColumn.key = `column${item.index}`
          newColumn.width = item.width
        }
      })
     }
    for(let item of el.children){
      const {cell,mergeCells,style,type,value} = item
      if(type === 'text'){
        const newCell = sheet.getCell(cell)
        newCell.value = value
        if(mergeCells){
          sheet.mergeCells(mergeCells);
        }
        if(style){
          const fontDict = ['name','size','color','bold','italic',]
          const borderDict =['borderTop','borderRight','borderBottom','borderLeft']
          const alignDict =['horizontal','vertical','wrapText']
          const [font,alignment] = [{},{}]
          let border = {}
          for(let key in style){
            if(fontDict.includes(key)){
              font[key] = key === 'color'?{argb:`FF${style[key]}`}: style[key]
            }
            if(style.border){
              border = {
                top: {style:'thin', color: {argb:style.border}},
                left: {style:'thin', color: {argb:style.border}},
                bottom: {style:'thin', color: {argb:style.border}},
                right: {style:'thin', color: {argb:style.border}}
              }
            }else if(borderDict.includes(key)){
              const borderKey = key.substring(6,key.length).toLowerCase()
              // border[borderKey] =  {style:'thin', color: {argb:`FF${style[key]}`}}
              border[borderKey] =  {style:'thin', color: {argb:style[key]}}
            }
            if(alignDict.includes(key)){
              alignment[key] = style[key]
            }
            if(style.bgColor){
              newCell.fill = {
                type: 'pattern',
                pattern:'solid',
                fgColor:{argb:style.bgColor}
              }
            }
          }
          newCell.font = font
          newCell.border = border
          newCell.alignment = alignment
        }
      }
      // 插入图片 1、ossurl 华为云/网络图片 2、本地图片
      if(type === 'image'){
        const fileName =  value.split('?')[0]
        if(fileName){
          const imageBase64 = await ossUrlToBase64(fileName)
          if(imageBase64) {
            const newImage = workbook.addImage({
              base64: imageBase64,
              extension: fileName.split('.')[1],
            });
            sheet.addImage(newImage, mergeCells);
          }
        }
      }
      if(type === 'network'){
        const imageBase64 = await netUrlToBase64(value)
        if(imageBase64) {
          const newImage = workbook.addImage({
            base64: imageBase64,
            extension:  value.split('?')[0].split('.')[1],
          })
          sheet.addImage(newImage, mergeCells);
        }
      }
      if(type === 'require'){
        const handleRequireImg = ()=>{
        return  new Promise((resolve,reject)=>{
            const requireImg = require('@/assets/logo.png');
            let image = new Image();
            image.src = requireImg
            image.crossOrigin='Anonymous'
            image.onload = ()=>{
             resolve(getBase64Image(image))
            }
          })
        }

       const imageBase64 = await handleRequireImg()
          const newImage = workbook.addImage({
            base64: imageBase64,
            extension: value.split('.')[1],
          });
          sheet.addImage(newImage, mergeCells);
      }
    }
  }
  // 下载excel
  const downloadExcel = async()=>{
    const buffer = await workbook.xlsx.writeBuffer();
    const link = document.createElement('a')
    // application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
    const blob = new Blob([buffer], { type: 'application/octet-stream' })
    link.style.display = 'none'
    link.href = URL.createObjectURL(blob)
    link.download = `${filename}.xlsx`
    document.body.appendChild(link)
    link.click()
  }
  downloadExcel()
}

export const export_excel_of_exceljs2 = async({
  data = [],
  filename,
  sheetname
}) => {
  // 声明Excel
  const workbook = new ExcelJS.Workbook();
  workbook.views = [
    {
      x: 0, y: 0, width: 10000, height: 20000,
      firstSheet: 0, activeTab: 1, visibility: 'visible'
    }
  ];

  // 声明工作表
  const sheet = workbook.addWorksheet(sheetname || '工作表');

  // 添加合并单元格
  sheet.mergeCells('A1:B3');
  sheet.mergeCells('C1:R1');

  // 设置单元格 A1 图片
  const handleRequireImg = () => {
    return new Promise((resolve, reject) => {
      const requireImg = require('@/assets/logo.png');
      let image = new Image();
      image.src = requireImg;
      image.crossOrigin = 'Anonymous';
      image.onload = () => {
        resolve(getBase64Image(image));
      };
    });
  };

  const imageBase64 = await handleRequireImg();
  const newImage = workbook.addImage({
    base64: imageBase64,
    extension: 'png',
  });
  sheet.addImage(newImage, 'A1:B3');

  // 设置单元格 C1
  const cellC1 = sheet.getCell('C1');
  cellC1.value = 'XIAMEN C&D LIGHT INDUSTRY CO.， LTD.   厦门建发轻工有限公司';
  cellC1.alignment = { horizontal: 'center' };
  cellC1.font = { size: 18, bold: true };

  for (let item of data) {
    const { cell, mergeCells, style, type, value } = item;
    if (type === 'text') {
      const newCell = sheet.getCell(cell);
      newCell.value = value;
      if (mergeCells) {
        sheet.mergeCells(mergeCells);
      }
      if (style) {
        const fontDict = ['size', 'color', 'bold', 'italic'];
        const borderDict = ['borderTop', 'borderRight', 'borderBottom', 'borderLeft'];
        const alignDict = ['horizontal', 'vertical', 'wrapText'];
        const [font, alignment] = [{}, {}];
        let border = {};
        for (let key in style) {
          if (fontDict.includes(key)) {
            font[key] = key === 'color' ? { argb: `FF${style[key]}` } : style[key];
          }
          if (style.border) {
            border = {
              top: { style: 'thin', color: { argb: `FF${style[border]}` } },
              left: { style: 'thin', color: { argb: `FF${style[border]}` } },
              bottom: { style: 'thin', color: { argb: `FF${style[border]}` } },
              right: { style: 'thin', color: { argb: `FF${style[border]}` } }
            };
          } else if (borderDict.includes(key)) {
            const borderKey = key.substring(6, key.length).toLowerCase();
            border[borderKey] = { style: 'thin', color: { argb: `FF${style[border]}` } };
          }
          if (alignDict.includes(key)) {
            alignment[key] = style[key];
          }
        }
        newCell.font = font;
        newCell.border = border;
        newCell.alignment = alignment;
      }
    }
    // 插入图片 1、ossurl 华为云/网络图片 2、本地图片
    if (type === 'image') {
      const fileName = value.split('?')[0];
      if (fileName) {
        const imageBase64 = await ossUrlToBase64(fileName);
        if (imageBase64) {
          const newImage = workbook.addImage({
            base64: imageBase64,
            extension: fileName.split('.')[1],
          });
          sheet.addImage(newImage, mergeCells);
        }
      }
    }
    if (type === 'network') {
      const imageBase64 = await netUrlToBase64(value);
      if (imageBase64) {
        const newImage = workbook.addImage({
          base64: imageBase64,
          extension: value.split('?')[0].split('.')[1],
        });
        sheet.addImage(newImage, mergeCells);
      }
    }
    if (type === 'require') {
      const handleRequireImg = () => {
        return new Promise((resolve, reject) => {
          const requireImg = require('@/assets/logo.png');
          let image = new Image();
          image.src = requireImg;
          image.crossOrigin = 'Anonymous';
          image.onload = () => {
            resolve(getBase64Image(image));
          };
        });
      };

      const imageBase64 = await handleRequireImg();
      const newImage = workbook.addImage({
        base64: imageBase64,
        extension: value.split('.')[1],
      });
      sheet.addImage(newImage, mergeCells);
    }
  }

  // 下载excel
  const downloadExcel = async () => {
    const buffer = await workbook.xlsx.writeBuffer();
    const link = document.createElement('a');

    // application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
    const blob = new Blob([buffer], { type: 'application/octet-stream' });
    link.style.display = 'none';
    link.href = URL.createObjectURL(blob);
    link.download = `${filename}.xlsx`;
    document.body.appendChild(link);
    link.click();
  };
  downloadExcel();
};

