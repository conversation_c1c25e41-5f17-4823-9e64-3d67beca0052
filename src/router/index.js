/*
 * @Author: 沈鹭荣
 * @Date: 2021-02-25 10:03:29
 * @LastEditors: 沈鹭荣
 * @LastEditTime: 2021-03-16 19:59:22
 * @Description:
 */
import Vue from 'vue'
import VueRouter from 'vue-router'
import ConstractRoutes from './modules/contractConfig'
import saleorder from './modules/saleorder'
import Inovice from './modules/invoice'
// 资金管理
import capitalManage from './modules/capitalManage'
import MoneyFlow from './modules/moneyFlow' // 旧路由
import logistics from './modules/logistics'
import MoneyFlowConfig from './modules/moneyFlowConfig'
import QueryStatistic from './modules/queryStatistic.js'
import System from './modules/system'
import DomesticProcurementList from '@/views/contract/domesticProcurement/Index.vue'
import PreContractList from './modules/preContractConfig'
import SteelInfoManage from './modules/steelInfoManage'
import RelationConfigWorkbench from './modules/relationConfigWorkbench'
import processModule from './modules/processModule'
import stocktakeManage from './modules/stocktakeManage'
import returnManage from './modules/returnManage'
import steelMillCenterRegulate from './modules/steelMillCenterRegulate'
import retailPricing from './modules/retailPricing'
import TravellingMerchant from './modules/travellingMerchant'
Vue.use(VueRouter)

export const tempRoutes = [
  {
    path: '',
    name: 'DomesticProcurementList',
    component: DomesticProcurementList,
    meta: { keepAlive: true }
  }
]
const aggridDemo = [
  {
    path: '/aggridDemo',
    name: 'aggridDemo',
    component: () => import('@/views/aggridDemo/index.vue'),
    meta: { keepAlive: true }
  }
]
export const routes = tempRoutes.concat(
  aggridDemo,
  ConstractRoutes,
  PreContractList,
  Inovice,
  logistics,
  capitalManage,
  MoneyFlow,
  MoneyFlowConfig,
  QueryStatistic,
  saleorder,
  SteelInfoManage,
  System,
  processModule,
  stocktakeManage,
  returnManage,
  retailPricing,
  steelMillCenterRegulate,
  RelationConfigWorkbench,
  TravellingMerchant
)
const router = new VueRouter({
  mode: 'history', // require service support
  routes
})

export default router
