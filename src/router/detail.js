/* Layout */
import Layout from '@/layout'

const detailRoutes = [
  {
    path: '/detail',
    component: Layout,
    hidden: true,
    meta: {
      title: '详情'
    },
    children: [
      // plusscm通用详情配置
      {
        path: '/plusSCM/:path/:code',
        name: 'scm详情页菜单',
        id: 'plusSCM',
        meta: {
          key: 'plusSCM',
          title: 'plusSCM',
          keepAlive: true
        }
      },
      // system通用详情配置
      {
        path: '/system/:path/:code',
        name: 'system详情页菜单',
        id: 'system',
        meta: {
          key: 'system',
          title: 'system',
          keepAlive: true
        }
      },
      // egl通用详情配置
      {
        path: '/egl/:path/:code',
        name: 'egl详情页菜单',
        id: 'egl',
        meta: {
          key: 'egl',
          title: 'egl',
          keepAlive: true
        }
      },
      // egl通用详情配置
      {
        path: '/mall-manage/:path/:code',
        name: 'mall-manage详情页菜单',
        id: 'mall-manage',
        meta: {
          key: 'mall-manage',
          title: 'mall-manage',
          keepAlive: true
        }
      }
    ]
  }
]

export default detailRoutes
