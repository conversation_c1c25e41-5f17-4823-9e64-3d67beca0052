const PreContractList = () => import('@/views/preContract/preContractQuery/Index.vue')
const PreContractDetail = () => import('@/views/preContract/preContractQuery/PreContractDetail.vue')
const specificationConfiguration = () => import('@/views/preContract/preContractQuery/specificationConfiguration.vue')
const PreContractRoutes = [
  // 材质、规格配置
  {
    path: '/specificationConfiguration',
    name: 'specificationConfiguration',
    component: specificationConfiguration,
    meta: { keepAlive: true }
  },
  // 预录入合同列表
  {
    path: '/preContractList',
    name: 'PreContractList',
    component: PreContractList,
    meta: { keepAlive: true }
  },
  // 预录入合同详情
  {
    path: '/preContractDetail/:code',
    name: 'PreContractDetail',
    component: PreContractDetail,
    meta: { keepAlive: true }
  }

]

export default PreContractRoutes
