const InventoryLossOrderManage = () => import('@/views/stocktakeManage/inventoryLoss/index.vue')
const InventoryLossOrderDetail = () => import('@/views/stocktakeManage/inventoryLoss/detail/index.vue')
const InventoryLossOrderWorkbench = () => import('@/views/stocktakeManage/inventoryLoss/workbench.vue')

export default [
  {
    path: '/inventoryLossOrderManage',
    name: 'InventoryLossOrderManage',
    component: InventoryLossOrderManage,
    meta: { keepAlive: true }
  },
  {
    path: '/inventoryLossOrderDetail/:code',
    name: 'InventoryLossOrderDetail',
    component: InventoryLossOrderDetail,
    meta: { keepAlive: true }
  },
  {
    path: '/inventoryLossOrderWorkbench',
    name: 'InventoryLossOrderWorkbench',
    component: InventoryLossOrderWorkbench,
    meta: { keepAlive: true }
  }
]
