const InventoryProfitManage = () => import('@/views/stocktakeManage/inventoryProfit/index.vue')
const InventoryProfitDetail = () => import('@/views/stocktakeManage/inventoryProfit/detail/index.vue')
const InventoryProfitWorkbench = () => import('@/views/stocktakeManage/inventoryProfit/workbench.vue')

export default [
  {
    path: '/inventoryProfitManage',
    name: 'InventoryProfitManage',
    component: InventoryProfitManage,
    meta: { keepAlive: true }
  },
  {
    path: '/inventoryProfitDetail/:code',
    name: 'InventoryProfitDetail',
    component: InventoryProfitDetail,
    meta: { keepAlive: true }
  },
  {
    path: '/inventoryProfitWorkbench',
    name: 'InventoryProfitWorkbench',
    component: InventoryProfitWorkbench,
    meta: { keepAlive: true }
  }
]
