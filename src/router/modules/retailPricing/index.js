
const PricingDepartment = () => import('@/views/retailPricing/pricingDepartment/index.vue')
const PricingDepartmentDetail = () => import('@/views/retailPricing/pricingDepartment/detail/index.vue')
const PricingManage = () => import('@/views/retailPricing/pricingManage/index.vue')
const PricingApproval = () => import('@/views/retailPricing/pricingApproval/index.vue')
const PricingApprovalDetail = () => import('@/views/retailPricing/pricingApproval/detail/index.vue')
const FreightConfig = () => import('@/views/retailPricing/freightConfig/index.vue')
const FreightConfigDetail = () => import('@/views/retailPricing/freightConfig/detail/index.vue')
const retailPricingRoutes = [
  {
    path: '/pricingDepartment',
    name: 'PricingDepartment',
    component: PricingDepartment,
    meta: { keepAlive: true }
  },
  {
    path: '/pricingDepartmentDetail/:code',
    name: 'PricingDepartmentDetail',
    component: PricingDepartmentDetail,
    meta: { keepAlive: true }
  },
  {
    path: '/pricingManage',
    name: 'PricingManage',
    component: PricingManage,
    meta: { keepAlive: true }
  },
  {
    path: '/pricingApproval',
    name: 'PricingApproval',
    component: PricingApproval,
    meta: { keepAlive: true }
  },
  {
    path: '/pricingApprovalDetail/:code',
    name: 'PricingApprovalDetail',
    component: PricingApprovalDetail,
    meta: { keepAlive: true }
  },
  {
    path: '/freightConfig',
    name: 'FreightConfig',
    component: FreightConfig,
    meta: { keepAlive: true }
  },
  {
    path: '/freightConfigDetail/:code',
    name: 'FreightConfigDetail',
    component: FreightConfigDetail,
    meta: { keepAlive: true }
  }
]

export default retailPricingRoutes
