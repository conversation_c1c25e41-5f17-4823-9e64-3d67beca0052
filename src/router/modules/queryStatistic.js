/*
 * @Author: 沈鹭荣
 * @Date: 2021-01-11 11:35:51
 * @LastEditors: 沈鹭荣
 * @LastEditTime: 2021-01-12 11:24:22
 * @Description:
 */
/*
 * @Description: 查询统计
 */
const StockQuery = () => import('@/views/queryStatistic/stockQuery/Index.vue') // 库存查询统计
// const SaleQuery = () => import('@/views/queryStatistic/saleQuery/Index.vue') // 销售查询
const SaleQueryV2 = () => import('@/views/queryStatistic/saleQueryV2/Index.vue') // 销售查询
const ProjectInvoice = () => import('@/views/queryStatistic/projectInvoice/index.vue') // 项目发货单查询
const PurchaseQuerys = () => import('@/views/queryStatistic/purchaseQuery/Index.vue') // 采购查询
const ContractLedger = () => import('@/views/queryStatistic/contractLedger/Index.vue') // 合同台账
const CustomerLedger = () => import('@/views/queryStatistic/customerLedger/Index.vue') // 客户余额台账
const PurchaseContractLedger = () => import('@/views/queryStatistic/purchaseContractLedger/Index.vue') // 采购合同台账
const MerchantLedger = () => import('@/views/queryStatistic/merchantLedger/Index.vue') // 客商余额台账
const SaleContractContrast = () => import('@/views/queryStatistic/saleContractContrast/Index.vue') // 客商余额台账
const PurContractContrast = () => import('@/views/queryStatistic/purContractContrast/Index.vue') // 客商余额台账
const SaleContractCollection = () => import('@/views/queryStatistic/saleContractCollection/index.vue') // 合同收汇明细
const Certificate = () => import('@/views/queryStatistic/certificate/index.vue') // 锐眼存证查询
const MaintenanceManagement = () => import('@/views/queryStatistic/maintenanceManagement/index.vue')
const BusinessReport = () => import('@/views/queryStatistic/businessReport/Index.vue') // 云钢业务数据统计报表
const ReceiptInquiry = () => import('@/views/queryStatistic/receiptInquiry/index.vue')
const ZhongtuoQuery = () => import('@/views/queryStatistic/zhongtuoQuery/index.vue')
const LogisticsStockQuery = () => import('@/views/queryStatistic/logisticsStock/index.vue')
const CreditDetailQuery = () => import('@/views/queryStatistic/creditDetailQuery/index.vue')
const QueryFacility = () => import('@/views/queryStatistic/queryFacility/index.vue')
const ProjectReceiveQuery = () => import('@/views/queryStatistic/projectReceiveQuery/index.vue')
const ProcessFinishedProductLedger = () => import('@/views/queryStatistic/processFinishedProductLedger/index.vue') // 客商余额台账

const MallInventoryLog = () => import('@/views/queryStatistic/mallInventoryLog/index.vue')
const MallInventoryLedger = () => import('@/views/queryStatistic/mallInventoryLedger/Index.vue') // 客商余额台账
const PurchaseCostDataQuery = () => import('@/views/queryStatistic/purchaseCostDataQuery/index.vue')
const RetailInvoice = () => import('@/views/queryStatistic/RetailInvoice/index.vue')
const DataCenterQuery = () => import('@/views/queryStatistic/DataCenterQuery/index.vue')
const PurchaseSalesRelation = () => import('@/views/queryStatistic/PurchaseSalesRelation/index.vue')

const queryStatistic = [
  // 库存查询统计
  {
    path: '/stockQuery',
    name: 'StockQuery',
    component: StockQuery,
    meta: { keepAlive: true }
  },
  // 销售查询
  {
    path: '/saleQuery',
    name: 'SaleQuery',
    component: SaleQueryV2,
    meta: { keepAlive: true }
  },
  {
    path: '/SaleQueryV2',
    name: 'SaleQueryV2',
    component: SaleQueryV2,
    meta: { keepAlive: true }
  },
  // 项目发货单查询
  {
    path: '/ProjectInvoice',
    name: 'ProjectInvoice',
    component: ProjectInvoice,
    meta: { keepAlive: true }
  },
  // 采购查询
  {
    path: '/purchaseQuerys',
    name: 'PurchaseQuerys',
    component: PurchaseQuerys,
    meta: { keepAlive: true }
  },
  // 合同台账
  {
    path: '/contractLedger',
    name: 'ContractLedger',
    component: ContractLedger,
    meta: { keepAlive: true }
  },
  // 客户余额台账
  {
    path: '/customerLedger',
    name: 'CustomerLedger',
    component: CustomerLedger,
    meta: { keepAlive: true }
  },
  // 采购合同台账
  {
    path: '/purchaseContractLedger',
    name: 'PurchaseContractLedger',
    component: PurchaseContractLedger,
    meta: { keepAlive: true }
  },
  // 客户余额台账
  {
    path: '/merchantLedger',
    name: 'MerchantLedger',
    component: MerchantLedger,
    meta: { keepAlive: true }
  },
  // 销售合同台账对比
  {
    path: '/saleContractContrast',
    name: 'SaleContractContrast',
    component: SaleContractContrast,
    meta: { keepAlive: true }
  },
  // 采购合同台账对比
  {
    path: '/purContractContrast',
    name: 'PurContractContrast',
    component: PurContractContrast,
    meta: { keepAlive: true }
  },
  // 合同收汇明细
  {
    path: '/saleContractCollection',
    name: 'SaleContractCollection',
    component: SaleContractCollection,
    meta: { keepAlive: true }
  },
  // 锐眼存证查询
  {
    path: '/certificate',
    name: 'Certificate',
    component: Certificate,
    meta: { keepAlive: true }
  },
  // 运维单据管理
  {
    path: '/maintenanceManagement',
    name: 'MaintenanceManagement',
    component: MaintenanceManagement,
    meta: { keepAlive: true }
  },
  {
    path: '/queryFacility',
    name: 'QueryFacility',
    component: QueryFacility,
    meta: { keepAlive: true }
  },
  // 云钢业务数据统计报表
  {
    path: '/businessReport',
    name: 'BusinessReport',
    component: BusinessReport,
    meta: { keepAlive: true }
  },
  {
    path: '/receiptInquiry',
    name: 'ReceiptInquiry',
    component: ReceiptInquiry,
    meta: { keepAlive: true }
  },
  {
    path: '/zhongtuoQuery',
    name: 'ZhongtuoQuery',
    component: ZhongtuoQuery,
    meta: { keepAlive: true }
  },
  {
    path: '/creditDetailQuery',
    name: 'CreditDetailQuery',
    component: CreditDetailQuery,
    meta: { keepAlive: true }
  },
  {
    path: '/logisticsStockQuery',
    name: 'LogisticsStockQuery',
    component: LogisticsStockQuery,
    meta: { keepAlive: true }
  },
  {
    path: '/projectReceiveQuery',
    name: 'ProjectReceiveQuery',
    component: ProjectReceiveQuery,
    meta: { keepAlive: true }
  },
  {
    path: '/processFinishedProductLedger',
    name: 'ProcessFinishedProductLedger',
    component: ProcessFinishedProductLedger,
    meta: { keepAlive: true }
  },
  {
    path: '/mallInventoryLog',
    name: 'MallInventoryLog',
    component: MallInventoryLog,
    meta: { keepAlive: true }
  },
  {
    path: '/mallInventoryLedger',
    name: 'MallInventoryLedger',
    component: MallInventoryLedger,
    meta: { keepAlive: true }
  },
  {
    path: '/purchaseCostDataQuery',
    name: 'PurchaseCostDataQuery',
    component: PurchaseCostDataQuery,
    meta: { keepAlive: true }
  },
  // 零售发货明细查询
  {
    path: '/RetailInvoice',
    name: 'RetailInvoice',
    component: RetailInvoice,
    meta: { keepAlive: true }
  },
  // 数据中台查询
  {
    path: '/DataCenterQuery',
    name: 'DataCenterQuery',
    component: DataCenterQuery,
    meta: { keepAlive: true }
  },
  // 购销关系查询列表
  {
    path: '/PurchaseSalesRelation',
    name: 'PurchaseSalesRelation',
    component: PurchaseSalesRelation,
    meta: { keepAlive: true }
  }
]
export default queryStatistic
