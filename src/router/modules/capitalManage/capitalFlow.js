/*
 * @Author: 沈鹭荣
 * @Date: 2021-01-12 10:51:24
 * @LastEditors: 沈鹭荣
 * @LastEditTime: 2021-02-05 16:30:17
 * @Description:
 */
const CapitalFlowCash = () => import('@/views/capitalManage/capitalFlow/cash.vue')
const CapitalFlowBankAcce = () => import('@/views/capitalManage/capitalFlow/bankAcceptance.vue')
const CapitalFlowRebate = () => import('@/views/capitalManage/capitalFlow/rebate.vue')
const CapitalFlowbond = () => import('@/views/capitalManage/capitalFlow/bond.vue')
const AcceptanceDiscount = () => import('@/views/capitalManage/capitalFlow/acceptanceDiscount.vue')
export default [
  {
    // 现款
    path: '/capitalFlowCash',
    name: 'CapitalFlowCash',
    component: CapitalFlowCash,
    meta: { keepAlive: true }
  },
  {
    // 银承
    path: '/capitalFlowBankAcce',
    name: 'CapitalFlowBankAcce',
    component: CapitalFlowBankAcce,
    meta: { keepAlive: true }
  },
  {
    // 承兑贴息
    path: '/acceptanceDiscount',
    name: 'AcceptanceDiscount',
    component: AcceptanceDiscount,
    meta: { keepAlive: true }
  },
  {
    // 返利
    path: '/capitalFlowRebate',
    name: 'CapitalFlowRebate',
    component: CapitalFlowRebate,
    meta: { keepAlive: true }
  },
  {
    // 保证金
    path: '/capitalFlowbond',
    name: 'CapitalFlowbond',
    component: CapitalFlowbond,
    meta: { keepAlive: true }
  }
]
