import capitalFlow from './capitalFlow'
const paymentApply = () => import('@/views/capitalManage/paymentApply')
const paymentApplyDetail = () => import('@/views/capitalManage/paymentApply/payAppDialog/index.vue')
const MerchantsMoneyFlow = () => import('@/views/capitalManage/merchantsMoneyFlow.vue')
const RefundWorkbench = () => import('@/views/capitalManage/paymentApply/refundWorkbench.vue')
// import capitalAdjustment = () => import('./capitalAdjustment'

const routeMap = [
  ...capitalFlow,
  {
    // 付款申请单
    path: '/paymentApply',
    name: 'PaymentApply',
    component: paymentApply,
    meta: { keepAlive: true }
  },
  {
    // 付款申请单详情
    path: '/paymentApplyDetail/:code',
    name: 'paymentApplyDetail',
    component: paymentApplyDetail,
    meta: { keepAlive: true }
  },
  {
    // 客商往来款查询
    path: '/merchantsMoneyFlow',
    name: 'MerchantsMoneyFlow',
    component: MerchantsMoneyFlow,
    meta: { keepAlive: true }
  },
  {
    // 退款工作台
    path: '/refundWorkbench',
    name: 'RefundWorkbench',
    component: RefundWorkbench,
    meta: { keepAlive: true }
  }
]

export default routeMap
