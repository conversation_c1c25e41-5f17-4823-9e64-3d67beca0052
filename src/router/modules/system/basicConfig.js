const CustMaintain = () => import('@/views/sysConfig/Customer/Index')
const CustMaintainDetail = () => import('@/views/sysConfig/Customer/EditCustMaintainDialog')
const BasicWarehouse = () => import('@/views/sysConfig/Warehouse/Index')
const BasicWarehouseDetail = () => import('@/views/sysConfig/Warehouse/EditDictDialog')
const BillManager = () => import('@/views/sysConfig/BillManager/Index')
const BillManagerDetail = () => import('@/views/sysConfig/BillManager/EditDictDialog')
const DataPermission = () => import('@/views/sysConfig/DataPermission/index')
const ApprovalBench = () => import('@/views/sysConfig/ApprovalBench/Index')
const ApprovalQuery = () => import('@/views/sysConfig/ApprovalBench/approvalQuery')
const RoleManager = () => import('@/views/sysConfig/Role/Index')
const CustomerRecipientList = () => import('@/views/sysConfig/CustomerRecipient/index')
const CustomerRecipientDetail = () => import('@/views/sysConfig/CustomerRecipient/detail')
const CustomerOutConfig = () => import('@/views/sysConfig/CustomerOutConfig/index')
const CustomerOutConfigDetail = () => import('@/views/sysConfig/CustomerOutConfig/detail')
const ExcRequestWorkbench = () => import('@/views/sysConfig/exceptionRequestWorkbench/Index.vue')
const ExcRequestDetail = () => import('@/views/sysConfig/exceptionRequestWorkbench/exceptionDetail.vue')

const WarehouseConfig = () => import('@/views/sysConfig/Warehouse/warehouseConfig.vue')
const RpaWarehouseConfig = () => import('@/views/sysConfig/Warehouse/rpa/index.vue')
const RpaWarehouseConfigDetail = () => import('@/views/sysConfig/Warehouse/rpa/detail.vue')

const EmailConfig = () => import('@/views/sysConfig/emailConfig/index.vue')
const EmailConfigDetail = () => import('@/views/sysConfig/emailConfig/detail.vue')

const CreditCustomerConfig = () => import('@/views/sysConfig/CreditCustomerConfig/index')

const LogisticsTransferConfig = () => import('@/views/sysConfig/logisticsTransferConfig/index.vue')
const LogisticsTransferConfigDetail = () => import('@/views/sysConfig/logisticsTransferConfig/detail.vue')

const SeriesCustomerQuery = () => import('@/views/sysConfig/seriesCustomer/index.vue')
const ControlConfigList = () => import('@/views/sysConfig/controlConfigList/index.vue')

const WarehouseTransferConfig = () => import('@/views/sysConfig/warehouseTransferConfig/index.vue')
const SupplierSteelTemplateConfig = () => import('@/views/sysConfig/supplierSteelTemplateConfig/index.vue')
const WarehouselimitOutConfig = () => import('@/views/sysConfig/warehouselimitOutConfig/index.vue')

const MallsalesLimitConfig = () => import('@/views/sysConfig/MallsalesLimitConfig/index.vue')

const IntelligentPricingConfig = () => import('@/views/sysConfig/intelligentPricingConfig/index.vue')
const IntelligentPricingConfigDetail = () => import('@/views/sysConfig/intelligentPricingConfig/detail.vue')

// 基础资料模块
export default [
  // 客商维护
  {
    path: '/customerAlloc',
    name: 'CustomerAlloc',
    component: CustMaintain,
    meta: { keepAlive: true }
  },
  // 客商维护详情页
  {
    path: '/customerAllocDetail/:code',
    name: 'CustomerAllocDetail',
    component: CustMaintainDetail,
    meta: { keepAlive: true }
  },
  // 基础码表-仓库管理
  {
    path: '/basicWarehouse',
    name: 'BasicWarehouse',
    component: BasicWarehouse,
    meta: { keepAlive: true }
  },
  // 基础码表-仓库管理详情
  {
    path: '/basicWarehouseDetail/:code',
    name: 'BasicWarehouseDetail',
    component: BasicWarehouseDetail,
    meta: { keepAlive: true }
  },
  // 单据管理
  {
    path: '/billManager',
    name: 'BillManager',
    component: BillManager,
    meta: { keepAlive: true }
  },
  // 单据管理详情
  {
    path: '/billManagerDetail/:code',
    name: 'BillManagerDetail',
    component: BillManagerDetail,
    meta: { keepAlive: true }
  },
  {
    path: '/dataPermission',
    name: 'DataPermission',
    component: DataPermission,
    meta: { keepAlive: true }
  },
  // 审批工作台
  {
    path: '/ApprovalBench',
    name: 'ApprovalBench',
    component: ApprovalBench,
    meta: { keepAlive: true }
  },
  {
    path: '/approvalQuery',
    name: 'ApprovalQuery',
    component: ApprovalQuery,
    meta: { keepAlive: true }
  },
  {
    path: '/roleManager',
    name: 'RoleManager',
    component: RoleManager,
    meta: { keepAlive: true }
  },

  // 合同附件收件人管理
  {
    path: '/customer_recipient_list',
    name: 'CustomerRecipientList',
    component: CustomerRecipientList,
    meta: { keepAlive: true }
  },
  // 合同附件收件人管理详情
  {
    path: '/customer_recipient_detail/:code',
    name: 'CustomerRecipientDetail',
    component: CustomerRecipientDetail,
    meta: { keepAlive: true }
  },
  // 客商出仓配置管理
  {
    path: '/customerOutConfig',
    name: 'CustomerOutConfig',
    component: CustomerOutConfig,
    meta: { keepAlive: true }
  },
  // 客商出仓配置管理详情
  {
    path: '/customerOutConfigDetail/:code',
    name: 'CustomerOutConfigDetail',
    component: CustomerOutConfigDetail,
    meta: { keepAlive: true }
  },
  // 异常请求工作台
  {
    path: '/excRequestWorkbench',
    name: 'ExcRequestWorkbench',
    component: ExcRequestWorkbench,
    meta: { keepAlive: true }
  },
  // 异常请求详情
  {
    path: '/excRequestDetail/:code',
    name: 'ExcRequestDetail',
    component: ExcRequestDetail,
    meta: { keepAlive: true }
  },

  // 基础码表-仓库配置表
  {
    path: '/warehouseConfig',
    name: 'WarehouseConfig',
    component: WarehouseConfig,
    meta: { keepAlive: true }
  },
  {
    path: '/rpaWarehouseConfig',
    name: 'RpaWarehouseConfig',
    component: RpaWarehouseConfig,
    meta: { keepAlive: true }
  },
  {
    path: '/rpaWarehouseConfigDetail/:code',
    name: 'RpaWarehouseConfigDetail',
    component: RpaWarehouseConfigDetail,
    meta: { keepAlive: true }
  },
  {
    path: '/emailConfig',
    name: 'EmailConfig',
    component: EmailConfig,
    meta: { keepAlive: true }
  },
  {
    path: '/emailConfigDetail/:code',
    name: 'EmailConfigDetail',
    component: EmailConfigDetail,
    meta: { keepAlive: true }
  },

  {
    path: '/creditCustomerConfig',
    name: 'CreditCustomerConfig',
    component: CreditCustomerConfig,
    meta: { keepAlive: true }
  },

  {
    path: '/logisticsTransferConfig',
    name: 'LogisticsTransferConfig',
    component: LogisticsTransferConfig,
    meta: { keepAlive: true }
  },
  {
    path: '/logisticsTransferConfigDetail/:code',
    name: 'LogisticsTransferConfigDetail',
    component: LogisticsTransferConfigDetail,
    meta: { keepAlive: true }
  },
  {
    path: '/seriesCustomerQuery',
    name: 'SeriesCustomerQuery',
    component: SeriesCustomerQuery,
    meta: { keepAlive: true }
  },

  {
    path: '/controlConfigList',
    name: 'ControlConfigList',
    component: ControlConfigList,
    meta: { keepAlive: true }
  },

  {
    path: '/warehouseTransferConfig',
    name: 'WarehouseTransferConfig',
    component: WarehouseTransferConfig,
    meta: { keepAlive: true }
  },
  {
    path: '/supplierSteelTemplateConfig',
    name: 'SupplierSteelTemplateConfig',
    component: SupplierSteelTemplateConfig,
    meta: { keepAlive: true }
  },
  {
    path: '/warehouselimitOutConfig',
    name: 'WarehouselimitOutConfig',
    component: WarehouselimitOutConfig,
    meta: { keepAlive: true }
  },
  {
    path: '/mallsalesLimitConfig',
    name: 'mallsalesLimitConfig',
    component: MallsalesLimitConfig,
    meta: { keepAlive: true }
  },
  {
    path: '/intelligentPricingConfig',
    name: 'IntelligentPricingConfig',
    component: IntelligentPricingConfig,
    meta: { keepAlive: true }
  },
  {
    path: '/intelligentPricingConfigDetail/:code',
    name: 'IntelligentPricingConfigDetail',
    component: IntelligentPricingConfigDetail,
    meta: { keepAlive: true }
  }

]
