const UserList = () => import('@/views/sysConfig/User/Index.vue')
const userEditUserDialog = () =>
  import('@/views/sysConfig/User/EditUserDialog.vue')
const CostOrgegl = () => import('@/views/sysConfig/BasicInfo/Cost/Index')
const EditCostOrgDialog = () =>
  import('@/views/sysConfig/BasicInfo/Cost/EditCostOrgDialog')
const CorpTaxControl = () =>
  import('@/views/sysConfig/BasicInfo/CorpTaxControl/Index')
const EditCorpTaxDialog = () =>
  import('@/views/sysConfig/BasicInfo/CorpTaxControl/EditCorpTaxDialog')
const BillWorkflowV2 = () => import('@/views/sysConfig/BillWorkflowV2/Index')

const OriginConfig = () => import('@/views/sysConfig/OriginConfig/index.vue')
const OriginConfigDetail = () => import('@/views/sysConfig/OriginConfig/detail')
const StaffingList = () =>
  import('@/views/sysConfig/personnel/staffingList.vue')
const CustomerAnalysis = () =>
  import('@/views/sysConfig/customerAnalysis/Index.vue')
const CustomerAnalysisDetail = () =>
  import('@/views/sysConfig/customerAnalysis/Detail.vue')
const CustomerScoreList = () =>
  import('@/views/sysConfig/customerScore/index.vue')
const CustomerHistoryScoreList = () =>
  import('@/views/sysConfig/customerScore/history.vue')
const CustomerRating = () =>
  import('@/views/sysConfig/customerRating/customerRating.vue')
const DivisionSetup = () => import('@/views/sysConfig/divisionSetup/index.vue')

const asynchronousInterface = () =>
  import('@/views/sysConfig/asynchronousInterface/AsynchronousInterface.vue')

const Announcementlist = () =>
  import('@/views/sysConfig/announcementlist/index.vue')
const AnnounceDetail = () =>
  import('@/views/sysConfig/announcementlist/detail/index.vue')
const messagelist = () => import('@/views/sysConfig/messagelist/index.vue')
const MessageControl = () =>
  import('@/views/sysConfig/MessageControl/index.vue')

const MessageManagement = () =>
  import('@/views/sysConfig/messageManagement/index.vue')
const MessageDetail = () =>
  import('@/views/sysConfig/messageManagement/detail/index.vue')
const SteelMillDockingConfig = () =>
  import('@/views/sysConfig/steelMillDockingConfig/index.vue')
const SteelMillDockingConfigDetail = () =>
  import('@/views/sysConfig/steelMillDockingConfig/detail.vue')
export default [
  {
    path: '/userList',
    name: 'UserList',
    component: UserList,
    meta: { keepAlive: true }
  },
  {
    path: '/userEditUserDialog/:acc',
    name: 'userEditUserDialog',
    component: userEditUserDialog,
    meta: { keepAlive: true }
  },
  // 成本结算组织
  {
    path: '/costOrgegl',
    name: 'CostOrgegl',
    component: CostOrgegl,
    meta: { keepAlive: true }
  },
  // 成本结算组织--详情
  {
    path: '/EditCostOrgDialog/:code',
    name: 'EditCostOrgDialog',
    component: EditCostOrgDialog,
    meta: { keepAlive: true }
  },
  // 法人税控信息
  {
    path: '/corpTaxControl',
    name: 'CorpTaxControl',
    component: CorpTaxControl,
    meta: { keepAlive: true }
  },
  {
    path: '/EditCorpTaxDialog/:code',
    name: 'EditCorpTaxDialog',
    component: EditCorpTaxDialog,
    meta: { keepAlive: true }
  },
  {
    path: '/billWorkflowV2',
    name: 'BillWorkflowV2',
    component: BillWorkflowV2,
    meta: { keepAlive: true }
  },
  {
    path: '/originConfig',
    name: 'OriginConfig',
    component: OriginConfig,
    meta: { keepAlive: true }
  },
  {
    path: '/originConfigDetail/:code',
    name: 'OriginConfigDetail',
    component: OriginConfigDetail,
    meta: { keepAlive: true }
  },
  {
    path: '/customerAnalysis',
    name: 'CustomerAnalysis',
    component: CustomerAnalysis,
    meta: { keepAlive: true }
  },
  {
    path: '/customerAnalysisDateil/:code',
    name: 'CustomerAnalysisDetail',
    component: CustomerAnalysisDetail,
    meta: { keepAlive: true }
  },
  {
    path: '/staffingList',
    name: 'StaffingList',
    component: StaffingList,
    meta: { keepAlive: true }
  },
  {
    path: '/customerScoreList',
    name: 'customerScoreList',
    component: CustomerScoreList,
    meta: { keepAlive: true }
  },
  {
    path: '/customerHistoryScoreList/:code',
    name: 'customerHistoryScoreList',
    component: CustomerHistoryScoreList,
    meta: { keepAlive: true }
  },
  {
    path: '/customerRating',
    name: 'customerRating',
    component: CustomerRating,
    meta: { keepAlive: true }
  },
  {
    path: '/divisionSetup',
    name: 'divisionSetup',
    component: DivisionSetup,
    meta: { keepAlive: true }
  },

  // 异步事件工作台
  {
    path: '/asynchronousInterface',
    name: 'asynchronousInterface',
    component: asynchronousInterface,
    meta: { keepAlive: true }
  },
  // 公告管理
  {
    path: '/announcementlist',
    name: 'announcementlist',
    component: Announcementlist,
    meta: { keepAlive: true }
  },
  // 公告管理详情
  {
    path: '/AnnounceDetail/:code',
    name: 'AnnounceDetail',
    component: AnnounceDetail,
    meta: { keepAlive: true }
  },
  // 消息列表
  {
    path: '/messagelist',
    name: 'messagelist',
    component: messagelist,
    meta: { keepAlive: true }
  },
  // 消息列表
  {
    path: '/MessageControl',
    name: 'MessageControl',
    component: MessageControl,
    meta: { keepAlive: true }
  },
  // 通知列表
  {
    path: '/MessageManagement',
    name: 'MessageManagement',
    component: MessageManagement,
    meta: { keepAlive: true }
  },
  // 通知列表详情
  {
    path: '/MessageDetail/:code',
    name: 'MessageDetail',
    component: MessageDetail,
    meta: { keepAlive: true }
  },
  {
    path: '/steelMillDockingConfig',
    name: 'SteelMillDockingConfig',
    component: SteelMillDockingConfig,
    meta: { keepAlive: true }
  },
  {
    path: '/steelMillDockingConfigDetail/:code',
    name: 'SteelMillDockingConfigDetail',
    component: SteelMillDockingConfigDetail,
    meta: { keepAlive: true }
  }
]
