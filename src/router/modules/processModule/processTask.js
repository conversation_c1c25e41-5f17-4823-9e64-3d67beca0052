const ProcessTaskManage = () => import('@/views/processModule/processTask/index.vue')
const ProcessTaskWorkbench = () => import('@/views/processModule/processTask/workbench.vue')
const ProcessTaskDetail = () => import('@/views/processModule/processTask/detail/index.vue')

export default [
  {
    path: '/processTaskManage',
    name: 'ProcessTaskManage',
    component: ProcessTaskManage,
    meta: { keepAlive: true }
  },
  {
    path: '/processTaskWorkbench',
    name: 'ProcessTaskWorkbench',
    component: ProcessTaskWorkbench,
    meta: { keepAlive: true }
  },
  {
    path: '/processTaskDetail/:code',
    name: 'ProcessTaskDetail',
    component: ProcessTaskDetail,
    meta: { keepAlive: true }
  }
]
