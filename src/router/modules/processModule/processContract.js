const ProcessContractManage = () => import('@/views/processModule/processContract/index.vue')
const ProcessContractDetail = () => import('@/views/processModule/processContract/detail/index.vue')

export default [
  {
    path: '/processContractManage',
    name: 'ProcessContractManage',
    component: ProcessContractManage,
    meta: { keepAlive: true }
  },
  {
    path: '/processContractDetail/:code',
    name: 'ProcessContractDetail',
    component: ProcessContractDetail,
    meta: { keepAlive: true }
  }
]
