const FinishedProductArrivalManage = () => import('@/views/processModule/finishedProductArrival/index.vue')
const FinishedProductArrivalWorkbench = () => import('@/views/processModule/finishedProductArrival/workbench.vue')
const FinishedProductArrivalDetail = () => import('@/views/processModule/finishedProductArrival/detail/index.vue')

export default [
  {
    path: '/finishedProductArrivalManage',
    name: 'FinishedProductArrivalManage',
    component: FinishedProductArrivalManage,
    meta: { keepAlive: true }
  },
  {
    path: '/finishedProductArrivalWorkbench',
    name: 'FinishedProductArrivalWorkbench',
    component: FinishedProductArrivalWorkbench,
    meta: { keepAlive: true }
  },
  {
    path: '/finishedProductArrivalDetail/:code',
    name: 'FinishedProductArrivalDetail',
    component: FinishedProductArrivalDetail,
    meta: { keepAlive: true }
  }
]
