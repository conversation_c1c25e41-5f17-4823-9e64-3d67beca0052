const ProcessWarehouseConfig = () => import('@/views/processModule/processWarehouseConfig/index.vue')
const ProcessWarehouseDetail = () => import('@/views/processModule/processWarehouseConfig/detail/index.vue')

export default [
  {
    path: '/processWarehouseConfig',
    name: 'ProcessWarehouseConfig',
    component: ProcessWarehouseConfig,
    meta: { keepAlive: true }
  },
  {
    path: '/processWarehouseDetail/:code',
    name: 'ProcessWarehouseDetail',
    component: ProcessWarehouseDetail,
    meta: { keepAlive: true }
  }
]
