// 钢厂信息管理

const aggregateMethodContract = () =>
  import('@/views/aggregateMethodContract/index.vue')

const aggregateMethodContractDetail = () =>
  import('@/views/aggregateMethodContract/PreContractDetail.vue')

const pactAide = () => import('@/views/pactAide/index.vue')

const pactAideDetail = () => import('@/views/pactAide/detail/index.vue')

export default [
  // 合同关系配置工作台
  {
    path: '/aggregateMethodContract',
    name: 'AggregateMethodContract',
    component: aggregateMethodContract,
    meta: { keepAlive: true }
  },
  // 预录入合同详情
  {
    path: '/aggregateMethodContractDetail/:code',
    name: 'aggregateMethodContractDetail',
    component: aggregateMethodContractDetail,
    meta: { keepAlive: true }
  },
  // 合同编辑列表
  {
    path: '/pactAide',
    name: 'PactAide',
    component: pactAide,
    meta: { keepAlive: true }
  },
  // 预录入合同编辑详情
  {
    path: '/pactAideDetail/:code',
    name: 'PactAideDetail',
    component: pactAideDetail,
    meta: { keepAlive: true }
  },
  // 预录入合同新增
  {
    path: '/pactAideDetail/add',
    name: 'PactAideDetailAdd',
    component: pactAideDetail,
    meta: { keepAlive: true }
  }
]
