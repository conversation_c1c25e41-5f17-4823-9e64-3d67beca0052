/*
 * @Description: 合同管理模块路由
 */
const DomesticProcurementList = () => import('@/views/contract/domesticProcurement/Index.vue')
const DomesticSalesList = () => import('@/views/contract/domesticSales/Index.vue')
const DomesticSalesEdit = () => import('@/views/contract/domesticSales/DomesticSalesEdit.vue')
const LogisticsList = () => import('@/views/contract/logistics/Index.vue')
const LogisticsEdit = () => import('@/views/contract/logistics/LogisticsEdit.vue')
const OrderPlanWaitBuildList = () => import('@/views/contract/orderPlan/waitBuild/Index.vue')
const OrderPlanSearchList = () => import('@/views/contract/orderPlan/planSearch/Index.vue')
const planSearchEdit = () => import('@/views/contract/orderPlan/components/Edit/Edit.vue')
const DomesticProcurementEdit = () => import('@/views/contract/domesticProcurement/DomesticProcurementEdit.vue')
const BottomTon = () => import('@/views/contract/futures/bottomTon/index.vue')
const BottomTonDetail = () => import('@/views/contract/futures/bottomTon/detail.vue')
const PointPriceManage = () => import('@/views/contract/futures/pointPrice/index.vue')
const PointPriceWb = () => import('@/views/contract/futures/pointPrice/workbench.vue')
const PointPriceDetail = () => import('@/views/contract/futures/pointPrice/detail/index.vue')
const ContainsTrade = () => import('@/views/contract/futures/containsTrade/index.vue')
const ContainsTradeWb = () => import('@/views/contract/futures/containsTrade/workbench.vue')
const ContainsTradeDetail = () => import('@/views/contract/futures/containsTrade/detail/index.vue')

const ProfitLossProcess = () => import('@/views/contract/futures/profitLossProcess/index.vue')
const CostComposition = () => import('@/views/contract/costComposition/index.vue')
const CostCompositionDetail = () => import('@/views/contract/costComposition/detail/index.vue')

const ProcessAgreement = () => import('@/views/contract/processAgreement/index.vue')
const ProcessAgreementDetail = () => import('@/views/contract/processAgreement/detail/index.vue')

const ConstractRoutes = [
  // 国内采购
  {
    path: '/domesticProcurementList',
    name: 'DomesticProcurementList',
    component: DomesticProcurementList,
    meta: { keepAlive: true }
  },
  // 国内采购合同详情
  {
    path: '/DomesticProcurementEdit/:code',
    name: 'DomesticProcurementEdit',
    component: DomesticProcurementEdit,
    meta: { keepAlive: true }
  },
  // 国内销售
  {
    path: '/domesticSalesList',
    name: 'DomesticSalesList',
    component: DomesticSalesList,
    meta: { keepAlive: true }
  },
  // 国内销售合同详情
  {
    path: '/DomesticSalesEdit/:code',
    name: 'DomesticSalesEdit',
    component: DomesticSalesEdit,
    meta: { keepAlive: true }
  },
  // 物流运输
  {
    path: '/logisticsList',
    name: 'LogisticsList',
    component: LogisticsList,
    meta: { keepAlive: true }
  },
  // 物流运输合同详情
  {
    path: '/LogisticsEdit/:code',
    name: 'LogisticsEdit',
    component: LogisticsEdit,
    meta: { keepAlive: true }
  },
  // 待生成订货计划工作台
  {
    path: '/orderPlanWaitBuildList',
    name: 'OrderPlanWaitBuildList',
    component: OrderPlanWaitBuildList,
    meta: { keepAlive: true }
  },
  // 订货计划
  {
    path: '/orderPlanSearchList',
    name: 'OrderPlanSearchList',
    component: OrderPlanSearchList,
    meta: { keepAlive: true }
  },
  // 订货计划详情
  {
    path: '/planSearchEdit/:code',
    name: 'PlanSearchEdit',
    component: planSearchEdit,
    meta: { keepAlive: true }
  },
  // 合同底吨
  {
    path: '/bottomTon',
    name: 'BottomTon',
    component: BottomTon,
    meta: { keepAlive: true }
  },
  // 合同底吨详情
  {
    path: '/bottomTonDetail/:code',
    name: 'BottomTonDetail',
    component: BottomTonDetail,
    meta: { keepAlive: true }
  },
  // 点价平仓工作台
  {
    path: '/pointPriceWb',
    name: 'PointPriceWb',
    component: PointPriceWb,
    meta: { keepAlive: true }
  },
  // 点价平仓单管理
  {
    path: '/pointPriceManage',
    name: 'PointPriceManage',
    component: PointPriceManage,
    meta: { keepAlive: true }
  },
  {
    path: '/pointPriceDetail/:code',
    name: 'PointPriceDetail',
    component: PointPriceDetail,
    meta: { keepAlive: true }
  },
  {
    path: '/containsTradeWb',
    name: 'ContainsTradeWb',
    component: ContainsTradeWb,
    meta: { keepAlive: true }
  },
  {
    path: '/containsTrade',
    name: 'ContainsTrade',
    component: ContainsTrade,
    meta: { keepAlive: true }
  },
  {
    path: '/containsTradeDetail/:code',
    name: 'ContainsTradeDetail',
    component: ContainsTradeDetail,
    meta: { keepAlive: true }
  },
  {
    path: '/profitLossProcess',
    name: 'ProfitLossProcess',
    component: ProfitLossProcess,
    meta: { keepAlive: true }
  },
  {
    path: '/costComposition',
    name: 'CostComposition',
    component: CostComposition,
    meta: { keepAlive: true }
  },
  {
    path: '/costCompositionDetail/:code',
    name: 'CostCompositionDetail',
    component: CostCompositionDetail,
    meta: { keepAlive: true }
  },
  {
    path: '/processAgreement',
    name: 'ProcessAgreement',
    component: ProcessAgreement,
    meta: { keepAlive: true }
  },
  {
    path: '/processAgreementDetail/:code',
    name: 'ProcessAgreementDetail',
    component: ProcessAgreementDetail,
    meta: { keepAlive: true }
  }
]

export default ConstractRoutes
