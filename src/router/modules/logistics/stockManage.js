// 存货管理
const EnterWarehouseWorkbench = () => import('@/views/logistics/stockManage/enterWarehouse/index')
const EnterWarehouseWorkbenchV2 = () => import('@/views/logistics/stockManage/enterWarehouse/indexV2')
const EnterWarehouseManage = () => import('@/views/logistics/stockManage/enterWarehouse/manage')
const EnterWarehouseManageDetail = () => import('@/views/logistics/stockManage/enterWarehouse/detail')
const WarehouseTransferWorkbench = () => import('@/views/logistics/inventoryManage/warehouseTransfer/workbench')
const WarehouseTransferManage = () => import('@/views/logistics/inventoryManage/warehouseTransfer/manage')
const WarehouseTransferManageRpa = () => import('@/views/logistics/inventoryManage/warehouseTransfer/manage/rpa')
const WarehouseTransferManagedetailDialog = () => import('@/views/logistics/inventoryManage/warehouseTransfer/detailDialog')
const OutWarehouseWorkbench = () => import('@/views/logistics/stockManage/outWarehouse/index')
const OutWarehouseManage = () => import('@/views/logistics/stockManage/outWarehouse/manage')
const OutWarehouseManageDetail = () => import('@/views/logistics/stockManage/outWarehouse/detail')
const InventoryAdjustWorkbench = () => import('@/views/logistics/inventoryManage/inventoryAdjust/workbench')
const InventoryAdjustManage = () => import('@/views/logistics/inventoryManage/inventoryAdjust/manage')
const InventoryAdjustDetail = () => import('@/views/logistics/inventoryManage/inventoryAdjust/detailDialog')

// 第三方出仓
const ThirdOutWarehouseWorkbench = () => import('@/views/logistics/stockManage/thirdOutWarehouse/index')
const ThirdOutWarehouseManage = () => import('@/views/logistics/stockManage/thirdOutWarehouse/manage')
const ThirdOutWarehouseManageDetail = () => import('@/views/logistics/stockManage/thirdOutWarehouse/detail')

const DivideGoodsWorkbench = () => import('@/views/logistics/stockManage/divideGoods/workbench')
const DivideGoodsManage = () => import('@/views/logistics/stockManage/divideGoods/index')
const DivideGoodsManageDetail = () => import('@/views/logistics/stockManage/divideGoods/detail/index')

const MandatoryWriteoff = () => import('@/views/logistics/stockManage/mandatoryWriteoff/index')
const MandatoryWriteoffDetail = () => import('@/views/logistics/stockManage/mandatoryWriteoff/detail/index')
export default [
  // 进仓调整单工作台
  {
    path: '/enterWarehouseWorkbench',
    name: 'EnterWarehouseWorkbench',
    component: EnterWarehouseWorkbench,
    meta: { keepAlive: true }
  },
  // 进仓调整单工作台(二期V2)
  {
    path: '/enterWarehouseWorkbenchV2',
    name: 'EnterWarehouseWorkbenchV2',
    component: EnterWarehouseWorkbenchV2,
    meta: { keepAlive: true }
  },
  // 进仓调整单管理
  {
    path: '/enterWarehouseManage',
    name: 'EnterWarehouseManage',
    component: EnterWarehouseManage,
    meta: { keepAlive: true }
  },
  // 进仓调整单-详情
  {
    path: '/enterWarehouseManageDetail/:code',
    name: 'EnterWarehouseManageDetail',
    component: EnterWarehouseManageDetail,
    meta: { keepAlive: true }
  },
  // 移仓调拨工作台
  {
    path: '/warehouseTransferWorkbench',
    name: 'WarehouseTransferWorkbench',
    component: WarehouseTransferWorkbench,
    meta: { keepAlive: true }
  },
  // 移仓调拨单管理
  {
    path: '/warehouseTransferManage',
    name: 'WarehouseTransferManage',
    component: WarehouseTransferManage,
    meta: { keepAlive: true }
  },
  // RPA移仓调拨单管理
  {
    path: '/warehouseTransferManageRpa',
    name: 'WarehouseTransferManageRpa',
    component: WarehouseTransferManageRpa,
    meta: { keepAlive: true }
  },
  // 移仓调拨单--详情
  {
    path: '/warehouseTransferManagedetailDialog/:code',
    name: 'WarehouseTransferManagedetailDialog',
    component: WarehouseTransferManagedetailDialog,
    meta: { keepAlive: true }
  },
  // 出仓调整单工作台
  {
    path: '/outWarehouseWorkbench',
    name: 'OutWarehouseWorkbench',
    component: OutWarehouseWorkbench,
    meta: { keepAlive: true }
  },
  // 出仓调整单管理
  {
    path: '/outWarehouseManage',
    name: 'OutWarehouseManage',
    component: OutWarehouseManage,
    meta: { keepAlive: true }
  },
  // 出仓调整单-详情
  {
    path: '/outWarehouseManageDetail/:code',
    name: 'OutWarehouseManageDetail',
    component: OutWarehouseManageDetail,
    meta: { keepAlive: true }
  },
  // 存货合同调整工作台
  {
    path: '/inventoryAdjustWorkbench',
    name: 'InventoryAdjustWorkbench',
    component: InventoryAdjustWorkbench,
    meta: { keepAlive: true }
  },
  // 存货合同调整单管理
  {
    path: '/inventoryAdjustManage',
    name: 'InventoryAdjustManage',
    component: InventoryAdjustManage,
    meta: { keepAlive: true }
  },
  // 存货合同调整单-详情
  {
    path: '/InventoryAdjustDetail/:code',
    name: 'InventoryAdjustDetail',
    component: InventoryAdjustDetail,
    meta: { keepAlive: true }
  },

  // 第三方出仓调整单工作台
  {
    path: '/thirdOutWarehouseWorkbench',
    name: 'ThirdOutWarehouseWorkbench',
    component: ThirdOutWarehouseWorkbench,
    meta: { keepAlive: true }
  },
  // 第三方出仓调整单管理
  {
    path: '/thirdOutWarehouseManage',
    name: 'ThirdOutWarehouseManage',
    component: ThirdOutWarehouseManage,
    meta: { keepAlive: true }
  },
  // 第三方出仓调整单-详情
  {
    path: '/thirdOutWarehouseManageDetail/:code',
    name: 'ThirdOutWarehouseManageDetail',
    component: ThirdOutWarehouseManageDetail,
    meta: { keepAlive: true }
  },

  // 库存调整工作台
  {
    path: '/divideGoodsWorkbench',
    name: 'DivideGoodsWorkbench',
    component: DivideGoodsWorkbench,
    meta: { keepAlive: true }
  },
  {
    path: '/divideGoodsManage',
    name: 'DivideGoodsManage',
    component: DivideGoodsManage,
    meta: { keepAlive: true }
  },
  {
    path: '/divideGoodsManageDetail/:code',
    name: 'DivideGoodsManageDetail',
    component: DivideGoodsManageDetail,
    meta: { keepAlive: true }
  },

  {
    path: '/mandatoryWriteoff',
    name: 'MandatoryWriteoff',
    component: MandatoryWriteoff,
    meta: { keepAlive: true }
  },
  {
    path: '/mandatoryWriteoffDetail/:code',
    name: 'MandatoryWriteoffDetail',
    component: MandatoryWriteoffDetail,
    meta: { keepAlive: true }
  }

]
