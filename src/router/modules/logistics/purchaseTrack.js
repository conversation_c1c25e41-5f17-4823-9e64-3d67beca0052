/*
 * @Author: 沈鹭荣
 * @Date: 2021-01-27 15:34:56
 * @LastEditors: 沈鹭荣
 * @LastEditTime: 2021-02-02 15:03:13
 * @Description:
 */
const SteelPlantWorkbench = () => import('@/views/logistics/registrationData/steelPlantList/workbench')
const steelPlantListManage = () => import('@/views/logistics/registrationData/steelPlantList/manage/index')
const SteelPlantListDetail = () => import('@/views/logistics/registrationData/steelPlantList/manage/detail')
const FactoryData = () => import('@/views/logistics/purchaseTrack/intransitGoods/factoryData')
const IntransitList = () => import('@/views/logistics/purchaseTrack/intransitGoods/intransit')
const IntransitManage = () => import('@/views/logistics/purchaseTrack/intransitGoods/intransitManage/index')
const intransitDetail = () => import('@/views/logistics/purchaseTrack/intransitGoods/intransitDialog/index')
const OnArrivalList = () => import('@/views/logistics/registrationData/purchaseArrival/onArrival/Index.vue')
const ArrivalNoteList = () => import('@/views/logistics/registrationData/purchaseArrival/onArrival/arrivalNote/Index.vue')
const ArrivalNoteDetail = () => import('@/views/logistics/registrationData/purchaseArrival/onArrival/arrivalNote/Detail')
// 采购物流跟踪
export default [
  // 钢厂可直放清单工作台
  {
    path: '/steelPlantWorkbench',
    name: 'SteelPlantWorkbench',
    component: SteelPlantWorkbench,
    meta: { keepAlive: true }
  },
  // 钢厂可直放清单
  {
    path: '/steelPlantListManage',
    name: 'SteelPlantListManage',
    component: steelPlantListManage,
    meta: { keepAlive: true }
  },
  // 钢厂可直放清单详情
  {
    path: '/steelPlantListDetail/:code',
    name: 'SteelPlantListDetail',
    component: SteelPlantListDetail,
    meta: { keepAlive: true }
  },
  // 在途货物工作台 - 出厂数据
  {
    path: '/factoryData',
    name: 'FactoryData',
    component: FactoryData,
    meta: { keepAlive: true }
  },
  // 在途货物工作台 - 在途单
  {
    path: '/intransitList',
    name: 'IntransitList',
    component: IntransitList,
    meta: { keepAlive: true }
  },
  // 在途单管理工作台
  {
    path: '/intransitManage',
    name: 'IntransitManage',
    component: IntransitManage,
    meta: { keepAlive: true }
  },
  // 在途单管理详情
  {
    path: '/intransitDetail/:code',
    name: 'intransitDetail',
    component: intransitDetail,
    meta: { keepAlive: true }
  },
  // 采购到货单工作台
  {
    path: '/onArrivalList',
    name: 'OnArrivalList',
    component: OnArrivalList,
    meta: { keepAlive: true }
  },
  // 采购到货单管理
  {
    path: '/arrivalNoteList',
    name: 'ArrivalNoteList',
    component: ArrivalNoteList,
    meta: { keepAlive: true }
  },
  // 采购到货单管理详情
  {
    path: '/arrivalNoteDetail/:code',
    name: 'ArrivalNoteDetail',
    component: ArrivalNoteDetail,
    meta: { keepAlive: true }
  }
]
