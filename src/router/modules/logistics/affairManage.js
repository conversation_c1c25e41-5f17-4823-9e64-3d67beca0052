// 物流事务管理
const LongTermConsignorManage = () => import('@/views/logistics/affairManage/longTermConsignor/index')
const ConsignorManageDetail = () => import('@/views/logistics/affairManage/longTermConsignor/detail')

export default [
  // 长期提货人管理
  {
    path: '/longTermConsignorManage',
    name: 'LongTermConsignorManage',
    component: LongTermConsignorManage,
    meta: { keepAlive: true }
  },
  // 长期提货人管理详情
  {
    path: '/ConsignorManageDetail/:code',
    name: 'ConsignorManageDetail',
    component: ConsignorManageDetail,
    meta: { keepAlive: true }
  }
]
