/*
 * @Author: 沈鹭荣
 * @Date: 2021-01-12 10:51:24
 * @LastEditors: 沈鹭荣
 * @LastEditTime: 2021-02-22 16:25:40
 * @Description:
 */
// 销售发货管理)
const SalesdeliveryWorkbench = () => import('@/views/logistics/saleDelivery/saleorder/salesdeliveryWorkbench/Index.vue')
const salesdeliveryDialog = () => import('@/views/logistics/saleDelivery/saleorder/salesdeliveryList/salesdeliveryDialog/index.vue')
const Salesdeliverylist = () => import('@/views/logistics/saleDelivery/saleorder/salesdeliveryList/Index.vue')
const Receiptlist = () => import('@/views/logistics/saleDelivery/receipt/index.vue')
const ReceiptlistDialog = () => import('@/views/logistics/saleDelivery/receipt/salesdeliveryDialog/index.vue')

const Rpasalesdeliverylist = () => import('@/views/logistics/saleDelivery/rpa/index.vue')
const SalesdeliveryResaleList = () => import('@/views/logistics/saleDelivery/saleorder/salesdeliveryResaleList/Index.vue')
const SalesdeliveryResaleDialog = () => import('@/views/logistics/saleDelivery/saleorder/salesdeliveryResaleList/salesdeliveryDialog/index.vue')

const NonpartysalesdeliveryWorkbench = () => import('@/views/logistics/saleDelivery/saleorder/nonpartysalesdeliveryWorkbench/Index.vue') // 第三方发货工作台
const Nonpartysalesdeliverylist = () => import('@/views/logistics/saleDelivery/saleorder/nonpartysalesdeliverylist/Index.vue') // 第三方发货单管理
const NonpartysalesdeliveryDialog = () => import('@/views/logistics/saleDelivery/saleorder/nonpartysalesdeliverylist/salesdeliveryDialog/index.vue')

const RetailDeliveryWorkbench = () => import('@/views/logistics/saleDelivery/saleorder/retailDeliveryWorkbench/Index.vue')
const RetailDeliveryList = () => import('@/views/logistics/saleDelivery/saleorder/retailDeliveryList/Index.vue')
const RetailDeliveryDialog = () => import('@/views/logistics/saleDelivery/saleorder/retailDeliveryList/salesdeliveryDialog/index.vue')

const LinkmanWorkbench = () => import('@/views/logistics/saleDelivery/saleorder/linkmanManage/workbench.vue')
const LinkmanManage = () => import('@/views/logistics/saleDelivery/saleorder/linkmanManage/index.vue')
const LinkmanDetail = () => import('@/views/logistics/saleDelivery/saleorder/linkmanManage/detail/index.vue')

const IntentListManagement = () => import('@/views/logistics/saleDelivery/saleorder/intentListManagement/index.vue')
const ShelfOrderManagement = () => import('@/views/logistics/saleDelivery/saleorder/shelfOrderManagement/index.vue')

const MallRetailOrder = () => import('@/views/logistics/saleDelivery/saleorder/mallRetailOrder/Index.vue')
const MallRetailOrderDetail = () => import('@/views/logistics/saleDelivery/saleorder/mallRetailOrder/detail/index.vue')

const ShelfOrderWorkbench = () => import('@/views/logistics/saleDelivery/saleorder/shelfOrderManagement/workbench.vue')

const CombineSign = () => import('@/views/logistics/saleDelivery/combineSign/index.vue')
const CombineSignDetail = () => import('@/views/logistics/saleDelivery/combineSign/detail/index.vue')

const SeparateWorkbench = () => import('@/views/logistics/saleDelivery/saleorder/separateWorkbench/index.vue')

export default [
  // 销售待发货管理
  {
    path: '/salesdeliverylist',
    name: 'Salesdeliverylist',
    component: Salesdeliverylist,
    meta: { keepAlive: true }
  },
  // 销售待发货工作台
  {
    path: '/salesdeliveryWorkbench',
    name: 'SalesdeliveryWorkbench',
    component: SalesdeliveryWorkbench,
    meta: { keepAlive: true }
  },
  // 销售待发货工作台-详情
  {
    path: '/salesdeliveryDialog/:code',
    name: 'SalesdeliveryDialog',
    component: salesdeliveryDialog,
    meta: { keepAlive: true }
  },
  // 收获收据
  {
    path: '/receiptlist',
    name: 'Receiptlist',
    component: Receiptlist,
    meta: { keepAlive: true }
  },
  // 收获收据--详情
  {
    path: '/ReceiptlistDialog/:code',
    name: 'ReceiptlistDialog',
    component: ReceiptlistDialog,
    meta: { keepAlive: true }
  },

  {
    path: '/rpasalesdeliverylist',
    name: 'Rpasalesdeliverylist',
    component: Rpasalesdeliverylist,
    meta: { keepAlive: true }
  },

  // 转售单管理
  {
    path: '/salesdeliveryResaleList',
    name: 'SalesdeliveryResaleList',
    component: SalesdeliveryResaleList,
    meta: { keepAlive: true }
  },
  // 转售单详情
  {
    path: '/salesdeliveryResaleDialog/:code',
    name: 'SalesdeliveryResaleDialog',
    component: SalesdeliveryResaleDialog,
    meta: { keepAlive: true }
  },

  // 第三方待发货管理
  {
    path: '/nonpartysalesdeliverylist',
    name: 'Nonpartysalesdeliverylist',
    component: Nonpartysalesdeliverylist,
    meta: { keepAlive: true }
  },
  // 第三方待发货工作台
  {
    path: '/nonpartysalesdeliveryWorkbench',
    name: 'NonpartysalesdeliveryWorkbench',
    component: NonpartysalesdeliveryWorkbench,
    meta: { keepAlive: true }
  },
  // 第三方待发货工作台-详情
  {
    path: '/nonpartysalesdeliveryDialog/:code',
    name: 'NonpartysalesdeliveryDialog',
    component: NonpartysalesdeliveryDialog,
    meta: { keepAlive: true }
  },

  {
    path: '/retailDeliveryList',
    name: 'RetailDeliveryList',
    component: RetailDeliveryList,
    meta: { keepAlive: true }
  },
  {
    path: '/retailDeliveryWorkbench',
    name: 'RetailDeliveryWorkbench',
    component: RetailDeliveryWorkbench,
    meta: { keepAlive: true }
  },
  {
    path: '/retailDeliveryDialog/:code',
    name: 'RetailDeliveryDialog',
    component: RetailDeliveryDialog,
    meta: { keepAlive: true }
  },

  {
    path: '/linkmanWorkbench',
    name: 'LinkmanWorkbench',
    component: LinkmanWorkbench,
    meta: { keepAlive: true }
  },
  {
    path: '/linkmanManage',
    name: 'LinkmanManage',
    component: LinkmanManage,
    meta: { keepAlive: true }
  },
  {
    path: '/linkmanDetail/:code',
    name: 'LinkmanDetail',
    component: LinkmanDetail,
    meta: { keepAlive: true }
  },

  {
    path: '/intentListManagement',
    name: 'IntentListManagement',
    component: IntentListManagement,
    meta: { keepAlive: true }
  },
  {
    path: '/shelfOrderManagement',
    name: 'ShelfOrderManagement',
    component: ShelfOrderManagement,
    meta: { keepAlive: true }
  },

  {
    path: '/mallRetailOrder',
    name: 'MallRetailOrder',
    component: MallRetailOrder,
    meta: { keepAlive: true }
  },
  {
    path: '/mallRetailOrderDetail/:code',
    name: 'MallRetailOrderDetail',
    component: MallRetailOrderDetail,
    meta: { keepAlive: true }
  },
  {
    path: '/shelfOrderWorkbench',
    name: 'ShelfOrderWorkbench',
    component: ShelfOrderWorkbench,
    meta: { keepAlive: true }
  },

  {
    path: '/combineSign',
    name: 'CombineSign',
    component: CombineSign,
    meta: { keepAlive: true }
  },
  {
    path: '/combineSignDetail/:code',
    name: 'CombineSignDetail',
    component: CombineSignDetail,
    meta: { keepAlive: true }
  },
  {
    path: '/separateWorkbench',
    name: 'SeparateWorkbench',
    component: SeparateWorkbench,
    meta: { keepAlive: true }
  }

]
