// 客商二期

const Tasks = () => import('@/views/travellingMerchant/tasks/index.vue')
const BusinessPartnerProfile = () =>
  import('@/views/travellingMerchant/businessPartnerProfile/index.vue')

const CustomerDetails = () =>
  import(
    '@/views/travellingMerchant/businessPartnerProfile/customerDetails/index.vue'
  )
const VisitPlan = () => import('@/views/travellingMerchant/visit/visitPlan.vue')
const VisitPlanDetails = () =>
  import(
    '@/views/travellingMerchant/visit/visitPlanDetails/visitPlanDetails.vue'
  )
const VisitRecord = () =>
  import('@/views/travellingMerchant/visit/visitRecord.vue')
const VisitRecordDetails = () =>
  import('@/views/travellingMerchant/visit/visitRecordDetails/index.vue')

const Access = () => import('@/views/travellingMerchant/access/index.vue')
const CustomerDetailsAccess = () =>
  import('@/views/travellingMerchant/access/customerDetails/index.vue')

const WeeklyReport = () =>
  import('@/views/travellingMerchant/weeklyReport/index.vue')
const weeklyReportConfiguration = () =>
  import('@/views/travellingMerchant/weeklyReportConfiguration/index.vue')
const AddWeekly = () =>
  import('@/views/travellingMerchant/weeklyReportConfiguration/addWeekly.vue')
const WeeklyReportDetails = () =>
  import('@/views/travellingMerchant/weeklyReport/customerDetails/index.vue')
export default [
  // 合同关系配置工作台
  {
    path: '/Tasks',
    name: 'Tasks',
    component: Tasks,
    meta: { keepAlive: true }
  },
  {
    path: '/BusinessPartnerProfile',
    name: 'BusinessPartnerProfile',
    component: BusinessPartnerProfile,
    meta: { keepAlive: true }
  },
  {
    path: '/VisitPlan',
    name: 'VisitPlan',
    component: VisitPlan,
    meta: { keepAlive: true }
  },
  {
    path: '/VisitRecord',
    name: 'VisitRecord',
    component: VisitRecord,
    meta: { keepAlive: true }
  },
  {
    path: '/Access',
    name: 'Access',
    component: Access,
    meta: { keepAlive: true }
  },
  {
    path: '/WeeklyReport',
    name: 'WeeklyReport',
    component: WeeklyReport,
    meta: { keepAlive: true }
  },
  {
    path: '/weeklyReportConfiguration',
    name: 'weeklyReportConfiguration',
    component: weeklyReportConfiguration,
    meta: { keepAlive: true }
  },
  {
    path: '/CustomerDetails/:code',
    name: 'CustomerDetails',
    component: CustomerDetails,
    meta: { keepAlive: true }
  },
  {
    path: '/VisitPlanDetails/:code',
    name: 'VisitPlanDetails',
    component: VisitPlanDetails,
    meta: { keepAlive: true }
  },
  {
    path: '/VisitRecordDetails/:code',
    name: 'VisitRecordDetails',
    component: VisitRecordDetails,
    meta: { keepAlive: true }
  },
  {
    path: '/CustomerDetailsAccess/:code',
    name: 'CustomerDetailsAccess',
    component: CustomerDetailsAccess,
    meta: { keepAlive: true }
  },
  {
    path: '/WeeklyReportDetails/:code',
    name: 'WeeklyReportDetails',
    component: WeeklyReportDetails,
    meta: { keepAlive: true }
  },
  {
    path: '/AddWeekly/:code',
    name: 'AddWeekly',
    component: AddWeekly,
    meta: { keepAlive: true }
  }
  // 预录入合同详情
  //   {
  //     path: '/aggregateMethodContractDetail/:code',
  //     name: 'aggregateMethodContractDetail',
  //     component: aggregateMethodContractDetail,
  //     meta: { keepAlive: true }
  //   }
]
