const PricingDepartment = () =>
  import('@/views/retailPricing/pricingDepartment/index.vue')
const SteelMillMessageMaintain = () =>
  import('@/views/steelMillCenterRegulate/steelMillMessageMaintain/index.vue')
const SteelPlantResources = () =>
  import('@/views/steelMillCenterRegulate/steelPlantResources/index.vue')
const ErpResources = () =>
  import('@/views/steelMillCenterRegulate/erpResources/index.vue')
const SelfSupportResources = () =>
  import('@/views/steelMillCenterRegulate/selfSupportResources/index.vue')
const Requirements = () =>
  import('@/views/steelMillCenterRegulate/requirements/index.vue')
const PointsView = () =>
  import('@/views/steelMillCenterRegulate/pointsView/index.vue')
const PointsDetails = () =>
  import('@/views/steelMillCenterRegulate/pointsView/pointsDetails.vue')
const PointsConfiguration = () =>
  import('@/views/steelMillCenterRegulate/pointsView/pointsConfiguration.vue')
const ViewDetails = () =>
  import('@/views/steelMillCenterRegulate/steelPlantResources/viewDetails.vue')
const Maintenance = () =>
  import('@/views/steelMillCenterRegulate/maintenance/index.vue')
const RequirementViewing = () =>
  import('@/views/steelMillCenterRegulate/requirements/requirementViewing.vue')

// 关注品种
const FollowBreed = () =>
  import('@/views/steelMillCenterRegulate/follow/followBreed.vue')
// 关注钢厂
const FollowSteelWorks = () =>
  import('@/views/steelMillCenterRegulate/follow/followSteelWorks.vue')

const retailPricingRoutes = [
  {
    path: '/pricingDepartment',
    name: 'PricingDepartment',
    component: PricingDepartment,
    meta: { keepAlive: true }
  },
  {
    path: '/SteelMillMessageMaintain',
    name: 'SteelMillMessageMaintain',
    component: SteelMillMessageMaintain,
    meta: { keepAlive: true }
  },
  {
    path: '/maintenance/:code',
    name: 'Maintenance',
    component: Maintenance,
    meta: { keepAlive: true }
  },
  {
    path: '/SteelPlantResources',
    name: 'SteelPlantResources',
    component: SteelPlantResources,
    meta: { keepAlive: true }
  },
  {
    path: '/ViewDetails/:code',
    name: 'ViewDetails',
    component: ViewDetails,
    meta: { keepAlive: true }
  },
  {
    path: '/ErpResources',
    name: 'ErpResources',
    component: ErpResources,
    meta: { keepAlive: true }
  },
  {
    path: '/SelfSupportResources',
    name: 'SelfSupportResources',
    component: SelfSupportResources,
    meta: { keepAlive: true }
  },
  {
    path: '/Requirements',
    name: 'Requirements',
    component: Requirements,
    meta: { keepAlive: true }
  },
  {
    path: '/RequirementViewing/:code',
    name: 'RequirementViewing',
    component: RequirementViewing,
    meta: { keepAlive: true }
  },
  {
    path: '/PointsView',
    name: 'PointsView',
    component: PointsView,
    meta: { keepAlive: true }
  },

  {
    path: '/PointsDetails/:code',
    name: 'PointsDetails',
    component: PointsDetails,
    meta: { keepAlive: true }
  },
  {
    path: '/PointsConfiguration',
    name: 'PointsConfiguration',
    component: PointsConfiguration,
    meta: { keepAlive: true }
  },
  {
    path: '/FollowBreed',
    name: 'FollowBreed',
    component: FollowBreed,
    meta: { keepAlive: true }
  },
  {
    path: '/FollowSteelWorks',
    name: 'FollowSteelWorks',
    component: FollowSteelWorks,
    meta: { keepAlive: true }
  }
]

export default retailPricingRoutes
