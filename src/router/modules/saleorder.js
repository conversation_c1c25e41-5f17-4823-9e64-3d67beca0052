/*
 * @Author: 沈鹭荣
 * @Date: 2021-03-16 19:55:21
 * @LastEditors: 沈鹭荣
 * @LastEditTime: 2021-03-17 10:51:13
 * @Description:
 */
/*
 * @Description: 结算
 */
const Settlementlist = () => import('@/views/settlement/settlementlist/Index.vue')
const SettlementDetail = () => import('@/views/settlement/settlementlist/salesdeliveryDialog/index.vue')
const SettlementwWorkbench = () => import('@/views/settlement/settlementwWorkbench.vue')
const TrialSettleOrder = () => import('@/views/settlement/settlementlist/trialSettleOrder.vue')

const SaleorderRoutes = [
  // 结算工作台啊
  {
    path: '/settlementwWorkbench',
    name: 'SettlementwWorkbench',
    component: SettlementwWorkbench,
    meta: { keepAlive: true }
  },
  // 结算列表
  {
    path: '/settlementlist',
    name: 'Settlementlist',
    component: Settlementlist,
    meta: { keepAlive: true }
  },
  {
    path: '/trialSettleOrder',
    name: 'TrialSettleOrder',
    component: TrialSettleOrder,
    meta: { keepAlive: true }
  },
  // 结算单详情
  {
    path: '/settlementDetail/:code',
    name: 'SettlementDetail',
    component: SettlementDetail,
    meta: { keepAlive: true }
  }
]

export default SaleorderRoutes
