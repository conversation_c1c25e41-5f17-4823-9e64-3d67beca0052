// 钢厂信息管理

const RelationConfigWorkbench = () =>
  import('@/views/steelInfoManage/steelContract/relationConfigWorkbench.vue')
const SteelContractManage = () =>
  import('@/views/steelInfoManage/steelContract/manage.vue')
const SteelContractDetail = () =>
  import('@/views/steelInfoManage/steelContract/detail/index.vue')
const PurchaseInvoice = () =>
  import('@/views/steelInfoManage/purchaseInvoice/index.vue')
const ProductionProcess = () =>
  import('@/views/steelInfoManage/productionProcess/index.vue')
const ShippingInfo = () =>
  import('@/views/steelInfoManage/shippingInfo/index.vue')
const BasisSelfCollectionSheet = () =>
  import('@/views/steelInfoManage/basisSelfCollectionSheet/index.vue')
const BasisSelfCollectionSheetDetail = () =>
  import(
    '@/views/steelInfoManage/basisSelfCollectionSheet/detail/BasisSelfCollectionSheetDetail.vue'
  )
const BasisSelfCollectionSheetContractDetail = () =>
  import(
    '@/views/steelInfoManage/basisSelfCollectionSheet/detail/BasisSelfCollectionSheetContractDetail.vue'
  )
const BasisSelfCollectionSheetContractPriorDetail = () =>
  import(
    '@/views/steelInfoManage/basisSelfCollectionSheet/detail/BasisSelfCollectionSheetContractPriorDetail.vue'
  )
const BasisSelfTrackingSheetDetail = () =>
  import(
    '@/views/steelInfoManage/basisSelfCollectionSheet/detail/BasisSelfTrackingSheetDetail.vue'
  )
const BasisSelfCollectionSheetExport = () =>
  import(
    '@/views/steelInfoManage/basisSelfCollectionSheet/detail/BasisSelfCollectionSheetExport.vue'
  )
const ProductInfo = () =>
  import('@/views/steelInfoManage/productInfo/index.vue')
const ProductInfoDetail = () =>
  import('@/views/steelInfoManage/productInfo/detail.vue')
const CustomerInfo = () =>
  import('@/views/steelInfoManage/customerInfo/index.vue')
const CustomerInfoDetail = () =>
  import('@/views/steelInfoManage/customerInfo/detail.vue')
const QualityGuarantee = () =>
  import('@/views/steelInfoManage/qualityGuarantee/index.vue')
export default [
  // 合同关系配置工作台
  {
    path: '/relationConfigWorkbench',
    name: 'RelationConfigWorkbench',
    component: RelationConfigWorkbench,
    meta: { keepAlive: true }
  },
  // 钢厂合同管理
  {
    path: '/steelContractManage',
    name: 'SteelContractManage',
    component: SteelContractManage,
    meta: { keepAlive: true }
  },
  // 钢厂合同管理详情页
  {
    path: '/steelContractDetail/:code',
    name: 'SteelContractDetail',
    component: SteelContractDetail,
    meta: { keepAlive: true }
  },
  // 钢厂生产进程
  {
    path: '/productionProcess',
    name: 'ProductionProcess',
    component: ProductionProcess,
    meta: { keepAlive: true }
  },
  // 钢厂发运信息
  {
    path: '/shippingInfo',
    name: 'ShippingInfo',
    component: ShippingInfo,
    meta: { keepAlive: true }
  },
  // 钢厂采购发票
  {
    path: '/purchaseInvoice',
    name: 'PurchaseInvoice',
    component: PurchaseInvoice,
    meta: { keepAlive: true }
  },
  // 基差自营收集单
  {
    path: '/basisSelfCollectionSheet',
    name: 'BasisSelfCollectionSheet',
    component: BasisSelfCollectionSheet,
    meta: { keepAlive: true }
  },
  // 基差自营收集单详情
  {
    path: '/basisSelfCollectionSheetDetail/:code',
    name: 'BasisSelfCollectionSheetDetail',
    component: BasisSelfCollectionSheetDetail,
    meta: { keepAlive: true }
  },
  // 基差自营收集单合同详情
  {
    path: '/basisSelfCollectionSheetContractDetail/:code',
    name: 'BasisSelfCollectionSheetContractDetail',
    component: BasisSelfCollectionSheetContractDetail,
    meta: { keepAlive: true }
  },
  // 基差自营收集单上期填报数据、合同维护填报
  {
    path: '/basisSelfCollectionSheetContractPriorDetail/:code',
    name: 'BasisSelfCollectionSheetContractPriorDetail',
    component: BasisSelfCollectionSheetContractPriorDetail,
    meta: { keepAlive: true }
  },
  // 基差自营跟踪单详情
  {
    path: '/basisSelfTrackingSheetDetail/:code',
    name: 'BasisSelfTrackingSheetDetail',
    component: BasisSelfTrackingSheetDetail,
    meta: { keepAlive: true }
  },
  // 基差自营收集单导出
  {
    path: '/basisSelfCollectionSheetExport/:code',
    name: 'BasisSelfCollectionSheetExport',
    component: BasisSelfCollectionSheetExport,
    meta: { keepAlive: true }
  },
  // 钢厂产品信息
  {
    path: '/productInfo',
    name: 'ProductInfo',
    component: ProductInfo
  },
  // 钢厂产品信息详情
  {
    path: '/productInfoDetail/:code',
    name: 'ProductInfoDetail',
    component: ProductInfoDetail
  },
  // 客户信息
  {
    path: '/customerInfo',
    name: 'CustomerInfo',
    component: CustomerInfo
  },
  // 客户信息详情
  {
    path: '/customerInfoDetail/:code',
    name: 'CustomerInfoDetail',
    component: CustomerInfoDetail
  },
  // 质保书管理
  {
    path: '/qualityGuarantee',
    name: 'QualityGuarantee',
    component: QualityGuarantee,
    meta: { keepAlive: true }
  }
]
