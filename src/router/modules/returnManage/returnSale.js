const ReturnSaleManage = () => import('@/views/returnManage/returnSale/index.vue')
const ReturnSaleDetail = () => import('@/views/returnManage/returnSale/detail/index.vue')
const ReturnSaleWorkbench = () => import('@/views/returnManage/returnSale/workbench.vue')

export default [
  {
    path: '/returnSaleManage',
    name: 'ReturnSaleManage',
    component: ReturnSaleManage,
    meta: { keepAlive: true }
  },
  {
    path: '/returnSaleDetail/:code',
    name: 'ReturnSaleDetail',
    component: ReturnSaleDetail,
    meta: { keepAlive: true }
  },
  {
    path: '/returnSaleWorkbench',
    name: 'ReturnSaleWorkbench',
    component: ReturnSaleWorkbench,
    meta: { keepAlive: true }
  }
]
