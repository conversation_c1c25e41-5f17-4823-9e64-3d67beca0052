const ReturnProcureManage = () => import('@/views/returnManage/returnProcure/index.vue')
const ReturnProcureDetail = () => import('@/views/returnManage/returnProcure/detail/index.vue')
const ReturnProcureWorkbench = () => import('@/views/returnManage/returnProcure/workbench.vue')

export default [
  {
    path: '/returnProcureManage',
    name: 'ReturnProcureManage',
    component: ReturnProcureManage,
    meta: { keepAlive: true }
  },
  {
    path: '/returnProcureDetail/:code',
    name: 'ReturnProcureDetail',
    component: ReturnProcureDetail,
    meta: { keepAlive: true }
  },
  {
    path: '/returnProcureWorkbench',
    name: 'ReturnProcureWorkbench',
    component: ReturnProcureWorkbench,
    meta: { keepAlive: true }
  }
]
