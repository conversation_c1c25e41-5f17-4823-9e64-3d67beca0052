/*
 * @Author: 沈鹭荣
 * @Date: 2021-02-23 10:55:05
 * @LastEditors: 沈鹭荣
 * @LastEditTime: 2021-03-15 10:08:06
 * @Description:
 */
const CashFlowManage = () => import('@/views/moneyFlow/cashFlow/cashFlowManage/Index.vue')
const CashFlowManageDetail = () => import('@/views/moneyFlow/cashFlow/components/Detail/Detail.vue')
const ListDialogAdd = () => import('@/views/moneyFlow/cashFlow/components/ListDialog/ListDialog.vue')
const CashFlowWorkbench = () => import('@/views/moneyFlow/cashFlow/cashFlowWorkbench/Index.vue')
const CashFlowFrozen = () => import('@/views/moneyFlow/cashFlow/cashFlowFrozen/Index.vue')
const CashFlowFrozenDetail = () => import('@/views/moneyFlow/cashFlow/cashFlowFrozen/Detail.vue')

export default [
  // 往来调整管理
  {
    path: '/cashFlowManage',
    name: 'CashFlowManage',
    component: CashFlowManage,
    meta: { keepAlive: true }
  },
  // 往来调整管理详情
  {
    path: '/cashFlowManageDetail/:code',
    name: 'CashFlowManageDetail',
    component: CashFlowManageDetail,
    meta: { keepAlive: true }
  },

  // 往来调整管理新增
  {
    path: '/ListDialogAdd/:code',
    name: 'ListDialogAdd',
    component: ListDialogAdd,
    meta: { keepAlive: true }
  },
  // 往来调整工作台
  {
    path: '/cashFlowWorkbench',
    name: 'CashFlowWorkbench',
    component: CashFlowWorkbench,
    meta: { keepAlive: true }
  },
  // 冻结解冻单管理
  {
    path: '/cashFlowFrozen',
    name: 'CashFlowFrozen',
    component: CashFlowFrozen,
    meta: { keepAlive: true }
  },
  // 冻结解冻单详情
  {
    path: '/cashFlowFrozenDetail/:code',
    name: 'CashFlowFrozenDetail',
    component: CashFlowFrozenDetail,
    meta: { keepAlive: true }
  }
]
