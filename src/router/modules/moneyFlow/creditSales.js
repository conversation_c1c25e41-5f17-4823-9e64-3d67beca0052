const CreditRepayment = () => import('@/views/moneyFlow/creditSales/creditRepayment/index.vue')
const CreditRepaymentDetail = () => import('@/views/moneyFlow/creditSales/creditRepayment/detail/index.vue')
const ReceivableAdjustWorkbench = () => import('@/views/moneyFlow/creditSales/receivableAdjust/workbench.vue')
const ReceivableAdjustDetail = () => import('@/views/moneyFlow/creditSales/receivableAdjust/detail/index.vue')
const ReceivableAdjustManger = () => import('@/views/moneyFlow/creditSales/receivableAdjust/index.vue')
export default [
  {
    path: '/creditRepayment',
    name: 'CreditRepayment',
    component: CreditRepayment,
    meta: { keepAlive: true }
  },
  {
    path: '/creditRepaymentDetail/:code',
    name: 'CreditRepaymentDetail',
    component: CreditRepaymentDetail,
    meta: { keepAlive: true }
  },
  {
    path: '/receivableAdjustWorkbench',
    name: 'ReceivableAdjustWorkbench',
    component: ReceivableAdjustWorkbench,
    meta: { keepAlive: true }
  },
  {
    path: '/receivableAdjustManger',
    name: 'ReceivableAdjustManger',
    component: ReceivableAdjustManger,
    meta: { keepAlive: true }
  },
  {
    path: '/receivableAdjustDetail/:code',
    name: 'ReceivableAdjustDetail',
    component: ReceivableAdjustDetail,
    meta: { keepAlive: true }
  }
]
