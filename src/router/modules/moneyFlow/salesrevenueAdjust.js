const SalesrevenueAdjustList = () => import('@/views/moneyFlow/salesrevenueAdjust/index.vue')
const SalesrevenueAdjustWorkbench = () => import('@/views/moneyFlow/salesrevenueAdjust/workbench.vue')
const SalesrevenueAdjustDetail = () => import('@/views/moneyFlow/salesrevenueAdjust/detail/index.vue')

export default [
  // 收入调整单管理
  {
    path: '/salesrevenue_adjust_list',
    name: 'SalesrevenueAdjustList',
    component: SalesrevenueAdjustList,
    meta: { keepAlive: true }
  },
  // 收入调整单详情
  {
    path: '/salesrevenue_adjust_detail/:code',
    name: 'SalesrevenueAdjustDetail',
    component: SalesrevenueAdjustDetail,
    meta: { keepAlive: true }
  },
  // 收入调整单工作台
  {
    path: '/salesrevenue_adjust_workbench',
    name: 'SalesrevenueAdjustWorkbench',
    component: SalesrevenueAdjustWorkbench,
    meta: { keepAlive: true }
  }
]
