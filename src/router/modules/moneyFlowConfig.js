/*
 * @Author: 沈鹭荣
 * @Date: 2021-02-09 15:49:16
 * @LastEditors: 沈鹭荣
 * @LastEditTime: 2021-03-09 11:20:59
 * @Description:
 */
/*
 * @Description: 款项往来
 */

const TransactionAdjustmentDocumentList = () => import('@/views/moneyFlow/transactionAdjustmentDocument/Index.vue')
const paymentcsm = () => import('@/views/paymentcsm/claim/Index.vue')
const ClaimDetail = () => import('@/views/paymentcsm/claim/ClaimDialog.vue')
const MoneyFlowRoutes = [
  // 实收付调整单管理
  {
    path: '/transactionAdjustmentDocumentList',
    name: 'TransactionAdjustmentDocumentList',
    component: TransactionAdjustmentDocumentList,
    meta: { keepAlive: true }
  },
  {
    path: '/paymentcsm',
    name: 'Paymentcsm',
    component: paymentcsm,
    meta: { keepAlive: true }
  },
  {
    path: '/ClaimDetail/:code',
    name: 'ClaimDetail',
    component: ClaimDetail,
    meta: { keepAlive: true }
  }
]

export default MoneyFlowRoutes
