// 销售发票管理
const PurchaseWorkbench = () => import('@/views/invoice/purchase/workbench/index.vue')
const PurchaseQuery = () => import('@/views/invoice/purchase/query/index.vue')
const PurchaseLeftStock = () => import('@/views/invoice/purchase/leftStock/index.vue')
const PurchaseMatchWb = () => import('@/views/invoice/purchase/matchWb/index.vue')
const InvoicePurchaseDetail = () => import('@/views/invoice/purchase/invoicePurchaseDialog/index.vue')

export default [
  {
    path: '/purchaseWorkbench',
    name: 'PurchaseWorkbench',
    component: PurchaseWorkbench,
    meta: { keepAlive: true }
  },
  {
    path: '/purchaseQuery',
    name: 'PurchaseQuery',
    component: PurchaseQuery,
    meta: { keepAlive: true }
  },
  {
    path: '/purchaseLeftStock',
    name: 'PurchaseLeftStock',
    component: PurchaseLeftStock,
    meta: { keepAlive: true }
  },
  {
    path: '/purchaseMatchWb',
    name: 'PurchaseMatchWb',
    component: PurchaseMatchWb,
    meta: { keepAlive: true }
  },
  {
    path: '/invoicePurchaseDetail/:code',
    name: 'InvoicePurchaseDetail',
    component: InvoicePurchaseDetail,
    meta: { keepAlive: true }
  }
]
