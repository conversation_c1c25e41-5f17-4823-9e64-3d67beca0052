// 销售发票管理
const SalesInvoiceWorkbench = () => import('@/views/invoice/sale')
const SalesInvoiceApplicationForm = () => import('@/views/invoice/sale/applicationForm')
const SalesInvoiceDetail = () => import('@/views/invoice/sale/applicationForm/detail')
const SalesTicketWorkbench = () => import('@/views/invoice/salesTicketWorkbench/index')
const InvoiceWriteoffOrder = () => import('@/views/invoice/writeoff/index.vue')
const InvoiceWriteoffOrderDetail = () => import('@/views/invoice/writeoff/detail/index.vue')
export default [
  // 销售待开票工作台
  {
    path: '/salesInvoiceWorkbench',
    name: 'SalesInvoiceWorkbench',
    component: SalesInvoiceWorkbench,
    meta: { keepAlive: true }
  },
  // 销售发票申开单查询
  {
    path: '/salesInvoiceApplicationForm',
    name: 'SalesInvoiceApplicationForm',
    component: SalesInvoiceApplicationForm,
    meta: { keepAlive: true }
  },
  // 销售发票详情页
  {
    path: '/salesInvoiceDetail/:code',
    name: 'SalesInvoiceDetail',
    component: SalesInvoiceDetail,
    meta: { keepAlive: true }
  },
  {
    path: '/salesTicketWorkbench',
    name: 'SalesTicketWorkbench',
    component: SalesTicketWorkbench,
    meta: { keepAlive: true }
  },

  {
    path: '/invoiceWriteoffOrder',
    name: 'InvoiceWriteoffOrder',
    component: InvoiceWriteoffOrder,
    meta: { keepAlive: true }
  },
  {
    path: '/invoiceWriteoffOrderDetail/:code',
    name: 'InvoiceWriteoffOrderDetail',
    component: InvoiceWriteoffOrderDetail,
    meta: { keepAlive: true }
  }

]
