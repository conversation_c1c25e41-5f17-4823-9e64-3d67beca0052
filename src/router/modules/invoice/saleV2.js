// 销售发票管理
const SalesInvoiceWorkbenchV2 = () => import('@/views/invoice/saleV2/index')
const SalesInvoiceApplicationFormV2 = () => import('@/views/invoice/saleV2/applicationForm/index')
const SalesInvoiceDetailV2 = () => import('@/views/invoice/saleV2/applicationForm/detail')

export default [
  // 销售待开票工作台
  {
    path: '/salesInvoiceWorkbenchV2',
    name: 'SalesInvoiceWorkbenchV2',
    component: SalesInvoiceWorkbenchV2,
    meta: { keepAlive: true }
  },
  // 销售发票申开单查询
  {
    path: '/salesInvoiceApplicationFormV2',
    name: 'SalesInvoiceApplicationFormV2',
    component: SalesInvoiceApplicationFormV2,
    meta: { keepAlive: true }
  },
  // 销售发票详情页
  {
    path: '/salesInvoiceDetailV2/:code',
    name: 'SalesInvoiceDetailV2',
    component: SalesInvoiceDetailV2,
    meta: { keepAlive: true }
  }
]
