
const state = {
  visitedViews: [],
  cachedViews: []
}

const mutations = {
  SET_VISITED_VIEW: (state, views) => {
    state.visitedViews = views
  },
  // -----新增页签-----
  ADD_VISITED_VIEW: (state, view) => {
    if (state.visitedViews.some((v) => v.fullPath === view.fullPath)) return
    state.visitedViews[view.meta.affix ? 'unshift' : 'push'](
      Object.assign({}, view, {
        title: view.meta.title || 'no-name'
      })
    )
    // console.log('存储view前-', state.visitedViews)
    // tags保存到会话缓存中
    // 设置matched属性不可枚举
    state.visitedViews.forEach((item, index) => {
      item.matched && Object.defineProperty(item, 'matched', { enumerable: false })
      if (item.meta.affix && index !== 0) {
        state.visitedViews.splice(index, 1)
      }
    })

    // 更新tags
    sessionStorage.setItem('visitedViews', JSON.stringify(state.visitedViews))
  },
  ADD_CACHED_VIEW: (state, view) => {
    if (state.cachedViews.includes(view.path)) return
    if (!view.meta.noCache) {
      state.cachedViews.push(view.path)
    }
  },
  // -----删除页签-----
  DEL_VISITED_VIEW: (state, view) => {
    for (const [i, v] of state.visitedViews.entries()) {
      if (v.fullPath === view.fullPath) {
        state.visitedViews.splice(i, 1)
        break
      }
    }
    sessionStorage.setItem('visitedViews', JSON.stringify(state.visitedViews))
  },
  DEL_CACHED_VIEW: (state, view) => {
    for (const i of state.cachedViews) {
      if (i === view.path) {
        const index = state.cachedViews.indexOf(i)
        state.cachedViews.splice(index, 1)
        break
      }
    }
  },
  // -----删除其他页签-----
  DEL_OTHERS_VISITED_VIEWS: (state, view) => {
    state.visitedViews = state.visitedViews.filter(v => {
      return v.meta.affix || v.fullPath === view.fullPath
    })
    state.visitedViews.forEach((item) => {
      item.matched &&
        Object.defineProperty(item, 'matched', { enumerable: false })
    })
    sessionStorage.setItem('visitedViews', JSON.stringify(state.visitedViews))
  },
  DEL_OTHERS_CACHED_VIEWS: (state, view) => {
    for (const i of state.cachedViews) {
      if (i === view.path) {
        const index = state.cachedViews.indexOf(i)
        state.cachedViews = state.cachedViews.slice(index, index + 1)
        break
      }
    }
  },
  // -----删除右侧页签-----
  DEL_RIGHT_VISITED_VIEWS: (state, view) => {
    state.visitedViews.filter((v, index) => {
      if (v.fullPath === view.fullPath) {
        state.visitedViews = state.visitedViews.slice(0, index + 1)
      }
    })
    sessionStorage.setItem('visitedViews', JSON.stringify(state.visitedViews))
  },
  DEL_RIGHT_CACHED_VIEWS: (state, view) => {
    state.cachedViews.filter((v, index) => {
      if (v.fullPath === view.fullPath) {
        state.cachedViews = state.cachedViews.slice(0, index + 1)
      }
    })
  },
  // -----删除左侧页签-----
  DEL_LEFT_VISITED_VIEWS: (state, view) => {
    const arr = state.visitedViews
    state.visitedViews.filter((v, index) => {
      if (v.fullPath === view.fullPath) {
        state.visitedViews = arr.slice(index, state.visitedViews.length)
      }
    })
    state.visitedViews.unshift(arr[0])
    sessionStorage.setItem('visitedViews', JSON.stringify(state.visitedViews))
  },
  DEL_LEFT_CACHED_VIEWS: (state, view) => {
    const arr = state.cachedViews
    state.cachedViews.filter((v, index) => {
      if (v.fullPath === view.fullPath) {
        state.cachedViews = arr.slice(index, state.visitedViews.length)
      }
    })
    state.cachedViews.unshift(arr[0])
  },
  // -----删除全部页签-----
  DEL_ALL_VISITED_VIEWS: (state) => {
    // keep affix tags
    const affixTags = state.visitedViews.filter((tag) => tag.meta.affix)
    state.visitedViews = affixTags
    state.visitedViews.forEach((item) => {
      item.matched &&
        Object.defineProperty(item, 'matched', { enumerable: false })
    })
    sessionStorage.setItem('visitedViews', JSON.stringify(state.visitedViews))
  },
  DEL_ALL_CACHED_VIEWS: state => {
    state.cachedViews = []
  },
  // -----更新页签-----
  UPDATE_VISITED_VIEW: (state, view) => {
    for (let v of state.visitedViews) {
      if (v.fullPath === view.fullPath) {
        v = Object.assign(v, view)
        break
      }
    }
  }
}

const actions = {
  setStatus({ commit }, view) {
    commit('SET_STATUS', view)
  },
  // -----新增页签-----
  addView({ dispatch }, view) {
    const tempView = {
      fullPath: view.fullPath,
      meta: view.meta,
      // name: view.name,
      params: view.params,
      path: view.path,
      query: view.query
    }
    dispatch('addVisitedView', tempView)
    dispatch('addCachedView', tempView)
  },
  setVisitedView({ commit }, views) {
    commit('SET_VISITED_VIEW', views)
  },
  addVisitedView({ commit }, view) {
    commit('ADD_VISITED_VIEW', view)
  },
  addCachedView({ commit }, view) {
    commit('ADD_CACHED_VIEW', view)
  },
  // -----删除页签-----
  delView({ dispatch, state }, view) {
    return new Promise(resolve => {
      dispatch('delVisitedView', view)
      dispatch('delCachedView', view)
      resolve({
        visitedViews: [...state.visitedViews],
        cachedViews: [...state.cachedViews]
      })
    })
  },
  delVisitedView({ commit, state }, view) {
    return new Promise(resolve => {
      commit('DEL_VISITED_VIEW', view)
      resolve([...state.visitedViews])
    })
  },
  delCachedView({ commit, state }, view) {
    return new Promise(resolve => {
      commit('DEL_CACHED_VIEW', view)
      resolve([...state.cachedViews])
    })
  },

  // -----删除其他页签-----
  delOthersViews({ dispatch, state }, view) {
    return new Promise(resolve => {
      dispatch('delOthersVisitedViews', view)
      dispatch('delOthersCachedViews', view)
      resolve({
        visitedViews: [...state.visitedViews],
        cachedViews: [...state.cachedViews]
      })
    })
  },
  delOthersVisitedViews({ commit, state }, view) {
    return new Promise(resolve => {
      commit('DEL_OTHERS_VISITED_VIEWS', view)
      resolve([...state.visitedViews])
    })
  },
  delOthersCachedViews({ commit, state }, view) {
    return new Promise(resolve => {
      commit('DEL_OTHERS_CACHED_VIEWS', view)
      resolve([...state.cachedViews])
    })
  },

  // -----删除右侧页签-----
  delRightViews({ dispatch, state }, view) {
    return new Promise(resolve => {
      dispatch('delRightVisitedViews', view)
      dispatch('delRightCachedViews', view)
      resolve({
        visitedViews: [...state.visitedViews],
        cachedViews: [...state.cachedViews]
      })
    })
  },
  delRightVisitedViews({ commit, state }, view) {
    return new Promise(resolve => {
      commit('DEL_RIGHT_VISITED_VIEWS', view)
      resolve([...state.visitedViews])
    })
  },
  delRightCachedViews({ commit, state }, view) {
    return new Promise(resolve => {
      commit('DEL_RIGHT_CACHED_VIEWS', view)
      resolve([...state.cachedViews])
    })
  },

  // -----删除左侧页签-----
  delLeftViews({ dispatch, state }, view) {
    return new Promise(resolve => {
      dispatch('delLeftVisitedViews', view)
      dispatch('delLeftCachedViews', view)
      resolve({
        visitedViews: [...state.visitedViews],
        cachedViews: [...state.cachedViews]
      })
    })
  },
  delLeftVisitedViews({ commit, state }, view) {
    return new Promise(resolve => {
      commit('DEL_LEFT_VISITED_VIEWS', view)
      resolve([...state.visitedViews])
    })
  },
  delLeftCachedViews({ commit, state }, view) {
    return new Promise(resolve => {
      commit('DEL_LEFT_CACHED_VIEWS', view)
      resolve([...state.cachedViews])
    })
  },

  // -----删除全部页签-----
  delAllViews({ dispatch, state }, view) {
    return new Promise(resolve => {
      dispatch('delAllVisitedViews', view)
      dispatch('delAllCachedViews', view)
      resolve({
        visitedViews: [...state.visitedViews],
        cachedViews: [...state.cachedViews]
      })
    })
  },
  delAllVisitedViews({ commit, state }) {
    return new Promise(resolve => {
      commit('DEL_ALL_VISITED_VIEWS')
      resolve([...state.visitedViews])
    })
  },
  delAllCachedViews({ commit, state }) {
    return new Promise(resolve => {
      commit('DEL_ALL_CACHED_VIEWS')
      resolve([...state.cachedViews])
    })
  },

  // -----更新页签-----
  updateVisitedView({ commit }, view) {
    commit('UPDATE_VISITED_VIEW', view)
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
