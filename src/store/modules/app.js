import Cookies from 'js-cookie'

const state = {
  sidebar: {
    opened: Cookies.get('sidebarStatus') ? !!+Cookies.get('sidebarStatus') : true,
    withoutAnimation: false
  },
  device: 'desktop',
  language: Cookies.get('language') || 'zh_CN',
  size: Cookies.get('size') || 'medium',
  microSystem: {},
  socket: '',
  appLoading: false,
  authenticationMethod: 'token' // sessionId, token
}

const mutations = {
  // 存储socket协议
  SET_SOCKET: (state, socket) => {
    state.socket = socket
  },
  SET_SYSTEM_MICRO: (stste, micro) => {
    if (micro && Object.keys(micro).length === 0) {
      stste.microSystem = micro
    } else {
      stste.microSystem = Object.assign(stste.microSystem, micro)
    }
  },
  TOGGLE_SIDEBAR: state => {
    state.sidebar.opened = !state.sidebar.opened
    state.sidebar.withoutAnimation = false
    if (state.sidebar.opened) {
      Cookies.set('sidebarStatus', 1)
    } else {
      Cookies.set('sidebarStatus', 0)
    }
  },
  CLOSE_SIDEBAR: (state, withoutAnimation) => {
    Cookies.set('sidebarStatus', 0)
    state.sidebar.opened = false
    state.sidebar.withoutAnimation = withoutAnimation
  },
  TOGGLE_DEVICE: (state, device) => {
    state.device = device
  },
  SET_LANGUAGE: (state, language) => {
    state.language = language
    Cookies.set('language', language)
  },
  SET_SIZE: (state, size) => {
    state.size = size
    Cookies.set('size', size)
  },
  APP_LOADING: (state, loading) => {
    state.appLoading = loading
  }
}

const actions = {
  // app loading
  appLoading({ commit }, loading) {
    commit('APP_LOADING', loading)
  },
  toggleSideBar({ commit }) {
    commit('TOGGLE_SIDEBAR')
  },
  closeSideBar({ commit }, { withoutAnimation }) {
    commit('CLOSE_SIDEBAR', withoutAnimation)
  },
  toggleDevice({ commit }, device) {
    commit('TOGGLE_DEVICE', device)
  },
  setLanguage({ commit }, language) {
    commit('SET_LANGUAGE', language)
  },
  setSize({ commit }, size) {
    commit('SET_SIZE', size)
  },
  setSystemMicro({ commit }, micro) {
    if (Object.values(micro).length) {
      commit('SET_SYSTEM_MICRO', micro)
    } else {
      for (var i in state.microSystem) {
        state.microSystem[i].unmount()
      }
      setTimeout(() => {
        commit('SET_SYSTEM_MICRO', micro)
      }, 100)
    }
  },
  setSocket({ commit }, socket) {
    commit('SET_SOCKET', socket)
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
