import { getBtnList, logMenu, getMenuList } from '@/api/menu'
import store from '@/store'
import { formatMenu, loadApp } from '@/utils/index'
// import otherRoutes from '@/router/detail'
// import { filterAsyncRouter } from '@/store/modules/permission'

const state = {
  flagInit: true,
  unformattedMenu: []
}

const mutations = {
  SetUnformattedMenu: (state, list) => {
    state.unformattedMenu = list
    state.flagInit = false
  }
}

const actions = {
  asyncSetUnformattedMenu({ commit }, list) {
    commit('SetUnformattedMenu', list)
  },
  asyncGetBtnList({ commit }, menuId) {
    return new Promise((resolve, reject) => {
      getBtnList({ menuId: menuId }).then(res => {
        // console.log('getBtnList', res)
        // console.log('拿到按钮权限-1')
        const tempArr = []
        res.data.forEach(item => {
          if (item.hasAuthority) tempArr.push(item.code)
        })
        // qiankun全局变量-authenticationList设置
        resolve(tempArr)
      }).catch(error => {
        reject(error)
      })
    })
  },
  asyncLogMenu({ commit }, menuId) {
    if (menuId) {
      return new Promise((resolve, reject) => {
        logMenu(menuId).then(res => {
          resolve(res)
        }).catch(error => {
          reject(error)
        })
      })
    }
  },
  asyncGetMenuList({ commit }, userId) {
    return new Promise((resolve, reject) => {
      getMenuList({
        userId: userId,
        applicationId: 'c91d2e57-ad84-4451-9340-8aecfc56fd8c'
      }).then((response) => {
        const { data } = response
        // loadApp(data)
        store.dispatch('menu/asyncSetUnformattedMenu', data)
        const temp_routes = formatMenu(data)
        // const asyncRouter = filterAsyncRouter(temp_routes).concat(otherRoutes)
        resolve(temp_routes)
      }).catch((error) => {
        console.log('error--', error)
      })
    })
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
