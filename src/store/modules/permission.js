import { constantRoutes } from '@/router'
/* Layout */
import Layout from '@/layout'
import SecondMenu from '@/secondMenu'

export const filterAsyncRouter = (routers) => { // 遍历后台传来的路由字符串，转换为组件对象
  return routers.filter(router => {
    if (router.children && router.children.length) {
      router.component = router.meta.parentId ? SecondMenu : Layout
      router.children = filterAsyncRouter(router.children)
    }
    return true
  })
}

export const loadView = (view) => {
  return (resolve) => require([`@/views/${view}`], resolve)
}

const state = {
  routes: [],
  addRoutes: [],
  permission_routes: [],
  flagLoadMenu: false
}

const mutations = {
  SET_ROUTES: (state, routes) => {
    let curRoutes = []
    if (routes !== 'reset') {
      state.flagLoadMenu = true
      curRoutes = routes
    } else {
      state.flagLoadMenu = false
    }
    state.permission_routes = curRoutes
    state.addRoutes = curRoutes
    state.routes = constantRoutes.concat(curRoutes)
  }
}

const actions = {
  // 生成路由
  GenerateRoutes({ commit }, asyncRouter) {
    return new Promise(resolve => {
      commit('SET_ROUTES', asyncRouter)
      resolve(asyncRouter)
    })
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
