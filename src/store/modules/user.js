import { login, logout, getInfo, getManagement } from '@/api/user'
import {
  setSessionId,
  removeSessionId,
  setToken,
  removeToken,
  removeRefreshToken,
  setRefreshToken
} from 'cnd-horizon-utils/src/utils/auth'
import router, { resetRouter } from '@/router'

const state = {
  name: '',
  avatar: '',
  introduction: '',
  roles: [],
  userId: '',
  firstLoading: false
}

const mutations = {
  SET_TOKEN: (state, token) => {
    state.token = token
  },
  SET_INTRODUCTION: (state, introduction) => {
    state.introduction = introduction
  },
  SET_NAME: (state, name) => {
    state.name = name
  },
  SET_AVATAR: (state, avatar) => {
    state.avatar = avatar
  },
  SET_ROLES: (state, roles) => {
    state.roles = roles
  },
  SET_USERID: (state, id) => {
    // console.log('用户id--', id)
    state.userId = id
  },
  FIRST_LOADING: (state, loading) => {
    state.firstLoading = loading
  }
}

const actions = {

  firstLoading({ commit }, loading) {
    commit('FIRST_LOADING', loading)
  },

  // user login
  login({ commit }, userInfo) {
    // const { username, credential } = userInfo
    return new Promise((resolve, reject) => {
      login(userInfo)
      // login({ username: username.trim(), credential: credential })
        .then((response) => {
          const { data } = response
          if (data?.token?.access_token) {
            // dispatch('app/authenticationMethodChange', 'token')
            setToken(`Bearer ${data.token.access_token}`)
            setRefreshToken(data.token.refresh_token)
            commit('SET_TOKEN', data.token.access_token)
            setSessionId(data.server)
          } else {
            // dispatch('app/authenticationMethodChange', 'sessionId')
            commit('SET_TOKEN', data.server)
            setSessionId(data.server)
          }
          // 是否初次登录
          commit('FIRST_LOADING', true)
          resolve(data)
          // getMenu().then(res => {
          //   const data = {}
          //   if (res.data) {
          //     data.path = res.data
          //     sessionStorage.setItem('navbar', res.data)
          //   }
          //   resolve(data)
          // })
        })
        .catch((error) => {
          reject(error)
        })
    })
  },

  // get user info
  getInfo({ commit, dispatch, state }) {
    return new Promise((resolve, reject) => {
      getInfo().then(response => {
        const { data } = response

        if (!data) {
          reject('用户信息获取失败请重新登陆！')
        }
        // 用户信息存到缓存
        sessionStorage.setItem('userInfo', JSON.stringify(data))
        // 用户信息存到store
        const { id, name, avatar, description, account } = data
        // roles must be a non-empty array
        // if (!roles || roles.length <= 0) {
        //   reject('getInfo: roles must be a non-null array!')
        // }

        // commit('SET_ROLES', roles)
        commit('SET_USERID', id)
        commit('SET_NAME', name)
        commit('SET_AVATAR', avatar)
        commit('SET_INTRODUCTION', description)

        dispatch('getManagement', account)
        resolve(data)
      }).catch(error => {
        reject(error)
      })
    })
  },

  // user logout
  logout({ commit, state, dispatch }) {
    return new Promise((resolve, reject) => {
      logout(state.token)
        .then(() => {
          removeToken()
          removeRefreshToken()
          removeSessionId()
          dispatch('app/setSystemMicro', {}, { root: true })
          // 退出清空tag标签缓存
          sessionStorage.removeItem('visitedViews')
          // sessionStorage.removeItem('navbar')
          // 退出清除缓存用户信息
          sessionStorage.removeItem('userInfo')
          sessionStorage.removeItem('managementInfo')
          // commit('SET_TOKEN', '')
          commit('SET_ROLES', [])
          commit('SET_USERID', '')
          commit('FIRST_LOADING', false)
          // 登出清空已加载应用信息
          resetRouter()
          resolve()
        })
        .catch((error) => {
          reject(error)
        })
    })
  },

  // remove token
  resetToken({ commit, dispatch }) {
    return new Promise((resolve) => {
      sessionStorage.removeItem('navbar')
      removeToken()
      removeSessionId()
      removeRefreshToken()
      dispatch('app/setSystemMicro', {}, { root: true })
      // if (this.getters.authenticationMethod === 'token') {
      //   removeToken()
      // } else {
      //   removeSessionId()
      // }
      // commit('SET_TOKEN', '')
      commit('SET_ROLES', [])
      commit('SET_USERID', '')
      resolve()
    })
  },

  // Dynamically modify permissions
  changeRoles({ commit, dispatch }, role) {
    return new Promise(resolve => {
      // const token = role + '-token'

      // commit('SET_TOKEN', token)
      // setSessionId(token)

      const { roles } = dispatch('getInfo')

      resetRouter()

      // generate accessible routes map based on roles
      const accessRoutes = dispatch('permission/generateRoutes', roles, { root: true })

      console.log('accessRoutes', accessRoutes)

      // dynamically add accessible routes
      router.addRoute(accessRoutes)

      resolve()
    })
  },

  // 获取用户经营单位
  getManagement(context, account) {
    new Promise((resolve, reject) => {
      getManagement(account).then(res => {
        if (res.data) {
          sessionStorage.setItem('managementInfo', JSON.stringify(res.data))
        }
        resolve(res)
      }).catch(error => {
        reject(error)
      })
    })
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
