import { request } from 'cnd-horizon-utils'
import getEnv from '@/utils/env'

// 用户密码修改
export function mdfPassword(data) {
  return request({
    url: `/sys/user/passwd`,
    method: 'PUT',
    data: data
  })
}

export function login(data) {
  return request({
    url: '/j_spring_security_check',
    method: 'POST',
    data: data
  })
}

export function getInfo() {
  return request({
    url: '/sys/user/current',
    method: 'GET'
  })
}

export function logout() {
  return request({
    url: '/logout',
    method: 'GET'
  })
}

export function checkAccount(data) {
  return request({
    url: `/user/ext/checkAccountV2?account=${data.account}`,
    method: 'POST'
  })
}
export function checkAccountV3(data) {
  return request({
    url: `/user/ext/checkAccountV3?account=${data.account}&uuid=${data.uuid}&captchaCode=${data.captchaCode}`,
    method: 'POST'
  })
}

export function getPhoneCode(data) {
  console.log('VUE_APP_ENV', getEnv('VUE_APP_ENV'))
  return request({
    url: getEnv('VUE_APP_ENV') === 'mall' ? `/esc/mall/user/getPhoneCode?account=${data.account}&phoneNumber=${data.phoneNumber}` : `/esc/user/ext/getPhoneCode?account=${data.account}&phoneNumber=${data.phoneNumber}`,
    method: 'POST',
    noLoading: true
  })
}

export function checkCode(data) {
  return request({
    url: getEnv('VUE_APP_ENV') === 'mall' ? `/esc/mall/user/checkUser?account=${data.account}&code=${data.code}&phone=${data.phoneNumber}&sCode=getUserPhoneCode` : `/esc/user/ext/checkUser?account=${data.account}&code=${data.code}&phone=${data.phoneNumber}&sCode=getUserPhoneCode`,
    method: 'PUT'
  })
}

export function updatePasswd(data) {
  return request({
    url: `/user/ext/update/passwd`,
    method: 'POST',
    data: data
  })
}

export function getManagement(account) {
  return request({
    url: `/esc/user/help/getManagement/${account}`,
    method: 'POST'
  })
}

export function annualReport(params) {
  return request({
    url: `/esc/annual/report/get/${params.sUserAccount}/${params.year}`,
    method: 'POST'
  })
}

export function jsapiTicket(data) {
  return request({
    url: `/esc/user/help/get/jsapi_ticket`,
    method: 'POST',
    data
  })
}

// 图形验证码校验
export function checkCaptchaCode(data) {
  return request({
    url: `/user/register/check/captchaCode?uuid=${data.uuid}&captchaCode=${data.captchaCode}`,
    method: 'PUT'
  })
}

export function getAccountCode(data) {
  return request({
    url: `/esc/user/ext/getAccountCode?account=${data.account}&device=${data.device}`,
    method: 'POST'
  })
}
