import { request } from 'cnd-horizon-utils'
// 获取用户授权菜单列表
console.log('env', process.env.NODE_ENV)
export function getMenuList(params) {
  return request({
    // url: '/sys/authority/menu',
    url: '/sys/menu-collections/relationships',
    method: 'POST',
    data: params
  })
}
// 获取用户授权按钮列表
export function getBtnList(params) {
  return request({
    url: `/sys/authority/component/${params.menuId}`,
    method: 'GET'
  })
}
// 获取登录默认首页
export function getMenu(params) {
  return request({
    url: `/sys/role/resource/getmenu`,
    method: 'GET'
  })
}
// 记录菜单
export function logMenu(params) {
  return request({
    url: `/user/log/menu?menuName=${params}`,
    method: 'GET'
  })
}
