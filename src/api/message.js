import { request } from 'cnd-horizon-utils'

// 获取消息通知列表
export function getMessageList(params) {
  return request({
    // 新接口
    url: `/esc/notice/v1/user/page/${params.userId}/0/99999999`,
    method: 'get'
  })
}

// 获取消息通知列表
export function sendMassage(params) {
  return request({
    url: `/info/sender/config/log/send/148dfa12-24ba-4b83-a578-f42530c93721`,
    method: 'POST',
    data: params
  })
}

// 获取模态弹窗列表
export function getAnnounceList(params, page) {
  return request({
    url: `/esc/notice/v1/user/list/${page.page}/${page.limit}`,
    method: 'POST',
    data: params
  })
}

// 获取模态弹窗列表--用户公告详情
export const browsingDetailById = (params) => {
  return request({
    url: `/esc/notice/v1/user/sys/${params}/info`,
    method: 'GET'
  })
}

// 获取模态信息状态
export const browsingStatus = (params) => {
  return request({
    url: `/esc/notice/v1/user/read`,
    method: 'POST',
    data: params
  })
}

// 根据用户id查询返回接收公告日志分页对象
export function getBellList(page) {
  return request({
    url: `/esc/notice/v1/user/receipt/log/list/${page.page}/${page.limit}`,
    method: 'GET'
  })
}

// 获取公告详情附件内容
export const postList = (data) => {
  return request({
    url: `/annex/oss/page/${data.pageNo}/${data.pageSize}`,
    method: 'POST',
    data: data
  })
}

// 查询用户开关配置
export const switchQueryList = (data) => {
  return request({
    url: `/rtps/user/switchConfig/${data.pageNo}/${data.pageSize}`,
    method: 'POST',
    data: data
  })
}
// 切换开关
export const switchConfig = (data) => {
  return request({
    url: `/rtps/user/switchConfig`,
    method: 'POST',
    data: data
  })
}

