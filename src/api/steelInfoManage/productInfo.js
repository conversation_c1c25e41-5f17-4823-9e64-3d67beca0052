import { request } from 'cnd-horizon-utils'

/**
 * 钢厂产品信息-列表
 * @param {Object} data 查询参数
 * @param {Object} page 分页参数
 * @returns {Promise}
 */
export const getProductPage = (data, page) => {
  return request({
    url: `/esc/steel/mill/product/page/${page.page}/${page.limit}`,
    method: 'POST',
    data: data
  })
}

/**
 * 钢厂产品信息-明细
 * @param {String} sId 主键ID
 * @returns {Promise}
 */
export const getProductDetail = (sId) => {
  return request({
    url: `/esc/steel/mill/product/get/${sId}`,
    method: 'GET'
  })
}

/**
 * 钢厂产品信息-新增/修改
 * @param {Object} data 产品信息
 * @returns {Promise}
 */
export const saveProduct = (data) => {
  return request({
    url: `/esc/steel/mill/product/save`,
    method: 'PUT',
    data: data
  })
}

/**
 * 钢厂产品信息-删除
 * @param {Array} ids ID数组
 * @returns {Promise}
 */
export const removeProducts = (ids) => {
  return request({
    url: `/esc/steel/mill/product/removes`,
    method: 'DELETE',
    data: ids
  })
}
