import { request } from 'cnd-horizon-utils'

/**
 * 钢厂客户信息-列表
 * @param {Object} data 查询参数
 * @param {Object} page 分页参数
 * @returns {Promise}
 */
export const getCustomerPage = (data, page) => {
  return request({
    url: `/esc/steel/mill/customer/page/${page.page}/${page.limit}`,
    method: 'POST',
    data: data
  })
}

/**
 * 钢厂客户信息-明细
 * @param {String} sId 主键ID
 * @returns {Promise}
 */
export const getCustomerDetail = (sId) => {
  return request({
    url: `/esc/steel/mill/customer/get/${sId}`,
    method: 'GET'
  })
}

/**
 * 钢厂客户信息-新增/修改
 * @param {Object} data 客户信息
 * @returns {Promise}
 */
export const saveCustomer = (data) => {
  return request({
    url: `/esc/steel/mill/customer/save`,
    method: 'PUT',
    data: data
  })
}

/**
 * 钢厂客户信息-删除
 * @param {Array} ids ID数组
 * @returns {Promise}
 */
export const removeCustomers = (ids) => {
  return request({
    url: `/esc/steel/mill/customer/removes`,
    method: 'DELETE',
    data: ids
  })
}
