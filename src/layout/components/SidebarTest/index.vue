<template>
  <div :class="{ 'has-logo': showLogo }">
    <logo v-if="showLogo" :collapse="isCollapse" />
    <div class="sidebar-wrapper" style="height: 100%;">
    <el-scrollbar :native="isCollapse" wrap-class="scrollbar-wrapper">
        <el-menu
          mode="vertical"
          :collapse="isCollapse"
          :default-active="$route.meta.id"
          :default-openeds="defaultOpeneds"
          :unique-opened="uniqueOpen"
          menu-trigger="click"
          :background-color="variables.menuBg"
          :text-color="variables.menuText"
          :active-text-color="variables.menuActiveText"
          :collapse-transition="collapseTransition"
          :style="{ height: isCollapse ? '100%' : '100%' }"
        >
          <template  v-for="item in permission_routes">
            <sidebar-item
              v-if="!item.hidden"
              :key="item.meta.id"
              :is-collapse="isCollapse"
              :item="item"
              :link-props="linkProps"
              :base-path="item.path"
            />
          </template>
        </el-menu>
      </el-scrollbar>
    </div>
  </div>
</template>

<script>
import SidebarItem from './SidebarItem'
import Logo from './Logo'
import { mapGetters } from 'vuex'
import variables from '@/styles/variables.scss'

export default {
  name: 'Sidebar',
  components: { SidebarItem, Logo },
  props: {
    // 是否显示汉堡图标
    hamburger: {
      type: Boolean,
      default: false
    },
    // 是否只保持一个子菜单的展开
    uniqueOpen: {
      type: Boolean,
      default: true
    },
    // 是否开启折叠动画
    collapseTransition: {
      type: Boolean,
      default: false
    },
    // 全部菜单
    allMenu: {
      type: Array,
      default: () => []
    },
    // 外部链接参数
    linkProps: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      defaultOpeneds: []
    }
  },
  computed: {
    ...mapGetters([
      'sidebar',
      'permission_routes'
    ]),
    showLogo() {
      return this.$store.state.settings.sidebarLogo
    },
    variables() {
      return variables
    },
    isCollapse() {
      return !this.sidebar.opened
    }
  },
  created() {
    this.defaultOpeneds.push(this.permission_routes[0].meta.id)
  }
}
</script>
