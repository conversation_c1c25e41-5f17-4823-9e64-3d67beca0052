<script>
export default {
  name: 'MenuItem',
  functional: true,
  props: {
    icon: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    },
    isLast: {
      type: Boolean,
      default: false
    },
    isCollapse: {
      type: Boolean,
      default: false
    },
    level: {
      type: Number,
      default: 0
    },
    showingChild: {
      type: Boolean,
      default: false
    }
  },
  render(h, context) {
    const { icon, title, level, isCollapse } = context.props
    const vnodes = []

    if (icon) {
      vnodes.push(<cnd-icon name={icon} />)
    }

    if (title) {
      let flagTip = false // 是否文字提醒
      let titleClass = 'menu-limit' // 菜单限制初始类
      if (isCollapse) {
        vnodes.push(
          <span slot='title' class={titleClass}>
            {title}
          </span>
        )
      } else {
        const len = title.length // 标题长度
        const iconWidth = icon ? 22 : 0
        const textWidth = 152 - (16 + level * 12 + iconWidth)
        const textLimitNum = textWidth / 12
        if (len > textLimitNum) flagTip = true
        titleClass = icon
          ? `${titleClass} menu-icon-level-${level}`
          : `${titleClass} menu-level-${level}`
        if (flagTip) {
          vnodes.push(
            <el-tooltip
              slot='title'
              class='item'
              effect='dark'
              content={title}
              placement='right'
            >
              <span class={titleClass}>{title}</span>
            </el-tooltip>
          )
        } else {
          vnodes.push(
            <span slot='title' class={titleClass}>
              {title}
            </span>
          )
        }
      }
    }
    return vnodes
  }
}
</script>
