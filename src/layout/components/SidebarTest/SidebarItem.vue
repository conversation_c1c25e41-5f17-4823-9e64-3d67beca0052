<template>
  <div class="menu-wrapper">
    <template v-if="!item.children">
      <app-link v-if="item.meta.title" :to="resolvePath(item.path)">
        <el-menu-item
          :index="item.meta.id"
          :class="{ 'submenu-title-noDropdown': !isNest }"
          :style="{
            'padding-left': isCollapse ? 16 + 'px' : 16 + 12 * level + 'px',
          }"
        >
          <item
            is-last
            :icon="item.meta.icon"
            :title="$i18n.locale === 'EN' ? item.meta.titleEn : item.meta.title"
            :level="level"
            :is-collapse="isCollapse"
          />
        </el-menu-item>
      </app-link>
    </template>

    <el-submenu
      v-else
      ref="subMenu"
      :class="`customize-submenu-${level}`"
      :index="item.meta.id"
      :popper-class="`popper-submenu-${level}`"
      popper-append-to-body
    >
      <template slot="title">
        <item
          v-if="item.meta.title"
          :icon="item.meta.icon"
          :title="$i18n.locale === 'EN' ? item.meta.titleEn : item.meta.title"
          :level="level"
          :is-collapse="isCollapse"
        />
      </template>
      <sidebar-item
        v-for="child in item.children"
        :key="child.id"
        is-nest
        :item="child"
        :base-path="resolvePath(child.path)"
        :level="level + 1"
        :link-props="linkProps"
        class="nest-menu"
      />
    </el-submenu>
  </div>
</template>

<script>
import path from 'path'
import Item from './Item'
import AppLink from './Link'
import FixIOSBug from './FixiOSBug'
import { formatJumpUrl } from '@/utils/index'
export default {
  name: 'SidebarItem',
  components: { AppLink, Item }, // Item
  mixins: [FixIOSBug],
  props: {
    isCollapse: {
      type: Boolean,
      default: false
    },
    item: {
      type: Object,
      required: true
    },
    isNest: {
      type: Boolean,
      default: false
    },
    basePath: {
      type: String,
      default: ''
    },
    level: {
      type: Number,
      default: 0
    },
    linkProps: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {}
  },
  methods: {
    isExternal(path) {
      return /^(https?:|mailto:|tel:)/.test(path)
    },
    resolvePath(routePath) {
      if (this.isExternal(routePath)) {
        const urlParams = new URL(routePath)
        Object.entries(this.linkProps).forEach(([k, v]) => {
          if (urlParams.searchParams.has(k)) {
            urlParams.searchParams.set(k, v)
          } else {
            urlParams.searchParams.append(k, v)
          }
        })
        const url = urlParams.toString()
        return formatJumpUrl(url)
      }
      return path.resolve(this.basePath, routePath)
    }
  }
}
</script>
