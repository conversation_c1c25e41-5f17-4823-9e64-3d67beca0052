<template>
  <section id="app-main" class="app-main">
    <dialog-message v-if="loadingMessage" />
    <transition name="fade-transform" mode="out-in">
      <keep-alive :include="cachedViews">
        <router-view :key="key" />
      </keep-alive>
    </transition>
    <transition name="fade-transform" mode="out-in">
      <div class="app-class">
        <!-- <div id="microApp" class="microApp" /> -->
        <div v-for="item in appList" v-show="$route.path.startsWith(`/${item.name}/`)" :id="item.name" :key="item.name" class="h100" />
      </div>
    </transition>
  </section>
</template>

<script>
import configSubApp from '@/subapp.config' // 子应用配置
import dialogMessage from '@/components/DialogMessage'

export default {
  name: 'AppMain',
  components: {
    dialogMessage
  },
  data() {
    return {
      loadingInstance: '',
      appList: configSubApp
    }
  },
  computed: {
    cachedViews() {
      return this.$store.state.tagsView.cachedViews
    },
    key() {
      return this.$route.fullPath
    },
    loadingMessage() {
      return this.$store.state.user.firstLoading
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/styles/variables.scss";

.app-main {
  /* 50= navbar  50  */
  min-height: calc(100vh - 50px);
  width: 100%;
  position: relative;
  overflow: hidden;
  // background-color: #ffffff;
  .app-class {
    position: absolute;
    top: $mainTop;
    right: 0;
    left: 0;
    bottom: 0;
    z-index: 1;
    .microApp {
      height: 100%;
    }
  }
}

.fixed-header+.app-main {
  padding-top: 50px;
}

.hasTagsView {
  .app-main {
    /* 84 = navbar + tags-view = 50 + 34 */
    min-height: calc(100vh - 0px);
  }

  .fixed-header+.app-main {
    padding-top: $mainTop;
  }
}
</style>

