<template>
  <div v-if="!item.hidden" class="menu-wrapper">
    <template
      v-if="
        hasOneShowingChild(item.children, item) &&
          (!onlyOneChild.children || onlyOneChild.noShowingChildren) &&
          !item.alwaysShow
      "
    >
      <app-link
        v-if="onlyOneChild.meta.title"
        :to="resolvePath(onlyOneChild.path)"
      >
        <el-menu-item
          :index="onlyOneChild.meta.id"
          :class="{ 'submenu-title-noDropdown': !isNest }"
          :style="{
            paddingLeft: isCollapse
              ? 20 + 'px'
              : 20 + 12 * level + 'px !important'
          }"
        >
          <item
            is-last
            :icon="onlyOneChild.meta.icon || item.meta.icon"
            :title="generateTitle(onlyOneChild.meta.title)"
            :showing-children="onlyOneChild.noShowingChildren"
            :level="level"
            :is-collapse="isCollapse"
          />
        </el-menu-item>
      </app-link>
    </template>

    <el-submenu
      v-else
      ref="subMenu"
      :class="`customize-submenu-${level}`"
      :index="item.meta.id"
      popper-append-to-body
    >
      <template slot="title">
        <item
          v-if="item.meta.title"
          :icon="item.meta.icon"
          :title="generateTitle(item.meta.title)"
          :level="level"
          :is-collapse="isCollapse"
        />
      </template>
      <sidebar-item
        v-for="child in item.children"
        :key="child.id"
        is-nest
        :item="child"
        :base-path="resolvePath(child.path)"
        :level="level + 1"
        class="nest-menu"
      />
    </el-submenu>
  </div>
</template>

<script>
import path from 'path'
import { generateTitle } from '@/utils/i18n'
import Common from '@/utils/common'
import Item from './Item'
import AppLink from './Link'
import FixiOSBug from './FixiOSBug'
import { mapGetters } from 'vuex'

export default {
  name: 'SidebarItem',
  components: { Item, AppLink },
  mixins: [FixiOSBug],
  props: {
    // route object
    item: {
      type: Object,
      required: true
    },
    isNest: {
      type: Boolean,
      default: false
    },
    basePath: {
      type: String,
      default: ''
    },
    level: {
      type: Number,
      default: 0
    }
  },
  data() {
    // To fix https://github.com/PanJiaChen/vue-admin-template/issues/237
    // TODO: refactor with render function
    this.onlyOneChild = null
    return {}
  },
  computed: {
    ...mapGetters(['sidebar']),
    isCollapse() {
      return !this.sidebar.opened
    }
  },
  methods: {
    hasOneShowingChild(children = [], parent) {
      const showingChildren = children.filter((item) => {
        if (item.hidden) {
          return false
        } else {
          // Temp set(will be used if only has one showing child)
          this.onlyOneChild = item
          // console.log('onlyOneChild--', this.onlyOneChild)
          return true
        }
      })

      // When there is only one child router, the child router is displayed by default
      if (showingChildren.length === 1) {
        return true
      }

      // Show parent if there are no child router to display
      if (showingChildren.length === 0) {
        this.onlyOneChild = { ...parent, path: '', noShowingChildren: true }
        return true
      }

      return false
    },
    resolvePath(routePath) {
      if (Common.isExternal(routePath)) {
        return routePath
      }
      // console.log('routePath--', routePath)
      // console.log('this.basePath--', this.basePath)
      // console.log('hebing--', path.resolve(this.basePath, routePath))
      return path.resolve(this.basePath, routePath)
    },

    generateTitle
  }
}
</script>
