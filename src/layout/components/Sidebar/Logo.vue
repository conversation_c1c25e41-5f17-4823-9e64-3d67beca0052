<template>
  <div class="sidebar-logo-container" :class="{'collapse': collapse}">
    <transition name="sidebarLogoFade">
      <router-link v-if="collapse" key="collapse" class="sidebar-logo-link" to="/">
        <img class="sidebar-logo-shrink" data-v-03c27fff="" :src="logo">
      </router-link>
      <router-link v-else key="expand" class="sidebar-logo-link flexC" to="/">
        <!-- <img class="sidebar-logo-unfold" data-v-03c27fff="" :src="logo"> -->
        <img class="sidebar-title" :src="logoName" alt="">
      </router-link>
    </transition>
    ::after
  </div>
</template>

<script>
export default {
  name: 'SidebarLogo',
  props: {
    collapse: {
      type: Boolean,
      required: true
    }
  },
  data() {
    return {
      title: 'admin-template',
      // logo: 'https://wpimg.wallstcn.com/69a1c46c-eb1c-4b46-8bd4-e9e686ef5251.png'
      logo: require('@/assets/login/logo.png'),
      logoName: require('@/assets/login/horizon.png')
    }
  }
}
</script>

<style lang="scss" scoped>
.flexC {
  display: flex !important;
  align-items: center;
}
</style>
