<template>
  <div class="navbar flexC">
    <hamburger :is-active="sidebar.opened" class="hamburger-container" @toggleClick="toggleSideBar" />
    <!-- <breadcrumb class="breadcrumb-container" /> -->
    <tags-view v-if="needTagsView" class="breadcrumb-container" />
    <div class="right-menu">
      <template v-if="device!=='mobile'">
        <annualReview class="right-menu-item"/>
        <search class="right-menu-item" />
        <message v-if="!bellShow" class="right-menu-item hover-effect" />
        <screenfull class="right-menu-item hover-effect" />
        <!-- <el-tooltip content="布局大小" effect="dark" placement="bottom">
          <size-select class="right-menu-item hover-effect" />
        </el-tooltip> -->
        <lang-select class="right-menu-item hover-effect" />
      </template>
      <el-dropdown class="avatar-container right-menu-item hover-effect" trigger="click">
        <div class="avatar-wrapper">
          <!-- <img :src="avatar+'?imageView2/1/w/80/h/80'" class="user-avatar"> -->
          <span v-if="name" class="cnd-username">{{ name }}</span>
          <cnd-icon v-else name="cnd-account" />
          <!-- <i class="el-icon-caret-bottom" /> -->
        </div>
        <el-dropdown-menu slot="dropdown">
          <!-- 首页 -->
          <router-link to="/">
            <el-dropdown-item>{{ $t('navbar.home') }}</el-dropdown-item>
          </router-link>
          <!-- 密码修改 -->
          <el-dropdown-item divided>
            <span style="display:block;" @click="mdfPassword">{{ $t('login.changePassword') }}</span>
          </el-dropdown-item>
          <!-- 通知设置 -->
          <el-dropdown-item divided>
            <span style="display:block;" @click="visibleMessage = true">通知设置</span>
          </el-dropdown-item>
          <!-- 退出 -->
          <el-dropdown-item divided>
            <span style="display:block;" @click="logout">{{ $t('login.logout') }}</span>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
    <!-- 密码修改 -->
    <div v-show="visiblePwd" class="fixed_mask flexCC">
      <div class="operate_wrap bg-white" @close="close">
        <p class="title">{{ $t('grid.others.userPasswordChange') }}</p>
        <div>
          <el-form ref="pswForm" :model="pswForm" :rules="pswRules" label-width="100px">
            <el-row>
              <el-col :span="22">
                <el-form-item :label="$t('grid.others.accountNumber')" prop="account">
                  <el-input v-model="pswForm.account" size="mini" disabled />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="22">
                <el-form-item :label="$t('grid.others.oldCode')" prop="oldPassword">
                  <el-input v-model="pswForm.oldPassword" size="mini" :type="oldPassword" />
                  <span class="show-pwd" @click="showPwd('oldPassword')">
                    <cnd-icon v-show="oldPassword === 'password'" name="cnd-eyes-close" />
                    <cnd-icon v-show="oldPassword !== 'password'" name="cnd-eyes" />
                  </span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="22">
                <el-form-item :label="$t('grid.btns.newPassword')" prop="password">
                  <div class="flexC">
                    <div clsss="new-password" style="position: relative;">
                      <el-input v-model="pswForm.password" size="mini" :type="newPassword" @input="inputAction" />
                      <span class="show-pwd" @click="showPwd('newPassword')">
                        <cnd-icon v-show="newPassword === 'password'" name="cnd-eyes-close" />
                        <cnd-icon v-show="newPassword !== 'password'" name="cnd-eyes" />
                      </span>
                    </div>
                    <div class="strong-level flexC" style="width:30%;margin-left:10px">
                      <span :class="{'border': true, 'bg-red': strongLevel === 1 }">{{ $t('grid.others.weak') }}</span>
                      <span :class="{'border': true, 'bg-yellow': strongLevel === 2 }">{{ $t('grid.others.middle') }}</span>
                      <span :class="{'border': true, 'bg-green': strongLevel > 2}">{{ $t('grid.others.strong') }}</span>
                    </div>
                  </div>
                </el-form-item>
              </el-col>
              <!-- <el-col :span="4">
                <el-form-item label-width="0">
                  <div class="strong-level flexC ml-10">
                    <span :class="{'border': true, 'bg-red': strongLevel === 1 }">弱</span>
                    <span :class="{'border': true, 'bg-yellow': strongLevel === 2 }">中</span>
                    <span :class="{'border': true, 'bg-green': strongLevel > 2}">强</span>
                  </div>
                </el-form-item>
              </el-col> -->
            </el-row>
            <el-row>
              <el-col :span="22">
                <el-form-item :label="$t('grid.others.passwordConfirmation')" prop="confirmPsw">
                  <el-input v-model="pswForm.confirmPsw" size="mini" :type="checkPassword" />
                  <span class="show-pwd" @click="showPwd('checkPassword')">
                    <cnd-icon v-show="checkPassword === 'password'" name="cnd-eyes-close" />
                    <cnd-icon v-show="checkPassword !== 'password'" name="cnd-eyes" />
                  </span>
                </el-form-item>
              </el-col>
            </el-row>
            <p class="pwd_tip">{{ $t('grid.others.passwordMustBeAnyTherThan8Key') }}</p>
          </el-form>
        </div>
        <div class="btn-action">
          <el-button size="mini" @click="close">{{ $t('btns.cancel') }}</el-button>
          <el-button
            type="primary"
            size="mini"
            @click="confirmMdf"
          >{{ $t('btns.confirmKey') }}</el-button>
        </div>
      </div>
    </div>
    <!-- 通知设置 -->
    <div v-show="visibleMessage" class="fixed_mask flexCC">
      <div class="message_wrap bg-white" @close="closeMessage">
        <div class="title">
          <span>通知设置</span>
          <cnd-icon name="cnd-close" class="cnd-close" @click="closeMessage" />
        </div>
        <div class="content">
          <div v-for="(item, index) in  messageData" :key="index" class="message-wrap" >
            <div class="message-header">
              <div class="message-title">{{ item.categoryName }}</div>
              <el-switch v-model="item.openFlag" @change="changeSwitch(item, 'parent')" />
            </div>
            <div  v-for="(i, index2) in  item.templateConfigList" :key="index2" class="message-content">
              <div class="con-name"><span class="name">{{ i.title }}</span>
                <el-popover
                  v-if="i.description !== ''"
                  placement="right-start"
                  trigger="hover"
                  class="ag-custom-header-popver"
                >
                  <div style="max-width: 320px;">{{ i.description }}</div>
                  <span slot="reference">
                    <cnd-icon name="cnd-question-mark" color="#409EFF" />
                  </span>
                </el-popover>
              </div>
              <div class="con-type">{{ i.modesStr }}</div>
              <el-switch v-model="i.openFlag" class="con-switch" @change="changeSwitch(item, 'child', i)" />
            </div>
          </div>
        </div>
        <el-pagination
          class="message-pagination"
          size="default"
          background
          layout="total, sizes, prev, pager, next"
          :total="total"
          :page-size="pagination.pageSize"
          :current-page="pagination.pageNo"
          :page-sizes="[2, 3, 5, 10]"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex'
// import Breadcrumb from '@/components/Breadcrumb'
import Hamburger from '@/components/Hamburger'
import TagsView from './TagsView'
import Screenfull from '@/components/Screenfull'
// import SizeSelect from '@/components/SizeSelect'
import LangSelect from '@/components/LangSelect'
import Search from '@/components/HeaderSearch'
import Message from '@/components/HeaderMessage'
import AnnualReview from '@/components/AnnualReview'
import { mdfPassword } from '@/api/user'
import { MessageUtil } from 'cnd-horizon-utils'
import { switchQueryList, switchConfig } from '@/api/message.js'

export default {
  components: {
    // Breadcrumb,
    LangSelect,
    Hamburger,
    Screenfull,
    // SizeSelect,
    TagsView,
    Search,
    Message,
    AnnualReview
  },
  data() {
    return {
      oldPassword: 'password',
      newPassword: 'password',
      checkPassword: 'password',
      visiblePwd: false,
      visibleMessage: false,
      // 密码修改
      pswForm: {
        account: '',
        oldPassword: '',
        password: '',
        confirmPsw: ''
      },
      pswRules: {
        oldPassword: [
          { required: true, message: this.$t('grid.others.pleaseEnterTheOldPassword'), trigger: ['blur', 'change'] }
        ],
        password: [
          { required: true, message: this.$t('grid.others.pleaseEnterNewPassword'), trigger: ['blur', 'change'] }
        ],
        confirmPsw: [
          { required: true, message: this.$t('grid.others.pleaseConfirmThePasswordAgain'), trigger: ['blur', 'change'] }
        ]
      },
      strongLevel: 0,
      // avatar: 'https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif',
      // name: 'chenjianbin',
      device: 'desktop',
      value1: true,
      total: 0,
      pagination: {
        pageNo: 1,
        pageSize: 2
      },
      messageData: []

      // sidebar: { 'opened': true, 'withoutAnimation': false }
    }
  },
  computed: {
    ...mapGetters([
      'sidebar',
      'name'
      // 'avatar',
      // 'device'
    ]),
    ...mapState({
      needTagsView: state => state.settings.tagsView
    }),
    // 小铃铛在买家系统不展示
    bellShow() {
      const hostname = location.hostname
      const allowedHostnames = ['suptest.esteellink.com', 'support.esteellink.com', 'mall']
      return allowedHostnames.some(host => hostname.includes(host))
    }
  },
  watch: {
    visibleMessage(value) {
      if (value) {
        this.handleCurrentChange(1)
      }
    }
  },
  methods: {
    // 显示密码
    showPwd(val) {
      if (this[val] === 'password') {
        this[val] = ''
      } else {
        this[val] = 'password'
      }
    },
    // 密码强度实时显示
    inputAction(v) {
      // 密码强度提示
      this.strongLevel = this.pswStrengthDetect(v)
    },
    // 确认密码修改
    confirmMdf() {
      this.$refs.pswForm.validate().then(res => {
        if (this.pswForm.password === this.pswForm.oldPassword) {
          MessageUtil.warning(this.$t('grid.others.newAndOldPasswordsAseResetKey'))
          return
        }
        if (this.pswForm.password !== this.pswForm.confirmPsw) {
          MessageUtil.warning(this.$t('grid.others.twoDifferentPasswordsPleaseReEnter'))
          this.pswForm.password = ''
          this.pswForm.confirmPsw = ''
          this.$nextTick(() => {
            this.$refs.pswForm.clearValidate()
          })
          return
        }
        if (this.strongLevel < 2) {
          MessageUtil.warning(this.$t('grid.others.passwordMustBeAnyTherThan8Key'))
          this.$nextTick(() => {
            this.$refs.pswForm.clearValidate()
          })
          return
        }
        // delete this.pswForm.confirmPsw
        // 调修改密码接口
        mdfPassword(this.pswForm).then(res => {
          this.close()
          // this.query()
          MessageUtil.success(this.$t('grid.others.passwordChangeSuccessfully'))
          setTimeout(() => {
            this.$store.dispatch('user/logout').then(() => {
              // 重置tab-view
              this.$store.dispatch('tagsView/delAllViews').then(() => {})
              // 重置动态路由变量
              this.$store.dispatch('permission/GenerateRoutes', 'reset').then(() => {
                this.$router.push(`/login`)
              })
            }).catch(err => {
              console.log('logout err--', err)
            })
          }, 1000)
        }).catch(err => {
          console.log('密码修改错误--', err)
        })
      }).catch(err => {
        console.log(err)
      })
    },
    // 关闭密码修改弹出框
    close() {
      // this.pswForm.confirmPsw = ''
      this.$refs.pswForm.resetFields()
      this.visiblePwd = false
    },
    closeMessage() {
      this.visibleMessage = false
    },
    // 修改密码
    mdfPassword() {
      // console.log('curSelRowData--', this.curSelRowData)
      // 用户信息存到缓存
      const userInfo = JSON.parse(sessionStorage.getItem('userInfo'))
      console.log('userInfo--', userInfo)
      // 获取当前用户信息
      this.pswForm.account = userInfo.account
      this.pswForm.id = userInfo.id
      this.pswForm.versionNo = userInfo.versionNo
      this.visiblePwd = true
    },
    confirmMessage() {

    },
    messageSet() {
      // 通知设置
      switchQueryList({
        ...this.pagination
      }).then((res) => {
        this.messageData = res.data.records
        this.total = res.data.total
      })
    },
    handleCurrentChange(val) {
      this.pagination.pageNo = val
      this.messageSet()
    },
    handleSizeChange(val) {
      this.pagination.pageSize = val
      this.messageSet()
    },
    changeSwitch(data, type, i) {
      if (type === 'parent') {
        data.templateConfigList.map((item) => {
          item.openFlag = data.openFlag
        })
      }
      if (type === 'child') {
        const res = data.templateConfigList.some((i) => !i.openFlag)
        if (res) {
          data.openFlag = false
        } else {
          data.openFlag = true
        }
      }
      switchConfig({
        switchConfig: data
      }).then((res) => {
        this.messageSet()
      })
    },
    // 密码强度检测
    pswStrengthDetect(psw) {
      let modes = 0
      // 正则匹配
      if (psw.length < 1) return modes
      if (/\d/.test(psw)) modes++
      if (/[a-z]/.test(psw)) modes++
      if (/[A-Z]/.test(psw)) modes++
      if (/\W/.test(psw)) modes++
      if (psw.length < 8) {
        modes = 1
      }
      // 强度返回
      switch (modes) {
        case 1:
          return 1
        case 2:
          return 2
        case 3:
        case 4:
          return psw.length < 12 ? 3 : 4
      }
    },
    toggleSideBar() {
      // const curWindowWidth = document.body.clientWidth
      // console.log('当前窗口宽度--', curWindowWidth)
      // if (curWindowWidth < 1000) return
      this.$store.dispatch('app/toggleSideBar')
      // 触发resize事件
      this.$nextTick(() => {
        const myEvent = new CustomEvent('menuResize', {
          detail: {
            isOpened: this.sidebar.opened
          },
          bubbles: true,
          cancelable: false
        })
        window.dispatchEvent(myEvent)
      })
    },
    async logout() {
      await this.$store.dispatch('user/logout').then(() => {
        // 重置tab-view
        this.$store.dispatch('tagsView/delAllViews').then(() => {})
        // 重置动态路由变量
        this.$store.dispatch('permission/GenerateRoutes', 'reset').then(() => {
          // this.$router.push(`/login?redirect=${this.$route.fullPath}`)
          this.$router.push(`/login`)
        })
      }).catch(err => {
        console.log('logout err--', err)
      })
    }
  }
}
</script>

<style lang="scss">
 @import "~@/styles/variables.scss";
$dark_gray: #889aa4;

.navbar {
  height: $navbarHeight;
  position: relative;
  background: #fff;
  /*box-shadow: 0 1px 4px rgba(0,21,41,.08);*/
  border-bottom: 1px solid #F3F4F6;
  box-shadow: 5px 0px 6px 0px #9ba4b270;
  // 密码修改
  .fixed_mask {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1000;
    background-color: rgba(0, 0, 0, .3);
    .operate_wrap {
      width: 35%;
      height: 500px;
      border-radius: 2px;
      .title {
        margin: 0;
        padding: 10px;
        border-bottom: 1px solid #dfe4ed;
      }
      .pwd_tip {
        font-size: 12px;
        color: #999;
        margin: 4px 0 0 100px;
        // margin: 0 22px 10px 100px;
      }
      .el-form{
        padding:10px 0;
        .el-row{
          margin-bottom: 10px;
          &:nth-child(4){
            margin-bottom: 0;
          }
        }
      }
    }
    .message_wrap {
      width: 700px;
      height: 500px;
      border-radius: 2px;
      position: relative;
      .title {
        margin: 5px 0 0 0;
        padding: 10px;
        border-bottom: 1px solid #dfe4ed;
        display: flex;
        justify-content: space-between;
        font-size: 16px;
        font-weight: 600;
        .cnd-close {
          cursor: pointer;
        }
      }
      .content {
        margin: 15px;
        min-height: 300px;
        max-height: 370px;
        margin-bottom: 15px;
        overflow-y: auto;
        .message-wrap {
          margin-bottom: 10px;
          .message-header {
            display: flex;
            justify-content: space-between;
            padding: 5px;
            height: 24px;
            align-items: center;
            margin-bottom: 5px;
            .message-title {
              font-size: 14px;
              font-weight: 600;
            }
          }
          .message-content {
            display: flex;
            justify-content: space-between;
            border: 1px solid #dfe4ed;
            line-height: 22px;
            padding: 5px;
            .con-name {
              padding-left: 10px;
              width: 300px;
              display: flex;
              .name {
                display: inline-block;
                max-width: 270px;
                overflow:hidden;
                text-overflow:ellipsis;
                white-space:nowrap;
                margin-right: 3px;
              }
            }
            .con-type {
              flex: 1;
              overflow:hidden;
              text-overflow:ellipsis;
              white-space:nowrap;
            }
            .con-switch {
              width: 100px;
              text-align: right;
              display: flex;
              justify-content: end;
            }
          }
        }
      }
      .message-pagination {
        position: absolute;
        bottom: 20px;
        right: 20px;
      }
    }
    .el-form-item {
      margin-bottom: 0;
      .el-form-item__content,.el-form-item__label{
        line-height: 28px;
      }
    }
    .el-form-item.is-error {
      margin-bottom: 15px;
    }
    .btn-action {
      text-align: right;
      padding:10px;
      border-top: 1px solid #dfe4ed;
    }
    .show-pwd {
      position: absolute;
      right: 10px;
      top: 2px;
      font-size: 20px;
      color: $dark_gray;
      cursor: pointer;
      user-select: none;
    }
  }

  .hamburger-container {
    line-height: ($navbarHeight - 4px);
    height: 100%;
    // float: left;
    cursor: pointer;
    transition: width .3s;
    -webkit-tap-highlight-color:transparent;

    &:hover {
      background: rgba(0, 0, 0, .025)
    }
  }

  .breadcrumb-container {
    // float: left;
    flex: 1;
  }

  .errLog-container {
    display: inline-block;
    vertical-align: top;
  }

  .right-menu {
    // float: right;
    height: 100%;
    line-height: $navbarHeight;
    width: max-content;

    &:focus {
      outline: none;
    }

    .right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      font-size: $SvgSize;
      color: #5a5e66;
      vertical-align: text-bottom;

      &.hover-effect {
        cursor: pointer;
        transition: background .3s;

        &:hover {
          background: rgba(0, 0, 0, .025)
        }
      }
    }

    .avatar-container {
      margin-right: 10px;

      .avatar-wrapper {
        position: relative;

        .user-avatar {
          cursor: pointer;
          width: 34px;
          height: 34px;
          border-radius: 20px;
        }

        .el-icon-caret-bottom {
          cursor: pointer;
          position: absolute;
          right: -20px;
          top: 15px;
          font-size: 12px;
        }

        .cnd-username {
          max-width: 80px;
          font-size: 16px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
  }
}

</style>
