<template>
  <div class="tags-view-container">
    <div class="tags-view-wrapper">
      <router-link
        v-for="(tag, index) in visitedViews"
        :key="tag.path"
        ref="tag"
        :style="{ 'padding': index === 0? '0 5px' : '0 17px 0 5px', 'max-width': index === 0 || isActive(tag) ? '100%' : '','overflow': index === 0 || isActive(tag) ? 'unset' :''}"
        :class="isActive(tag) ? 'active' : ''"
        :to="{ path: tag.path, query: tag.query, fullPath: tag.fullPath }"
        @click.middle.native="closeSelectedTag(tag)"
        @contextmenu.prevent.native="openMenu(tag, $event)"
      >
        <div
          @dragstart="dragStart($event, index)"
          @dragover="allowDrop"
          @drop="drop($event, index, visitedViews)"
        >
          <el-popover
            popper-class="tags-view-pop"
            visible-arrow="false"
            placement="bottom-start"
            trigger="hover"
            :draggable="true"
            :content="tag.query && tag.query.type ? tag.query.name : generateTitle(tag.meta.titleT?tag.meta.titleT:tag.meta.title)"
          >
            <el-button slot="reference" class="tags-view-item">{{ tag.query && tag.query.type ? tag.query.name : generateTitle(tag.meta.titleT?tag.meta.titleT:tag.meta.title) }}</el-button>
          </el-popover>
          <div v-if="!tag.meta.affix" class="close-icon">
            <cnd-icon
              :ref="isActive(tag) ? 'closeTag' : ''"
              :data-tag="tag"
              class="el-icon-close"
              name="cnd-close-circle"
              @click.prevent.stop="closeSelectedTag(tag)"
            />
          </div>

        </div>
      </router-link>
    </div>
    <ul
      v-show="visible"
      :style="{ left: left + 'px', top: top + 'px' }"
      class="contextmenu"
    >
      <li @click="refreshSelectedTag(selectedTag)">{{ $t('navbar.refresh') }}</li>
      <li v-if="!(selectedTag.meta && selectedTag.meta.affix)" @click="closeSelectedTag(selectedTag)">{{ $t('navbar.close') }}</li>
      <li @click="closeOthersTags">{{ $t('navbar.closeOther') }}</li>
      <li @click="closeAllTags(selectedTag)">{{ $t('navbar.closeAll') }}</li>
      <li v-if="!(selectedTag.meta && selectedTag.meta.affix)" @click="close(selectedTag,'left')">{{ $t('navbar.closeLeftSide') }}</li>
      <li @click="close(selectedTag,'right')">{{ $t('navbar.closeRightSide') }}</li>
    </ul>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
// import ScrollPane from './ScrollPane'
import { generateTitle } from '@/utils/i18n'
import path from 'path'
import actions from '../../../shared/action'

export default {
  // components: { ScrollPane },
  data() {
    return {
      maxwidth: 100,
      visible: false,
      top: 0,
      left: 0,
      selectedTag: {},
      affixTags: [],
      rePage: ''
    }
  },
  computed: {
    ...mapGetters(['sidebar', 'visitedViews']),
    visitedViews() {
      return this.$store.state.tagsView.visitedViews
    },
    routes() {
      return this.$store.state.permission.routes
    }
  },
  watch: {
    $route() {
      this.addTags()
      this.moveToCurrentTag()
      const views = JSON.parse(sessionStorage.getItem('visitedViews'))
      this.maxwidth = 95 / Number(views.length)
    },
    visible(value) {
      if (value) {
        document.body.addEventListener('click', this.closeMenu)
      } else {
        document.body.removeEventListener('click', this.closeMenu)
      }
    }
  },
  created() {
    const views = sessionStorage.getItem('visitedViews')
    if (views) {
      this.$store.dispatch('tagsView/setVisitedView', JSON.parse(views))
    }
  },
  mounted() {
    const _this = this
    this.initTags()
    this.addTags()
    actions.onGlobalStateChange(state => {
      if (state.g6_visible === false) {
        _this.closeSelectedTag({ path: '/editor/flow' })
      }
      if (state.deleteCurrent) {
        _this.closeSelectedTag(_this.$refs.closeTag[0].$attrs['data-tag'])
        actions.setGlobalState({ deleteCurrent: false })
        this.rePage = state.rePage
      }
    })
  },
  methods: {
    allowDrop(e) {
      e.preventDefault()
    },
    dragStart(e, index) {
      e.dataTransfer.setData('index', index)
    },
    drop(e, index, data) {
      this.allowDrop(e)
      const dragIndex = Number(e.dataTransfer.getData('index'))
      if (index !== 0 && dragIndex !== 0) {
        const temp = data.splice(dragIndex, 1)
        data.splice(index, 0, temp[0])
      }
      this.$store.dispatch('tagsView/setVisitedView', data)
      sessionStorage.setItem('visitedViews', JSON.stringify(data))
    },
    generateTitle, // generateTitle by vue-i18n
    isActive(route) {
      return route.fullPath === this.$route.fullPath
    },
    filterAffixTags(routes, basePath = '/') {
      let tags = []
      routes.forEach(route => {
        if (route.meta && route.meta.affix) {
          const tagPath = path.resolve(basePath, route.path)
          tags.push({
            fullPath: tagPath,
            path: tagPath,
            // name: route.name,
            meta: { ...route.meta }
          })
        }
        if (route.children) {
          const tempTags = this.filterAffixTags(route.children, route.path)
          if (tempTags.length >= 1) {
            tags = [...tags, ...tempTags]
          }
        }
      })
      return tags
    },
    initTags() {
      const affixTags = (this.affixTags = this.filterAffixTags(this.routes))
      for (const tag of affixTags) {
        // Must have tag name
        if (tag.path) {
          this.$store.dispatch('tagsView/addVisitedView', tag)
        }
      }
    },
    addTags() {
      const { name } = this.$route
      if (name) {
        this.$store.dispatch('tagsView/addView', this.$route)
      }
      return false
    },
    moveToCurrentTag() {
      const tags = this.$refs.tag
      this.$nextTick(() => {
        for (const tag of tags) {
          if (tag.to.path === this.$route.path) {
            // this.$refs.scrollPane.moveToTarget(tag)
            if (tag.to.fullPath !== this.$route.fullPath) {
              this.$store.dispatch('tagsView/updateVisitedView', this.$route)
            }
            break
          }
        }
      })
    },
    refreshSelectedTag(view) {
      this.$store.dispatch('tagsView/delCachedView', view).then(() => {
        const { fullPath } = view
        this.$nextTick(() => {
          this.$router.replace({ path: '/redirect' + fullPath }).then(() => {
            if ('/redirect' + fullPath === this.$route.fullPath) {
              actions.setGlobalState({ refreshTags: true })
            }
          }).catch((err) => {
            console.log('err--', err)
          })
        })
      })
    },
    closeSelectedTag(view) {
      this.$store
        .dispatch('tagsView/delView', view)
        .then(({ visitedViews }) => {
          if (this.isActive(view)) {
            this.toLastView(visitedViews)
          }
        })
      // 触发resize事件
      this.$nextTick(() => {
        const myEvent = new CustomEvent('menuResize', {
          detail: {
            isOpened: this.sidebar.opened,
            visitedViews: this.visitedViews
          },
          bubbles: true,
          cancelable: false
        })
        window.dispatchEvent(myEvent)
      })
    },
    closeOthersTags() {
      this.$router.push(this.selectedTag)
      this.$store
        .dispatch('tagsView/delOthersViews', this.selectedTag)
        .then(() => {
          this.moveToCurrentTag()
        })
    },
    closeAllTags(view) {
      this.$store.dispatch('tagsView/delAllViews').then(({ visitedViews }) => {
        // if (this.affixTags.some(tag => tag.path === view.path)) {
        //   return
        // }
        this.toLastView(visitedViews)
      })
    },
    close(view, direction = null) {
      if (direction === 'right') {
        this.$store.dispatch('tagsView/delRightViews', view).then(({ visitedViews }) => {
          this.toLastView(visitedViews)
        })
      } else if (direction === 'left') {
        this.$store.dispatch('tagsView/delLeftViews', view).then(({ visitedViews }) => {
        })
      }
    },
    toLastView(visitedViews) {
      const latestView = visitedViews.slice(-1)[0]
      if (this.rePage) {
        this.$router.push(this.rePage)
        this.rePage = ''
      } else if (latestView) {
        this.$router.push(latestView)
      } else {
        this.$router.push('/')
      }
    },
    openMenu(tag, e) {
      const menuMinWidth = 105
      const offsetLeft = this.$el.getBoundingClientRect().left // container margin left
      const offsetWidth = this.$el.offsetWidth // container width
      const maxLeft = offsetWidth - menuMinWidth // left boundary
      const left = e.clientX - offsetLeft + 15 // 15: margin right

      if (left > maxLeft) {
        this.left = maxLeft
      } else {
        this.left = left
      }

      this.top = e.clientY
      this.visible = true
      this.selectedTag = tag
    },
    closeMenu() {
      this.visible = false
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/styles/tags.scss';
</style>

<style lang="scss">
.tags-view-pop{
  padding:2px 5px !important;
  font-size: 12px !important;
  min-width: max-content !important;
}
</style>
