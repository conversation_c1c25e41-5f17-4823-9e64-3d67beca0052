import Cookies from 'js-cookie'

const SessionKey = 'JSESSIONID'
const TokenKey = 'TOKEN'

export function getSessionId() {
  return Cookies.get(SessionKey)
}

export function setSessionId(sessionId) {
  return Cookies.set(SessionKey, sessionId)
}

export function removeSessionId() {
  return Cookies.remove(SessionKey)
}

export function getToken() {
  return Cookies.get(TokenKey)
}

export function setToken(token) {
  return Cookies.set(TokenKey, token)
}

export function removeToken() {
  return Cookies.remove(TokenKey)
}
