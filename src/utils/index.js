import store from '@/store'
import { configSubApp, subApp } from '@/subapp.config' // 子应用配置
import { loadMicroApp } from 'qiankun'
import { MessageUtil } from 'cnd-horizon-utils'
import actions from '@/shared/action'
import { getToken } from '@/utils/auth'
// 触发resize事件
export function menuResize() {
  const myEvent = new CustomEvent('menuResize', {
    detail: {
      isOpened: store.getters.sidebar.opened,
      visitedViews: store.getters.visitedViews
    },
    bubbles: true,
    cancelable: false
  })
  window.dispatchEvent(myEvent)
}

// 判断当前跳转路由是否授权
export function judgeAuth(to) {
  let flagAuth = false
  if (to.meta && (to.meta.id || to.query.activeId)) {
    const unformattedMenu = store.getters.unformattedMenu
    flagAuth = to.meta.id ? matchRoute(unformattedMenu, to.meta.id) : matchRoute(unformattedMenu, to.query.activeId)
  }
  return flagAuth
}

// 遍历是否存在匹配id
export function matchRoute(arr, matchId) {
  for (const item of arr) {
    if (item.id === matchId) {
      return true
    }
    if (item.children && item.children.length) {
      const temp = matchRoute(item.children, matchId)
      if (temp) {
        return true
      }
    }
  }
}

// 规范菜单数据格式
export const formatMenu = (arr) => {
  const temp_menu = []
  arr.forEach(item => {
    const obj = {
      path: item.url,
      name: item.url,
      meta: {
        titleT: item.url === sessionStorage.getItem('navbar') ? '首页' : item.title,
        title: item.title,
        icon: item.icon,
        orderNo: item.orderNo,
        id: item.id,
        activeId: item.id,
        categoryId: item.categoryId,
        parentId: item.parentId,
        affix: item.url === sessionStorage.getItem('navbar')
      }
    }
    if (item.children && item.children.length) {
      obj.children = formatMenu(item.children)
    }
    if (sessionStorage.getItem('navbar') === obj.path && obj.fullPath) {
      store.dispatch('tagsView/addView', obj)
    }

    temp_menu.push(obj)
  })
  return temp_menu
}

export function loadApp(data = []) {
  const apps = getApps(data)
  apps && apps.forEach(item => {
    if (!store.state.app.microSystem[item]) {
      const appInfo = configSubApp.filter(item_ => {
        return item_.name === item
      })
      if (appInfo && appInfo.length > 0) {
        setTimeout(() => {
          const tempObj = {}
          tempObj[item] = loadMicroApp(appInfo[0], {
            excludeAssetFilter: false
          })
          store.dispatch('app/setSystemMicro', tempObj)
          Promise.all([store.state.app.microSystem[item].mountPromise]).then().catch(() => {
            process.env.NODE_ENV !== 'development' && MessageUtil.error('加载失败，请重试！')
          })
        }, 10)
      }
    }
  })
}

export function getApps(arr = []) {
  arr.forEach(item => {
    const obj = {}
    if (item.children && item.children.length) {
      obj.children = getApps(item.children)
    } else {
      const app = item.url ? item.url.split('/')[1] : ''
      const apps = JSON.parse(localStorage.getItem('apps')) || []
      if (!apps.includes(app) && subApp.includes(app)) {
        app && apps.push(app)
        localStorage.setItem('apps', JSON.stringify(apps))
      }
    }
  })
  return JSON.parse(localStorage.getItem('apps')) || []
}

export function menuOperation(to) {
  // to.meta.id && store.dispatch('menu/asyncLogMenu', to.meta.title) // 路由记录
  if (to.meta.id || to.query.activeId) {
    store.dispatch('menu/asyncGetBtnList', to.meta.id || to.query.activeId).then(authenticationList => {
      actions.setGlobalState({
        authenticationList: authenticationList // 按钮权限
      })
    })
  }
}

export function MaximumPages(to) {
  let openShow = true
  const visitedViews = JSON.parse(sessionStorage.getItem('visitedViews'))
  visitedViews &&
    visitedViews.filter(item => {
      if (item.fullPath === to.fullPath) {
        openShow = false
      }
    })
  if (visitedViews && openShow && visitedViews.length > 14) {
    return false
  }
  return true
}
export function getCharCount(str, char) {
  const regex = new RegExp(char, 'g')
  const result = str.match(regex)
  const count = !result ? 0 : result.length
  return count
}

export function getRedirect(to) {
  const redirect = getCharCount(to, '404') === 0 && getCharCount(to, 'redirect') < 6 ? to : '/navbar'
  return redirect
}

export function isIE() {
  if (!!window.ActiveXObject || 'ActiveXObject' in window) { return true } else { return false }
}

export function scriptJoin(key, url) {
  if (document.getElementsByTagName('head')[0].innerHTML.indexOf(key) === -1) {
    const script = document.createElement('script')
    script.type = 'text/javascript'
    script.id = key
    script.setAttribute('src', url)
    document.head.appendChild(script)
  }
}

export function formatJumpUrl(url) {
  if (url && (url.includes('************') || url.includes('************') || url.includes('*************'))) {
    const profileObj = {
      '*************': 'dev',
      '*************': 'pre',
      'erp.esteellink.com': 'prod'
    }
    const profile = profileObj[location.hostname] || 'dev'
    const token = getToken()
    const accessToken = token.slice(7) || ''
    const jumpURL = url
      .replace('{accessToken}', encodeURIComponent(accessToken))
      .replace('{profile}', encodeURIComponent(profile))
    return jumpURL
  } else {
    url
  }
}

export function defineReadOnly(obj, key, value) {
  Object.defineProperty(obj, key, {
    value,
    writable: false,
    configurable: false
  })
}
