import axios from 'axios'
import store from '@/store'
import { Loading } from 'element-ui'
import { messageUtil } from './common'
import moment from 'moment'
import { getToken } from '@/utils/auth'

// loading变量
let loadingInstance = null
// create an axios instance
const service = axios.create({
  baseURL: process.env.VUE_APP_BASE_API, // api 的 base_api
  withCredentials: true, // 跨域请求时发送 cookies
  timeout: 10000, // request timeout
  headers: {
    'Content-Type': 'application/json'
  },
  transformRequest: [function(data) {
    // eslint-disable-next-line
    Date.prototype.toJSON = function() {
      const formatDate = moment(this).format('YYYY-MM-DD HH:mm:ss')
      return formatDate.replace(' ', 'T')
    }
    data = JSON.stringify(data)
    return data
  }]
})

// request interceptor
service.interceptors.request.use(
  config => {
    if (!config?.noLoading || !config.noLoading) {
      if (!store.state.app.appLoading) {
        loadingInstance = Loading.service({
          fullscreen: true,
          text: '加载中...',
          spinner: 'el-icon-loading',
          background: 'rgba(255, 255, 255, .5)'
        })
      }
    }
    // Do something before request is sent
    const token = getToken()
    if (store.getters.authenticationMethod === 'token' && token) {
      // 让每个请求携带token-- ['Authorization']为自定义key 请根据实际情况自行修改
      config.headers['Authorization'] = token
    } else {
      delete config.headers['Authorization']
    }
    return config
  },
  error => {
    // Do something with request error
    console.log(error) // for debug
    Promise.reject(error)
  }
)

// response interceptor
service.interceptors.response.use(
  /**
   * If you want to get information such as headers or status
   * Please return  response => response
   */
  /**
   * 下面的注释为通过在response里，自定义code来标示请求状态
   * 当code返回如下情况则说明权限有问题，登出并返回到登录页
   * 如想通过 XMLHttpRequest 来状态码标识 逻辑可写在下面error中
   * 以下代码均为样例，请结合自生需求加以修改，若不需要，则可删除
   */
  response => {
    // console.log('response--', response)
    const res = response.data
    loadingInstance && loadingInstance.close()
    // 正常接口请求
    if (res && res.code) {
      switch (res.code) {
        case '0000':
          return res
        default:
          messageUtil.error(res.message)
          return Promise.reject(res)
      }
    }
  },
  error => {
    console.log('请求error--', error)
    loadingInstance && loadingInstance.close()

    if (error.toString().indexOf('timeout') > -1) {
      messageUtil.warning('请求超时，请重试！')
      return
    }
    console.log('error.response', error.response)
    if (error.response) {
      const statusRes = error.response.status
      const errRes = error.response.data
      const errArr = [
        'Full authentication is required to access this resource',
        'token modified!',
        'Token为空！'
      ]
      const codeArr = [
        'EXCEPT_SECURITY_DEF_.01-0070-000110',
        'EXCEPT_SECURITY_DEF_.01-0070-000120',
        'EXCEPT_SECURITY_DEF_.01-0070-000130'
      ]
      if (errArr.includes(errRes.message) || codeArr.includes(errRes.code)) {
        messageUtil.warning('登录超时，请重新登录！')
        // 这边要resetToken并跳转至登录页
        store.dispatch('user/resetToken').then(() => {
          // location.reload() // 为了重新实例化vue-router对象 避免bug
        })
      } else {
        switch (statusRes) {
          case 400:
            messageUtil.warning('客户端请求的语法错误，服务器无法理解')
            break
          case 401:
            messageUtil.warning('请求要求用户的身份认证')
            break
          case 404:
            messageUtil.warning('可能正在重启服务，请稍等！')
            break
          case 405:
            messageUtil.warning('客户端请求中的方法被禁止')
            break
          case 500:
            messageUtil.warning('服务器内部错误，无法完成请求')
            break
          case 502:
            messageUtil.warning('从远程服务器接收到了一个无效的响应')
            break
          default:
            messageUtil.warning(errRes.message)
            break
        }
      }
    }
    return Promise.reject(error)
    // if (error?.response?.data) {
    //   const errRes = error.response.data
    //   console.log('errRes--' + JSON.stringify(errRes)) // for debug
    //   if (errRes.code === '9999') {
    //     if (errRes.message === 'Full authentication is required to access this resource' || errRes.message === 'token modified!') {
    //       messageUtil.warning('登录超时，请重新登录！')
    //       // 这边要resetToken并跳转至登录页
    //       store.dispatch('user/resetToken').then(() => {
    //         // location.reload() // 为了重新实例化vue-router对象 避免bug
    //       })
    //     } else {
    //       messageUtil.warning(errRes.message)
    //     }
    //   } else if ((errRes?.status && errRes.status === 404) || errRes.toString().indexOf('404') > -1) {
    //     messageUtil.warning('可能正在重启服务，请稍等！')
    //     // 这边要resetToken并跳转至登录页
    //     store.dispatch('user/resetToken').then(() => {
    //       // location.reload() // 为了重新实例化vue-router对象 避免bug
    //     })
    //   } else {
    //     messageUtil.error(errRes.message)
    //   }
    //   return Promise.reject(errRes)
    // }
    // return Promise.reject(error)
  }
)

export default service
