/**
 * @file fixSelectDropdown.js
 * @description 修复移动设备上 Element UI 下拉框和日期选择器定位问题
 * <AUTHOR>
 * @created 2025-07-04
 * @updated 2025-07-04
 *
 * 该插件解决了在小屏设备（≤768px）上 Element UI 下拉框和日期选择器定位错误的问题
 * 主要功能：
 * 1. 修正下拉框位置，确保显示在输入框正下方
 * 2. 添加屏幕边缘检测，当右侧空间不足时向左对齐
 * 3. 添加平滑的展开动画效果
 */

export default {
  install(Vue) {
    if (typeof window === 'undefined') return

    const SCREEN_WIDTH_LIMIT = 768

    // 提前判断屏幕宽度，不是小屏设备直接返回，节省资源
    if (window.screen.width > SCREEN_WIDTH_LIMIT) return

    const fixedDropdowns = new WeakSet()
    let isUpdatingStyle = false

    const observer = new MutationObserver((mutations) => {
      for (const mutation of mutations) {
        for (const node of mutation.addedNodes) {
          if (
            node.nodeType === 1 &&
            (
              node.classList.contains('el-select-dropdown') ||
              node.classList.contains('el-picker-panel')
            ) &&
            !fixedDropdowns.has(node)
          ) {
            // 初始化动画状态
            node.style.opacity = '0'
            node.style.transform = 'translateY(-10px) scale(0.95)'

            requestAnimationFrame(() => {
              fixDropdownPosition(node)

              node.style.transition = 'opacity 0.2s ease-out, transform 0.2s ease-out'

              requestAnimationFrame(() => {
                node.style.opacity = '1'
                node.style.transform = ''
              })
            })
          }
        }
      }
    })

    observer.observe(document.body, {
      childList: true,
      subtree: true
    })

    function fixDropdownPosition(dropdownEl) {
      fixedDropdowns.add(dropdownEl)

      const styleObserver = new MutationObserver(() => {
        if (!isUpdatingStyle) {
          requestAnimationFrame(() => {
            applyFixedPosition(dropdownEl)
          })
        }
      })

      styleObserver.observe(dropdownEl, {
        attributes: true,
        attributeFilter: ['style']
      })

      applyFixedPosition(dropdownEl)

      // 监听关闭事件清理资源
      const closeHandler = () => {
        if (!document.body.contains(dropdownEl)) {
          styleObserver.disconnect()
          document.removeEventListener('click', closeHandler)
        }
      }
      document.addEventListener('click', closeHandler)

      setTimeout(() => {
        if (document.body.contains(dropdownEl)) {
          applyFixedPosition(dropdownEl)
        }
      }, 100)
    }

    function applyFixedPosition(dropdownEl) {
      // 查找参考元素
      let referenceEl

      if (dropdownEl.classList.contains('el-picker-panel')) {
        // 日期选择器特殊处理
        const popperJS = dropdownEl.__vue__?.popperJS
        if (popperJS && popperJS.reference) {
          referenceEl = popperJS.reference
        } else {
          const activeInput = document.querySelector('.el-date-editor.is-active')
          if (activeInput) {
            referenceEl = activeInput
          } else {
            referenceEl = document.activeElement?.closest('.el-date-editor')
          }
        }
      }

      if (!referenceEl) {
        referenceEl =
          dropdownEl.__vue__?.$parent?.$el ||
          dropdownEl.previousElementSibling ||
          document.activeElement?.parentElement
      }

      if (!referenceEl) return

      const rect = referenceEl.getBoundingClientRect()

      // 获取宽度并检查右侧空间
      let dropdownWidth
      if (dropdownEl.classList.contains('el-picker-panel')) {
        dropdownWidth = dropdownEl.offsetWidth || 322
      } else {
        dropdownWidth = rect.width
      }

      const viewportWidth = window.innerWidth
      const rightSpace = viewportWidth - rect.left
      const needsLeftAlign = rightSpace < dropdownWidth

      // 计算位置
      let targetLeft
      if (needsLeftAlign) {
        targetLeft = Math.max(0, rect.right - dropdownWidth)
      } else {
        targetLeft = rect.left
      }

      const targetTop = rect.bottom

      // 检查是否需要更新
      const currentLeft = parseFloat(dropdownEl.style.left)
      const currentTop = parseFloat(dropdownEl.style.top)

      if (
        dropdownEl.style.position === 'fixed' &&
        Math.abs(currentLeft - targetLeft) < 1 &&
        Math.abs(currentTop - targetTop) < 1
      ) {
        return
      }

      // 应用样式
      isUpdatingStyle = true

      try {
        dropdownEl.style.position = 'fixed'
        dropdownEl.style.left = `${targetLeft}px`
        dropdownEl.style.top = `${targetTop}px`

        if (!dropdownEl.classList.contains('el-picker-panel')) {
          dropdownEl.style.width = `${rect.width}px`
        }
      } finally {
        setTimeout(() => {
          isUpdatingStyle = false
        }, 0)
      }
    }
  }
}
