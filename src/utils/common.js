/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2020/04/8.
 */
import { Message } from 'element-ui'

export const messageUtil = {
  show(type, msg) {
    Message({
      message: msg,
      type: type,
      duration: 1000
    })
  },
  success(msg = '') {
    this.show('success', msg)
  },
  error(msg = '') {
    this.show('error', msg)
  },
  warning(msg = '') {
    this.show('warning', msg)
  },
  info(msg = '') {
    this.show('info', msg)
  }
}

function toggleClass(element, className) {
  if (!element || !className) {
    return
  }
  let classString = element.className
  const nameIndex = classString.indexOf(className)
  if (nameIndex === -1) {
    classString += '' + className
  } else {
    classString =
      classString.substr(0, nameIndex) +
      classString.substr(nameIndex + className.length)
  }
  element.className = classString
}

function hasClass(ele, cls) {
  return !!ele.className.match(new RegExp('(\\s|^)' + cls + '(\\s|$)'))
}
function addClass(ele, cls) {
  if (!hasClass(ele, cls)) ele.className += ' ' + cls
}
function removeClass(ele, cls) {
  if (hasClass(ele, cls)) {
    const reg = new RegExp('(\\s|^)' + cls + '(\\s|$)')
    ele.className = ele.className.replace(reg, ' ')
  }
}

/**
 * 是否为外部链接
 * @param {*} path 【必填】需要校验的数据
 */
function isExternal(path) {
  return /^(https?:|mailto:|tel:)/.test(path)
}

export default {
  toggleClass,
  hasClass,
  addClass,
  removeClass,
  isExternal
}
