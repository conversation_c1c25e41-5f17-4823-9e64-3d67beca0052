import router from './router'
import apps from './apps.js'
const configSubApp = apps.map((item) => {
  return {
    name: item,
    entry:
 process.env.NODE_ENV === 'development'
   ? process.env[`VUE_APP_${item.toUpperCase()}_URL`]
   : `/child/${item}/`,
    container: `#${item}`,
    props: {
      parentRouter: router,
      subApp: apps,
      data: {
        router
      }
    }
  }
})
export default configSubApp
