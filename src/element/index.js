// 引入ElementUI
import {
  // Tree,
  // Input,
  // Dialog,
  // Pagination,
  // Button,
  // TabPane,
  // Form,
  // FormItem,
  // Col,
  // Checkbox,
  // Row,
  // Option,
  // Select,
  // DatePicker,
  // Tabs,
  // Collapse,
  // CollapseItem,
  // Tooltip,
  // Loading,
  Message
} from 'element-ui'

const $message = options => {
  return Message({
    ...options,
    offset: 50,
    duration: options.type === 'warning' || options.type === 'error' ? 2500 : 500
  })
}

// 重写一遍success的方法,将offset写入options
['success', 'warning', 'info', 'error'].forEach(type => {
  $message[type] = options => {
    if (typeof options === 'string') {
      options = {
        message: options,
        offset: 50,
        duration: type === 'warning' || type === 'error' ? 2500 : 500
      }
    }
    options.type = type
    return Message(options)
  }
})
// 将$message挂载到this上
const element = {
  install: function(Vue) {
    // 使用ElementUI
    // Vue.use(Button)
    // .use(Checkbox)
    // .use(Form)
    // .use(FormItem)
    // .use(Input)
    // .use(Tree)
    // .use(Pagination)
    // .use(TabPane)
    // .use(Col)
    // .use(Option)
    // .use(Select)
    // .use(DatePicker)
    // .use(Row)
    // .use(Dialog)
    // .use(Tabs)
    // .use(Collapse)
    // .use(CollapseItem)
    // .use(Tooltip)
    // .use(Loading)
    Vue.prototype.$message = $message
  }
}
export default element
