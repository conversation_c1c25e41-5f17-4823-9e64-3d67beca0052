
import { Message } from 'element-ui'

const $message = options => {
  return Message({
    ...options,
    offset: 50,
    duration: options.type === 'warning' || options.type === 'error' ? 2500 : 500
  })
}

['success', 'warning', 'info', 'error'].forEach(type => {
  $message[type] = options => {
    if (typeof options === 'string') {
      options = {
        message: options,
        offset: 50,
        duration: type === 'warning' || type === 'error' ? 2500 : 500
      }
    }
    options.type = type
    return Message(options)
  }
})
$message.closeAll = function() {
  return Message.closeAll()
}

$message.close = function() {
  return Message.close()
}
const element = {
  install: function(Vue) {
    Vue.prototype.$message = $message
  }
}
export default element

