<template>
  <!--选择-->
  <cnd-dialog
    :title="title ? title : $t('btns.select')"
    :append-to-body="true"
    :visible="visible"
    :show-close="true"
    :fullscreen="false"
    :width="width"
    @close="handleClose"
  >
    <template slot="content" class="mr-top10">
      <el-tree
        v-if="visible"
        ref="tree"
        :data="treeData"
        :props="treeDefaultProps"
        check-on-click-node
        node-key="id"
        @node-click="checkPage"
      />
    </template>
    <template slot="footer">
      <el-button type="" size="mini" @click="handleClose">{{ $t('btns.cancel') }}</el-button>
      <el-button type="primary" size="mini" @click="handleConfirm">{{ $t('btns.confirmKey') }}</el-button>
    </template>
  </cnd-dialog>
</template>

<script>
import { ToggleDialogMixin } from '@/components/cnd/dialog-mixin'
import { MessageUtil } from '@/utils/common'
import StaffApi from '@/api/staff'

export default {
  mixins: [ToggleDialogMixin],
  props: {
    width: {
      type: String,
      default: '750px'
    },
    title: {
      type: String,
      default: ''
    },
    userId: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      treeData: [],
      treeDefaultProps: {
        children: 'children',
        label: 'name'
      },
      curStaff: ''
    }
  },
  watch: {
    visible(val) {
      this.$emit('update:dialogVisible', val)
      if (val) {
        this.getMenuByParentId()
      } else {
        this.handleClose()
      }
    },
    filterText(val) {
      this.$refs.tree.filter(val)
    }
  },
  methods: {
    // 选择人员
    checkPage(p1, p2, p3) {
      console.log('p1--', p1)
      console.log('p2--', p2)
      // console.log('p3--', p3)
      this.curNode = p2
      if (p1.children && p1.children.length > 0) {
        this.curStaff = ''
      } else {
        this.curStaff = p1
      }
    },
    handleClose() {
      this.$emit('update:dialogVisible', false)
      this.treeData = []
      if (this.$refs.tree) {
        this.$refs.tree.setCheckedKeys([])
      }
    },
    filterNode(value, data) {
      if (!value) return true
      return data.label.indexOf(value) !== -1
    },
    handleConfirm() {
      // const vm = this
      // const checkedNodes = vm.$refs.tree.getCheckedNodes()
      if (!this.curStaff) {
        MessageUtil.warning(this.$t('grid.others.pleaseSelectPersonnel'))
        return
      }
      this.$emit('onSelect', this.curStaff)
      this.handleClose()
    },

    // tree
    handleTreeCheckGroupNode: function(item, node) {
      // this.$refs.tree.setCheckedKeys([item.id])
    },
    handleTreeLoadNode(node, resolve) {
      const param = {
        'orgType': '100'
      }
      this.getMenuByParentId(param, resolve)
    },
    getMenuByParentId(param, resolve) {
      const param1 = {
        'orgType': '100'
      }
      StaffApi.staffTree(param1).then((result) => {
        this.treeData = result.data
        // const list = result || []
        // list.map((item) => {
        //   item.label = item.title
        //   return item
        // })
        // resolve && resolve(list)
      })
    }
  }
}
</script>
<style lang="scss" scoped>
@import '@/styles/user/user.scss';
</style>
