<template>
  <div class="page-container">
    <p class="page-title">产地配置</p>
    <div class="layout-content auto-page-title flexV">
      <steel-search-form ref="searchForm" :form-items="formItems" @search="onSearch" />
      <div class="btn-group mt-10" style="display: flex">
        <div class="text">
          产地列表
        </div>
        <div>
          <el-button
            type="primary"
            size="mini"
            @click="create"
          >
            {{ $t('btns.add') }}
          </el-button>
          <el-button
            type="danger"
            size="mini"
            class="mr-10"
            :disabled="delDisable"
            @click="removes"
          >
            {{ $t('btns.delete') }}
          </el-button>
        </div>
      </div>
      <steelTradeAggrid
        ref="aggrid"
        :column-defs="columnDefs"
        :row-data="rowData"
        :load-data="loadData"
        :auto-load-data="false"
        table-selection="multiple"
        @selectedChange="handleFooterCount"
        @rowDoubleClicked="onRowDoubleClicked"
      />
    </div>
    <addDialog
      :dialog-visible="dialogVisible"
      @close="closeDialogDialog"
    />
  </div>
</template>

<script>
import steelTradeAggrid from '@/components/steelTradeAggrid'
import addDialog from './addDialog.vue'
import { Moment } from 'cnd-utils'
import {
  customerOriginPage,
  customerOriginRemoves
} from '@/api/sysConfig/originConfig'
export default {
  name: 'OriginConfig',
  components: { steelTradeAggrid, addDialog },
  data() {
    return {
      searchInfo: null,
      formItems: [
        {
          label: '客商编码',
          value: 'sCustomerCode',
          type: 'elInput'
        },
        {
          label: '客商名',
          value: 'sName',
          type: 'elInput'
        },
        {
          label: '产地',
          value: 'sOrigin',
          type: 'elInput'
        },
        {
          label: '经营单位',
          value: 'sManagementId',
          type: 'cndInputDialog',
          dialogType: 'escOrg',
          itemType: 'occultation'
        },
        {
          label: this.$t('grid.title.createdAt'),
          value: ['sCreateTime', 'vCreateTimeTo'],
          placeholder: [this.$t('grid.others.startDate'), this.$t('grid.others.endDate')],
          unlinkPanels: true,
          type: 'elDatePicker'
        }
      ],
      columnDefs: [
        {
          headerName: '客商编码',
          field: 'sCustomerCode'
        },
        {
          headerName: '客商名',
          field: 'sName'
        },
        {
          headerName: '产地',
          field: 'sOrigin'
        },
        {
          headerName: '无需产地',
          field: 'sIsNeedOrigin',
          valueGetter: params => { return params.data.sIsNeedOrigin === '1' ? '√' : '' },
          cellRenderer: (params) => {
            const text = params.data.sIsNeedOrigin === '1' ? '√' : ''
            const sHtml = '<span  style=color:#3E8DDC;font-weight:bold>' + text + '</span>'
            return sHtml
          }
        },
        {
          field: 'vManagementName',
          headerName: '经营单位'
        },
        {
          headerName: this.$t('grid.title.createdBy'),
          field: 'vCreatorName'
        },
        {
          headerName: this.$t('grid.title.createdAt'),
          field: 'sCreateTime',
          minWidth: 150,
          valueFormatter(params) {
            return Moment.time('YYYY-MM-DD HH:mm:ss', params.data.sCreateTime)
          }
        }
      ],
      rowData: [],
      dialogVisible: false
    }
  },
  created() {
  },
  mounted() {
    this.onSearch(false)
  },
  methods: {
    onSearch(load = true) {
      const searchInfo = this.$refs.searchForm.getSearchData()
      searchInfo.sCustomerIds = searchInfo.sCustomerIds?.toString()
      this.searchInfo = searchInfo
      if (load) {
        this.$refs.aggrid.loadTableData()
      }
    },
    loadData(pagination) {
      return new Promise((resolve, reject) => {
        customerOriginPage(this.searchInfo, pagination).then(res => {
          this.rowData = res.data.content.map(item => {
            item._selected = false
            item._selectedKeys = []
            return item
          })
          resolve(res.data)
        }).catch(() => {
          reject([])
        })
      })
    },
    handleFooterCount(rowData) {
      // console.log(rowData)
    },

    create() {
      this.dialogVisible = true
    },
    removes() {
      this.$refs.aggrid.getSelectedData(res => {
        if (!res.length) {
          this.$message.warning(this.$t('grid.others.pleaseSelectData'))
          return
        }
        this.$confirm(this.$t('grid.tips.isTheDeletionConfirmed'), this.$t('grid.others.prompt'), {
          confirmButtonText: this.$t('grid.others.yes'),
          cancelButtonText: this.$t('btns.no'),
          type: 'warning'
        }).then(() => {
          customerOriginRemoves(
            res.map(item => item.sId)
          ).then(() => {
            this.$message.success(this.$t('grid.tips.deletionSuccess'))
            this.$refs.aggrid.reloadTableData()
          })
        })
      })
    },
    onRowDoubleClicked(params) {
      this.$router.push({
        path: `/originConfigDetail/${params.data.sId}`,
        query: {
          id: params.data.sId,
          type: 'edit',
          name: `产地配置【${params.data.sCustomerCode}】`,
          activeId: localStorage.getItem('menuId')
        }
      })
    },
    closeDialogDialog() {
      this.dialogVisible = false
      this.$refs.aggrid.loadTableData()
    }
  }
}
</script>

<style scoped>

</style>
