<template>
  <div class="page-container">
    <p class="page-title">商城销售上限配置</p>
    <div class="layout-content auto-page-title flexV">
      <steel-search-form
        ref="searchForm"
        label-width="80px"
        :form-items="formItems"
        @search="onSearch"
      />
      <div class="btn-group mt-10">
        <div class="text">
          商城销售上限配置列表
        </div>
        <div>
          <el-button
            v-has:esc_customer_retailSale_add
            type="primary"
            size="mini"
            @click="add"
          >{{ $t('btns.add') }}</el-button>
          <el-button
            v-has:esc_customer_retailSale_modify
            type="primary"
            size="mini"
            @click="modify"
          >修改</el-button>
          <el-button
            v-has:esc_customer_retailSale_removes
            type="danger"
            size="mini"
            @click="remove"
          >{{ $t('btns.delete') }}</el-button>
        </div>
      </div>
      <steelTradeAggrid
        ref="aggrid"
        :column-defs="columnDefs"
        :row-data="rowData"
        :load-data="loadData"
        table-selection="multiple"
        row-key="sId"
      />
    </div>
    <cnd-dialog
      v-if="addDialog"
      :visible="addDialog"
      :fullscreen="false"
      append-to-body
      :title="curTitle"
      width="560px"
      height="240px"
      @close="addDialog = false"
    >
      <template slot="content">
        <div>
          <el-form
            ref="addForm"
            :model="addForm"
            label-width="100px"
            size="small"
            :rules="rules"
            @submit.native.prevent
          >
            <cnd-form-item :custom-width="23" :label="$t('grid.others.department')" prop="sDepartments" :error-msg="rules.sDepartments[0].message">
              <horizon-search-select-item
                v-model="addForm.sDepartments"
                type="depart"
                multiple
                reserve-keyword
                :first-call="true"
                :option="{seledLabel:'sName',valueKey:'sId',label:'sPath'}"
                :placeholder="$t('components.pleaseSelect')"
                @change="handleChangeSelect"
              />
            </cnd-form-item>
            <cnd-form-item :custom-width="23" label="核算组">
              <horizon-search-select-item
                v-model="addForm.sCheckGroups"
                type="cost"
                multiple
                reserve-keyword
                :first-call="true"
                :option="{seledLabel:'sName',valueKey:'sId',label:'sName'}"
                :placeholder="$t('components.pleaseSelect')"
                disabled
              />
            </cnd-form-item>
            <cnd-form-item :custom-width="23" :label="$t('grid.others.supplier')" prop="sSupplierName" :error-msg="rules.sSupplierName[0].message">
              <horizon-search-select
                v-model="addForm.sSupplierName"
                type="customer"
                :customer-type="20"
                :default-url="`/esc/customer/page`"
                :option="{label:'sPath'}"
                @change="handleChange($event, 'sSupplierId')"
              />
            </cnd-form-item>
            <cnd-form-item :custom-width="23" label="当日销售上限（吨）" prop="sCeilingNumber" :error-msg="rules.sCeilingNumber[0].message">
              <cnd-input-number v-model="addForm.sCeilingNumber" :decimal-digit="4" clearable />
            </cnd-form-item>
            <cnd-form-item :custom-width="23" label="经营单位" prop="sManagementId" :error-msg="rules.sManagementId[0].message">
              <!-- <horizon-search-select
                v-model="addForm.sManagementId"
                type="escOrg"
                :customer-type="20"
                @change="handleChange($event, 'sManagementId')"
              /> -->
              <el-select v-model="addForm.sManagementId">
                <el-option
                  v-for="item in orgList"
                  :key="item.sId"
                  :label="item.sName"
                  :value="item.sId"
                />
              </el-select>
            </cnd-form-item>
          </el-form>
        </div>
      </template>
      <template slot="footer">
        <el-button
          size="mini"
          @click="addDialog = false"
        >{{ $t('btns.cancel') }}</el-button>
        <el-button
          type="primary"
          size="mini"
          @click="addSubmit"
        >{{ $t('btns.confirm') }}</el-button>
      </template>
    </cnd-dialog>
  </div>
</template>
<script>
import steelTradeAggrid from '@/components/steelTradeAggrid'
import { Moment } from 'cnd-utils'
import {
  customerRetailSale,
  customerRetailRemoves,
  customerRetailAdd,
  customerRetailModify,
  getFilterManagement
} from '@/api/sysConfig/mallsalesLimitConfig.js'
import UserApi from '@/api/user'
import { getCheckGroup } from '@/api/common'

export default {
  name: 'MallsalesLimitConfig',
  components: {
    steelTradeAggrid
  },
  data() {
    return {
      addDialog: false,
      addForm: {
        sDepartmentIds: [],
        sDepartments: [],
        // vCheckGroupNames: [],
        sSupplierName: '',
        sSupplierId: '',
        sManagementId: '',
        sCeilingNumber: 0,
        sCheckGroups: []
      },
      formItems: [
        {
          label: this.$t('grid.others.department'),
          value: 'sDepartmentId',
          type: 'cndInputDialog',
          dialogType: 'depart'
        },
        {
          label: this.$t('grid.others.supplier'),
          value: 'sSupplierId',
          type: 'cndInputDialogItem',
          dialogType: 'customer',
          defaultUrl: '/esc/customer/page',
          option: { valueKey: 'sPath' }
        },
        {
          label: '经营单位',
          value: 'sManagementId',
          type: 'cndInputDialog',
          dialogType: 'escOrg'
        }
      ],
      columnDefs: [
        {
          headerName: this.$t('grid.others.department'),
          field: 'sDepartmentName',
          minWidth: 360
        },
        {
          headerName: '核算组',
          field: 'sCheckGroupName',
          minWidth: 360
        },
        {
          headerName: this.$t('grid.others.supplier'),
          field: 'sSupplierName'
        },
        {
          headerName: '当日销售上限（吨）',
          field: 'sCeilingNumber'
        },
        {
          headerName: '经营单位',
          field: 'sManagementName'
        },
        {
          headerName: this.$t('grid.title.createdBy'),
          field: 'vCreatorName'
        },
        {
          headerName: this.$t('grid.title.createdAt'),
          field: 'sCreateTime',
          minWidth: 150,
          valueFormatter(params) {
            return Moment.time('YYYY-MM-DD HH:mm:ss', params.data.sCreateTime)
          }
        },
        {
          field: 'vModifierName',
          headerName: this.$t('grid.title.modifiedBy')
        },
        {
          field: 'sModifyTime',
          minWidth: 150,
          headerName: this.$t('grid.title.modifiedAt'),
          valueFormatter(params) {
            return Moment.time('YYYY-MM-DD HH:mm:ss', params.data.sModifyTime)
          }
        }
      ],
      rowData: [],
      rules: {
        sDepartments: [
          { required: true, message: this.$t('components.pleaseSelect'), trigger: 'change' }
        ],
        // vCheckGroupNames: [
        //   { required: true, message: this.$t('components.pleaseSelect'), trigger: 'change' }
        // ],
        sSupplierName: [
          { required: true, message: this.$t('components.pleaseSelect'), trigger: 'change' }
        ],
        sManagementId: [
          { required: true, message: this.$t('components.pleaseSelect'), trigger: 'change' }
        ],
        sCeilingNumber: [
          { required: true, message: this.$t('components.pleaseSelect'), trigger: 'change' }
        ]
      },
      isModify: false,
      curAccount: '',
      orgList: [],
      curTitle: '新增'
    }
  },
  created() {
    UserApi.getUserCurrent().then((res) => {
      this.curAccount = res.data.account
      this.loadDict()
    })
  },
  methods: {
    loadDict() {
      // 获取创建人经营单位
      getFilterManagement(this.curAccount).then(res => {
        this.orgList = res.data
      })
    },
    onSearch(load = true) {
      this.$refs.aggrid.loadTableData()
    },
    loadData(pagination) {
      return new Promise((resolve, reject) => {
        customerRetailSale(
          this.$refs.searchForm.getSearchData(),
          pagination
        ).then(res => {
          this.rowData = res.data.content.map(item => {
            item._selected = false
            return item
          })
          resolve(res.data)
        }).catch(() => {
          reject(0)
        })
      })
    },
    add() {
      this.addForm = {
        sDepartmentIds: [],
        sDepartments: [],
        // vCheckGroupNames: [],
        sSupplierName: '',
        sSupplierId: '',
        sManagementId: '',
        sCeilingNumber: 0,
        sCheckGroups: []
      }
      this.addDialog = true
      this.isModify = false
      this.curTitle = '新增'
    },
    modify() {
      this.$refs.aggrid.getSelectedData(res => {
        if (!res.length || res.length > 1) {
          this.$message.warning(this.$t('grid.others.pleaseSelectAData'))
          return
        }
        this.addForm = {
          ...res[0]
        }
        this.isModify = true
        this.addDialog = true
        this.curTitle = '修改'
      })
    },
    addSubmit() {
      this.$refs.addForm.validate((valid) => {
        if (valid) {
          const api = this.isModify ? customerRetailModify : customerRetailAdd
          const msg = this.isModify ? '修改成功' : '新增成功'
          api(this.addForm).then(() => {
            this.$message({
              message: msg,
              type: 'success'
            })
            this.addDialog = false
            this.$refs.aggrid.reloadTableData()
          })
        }
      })
    },
    async handleChangeSelect() {
      this.addForm.sDepartmentIds = []
      const departmentPromises = this.addForm.sDepartments.map(item => {
        this.addForm.sDepartmentIds.push(item.sId)
        // 循环请求核算组数据插入sCheckGroups
        return getCheckGroup({ pageNum: 0, pageSize: 9999 }, {
          sDepartmentId: item.sId
        }).then(res => res.data.content[0])
      })
      const checkGroups = await Promise.all(departmentPromises)
      this.addForm.sCheckGroups = checkGroups.filter(group => group !== undefined)
      return this.addForm.sDepartmentIds
    },
    remove() {
      this.$refs.aggrid.getSelectedData(res => {
        if (!res.length) {
          this.$message.warning(this.$t('grid.others.pleaseSelectData'))
          return
        }
        this.$confirm(this.$t('grid.tips.isTheDeletionConfirmed'), this.$t('grid.others.prompt'), {
          confirmButtonText: this.$t('grid.others.yes'),
          cancelButtonText: this.$t('btns.no'),
          type: 'warning'
        }).then(() => {
          // 删除接口
          customerRetailRemoves(
            res.map(item => item.sId)
          ).then(() => {
            this.$message.success(this.$t('grid.tips.deletionSuccess'))
            this.$refs.aggrid.reloadTableData()
          })
        })
      })
    },
    handleChange(val, key) {
      this.addForm[key] = val.sId || val.sCode
    },
    hendleChangeId() {

    }
  }
}
</script>
