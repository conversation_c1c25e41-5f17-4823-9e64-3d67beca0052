<template>
  <div class="page-container">
    <cnd-dialog :title="$t('grid.tabs.basicInformation')" :visible="true" @close="onClose">
      <template slot="leftBtn">
        <span>
          <el-button
            type="primary"
            size="mini"
            @click="save"
          >{{ $t('btns.save') }}</el-button>
          <el-button
            v-if="$route.query.type !== 'add'"
            type="danger"
            size="mini"
            @click="remove"
          >
            {{ $t('btns.delete') }}
          </el-button>
        </span>
      </template>
      <template slot="content">
        <el-form
          ref="form"
          class="el-form-w100"
          label-width="90px"
          inline
          :model="form"
          :rules="rules"
          size="small"
        >
          <cnd-form-card-list :active-panel="activeCollapseName">
            <cnd-form-card class="mb-10" :title="$t('grid.tabs.basicInformation')" name="1">
              <el-row>
                <cnd-form-item
                  :label="$t('grid.others.supplier')"
                  prop="vSupplierName"
                  :error-msg="rules.vSupplierName[0].message"
                >
                  <horizon-search-select
                    v-model="form.vSupplierName"
                    type="customer"
                    :placeholder="$t('components.pleaseSelect')"
                    @change="handleChangeSelect($event, 'sSupplierId')"
                  />
                </cnd-form-item>
                <cnd-form-item
                  label="经营单位"
                  prop="sManagementId"
                  :error-msg="rules.sManagementId[0].message"
                >
                  <el-select v-model="form.sManagementId">
                    <el-option
                      v-for="item in orgList"
                      :key="item.sId"
                      :label="item.sName"
                      :value="item.sId"
                    />
                  </el-select>
                </cnd-form-item>
                <cnd-form-item
                  label="ESB编码"
                  prop="sEsbCode"
                  :error-msg="rules.sEsbCode[0].message"
                >
                  <el-input v-model="form.sEsbCode" />
                </cnd-form-item>
                <cnd-form-item
                  label="sFlowId"
                  prop="sFlowId"
                >
                  <el-input v-model="form.sFlowId" />
                </cnd-form-item>
                <cnd-form-item
                  label="状态"
                  prop="sIsMainSwitch"
                  :error-msg="rules.sIsMainSwitch[0].message"
                >
                  <el-radio-group v-model="form.sIsMainSwitch">
                    <el-radio label="1">启用</el-radio>
                    <el-radio label="0">停用</el-radio>
                  </el-radio-group>
                </cnd-form-item>
                <cnd-form-item
                  label="是否推送"
                  prop="sIsSendRpa"
                  :error-msg="rules.sIsSendRpa[0].message"
                >
                  <el-radio-group v-model="form.sIsSendRpa">
                    <el-radio label="1">是</el-radio>
                    <el-radio label="0">否</el-radio>
                  </el-radio-group>
                </cnd-form-item>
              </el-row>
            </cnd-form-card>
            <cnd-form-card class="mb-10" :title="$t('grid.tabs.systemInformation')" name="2">
              <el-row>
                <cnd-form-item :label="$t('grid.title.createdBy')" prop="vCreatorName">
                  <el-input v-model="form.vCreatorName" disabled clearable />
                </cnd-form-item>
                <cnd-form-item :label="$t('grid.title.createdAt')" prop="sCreateTime">
                  <el-date-picker
                    v-model="form.sCreateTime"
                    format="yyyy-MM-dd HH:mm:ss"
                    type="date"
                    disabled
                    :placeholder="$t('grid.title.createdAt')"
                  />
                </cnd-form-item>
                <cnd-form-item :label="$t('grid.title.modifiedBy')" prop="vModifierName">
                  <el-input v-model="form.vModifierName" disabled clearable />
                </cnd-form-item>
                <cnd-form-item :label="$t('grid.title.modifiedAt')" prop="sModifyTime">
                  <el-date-picker
                    v-model="form.sModifyTime"
                    format="yyyy-MM-dd HH:mm:ss"
                    type="date"
                    disabled
                    :placeholder="$t('grid.title.modifiedAt')"
                  />
                </cnd-form-item>
              </el-row>
            </cnd-form-card>
          </cnd-form-card-list>
        </el-form>
      </template>
    </cnd-dialog>
  </div>
</template>

<script>
import {
  getSteelMillDockingConfigDetail,
  saveSteelMillDockingConfig,
  removeSteelMillDockingConfig
} from '@/api/sysConfig/steelMillDockingConfig'
import { getManagement } from '@/utils/auth'
import {
  getOrgDialog
} from '@/api/customerOutConfig'
export default {
  name: 'SteelMillDockingConfigDetail',
  data() {
    return {
      activeCollapseName: ['1', '2', '3'],
      form: {},
      rules: {
        vSupplierName: [
          { required: true, message: this.$t('components.pleaseSelect'), trigger: 'change' }
        ],
        sManagementId: [
          { required: true, message: this.$t('components.pleaseSelect'), trigger: 'change' }
        ],
        sEsbCode: [{ required: true, message: this.$t('components.pleaseEnter'), trigger: 'blur' }],
        sIsMainSwitch: [{ required: true, message: this.$t('components.pleaseSelect'), trigger: 'change' }],
        sIsSendRpa: [{ required: true, message: this.$t('components.pleaseSelect'), trigger: 'change' }]
      },
      orgList: [],
      dialogVisible: {
        annex: false
      }
    }
  },
  computed: {
    id() {
      return this.$route.query.id
    }
  },
  mounted() {
    this.loadDict()
    this.loadDetail()
  },
  methods: {
    loadDict() {
      getOrgDialog({}).then(res => {
        this.orgList = res.data.content
      })
    },
    loadDetail() {
      if (this.id) {
        getSteelMillDockingConfigDetail(this.id).then(res => {
          this.form = {
            ...res.data,
            ...res.data.sParamsDto
          }
          this.formDisabled = true
        })
      } else {
        if (this.$route.query.type === 'add') {
          this.form = {
            vSupplierName: null,
            sSupplierId: null,
            sManagementId: null,
            sEsbCode: null,
            sIsMainSwitch: '1',
            sIsSendRpa: '1',
            sFlowId: null,
            sParamsDto: {}
          }
          this.formDisabled = false
        }
      }
      if (!this.form.sManagementId) {
        const manage = getManagement('all')
        if (manage.length === 1) {
          this.form.sManagementId = manage[0].sId
        }
      }
    },
    save() {
      this.$refs.form.validate(valid => {
        if (valid) {
          const form = {
            ...this.form,
            sParamsDto: {
              ...this.form.sParamsDto,
              sEsbCode: this.form.sEsbCode,
              sIsMainSwitch: this.form.sIsMainSwitch,
              sIsSendRpa: this.form.sIsSendRpa,
              sFlowId: this.form.sFlowId
            }
          }
          if (this.id) {
            saveSteelMillDockingConfig(form).then(res => {
              this.$message.success(this.$t('tips.saveSuccess'))
              this.loadDetail()
            })
          } else {
            saveSteelMillDockingConfig(form).then(res => {
              this.$message.success(this.$t('tips.saveSuccess'))
              this.$tabDelete(
                `/egl/steelMillDockingConfigDetail/${res.data.sId}?id=${res.data.sId}&type=edit&name=钢厂对接配置【${res.data.vSupplierName}】&activeId=${localStorage.getItem('menuId')}&random=${Math.random()}`
              )
            })
          }
        }
      })
    },
    remove() {
      this.$confirm(this.$t('tips.isItOkToDelete'), this.$t('grid.others.prompt'), {
        confirmButtonText: this.$t('btns.confirm'),
        cancelButtonText: this.$t('btns.cancel'),
        type: 'info'
      }).then(() => {
        removeSteelMillDockingConfig([this.id]).then(() => {
          this.$tabDelete()
        })
      })
    },
    handleChangeSelect(val, key) {
      if (val) {
        this.form[key] = val.sId
      } else {
        this.form[key] = undefined
      }
    }
  }
}
</script>
