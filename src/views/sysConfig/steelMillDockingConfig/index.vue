<template>
  <div class="page-container">
    <p class="page-title">钢厂对接配置</p>
    <div class="layout-content auto-page-title flexV">
      <steel-search-form ref="searchForm" :form-items="formItems" @search="onSearch" />
      <div class="btn-group mt-10" style="display: flex">
        <div class="text">
          钢厂对接配置列表
        </div>
        <div>
          <el-button
            type="primary"
            size="mini"
            @click="create"
          >
            {{ $t('btns.add') }}
          </el-button>
          <el-button
            type="danger"
            size="mini"
            class="mr-10"
            :disabled="delDisable"
            @click="removes"
          >
            {{ $t('btns.delete') }}
          </el-button>
        </div>
      </div>
      <steelTradeAggrid
        ref="aggrid"
        :column-defs="columnDefs"
        :row-data="rowData"
        :load-data="loadData"
        :auto-load-data="false"
        table-selection="multiple"
        @selectedChange="handleFooterCount"
        @rowDoubleClicked="onRowDoubleClicked"
      />
    </div>
  </div>
</template>

<script>
import steelTradeAggrid from '@/components/steelTradeAggrid'
import { Moment } from 'cnd-utils'
import {
  getSteelMillDockingConfigPage,
  removeSteelMillDockingConfig
} from '@/api/sysConfig/steelMillDockingConfig'
import { getDictList, getCnDitc } from '@/utils/dict'
export default {
  name: 'SteelMillDockingConfig',
  components: { steelTradeAggrid },

  data() {
    const sIsMainSwitchDict = [
      {
        sCodeValue: '1',
        sCodeName: '启用'
      },
      {
        sCodeValue: '0',
        sCodeName: '停用'
      }
    ]
    return {
      searchInfo: null,
      formItems: [
        {
          label: this.$t('grid.others.supplier'), // 供应商,
          value: 'sSupplierId',
          type: 'cndInputDialogItem',
          dialogType: 'customer',
          defaultUrl: '/esc/customer/page',
          option: { valueKey: 'sPath' },
          placeholder: this.$t('grid.others.pleaseSelectSupplier')
        },
        {
          label: '经营单位',
          value: 'sManagementId',
          type: 'cndInputDialog',
          dialogType: 'escOrg'
        },
        {
          label: 'ESB编码',
          value: 'sEsbCode',
          type: 'elInput'
        },
        {
          label: '状态',
          value: 'sIsMainSwitch',
          type: 'elSelect',
          dict: sIsMainSwitchDict
        },
        {
          label: '是否推送',
          value: 'sIsSendRpa',
          type: 'elSelect',
          itemType: 'occultation',
          dict: 'base.yes-no'
        },
        {
          label: this.$t('grid.title.createdAt'),
          value: ['sCreateTime', 'vCreateTimeTo'],
          placeholder: [this.$t('grid.others.startDate'), this.$t('grid.others.endDate')],
          unlinkPanels: true,
          type: 'elDatePicker'
        }
      ],
      columnDefs: [
        {
          field: 'vSupplierName',
          headerName: this.$t('grid.others.supplier')
        },
        {
          field: 'vManagementName',
          headerName: '经营单位'
        },
        {
          field: 'sEsbCode',
          headerName: 'ESB编码'
        },
        {
          field: 'sIsSendRpa',
          headerName: '是否推送',
          valueGetter: params => {
            return getCnDitc(params, this.selectOps['base.yes-no'], 'sIsSendRpa')
          }
        },
        {
          field: 'sIsMainSwitch',
          headerName: '状态',
          valueGetter: params => {
            return getCnDitc(params, sIsMainSwitchDict, 'sIsMainSwitch')
          }
        },
        {
          field: 'vCreatorName',
          headerName: this.$t('grid.title.createdBy')
        },
        {
          field: 'sCreateTime',
          headerName: this.$t('grid.title.createdAt'),
          minWidth: 150,
          valueFormatter(params) {
            return Moment.time('YYYY-MM-DD HH:mm:ss', params.data.sCreateTime)
          }
        },
        {
          field: 'vModifierName',
          headerName: this.$t('grid.title.modifiedBy')
        },
        {
          headerName: this.$t('grid.title.modifiedAt'),
          field: 'sModifyTime',
          minWidth: 150,
          valueFormatter(params) {
            return Moment.time('YYYY-MM-DD HH:mm:ss', params.data.sModifyTime)
          }
        }
      ],
      rowData: [],
      selectOps: {
        'base.yes-no': []
      }
    }
  },
  created() {
  },
  mounted() {
    getDictList(this.selectOps)
    this.onSearch(false)
  },
  methods: {
    onSearch(load = true) {
      const searchInfo = this.$refs.searchForm.getSearchData()
      searchInfo.sParamsDto = {
        sEsbCode: searchInfo.sEsbCode,
        sIsSendRpa: searchInfo.sIsSendRpa,
        sIsMainSwitch: searchInfo.sIsMainSwitch
      }
      delete searchInfo.sEsbCode
      delete searchInfo.sIsSendRpa
      delete searchInfo.sIsMainSwitch
      this.searchInfo = searchInfo
      if (load) {
        this.$refs.aggrid.loadTableData()
      }
    },
    loadData(pagination) {
      return new Promise((resolve, reject) => {
        getSteelMillDockingConfigPage(this.searchInfo, pagination).then(res => {
          this.rowData = res.data.content.map(item => {
            return {
              ...item,
              ...item.sParamsDto,
              _selected: false,
              _selectedKeys: []
            }
          })
          resolve(res.data)
        }).catch(() => {
          reject([])
        })
        console.log('this.rowData: ', this.rowData)
      })
    },
    handleFooterCount(rowData) {
      // console.log(rowData)
    },

    create() {
      this.$router.push({
        path: `/steelMillDockingConfigDetail/add`,
        query: {
          type: 'add',
          name: `新增钢厂对接配置`,
          activeId: localStorage.getItem('menuId')
        }
      })
    },
    removes() {
      this.$refs.aggrid.getSelectedData(res => {
        if (!res.length) {
          this.$message.warning(this.$t('grid.others.pleaseSelectData'))
          return
        }
        this.$confirm(this.$t('grid.tips.isTheDeletionConfirmed'), this.$t('grid.others.prompt'), {
          confirmButtonText: this.$t('grid.others.yes'),
          cancelButtonText: this.$t('btns.no'),
          type: 'warning'
        }).then(() => {
          removeSteelMillDockingConfig(
            res.map(item => item.sId)
          ).then(() => {
            this.$message.success(this.$t('grid.tips.deletionSuccess'))
            this.$refs.aggrid.reloadTableData()
          })
        })
      })
    },
    onRowDoubleClicked(params) {
      this.$router.push({
        path: `/steelMillDockingConfigDetail/${params.data.sId}`,
        query: {
          id: params.data.sId,
          type: 'edit',
          name: `钢厂对接配置【${params.data.vSupplierName}】`,
          activeId: localStorage.getItem('menuId')
        }
      })
    }
  }
}
</script>

<style scoped>

</style>
