<template>
  <div>
    <cnd-dialog title="title" :visible="visible" :show-close="false">
      <template slot="leftBtn">
        <el-button type="primary" size="mini" plain @click="handleClose">{{ $t('grid.others.backToList') }}</el-button>
        <el-button type="primary" size="mini" @click="saveForm">{{ $t('btns.save') }}</el-button>
      </template>
      <template slot="rightBtn">
        {{ $t('grid.title.userAttribute') }}
      </template>
      <template slot="content">
        <el-form
          ref="formData"
          class="el-form-w100"
          label-width="100px"
          :inline="true"
          :model="form"
          size="small"
        >
          <cnd-form-card-list ref="cndFormCardList" :active-panel="activeCollapseName" :error-position="true">
            <cnd-form-card :title="$t('grid.tabs.basicInformation')" name="1">
              <el-row>
                <cnd-form-item :label="$t('grid.others.dictionaryName')" prop="sCodeTypeName">
                  <el-input v-model="form.sCodeTypeName" />
                </cnd-form-item>
                <cnd-form-item :label="$t('grid.others.dictionaryAlias')" prop="sCodeType">
                  <el-input v-model="form.sCodeType" />
                </cnd-form-item>
              </el-row>
            </cnd-form-card>
          </cnd-form-card-list>
        </el-form>

        <div class="btn-group">
          <div>
            <el-button
              type="primary"
              size="mini"
              @click="addRowData"
            >{{ $t('btns.add') }}</el-button>
            <el-button
              type="danger"
              size="mini"
              :disabled="!curSelRowData"
              @click="removeRowData"
            >{{ $t('btns.delete') }}</el-button>
          </div>
          <div class="text">
            {{ $t('grid.others.list') }}
          </div>
        </div>
        <ag-grid-wrap>
          <ag-grid-vue
            :suppress-copy-rows-to-clipboard="true"
            class="ag-theme-balham"
            style="height:500px"
            :row-drag-managed="true"
            :grid-options="gridData.gridOptions"
            :row-data="gridData.gridData"
            :modules="aGGridAllModules"
            @selection-changed="gridOnSelectionChanged"
            @grid-ready="query"
          />
        </ag-grid-wrap>
      </template>
    </cnd-dialog>
  </div>
</template>

<script>
import dictApi from '@/api/dictionary'
import { ToggleDialogMixin } from '@/components/cnd/dialog-mixin'
import { GridPageMixin } from '@/utils/page-mixins'
const initalFormData = {
  sCodeTypeName: '',
  sCodeType: '',
  sCategory: 'KF'
}
export default {
  name: 'EditDictDialog',
  mixins: [ToggleDialogMixin, GridPageMixin],
  props: {
    data: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      activeCollapseName: ['1', '2'],
      form: initalFormData,
      // 分页配置
      pagination: {
        pageNo: 0,
        pageSize: 20,
        total: 0
      },
      // 是否是删除操作
      isDelete: false,
      deleteParams: []
    }
  },
  watch: {
    dialogVisible(val) {
      if (val && this.data) {
        Object.assign(this.form, this.data)
        this.query()
      } else {
        this.form = initalFormData
        this.gridData.gridData = []
      }
    }
  },
  methods: {
    handleClose() {
      this.$emit('update:dialogVisible', false)
    },
    // 切换分页数据
    pageChange(obj) {
      this.pagination.pageNo = obj.page
      this.pagination.pageSize = obj.limit
      this.query()
    },
    // 获取grid配置参数（列的定义....）
    getDefaultGridOptions() {
      const vm = this
      const gridOptions = {
        columnDefs: [
          {
            field: 'index',
            headerName: '',
            minWidth: 40,
            maxWidth: 40,
            checkboxSelection: true,
            cellStyle: { 'justify-content': 'Center', textAlign: 'center' },
            cellClass: 'grid-cell-centered',
            pinned: 'left'
          },
          {
            field: 'sCodeValue',
            headerName: this.$t('grid.others.codeValue'),
            minWidth: 70
          },
          {
            field: 'sCodeName',
            headerName: this.$t('grid.others.codeNameTag'),
            minWidth: 110
          },
          {
            field: 'sIsEnabled',
            headerName: this.$t('grid.others.availableOrNot'),
            maxWidth: 110,
            cellEditor: 'agSelectCellEditor',
            cellEditorParams: { values: [this.$t('grid.others.yes'), this.$t('btns.no')] },
            cellRenderer: (params) => {
              params.value = params.value === this.$t('grid.others.yes') || params.value === '1' ? 1 : 0
              const result = {
                1: this.$t('grid.others.yes'),
                '0': this.$t('btns.no')
              }[params.value]
              return result
            }
          },
          {
            field: 'sSort',
            headerName: this.$t('grid.others.order'),
            minWidth: 110
          },
          {
            field: 'sDescribe',
            headerName: this.$t('grid.title.remarks'),
            minWidth: 110
          }
        ],
        rowSelection: 'single',
        defaultColDef: {
          resizable: true,
          editable: true
        },
        onRowDoubleClicked: (e) => {
          console.log('Grid双击事件，选中了', vm.curSelRowData)
        }
      }
      return gridOptions
    },
    query(refresh = false) {
      // this.gridData.gridOptions.api.showLoadingOverlay()
      var obj = {
        data: {
          content: this.data?.dicts
        }
      }
      this.setGridResult(obj)
    },
    removeRowData() {
      const row = this.gridData.gridOptions.api.getSelectedNodes().map(item => item.data)
      const index = this.form.dicts.findIndex(item => item.sCodeValue === row[0].sCodeValue)
      const index2 = this.gridData.gridData.findIndex(item => item.sCodeValue === row[0].sCodeValue)
      this.form.dicts.splice(index, 1)
      this.gridData.gridData.splice(index2, 1)
      this.isDelete = true
      this.deleteParams = row
    },
    addRowData() {
      if (!this.form.dicts) {
        this.form.dicts = []
        this.gridData.gridData = []
      }
      const rowTemplate = {
        sCodeName: '',
        sIsEnabled: '1',
        sLanguage: 'zh_CN',
        'sCodeValue': '',
        sPid: -1,
        sDescribe: ''
      }
      this.form.dicts.push(rowTemplate)
      this.gridData.gridData.push(rowTemplate)
    },
    tipMessage(res) {
      if (res.code === '0000') {
        this.$message({
          message: this.$t('tips.operationSuccessful'),
          type: 'success'
        })
      }
    },
    saveForm() {
      this.resetsIsEnabled()
      if (this.isDelete) {
        this.isDelete = false
        dictApi.deleteDict({
          sCodeType: this.form.sCodeType
        }, this.deleteParams).then(res => {
          this.tipMessage(res)
        })
        return
      }
      dictApi.modifyDictDetail(this.form).then(res => {
        this.tipMessage(res)
      })
    },
    resetsIsEnabled() {
      this.form?.dicts?.forEach(item => {
        let v = item.sIsEnabled
        if (v === '是') {
          v = '1'
        } else if (v === '否') {
          v = '0'
        }
        item.sIsEnabled = v
      })
    }
  }
}
</script>

<style scoped lang="scss">

</style>
