<template>
  <div class="right-tab3-top-container flexV">
    <cnd-btn-position top="3" right="402">
      <el-form
        ref="formDataForm"
        class="el-form-w100 mt-5"
        :inline="true"
        size="small"
        @submit.native.prevent
      >
        <cnd-form-item v-show="!docFieldConfigVisible" :custom-width="20" label="">
          <el-input
            ref="searchInput"
            v-model="keywords"
            clearable
            :placeholder="$t('grid.others.pleaseEnterTheContent')"
            size="mini"
            @keyup.native.stop.enter="query"
          >
            <i
              slot="suffix"
              class="el-icon-search el-input__icon"
              @click="query"
            /></el-input>
        </cnd-form-item>
      </el-form>
    </cnd-btn-position>
    <div class="btn-group">
      <div>
        {{ $t('grid.columns.userList') }}
      </div>
      <div>
        <el-button
          type="primary"
          size="mini"
          :disabled="!curSelRowData"
          @click="permissionFieldVisible = true"
        >批量授权核算组</el-button>
        <el-button
          type="primary"
          size="mini"
          :disabled="!curSelRowData"
          @click="showRoleBillGridDialog('选择来源单据',true)"
        >新增&授权单据</el-button>
        <el-button
          type="primary"
          size="mini"
          :disabled="!curSelRowData"
          @click="copyDialog = true"
        >{{ $t('btns.copy') }}</el-button>
        <el-button
          type="primary"
          size="mini"
          @click="addUserVisible = true"
        >{{ $t('btns.add') }}</el-button>
        <el-button
          type="danger"
          size="mini"
          :disabled="!curSelRowData"
          @click="delUser"
        >{{ $t('btns.delete') }}</el-button>
      </div>
    </div>
    <ag-grid-vue
      :suppress-copy-rows-to-clipboard="true"
      class="ag-theme-balham grid-class ag-grid-border-top"
      :row-drag-managed="true"
      :grid-options="gridData.gridOptions"
      :row-data="gridData.gridData"
      :modules="aGGridAllModules"
      @selection-changed="selectUser"
    />
    <cnd-pagination
      :total="totalNum"
      :page="params.page"
      :limit="params.limit"
      :event="paginationEvent"
    />
    <user-add-dialog
      v-model="addUserVisible"
      :selected-role="selectedRole"
      @query="query"
    />
    <!-- 用户编辑/详情 -->
    <!-- <edit-user-dialog
      :dialog-visible.sync="editUserDialogVisible"
      :title="editUserDialogTitle"
      :contact-data="editUserDialogData"
    /> -->

    <cnd-dialog
      :visible="copyDialog"
      :fullscreen="false"
      :title="$t('grid.btns.copyUserDocument')"
      height="28"
      width="400px"
      :append-to-body="true"
    >
      <div slot="content">
        <el-form>
          <cnd-form-item :label="$t('grid.title.userAccount')" :custom-width="24">
            <horizon-search-select
              v-model="userName"
              type="creater"
              @change="onSelectUser"
            />
          </cnd-form-item>
        </el-form>

      </div>
      <template slot="footer">
        <el-button size="mini" @click="copyDialog = false">{{ $t('btns.cancel') }}</el-button>
        <el-button
          type="primary"
          size="mini"
          @click="handleConfirm"
        >{{ $t('btns.confirmKey') }}</el-button>
      </template>
    </cnd-dialog>

    <!-- 角色单据授权 -->
    <role-bill-grid-dialog
      :dialog-visible.sync="roleBillGridDialogVisible"
      :title="roleBillGridDialogTitle"
      :is-single="roleBillGridDialogIsSingle"
      @onSelect="addUserDoc"
    />

    <role-bill-grid-dialog2
      :dialog-visible.sync="roleBillGridDialogVisible2"
      :title="roleBillGridDialogTitle2"
      @onSelect="addUserDoc2"
    />
    <PermissionTreeDialog
      :visible.sync="permissionFieldVisible"
      title="批量授权核算组"
      @onClose="permissionFieldVisible=false"
      @onSelect="setTreeSel"
    />
  </div>
</template>

<script>
import { GridPageMixin } from '@/utils/page-mixins'
import { RoleBillGridDialogMixin } from './dialog-mixins'
// import { EditUserDialogMixin } from './dialog-mixins'
import RoleApi from '@/api/role'
import UserAddDialog from './UserAddDialog'
import PermissionTreeDialog from './PermissionTreeDialog'

export default {
  components: {
    UserAddDialog,
    PermissionTreeDialog
  },
  mixins: [
    GridPageMixin,
    RoleBillGridDialogMixin
    // EditUserDialogMixin
  ],
  props: {
    selectedRole: {
      type: Array,
      default: function() {
        return []
      }
    },
    handleSelectUser: {
      type: Function,
      default: function() {
        return () => {}
      }
    }
  },
  data() {
    return {
      roleBillGridDialogTitle: '选择来源单据',
      docFieldConfigVisible: false,
      copyDialog: false,
      userId: '',
      userName: '',
      keywords: '',
      addUserVisible: false,
      // 分页
      totalNum: 0,
      params: {
        page: 0,
        limit: 10
      },
      sSheetId: '',
      permissionFieldVisible: false
    }
  },
  watch: {
    copyDialog() {
      this.userName = ''
      this.userId = ''
    },
    selectedRole() {
      this.params.page = 0
      this.query()
    }
  },
  mounted() {
    if (this.selectedRole && this.selectedRole.length) {
      this.query()
    }
    this.$EventBus.$on('hide', (state) => {
      this.docFieldConfigVisible = state
    })
  },
  methods: {
    handleConfirm() {
      if (!this.userId) {
        this.$message({
          message: this.$t('grid.others.selectAUser'),
          type: 'warning'
        })
        return
      }
      RoleApi.copyUser({ sourceRoleUserId: this.curSelRowData[0].id, targetUserIds: [this.userId] }).then(res => {
        if (res.code === '0000') {
          this.$message({
            message: this.$t('tips.operationSuccessful'),
            type: 'success'
          })
        }
        this.query()
        this.copyDialog = false
        this.userName = ''
        this.userId = ''
      })
    },
    onSelectUser(v) {
      if (v) {
        this.userId = v.id
      } else {
        this.userId = ''
      }
    },
    selectUser() {
      if (this.gridData.gridOptions && this.gridData.gridOptions.api) {
        const selectedNodes = this.gridData.gridOptions.api.getSelectedNodes()
        const selectedData = selectedNodes.map(node => node.data)
        if (selectedData && selectedData.length > 0) {
          this.curSelRowData = selectedData
        } else {
          this.curSelRowData = null
        }
        this.$emit('handleSelectUser', this.curSelRowData)
      }
    },
    // 获取grid配置参数（列的定义....）
    getDefaultGridOptions() {
      const vm = this
      const gridOptions = {
        columnDefs: [
          {
            headerName: '',
            minWidth: 40,
            maxWidth: 40,
            checkboxSelection: true,
            headerCheckboxSelection: true,
            pinned: 'left'
          },
          {
            field: 'userAccount',
            headerName: this.$t('grid.others.accountNumber'),
            suppressSizeToFit: true,
            minWidth: 50,
            width: 405
          },
          {
            field: 'userName',
            headerName: this.$t('grid.others.nameTag'),
            minWidth: 50,
            suppressSizeToFit: true,
            width: 405
          }
        ],
        rowSelection: 'multiple',
        suppressRowClickSelection: true,
        defaultColDef: {
          resizable: true
          // editable: true
        },
        onGridReady: function() {
          // 表格创建完成后执行的事件
          // this.gridData.gridOptions.api.sizeColumnsToFit()// 调整表格大小自适应
        },
        onRowDoubleClicked: e => {
          console.log('Grid双击事件，选中了', vm.curSelRowData)
        }
      }
      return gridOptions
    },
    // 分页事件
    paginationEvent(pages) {
      this.params = Object.assign({}, this.params, pages)
      this.query()
    },
    // 查询
    query() {
      // this.gridData.gridOptions.api.showLoadingOverlay()

      RoleApi.getSystemUserRole({
        ...this.params,
        roleId: this.selectedRole[0].id,
        param: this.keywords
      }).then(result => {
        this.$emit('handleSelectUser', null)
        this.gridData.gridData = result.data.content
        this.totalNum = result.data.totalElements
      })
    },
    // 删除该角色下的用户
    delUser() {
      const vm = this
      if (!vm.curSelRowData) {
        return
      }
      this.$confirm(this.$t('grid.tips.deleteRoleAccountTip'), this.$t('grid.others.prompt'), {
        confirmButtonText: this.$t('btns.confirm'),
        cancelButtonText: this.$t('btns.cancel'),
        type: 'warning'
      })
        .then(() => {
          console.log(vm.curSelRowData)
          let data = []
          data = vm.curSelRowData.map(item => {
            return item.userId
          })
          RoleApi.deleteUser({
            roleId: vm.selectedRole[0].id,
            userIds: data
          }).then(result => {
            this.$message({
              message: this.$t('tips.deletedSuccessfully'),
              type: 'success'
            })
            vm.query()
          })
        })
        .catch(() => {})
    },
    addUserDoc(curDocData) {
      this.sSheetId = curDocData[0].sId
      this.$nextTick(() => {
        this.showRoleBillGridDialog2('来源单据选择成功，请继续选择目标单据')
      })
    },
    addUserDoc2(curDocData) {
      const params = {
        sSheetId: this.sSheetId,
        sAppointId: this.curSelRowData?.map(item => item.id),
        sTagSheetIds: curDocData.map(item => item.sId)
      }
      console.log('选择完毕:', params)
      RoleApi.sheetParamUserCopy(params).then(res => {
        this.$message.success('操作成功')
        this.query()
      })
    },
    setTreeSel(data) {
      if (data) {
        const sOrgIds = data.map(v => v.id)
        const sAppointId = []
        this.curSelRowData.map(item => {
          sAppointId.push(item.id)
        })
        const params = {
          sAppointId,
          sOrgIds
        }
        RoleApi.userCopyCheck(params).then(result => {
          if (result.code === '0000') {
            this.$message({
              message: this.$t('tips.operationSuccessful'),
              type: 'success'
            })
            this.permissionFieldVisible = false
            this.query()
          }
        })
      } else {
        this.$message.error('请选择数据')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/Role/Role.scss';
</style>
