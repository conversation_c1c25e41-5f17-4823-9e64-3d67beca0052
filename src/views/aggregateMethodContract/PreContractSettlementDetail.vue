<template>
  <!-- 结算信息 -->
  <div>
    <el-form
      ref="form"
      class="el-form-w100"
      label-width="100px"
      :inline="true"
      :model="form"
      size="small"
      :disabled="true"
    >
      <cnd-form-card-list :active-panel="activeCollapseName">
        <cnd-form-card v-if="false" title="结算条款" name="1">
          <el-row>
            <cnd-form-item label="保证金支付方式" prop="sSettlementTerms">
              <el-select
                v-model="form.sSettlementTerms"
                :placeholder="$t('components.pleaseSelect')"
                clearable
                size="mini"
              >
                <el-option
                  v-for="item in options['msb.pact.settlement.rate']"
                  :key="item.sCodeValue"
                  :label="item.sCodeName"
                  :value="Number(item.sCodeValue)"
                />
              </el-select>
            </cnd-form-item>
            <cnd-form-item label="保证金比例" prop="sEarnestMoneyPercentage">
              <el-input v-model="form.sEarnestMoneyPercentage" />
            </cnd-form-item>
            <cnd-form-item label="保证金金额" prop="sEarnestMoneyAmount">
              <cnd-input-number v-model="form.sEarnestMoneyAmount" />
            </cnd-form-item>
            <cnd-form-item label="利息起算标准" prop="sInterestStartWay">
              <el-select
                v-model="form.sInterestStartWay"
                :placeholder="$t('components.pleaseSelect')"
                clearable
                size="mini"
              >
                <el-option
                  v-for="item in options['esc.settle.interest.start']"
                  :key="item.sCodeValue"
                  :label="item.sCodeName"
                  :value="item.sCodeValue"
                />
              </el-select>
            </cnd-form-item>
            <cnd-form-item label="结算保底天数" prop="sDaysOfSettlement">
              <el-input v-model="form.sDaysOfSettlement" />
            </cnd-form-item>
            <cnd-form-item label="结算周期" prop="sSettlementInterval">
              <el-input v-model="form.sSettlementInterval" />
            </cnd-form-item>
          </el-row>
          <el-row>
            <cnd-form-item label="现款返息" prop="sCashRebate">
              <el-select
                v-model="form.sCashRebate"
                :placeholder="$t('components.pleaseSelect')"
                clearable
                size="mini"
              >
                <el-option
                  v-for="item in options['base.yes-no']"
                  :key="item.sCodeValue"
                  :label="item.sCodeName"
                  :value="item.sCodeValue"
                />
              </el-select>
            </cnd-form-item>
            <cnd-form-item label="现款返息方式" prop="sCashRebateWay">
              <el-select
                v-model="form.sCashRebateWay"
                :placeholder="$t('components.pleaseSelect')"
                clearable
                size="mini"
              >
                <el-option
                  v-for="item in options['msb.return.interest.type']"
                  :key="item.sCodeValue"
                  :label="item.sCodeName"
                  :value="item.sCodeValue"
                />
              </el-select>
            </cnd-form-item>
            <cnd-form-item label="现款返息率" prop="sCashRebatePercentage">
              <el-input v-model="form.sCashRebatePercentage" />
            </cnd-form-item>
            <cnd-form-item label="单吨返息" prop="sCashTonRebate">
              <cnd-input-number v-model="form.sCashTonRebate" />
            </cnd-form-item>
          </el-row>

          <el-row>
            <cnd-form-item label="承兑贴息" prop="sAcceptanceDiscount">
              <el-select
                v-model="form.sAcceptanceDiscount"
                :placeholder="$t('components.pleaseSelect')"
                clearable
                size="mini"
              >
                <el-option
                  v-for="item in options['base.yes-no']"
                  :key="item.sCodeValue"
                  :label="item.sCodeName"
                  :value="item.sCodeValue"
                  :header-total="headerCount"
                />
              </el-select>
            </cnd-form-item>
            <cnd-form-item label="承兑贴息方式" prop="sAcceptanceDiscountWay">
              <el-select
                v-model="form.sAcceptanceDiscountWay"
                :placeholder="$t('components.pleaseSelect')"
                clearable
                size="mini"
              >
                <el-option
                  v-for="item in options['msb.return.interest.type']"
                  :key="item.sCodeValue"
                  :label="item.sCodeName"
                  :value="item.sCodeValue"
                />
              </el-select>
            </cnd-form-item>
            <cnd-form-item label="承兑返息率" prop="sAcceptanceDiscountPercentage">
              <el-input v-model="form.sAcceptanceDiscountPercentage" />
            </cnd-form-item>
            <cnd-form-item label="单吨返息" prop="sAcceptanceTonDiscount">
              <cnd-input-number v-model="form.sAcceptanceTonDiscount" />
            </cnd-form-item>
          </el-row>
        </cnd-form-card>
        <cnd-form-card title="结算条款" name="2">
          <steelTradeAggrid
            ref="aggrid"
            :heightinif="600"
            :column-defs="settlementColumnDefs"
            :auto-load-data="false"
            :row-data="rowData"
            :load-data="loadSettlementData"
            :header-total="headerCount"
            :footer-total="footerCount"
            table-selection
          />
        </cnd-form-card>
      </cnd-form-card-list>
    </el-form>
  </div>
</template>

<script>
import {
  getPreContractDetail,
  getSettlementRateDetail
} from '@/api/preContract/aggregateMethodContract'
import { getDictet } from '@/api/logistics/saleDelivery/saleorder'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import { getCnDitc } from '@/utils/common'
import { SteelFormat } from 'cnd-horizon-utils'

export default {
  name: 'PreContractSettlementDetail',
  components: {
    steelTradeAggrid
  },
  data() {
    return {
      headerCount: null,
      footerCount: null,
      searchInfo: null,
      getGridApi: () => {
        return this.$refs.aggrid.gridApi
      },
      activeCollapseName: ['2'],
      options: {
        'msb.pact.settlement.rate': '',
        'esc.settle.interest.start': '',
        'base.yes-no': '',
        'msb.return.interest.type': ''
      },
      form: {
        sEarnestMoneyPayWay: undefined,
        sEarnestMoneyPercentage: undefined,
        sEarnestMoneyAmount: undefined,
        sInterestStartWay: undefined,
        sDaysOfSettlement: undefined,
        sSettlementInterval: undefined,
        sCashRebate: undefined,
        sCashRebateWay: undefined,
        sCashRebatePercentage: undefined,
        sCashTonRebate: undefined,
        sAcceptanceDiscount: undefined,
        sAcceptanceDiscountWay: undefined,
        sAcceptanceDiscountPercentage: undefined,
        sAcceptanceTonDiscount: undefined
      },
      rowData: [],
      lastNum: 0,
      preNum: 0,
      settlementColumnDefs: [
        // 计息项目
        {
          field: 'sSettlementTerms',
          headerName: '计息项目',
          cellStyle: { textAlign: 'left' },
          valueGetter: (params) => {
            return getCnDitc(
              params,
              this.options['msb.pact.settlement.rate'],
              'sSettlementTerms'
            )
          }
        },
        // 起始天数
        {
          field: 'sDateStart',
          headerName: '提前/逾期天数（起）',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return SteelFormat.formatThousandthSign(params.value, 0)
          }
        },
        // 截止天数
        {
          field: 'sDateEnd',
          headerName: '提前/逾期天数（止）',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            let day
            if (params.value) {
              day = SteelFormat.formatThousandthSign(params.value, 0)
            } else {
              day = '‌∞'
            }
            return day
          }
        },
        // 是否无限期
        {
          field: 'sIndefinite',
          headerName: '是否无限期',
          cellStyle: { textAlign: 'left' },
          valueGetter: (params) => {
            return getCnDitc(params, this.options['base.yes-no'], 'sIndefinite')
          }
        },
        // 费率
        {
          field: 'sRate',
          headerName: '费率',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return (params.value || 0) + '%'
          }
        }
        // 加价
        // {
        //   field: 'sPriceRaise',
        //   headerName: '加价',
        //   pinned: 'left',
        //   cellStyle: { textAlign: 'left' },
        //   valueFormatter: params => {
        //     return SteelFormat.formatThousandthSign(params.value, 2)
        //   }
        // }
      ],
      yesOrNoType: [
        {
          sCodeValue: '1',
          sCodeName: '是',
          sSort: '0',
          sFilter: null,
          sisEnabled: '1',
          sLanguage: 'zh_CN'
        },
        {
          sCodeValue: '0',
          sCodeName: '否',
          sSort: '0',
          sFilter: null,
          sisEnabled: '1',
          sLanguage: 'zh_CN'
        }
      ],
      interestProjectList: [
        {
          sCodeValue: '10',
          sCodeName: '保底加价',
          sSort: '0',
          sFilter: null,
          sisEnabled: '1',
          sLanguage: 'zh_CN'
        },
        {
          sCodeValue: '20',
          sCodeName: '利息',
          sSort: '0',
          sFilter: null,
          sisEnabled: '2',
          sLanguage: 'zh_CN'
        },
        {
          sCodeValue: '30',
          sCodeName: '违约金',
          sSort: '0',
          sFilter: null,
          sisEnabled: '3',
          sLanguage: 'zh_CN'
        }
      ]
    }
  },
  created() {
    // this.getDictetInfo()
    // this.getDetail()
  },
  methods: {
    getDictetInfo() {
      getDictet([
        'msb.pact.settlement.rate',
        'esc.settle.interest.start',
        'base.yes-no',
        'msb.return.interest.type'
      ])
        .then((result) => {
          this.options['base.yes-no'] = result.data[2].dicts
          this.options['msb.pact.settlement.rate'] = result.data[0].dicts
          this.options['esc.settle.interest.start'] = result.data[1].dicts
          this.options['msb.return.interest.type'] = result.data[3].dicts
        })
        .catch(() => { })

      this.$nextTick(() => {
        this.$refs.aggrid.loadTableData()
      })
    },
    getDetail() {
      getPreContractDetail({
        id: this.$route.query.Id
      }).then((res) => {
        // res.data.sEarnestMoneyPercentage =
        //   parseFloat((res.data.sEarnestMoneyPercentage * 100).toFixed(6)) === 0 ? null : parseFloat((res.data.sEarnestMoneyPercentage * 100).toFixed(6)) + '%'
        // res.data.sCashRebatePercentage =
        //   parseFloat((res.data.sCashRebatePercentage * 100).toFixed(6)) === 0 ? null : parseFloat((res.data.sCashRebatePercentage * 100).toFixed(6)) + '%'
        // res.data.sAcceptanceDiscountPercentage =
        //   parseFloat((res.data.sAcceptanceDiscountPercentage * 100).toFixed(6)) === 0 ? null : parseFloat((res.data.sAcceptanceDiscountPercentage * 100).toFixed(6)) + '%'
        // this.form = res.data || {}
      })
    },
    loadSettlementData(pagination) {
      return new Promise((resolve, reject) => {
        this.searchInfo = { sContractId: this.$route.query.Id }
        getSettlementRateDetail(
          { sTotalContractBasicId: this.$route.query.Id },
          pagination
        )
          .then((res) => {
            this.rowData = res.data.content.map((item) => {
              item._selected = false
              item._selectedKeys = []
              item.details = []
              return item
            })
            console.log('111111```', this.rowData)
            resolve(res.data)
          })
          .catch(() => {
            reject([])
          })
      })
    }
  }
}
</script>

<style scoped></style>
