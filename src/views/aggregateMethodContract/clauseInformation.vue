<template>
  <!-- 合同条款信息 -->
  <div>
    <el-form
      ref="form"
      class="el-form-w100"
      label-width="100px"
      :inline="true"
      :model="form"
      size="small"
      :disabled="true"
    >
      <cnd-form-card-list :active-panel="activeCollapseName">
        <cnd-form-card title="合同条款信息" name="2">
          <el-row class="ml-20">
            <div class="weight-size">一、合同标的是否显示列“暂定含税运费单价(元/吨)”</div>
            <el-radio-group v-model="form.sFreight.level1" class="ml-20">
              <div
                v-for="(item, index) in selectList.contractList"
                :key="index"
                class="disabled-color"
              >
                <el-radio
                  v-if="item.labelName == form.sFreight.level1"
                  :label="item.labelName"
                  style="color: #000"
                >{{ item.name }}</el-radio>
              </div>
            </el-radio-group>
          </el-row>

          <el-row class="ml-20">
            <div class="weight-size">二、交货地点：</div>
            <el-radio-group v-model="form.sDeliveryLocation.level1" class="ml-20">
              <div v-for="(item, index) in selectList.deliver" :key="index" class="disabled-color">
                <el-radio
                  v-if="item.labelName == form.sDeliveryLocation.level1"
                  :label="item.labelName"
                  style="color: #000"
                >{{ item.name }}</el-radio>
              </div>
            </el-radio-group>

            <cnd-form-item
              class="ml-20"
              label="备案仓库"
              prop="sDeliveryLocationWarehouse"
              :custom-width="12"
            >
              <el-input
                :value="
                  msbPreTotalContractWarehouse(
                    form.sDeliveryLocationWarehouse
                  )
                "
                clearable
              />
            </cnd-form-item>
            <cnd-form-item class="ml-20" label="交货仓库/码头" prop="sCashRebatePercentage">
              <el-input v-model="form.sDeliveryLocation.value" />
            </cnd-form-item>
            <cnd-form-item class="ml-20" label="运输模式" prop="sCashRebatePercentage">
              <el-input v-model="transportMode" />
            </cnd-form-item>
            <cnd-form-item class="ml-20" label="物流费⽤承担" prop="sCashRebatePercentage">
              <el-input v-model="logisticsFeesType" />
            </cnd-form-item>
          </el-row>

          <el-row class="ml-20">
            <div class="weight-size">三、合理损耗：</div>
            <cnd-form-item class="ml-20" label="溢短装" prop="sCashRebatePercentage">
              <el-input v-model="form.sMoreOrLess">
                <span slot="suffix">%</span>
              </el-input>
            </cnd-form-item>
            <cnd-form-item label="磅差" prop="sCashRebatePercentage">
              <el-input v-model="form.sPoundDifference">
                <span slot="suffix">%</span>
              </el-input>
            </cnd-form-item>
          </el-row>

          <el-row class="ml-20">
            <div class="weight-size">四、保险：</div>
            <el-radio-group v-model="form.sInsurance.level1" class="ml-20">
              <div
                v-for="(item, index) in selectList.insurance"
                :key="index"
                class="disabled-color"
              >
                <el-radio
                  v-if="item.labelName == form.sInsurance.level1"
                  :label="item.labelName"
                  style="color: #000"
                >{{ item.name }}</el-radio>
              </div>
            </el-radio-group>
            <cnd-form-item
              v-if="form.sInsurance.level1 == 2"
              class="ml-20"
              label="保费"
              prop="sCashRebatePercentage"
            >
              <el-input v-model="form.sInsurance.value" />
            </cnd-form-item>
          </el-row>

          <el-row class="ml-20">
            <div class="weight-size">五、保证金：</div>

            <el-radio-group v-model="form.sBond.level1" class="ml-20">
              <div
                v-for="(item, index) in selectList.insuranceBenefit"
                :key="index"
                class="disabled-color radio-box"
              >
                <div v-for="(j, id) in form.sBond.level1" :key="id">
                  <el-radio v-if="item.labelName == j" :label="item.labelName">
                    <div class="disabled-color-bg" />
                    <div class="radio-span">{{ item.name }}</div>
                  </el-radio>
                </div>
              </div>
            </el-radio-group>
            <div class="ml-20">
              <cnd-form-item v-if="getValue('1')" label="保证金比例" prop="sCashRebatePercentage">
                <el-input v-model="form.sBond.value.depositRatio">
                  <span slot="suffix">%</span>
                </el-input>
              </cnd-form-item>

              <div v-if="getValue('2')">
                <cnd-form-item label="保证金金额" prop="sBond" required>
                  <el-input
                    v-model="form.sBond.value.bondMoney"
                    input-align="right"
                    type="number"
                    placeholder="请输入"
                  />
                </cnd-form-item>
                <cnd-form-item label="支付时间" prop="sBond" required>
                  <el-input
                    v-model="form.sBond.value.paymentTime"
                    input-align="right"
                    placeholder="请输入"
                  />
                </cnd-form-item>
              </div>

              <div v-if="getValue('3')">
                <cnd-form-item label="合约" prop="sBond" required>
                  <el-input
                    v-model="form.sBond.value.contract"
                    input-align="right"
                    placeholder="请输入"
                  />
                </cnd-form-item>
                <cnd-form-item label="上涨比例" prop="sBond" required>
                  <el-input
                    v-model="form.sBond.value.increaseRatio"
                    input-align="right"
                    placeholder="请输入"
                    type="number"
                    @input="inputBXJBL('increaseRatio', $event)"
                  >
                    <span slot="suffix">%</span>
                  </el-input>
                </cnd-form-item>

                <cnd-form-item label="追加保证金比例" prop="sBond" required>
                  <el-input
                    v-model="form.sBond.value.additionalMarginRatio"
                    input-align="right"
                    placeholder="请输入"
                    type="number"
                    @input="inputBXJBL('additionalMarginRatio', $event)"
                  >
                    <span slot="suffix">%</span>
                  </el-input>
                </cnd-form-item>
              </div>
              <!-- 4.1 -->
              <div v-if="getValue('5')">
                <cnd-form-item label="暂定单价比例" prop="sBond" required>
                  <el-input
                    v-model="form.sBond.value.provisionalUnitPriceRatio"
                    input-align="right"
                    placeholder="请输入"
                  />
                </cnd-form-item>
                <cnd-form-item label="提货天数" prop="sBond" required>
                  <el-input
                    v-model="form.sBond.value.deliveryDays"
                    input-align="right"
                    placeholder="请输入"
                    type="number"
                    @input="inputBXJBL('deliveryDays', $event)"
                  >
                    <span slot="suffix">%</span>
                  </el-input>
                </cnd-form-item>
              </div>
              <!-- 4.2 -->
              <div v-if="getValue('6')">
                <cnd-form-item label="暂定单价加价金额" prop="sBond" required>
                  <el-input
                    v-model="form.sBond.value.provisionalUnitPriceMarkup"
                    input-align="right"
                    placeholder="请输入"
                    type="number"
                  />
                </cnd-form-item>
                <cnd-form-item label="提货天数" prop="sBond" required>
                  <el-input
                    v-model="form.sBond.value.deliveryDays"
                    input-align="right"
                    placeholder="请输入"
                    type="number"
                  />
                </cnd-form-item>
              </div>
            </div>
          </el-row>

          <el-row class="ml-20">
            <div class="weight-size">六、结算期及结算方式：</div>

            <div class="ml-20">
              <div>1、价格条款</div>
              <el-radio-group v-model="form.sPriceTerms.level1" class="ml-20">
                <div
                  v-for="(item, index) in selectList.priceClause"
                  :key="index"
                  class="disabled-color radio-box"
                >
                  <!--  -->
                  <el-radio
                    v-if="form.sPriceTerms.level1 == item.labelName"
                    :label="item.labelName"
                  >
                    <div class="radio-span">{{ item.name }}</div>
                  </el-radio>
                </div>
              </el-radio-group>

              <div class="ml-20">
                <cnd-form-item
                  v-if="form.sPriceTerms.level1 == 3"
                  label="付款方式"
                  prop="sPriceTerms"
                  required
                >
                  <el-input
                    v-model="form.sPriceTerms.value.paymentMethod"
                    input-align="right"
                    placeholder="请输入"
                  />
                </cnd-form-item>

                <div v-if="form.sPriceTerms.level1 == 5">
                  <cnd-form-item label="参考网价" required prop="sPriceTerms">
                    <el-input
                      v-model="form.sPriceTerms.value.referenceOnlinePrice"
                      input-align="right"
                      placeholder="请输入"
                    />
                  </cnd-form-item>

                  <cnd-form-item label="销售下浮金额" required prop="sPriceTerms">
                    <el-input
                      v-model="form.sPriceTerms.value.salesDecreaseAmount"
                      input-align="right"
                      placeholder="请输入"
                    />
                  </cnd-form-item>
                </div>
              </div>
            </div>
          </el-row>
          <el-row class="ml-20">
            <div class="ml-20">
              <div>2、结算期：</div>
              <el-radio-group v-model="form.sSettlementPeriod.level1" class="ml-20">
                <div
                  v-for="(item, index) in selectList.result"
                  :key="index"
                  class="disabled-color radio-box"
                >
                  <!--  -->
                  <el-radio
                    v-if="form.sSettlementPeriod.level1 == item.labelName"
                    :label="item.labelName"
                  >
                    <div class="radio-span">{{ item.name }}</div>
                  </el-radio>
                </div>
              </el-radio-group>

              <div
                v-if="
                  form.sSettlementPeriod.level1 == 1 ||
                    form.sSettlementPeriod.level1 == 3 ||
                    form.sSettlementPeriod.level1 == 4
                "
              >
                <cnd-form-item label="付款日期" prop="sSettlementPeriod">
                  <el-input
                    v-model="form.sSettlementPeriod.value.paymentDate"
                    input-align="right"
                    placeholder="请选择付款日期"
                  />
                </cnd-form-item>
              </div>
              <div
                v-if="
                  form.sSettlementPeriod.level1 != 3 &&
                    form.sSettlementPeriod.level1 != ''
                "
              >
                <cnd-form-item label="几天内" prop="sSettlementPeriod" required>
                  <el-input
                    v-model="form.sSettlementPeriod.value.withinDays"
                    input-align="right"
                    placeholder="请输入"
                  />
                </cnd-form-item>
              </div>
              <div v-if="form.sSettlementPeriod.level1 == 4">
                <cnd-form-item label="付款方式" prop="sSettlementPeriod" required>
                  <el-input
                    v-model="form.sSettlementPeriod.value.paymentMethod"
                    input-align="right"
                    placeholder="请输入"
                  />
                </cnd-form-item>
                <cnd-form-item label="付款比例" prop="sSettlementPeriod" required>
                  <el-input
                    v-model="form.sSettlementPeriod.value.paymentRatio"
                    input-align="right"
                    placeholder="请输入"
                    @input="
                      inputFKBL('sSettlementPeriod', 'paymentRatio', $event)
                    "
                  >
                    <span slot="suffix">%</span>
                  </el-input>
                </cnd-form-item>

                <cnd-form-item label="付款日期" prop="sSettlementPeriod">
                  <el-input
                    v-model="form.sSettlementPeriod.value.paymentDateTwo"
                    input-align="right"
                    placeholder="请选择付款日期"
                  />
                </cnd-form-item>

                <!-- <cnd-form-item
                  required
                  label="付款日期2"
                  right-icon="calendar"
                  prop="sSettlementPeriod"
                  :clickable="true"
                  placeholder="请选择付款日期2"
                  @onClick="signDateShow1 = true"
                >
                  <div slot="right">
                    <text
                      :class="{
                        'picker-text':
                          !form.sSettlementPeriod.value.paymentDateTwo
                      }"
                    >
                      {{
                        form.sSettlementPeriod.value.paymentDateTwo ||
                        '请选择日期'
                      }}
                    </text>
                  </div>
                  <cube-date-picker
                    v-model="form.sSettlementPeriod.value.paymentDateTwo"
                    :visible="signDateShow1"
                    mode="date"
                    is-closable
                    @onCancel="signDateShow1 = false"
                    @onConfirm="signDateConfirm('付款日期2', $event)"
                  />
                </cnd-form-item>-->
                <cnd-form-item label="几天内2" prop="sSettlementPeriod" required>
                  <el-input
                    v-model="form.sSettlementPeriod.value.withinDaysTwo"
                    input-align="right"
                    placeholder="请输入"
                  />
                </cnd-form-item>

                <cnd-form-item label="付款方式2" prop="sSettlementPeriod" required>
                  <el-input
                    v-model="form.sSettlementPeriod.value.paymentMethodTwo"
                    input-align="right"
                    placeholder="请输入"
                  />
                </cnd-form-item>
              </div>

              <div v-if="form.sSettlementPeriod.level1 == 5">
                <cnd-form-item label="付款方式" prop="sSettlementPeriod" required>
                  <el-input
                    v-model="form.sSettlementPeriod.value.paymentMethod"
                    input-align="right"
                    placeholder="请输入"
                  />
                </cnd-form-item>
              </div>
            </div>
            <el-row />
            <div class="ml-20">
              <div>4、现金折扣</div>
              <div class="ml-20">
                <div class="pl-18">
                  <!-- {{ form.msbPreTotalContractClause.sCashDiscount }} -->
                  <div>
                    <div v-if="form.sCashDiscount.level1 == '1'" class="d-flex cash">
                      <div class="cash-checkbox" />
                      4.1、若需方合同支付方式从{支付方式}变更为银行承兑汇票；
                    </div>
                    <div class="cash-son">
                      <div
                        v-if="
                          form.sCashDiscount.level2 == '1' &&
                            form.sCashDiscount.level1 == '1'
                        "
                        class="d-flex cash-pt"
                      >
                        <div class="cash-checkbox" />
                        4.1.1、需方应按照{费率}/年支付从汇票转让日至汇票到期日之间的费用。
                      </div>
                      <div
                        v-if="
                          form.sCashDiscount.level2 == '2' &&
                            form.sCashDiscount.level1 == '1'
                        "
                        class="d-flex cash-pt"
                      >
                        <div class="cash-checkbox" />
                        4.1.2、本合同签订后{几日起}天起，需方应按照 {费率}
                        /月（含税）支付费用，期限为{承兑期现天数}天。
                      </div>
                    </div>
                  </div>
                  <div>
                    <div v-if="form.sCashDiscount.level1 == '2'" class="d-flex cash">
                      <div class="cash-checkbox" />
                      4.2、若需方合同支付方式从{支付方式}变更为现款，则供方从收到现款之日起按下述方式（择一）给予现金折让，具体现金折让金额在结算单中一并处理；
                    </div>
                    <div class="cash-son">
                      <div
                        v-if="
                          form.sCashDiscount.level2 == '1' &&
                            form.sCashDiscount.level1 == '2'
                        "
                        class="d-flex cash-pt"
                      >
                        <div class="cash-checkbox" />
                        4.2.1、本合同签订后 {几日起} 天起，以
                        {费率}/月（含税）计收现金折让金额，折让期限为{折让期现天数}天。
                      </div>
                      <div
                        v-if="
                          form.sCashDiscount.level2 == '2' &&
                            form.sCashDiscount.level1 == '2'
                        "
                        class="d-flex cash-pt"
                      >
                        <div class="cash-checkbox" />
                        4.2.2、以{折让金额}元/吨计收现金折让金额。
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!--4、现金折扣 支付方式 -->
              <div>
                <div>
                  <!-- v-if="fourArr[0] == '4.1'" -->
                  <cnd-form-item label="支付方式" prop="sCashDiscount" required>
                    <el-input
                      v-model="form.sCashDiscount.value.sPayType"
                      input-align="right"
                      placeholder="请输入"
                    />
                  </cnd-form-item>

                  <cnd-form-item
                    v-if="
                      form.sCashDiscount.level1 == '1' &&
                        form.sCashDiscount.level2 == '2'
                    "
                    label="几天起"
                    prop="sCashDiscount"
                    required
                  >
                    <el-input
                      v-model="form.sCashDiscount.value.startingDays"
                      input-align="right"
                      placeholder="请输入"
                    />
                  </cnd-form-item>

                  <cnd-form-item
                    v-if="
                      form.sCashDiscount.level1 == '1' &&
                        (form.sCashDiscount.level2 == '1' ||
                          form.sCashDiscount.level2 == '2')
                    "
                    label="费率"
                    prop="sCashDiscount"
                    required
                  >
                    <el-input
                      v-model="form.sCashDiscount.value.rate"
                      input-align="right"
                      placeholder="请输入"
                    >
                      <span slot="suffix">%</span>
                    </el-input>
                  </cnd-form-item>

                  <cnd-form-item
                    v-if="
                      form.sCashDiscount.level1 == '1' &&
                        form.sCashDiscount.level2 == '2'
                    "
                    label="承兑期限天数"
                    prop="sCashDiscount"
                    required
                  >
                    <el-input
                      v-model="form.sCashDiscount.value.acceptancePeriodDays"
                      input-align="right"
                      placeholder="请输入"
                    />
                  </cnd-form-item>
                </div>
                <div v-if="form.sCashDiscount.level1 == '2'">
                  <!-- <cnd-form-item label="支付方式" prop="sCashDiscount" required>
                    <el-input
                      v-model="form.sCashDiscount.value.sPayType"
                      input-align="right"
                      placeholder="请输入"
                    />
                  </cnd-form-item>-->

                  <cnd-form-item
                    v-if="
                      form.sCashDiscount.level1 == '2' &&
                        form.sCashDiscount.level2 == '1'
                    "
                    label="几天起"
                    prop="sCashDiscount"
                    required
                  >
                    <el-input
                      v-model="form.sCashDiscount.value.startingDays"
                      input-align="right"
                      placeholder="请输入"
                    />
                  </cnd-form-item>

                  <cnd-form-item
                    v-if="
                      form.sCashDiscount.level1 == '2' &&
                        form.sCashDiscount.level2 == '1'
                    "
                    label="费率"
                    prop="sCashDiscount"
                    required
                  >
                    <el-input
                      v-model="form.sCashDiscount.value.rate"
                      input-align="right"
                      placeholder="请输入"
                    >
                      <span slot="suffix">%</span>
                    </el-input>
                  </cnd-form-item>
                  <cnd-form-item
                    v-if="
                      form.sCashDiscount.level1 == '2' &&
                        form.sCashDiscount.level2 == '1'
                    "
                    label="折让期限天数"
                    prop="sCashDiscount"
                    required
                  >
                    <el-input
                      v-model="form.sCashDiscount.value.discountPeriodDays"
                      input-align="right"
                      placeholder="请输入"
                    />
                  </cnd-form-item>
                  <cnd-form-item
                    v-if="
                      form.sCashDiscount.level1 == '2' &&
                        form.sCashDiscount.level2 == '2'
                    "
                    label="折让金额"
                    prop="sCashDiscount"
                    required
                  >
                    <el-input
                      v-model="form.sCashDiscount.value.discountAmount"
                      input-align="right"
                      placeholder="请输入"
                    />
                  </cnd-form-item>
                </div>
              </div>
            </div>
          </el-row>

          <el-row class="ml-20">
            <div v-if="form.sCommercialDiscount.level1" class="ml-20">
              <div>5、商业折让</div>
              <el-radio-group v-model="form.sCommercialDiscount.level1" class="ml-20">
                <div
                  v-for="(item, index) in selectList.shopDiscount"
                  :key="index"
                  class="disabled-color radio-box"
                >
                  <!--  -->
                  <el-radio
                    v-if="item.labelName == form.sCommercialDiscount.level1"
                    :label="item.labelName"
                  >
                    <div class="radio-span">{{ item.name }}</div>
                  </el-radio>
                </div>

                <div class="ml-20">
                  <el-radio-group v-model="form.sCommercialDiscount.level2" direction="vertical">
                    <div
                      v-for="(item, index) in selectList.shopDiscountSon"
                      :key="index"
                      class="disabled-color radio-box"
                    >
                      <el-radio
                        v-if="item.labelName == form.sCommercialDiscount.level2"
                        :label="item.labelName"
                      >
                        <div class="radio-span">{{ item.name }}</div>
                      </el-radio>
                    </div>
                  </el-radio-group>
                </div>
              </el-radio-group>
            </div>
          </el-row>

          <el-row class="ml-20">
            <!-- 结算 -->
            <el-row class="ml-20">
              <div v-if="form.sSettlement.level1" class="ml-20">
                <div>5、结算</div>
                <el-radio-group v-model="form.sSettlement.level1" class="ml-20">
                  <div
                    v-for="(item, index) in selectList.settleAccount"
                    :key="index"
                    class="disabled-color radio-box"
                  >
                    <!--  -->
                    <el-radio
                      v-if="item.labelName == form.sSettlement.level1"
                      :label="item.labelName"
                    >
                      <div class="radio-span">{{ item.name }}</div>
                    </el-radio>
                  </div>
                </el-radio-group>

                <div v-if="form.sSettlement.level1 == 1">
                  <cnd-form-item label="合同签订后几天内" prop="sSettlement" required>
                    <el-input
                      v-model="
                        form.sSettlement.value.withinDaysAfterContractSigning
                      "
                      placeholder="请输入"
                      input-align="right"
                    />
                  </cnd-form-item>

                  <cnd-form-item label="结算数量加价1" prop="sSettlement" required>
                    <el-switch
                      v-model="
                        form.sSettlement.value.settlementQuantityMarkupOne
                      "
                      disabled
                      :active-value="1"
                      :inactive-value="0"
                    />
                  </cnd-form-item>

                  <cnd-form-item
                    v-if="form.sSettlement.value.settlementQuantityMarkupOne"
                    label="加价1"
                    prop="sSettlement"
                    required
                  >
                    <el-input v-model="form.sSettlement.value.markupOne" placeholder="请输入" />
                  </cnd-form-item>

                  <cnd-form-item
                    label="合同签订后几天及以上"
                    class="label-whites"
                    prop="sSettlement"
                    required
                  >
                    <el-input
                      v-model="
                        form.sSettlement.value.daysAfterContractSigningOrMore
                      "
                      placeholder="请输入"
                    />
                  </cnd-form-item>
                  <cnd-form-item label="结算数量加价2" prop="sSettlement" required>
                    <el-switch
                      v-model="
                        form.sSettlement.value.settlementQuantityMarkupTwo
                      "
                      disabled
                      :active-value="1"
                      :inactive-value="0"
                    />
                  </cnd-form-item>

                  <cnd-form-item
                    v-if="form.sSettlement.value.settlementQuantityMarkupTwo"
                    label="加价2"
                    prop="sSettlement"
                    required
                  >
                    <el-input v-model="form.sSettlement.value.markupTwo" placeholder="请输入" />
                  </cnd-form-item>
                </div>

                <div v-if="form.sSettlement.level1 == 2">
                  <cnd-form-item label="固定日期1" prop="sSettlement" required>
                    <el-input v-model="form.sSettlement.value.fixedDateOne" placeholder="请输入" />
                  </cnd-form-item>

                  <cnd-form-item label="合同签订后几天内" class="label-whites" prop="sSettlement" required>
                    <el-input
                      v-model="
                        form.sSettlement.value.withinDaysAfterContractSigning
                      "
                      placeholder="请输入"
                    />
                  </cnd-form-item>
                  <cnd-form-item label="加价1" prop="sSettlement" required>
                    <el-input v-model="form.sSettlement.value.markupOne" placeholder="请输入" />
                  </cnd-form-item>

                  <cnd-form-item label="固定日期2" prop="sSettlement" required>
                    <el-input v-model="form.sSettlement.value.fixedDateTwo" placeholder="请输入" />
                  </cnd-form-item>
                  <cnd-form-item
                    label="合同签订后几天及以上"
                    class="label-whites"
                    prop="sSettlement"
                    required
                  >
                    <el-input
                      v-model="
                        form.sSettlement.value.daysAfterContractSigningOrMore
                      "
                      placeholder="请输入"
                    />
                  </cnd-form-item>
                  <cnd-form-item label="加价2" prop="sSettlement" required>
                    <el-input v-model="form.sSettlement.value.markupTwo" placeholder="请输入" />
                  </cnd-form-item>
                </div>

                <div v-if="form.sSettlement.level1 == 3">
                  <cnd-form-item label="货物到达地点" prop="sSettlement" required>
                    <el-input v-model="form.sSettlement.value.deliveryLocation" placeholder="请输入" />
                  </cnd-form-item>
                  <cnd-form-item label="合同签订后几天内" class="label-whites" prop="sSettlement" required>
                    <el-input
                      v-model="
                        form.sSettlement.value.withinDaysAfterContractSigning
                      "
                      placeholder="请输入"
                    />
                  </cnd-form-item>
                  <cnd-form-item label="结算数量加价1" class="label-whites" prop="sSettlement" required>
                    <el-switch
                      v-model="
                        form.sSettlement.value.settlementQuantityMarkupOne
                      "
                      disabled
                      :active-value="1"
                      :inactive-value="0"
                    />
                  </cnd-form-item>

                  <cnd-form-item
                    v-if="form.sSettlement.value.settlementQuantityMarkupOne"
                    label="加价1"
                    prop="sSettlement"
                    required
                  >
                    <el-input v-model="form.sSettlement.value.markupOne" placeholder="请输入" />
                  </cnd-form-item>

                  <cnd-form-item
                    label="合同签订后几天及以上"
                    class="label-whites"
                    prop="sSettlement"
                    required
                  >
                    <el-input
                      v-model="
                        form.sSettlement.value.daysAfterContractSigningOrMore
                      "
                      placeholder="请输入"
                    />
                  </cnd-form-item>

                  <cnd-form-item label="结算数量加价2" class="label-whites" prop="sSettlement" required>
                    <el-switch
                      v-model="
                        form.sSettlement.value.settlementQuantityMarkupTwo
                      "
                      disabled
                      :active-value="1"
                      :inactive-value="0"
                    />
                  </cnd-form-item>

                  <cnd-form-item
                    v-if="form.sSettlement.value.settlementQuantityMarkupTwo"
                    label="加价2"
                    prop="sSettlement"
                    required
                  >
                    <el-input v-model="form.sSettlement.value.markupTwo" placeholder="请输入" />
                  </cnd-form-item>
                </div>

                <div v-if="form.sSettlement.level1 == 4">
                  <cnd-form-item label="加价" prop="markup" required>
                    <el-input v-model="form.sSettlement.value.markup" placeholder="请输入" />
                  </cnd-form-item>
                </div>

                <div v-if="form.sSettlement.level1 == 5">
                  <cnd-form-item label="合约" prop="sSettlement" required>
                    <el-input v-model="form.sSettlement.value.contract" placeholder="请输入" />
                  </cnd-form-item>

                  <cnd-form-item label="价格" prop="sSettlement" required>
                    <el-input v-model="form.sSettlement.value.price" placeholder="请输入" />
                  </cnd-form-item>

                  <cnd-form-item label="点价日期截止" prop="sSettlement" required>
                    <el-input v-model="form.sSettlement.value.pricingDateEnd" placeholder="请输入" />
                  </cnd-form-item>

                  <!-- <cnd-form-item
                required
                label="点价日期截止"
                right-icon="calendar"
                prop="sSettlementPeriod"
                :clickable="true"
                placeholder="请选择点价日期截止"
                @onClick="signDateShow = true"
              >
                <div slot="right">
                  <text
                    :class="{
                      'picker-text': !form.sSettlement.value.pricingDateEnd
                    }"
                  >
                    {{
                      formatDate(form.sSettlement.value.pricingDateEnd) ||
                      "请选择日期"
                    }}
                  </text>
                </div>
                <cube-date-picker
                  v-model="form.sSettlement.value.pricingDateEnd"
                  :visible="signDateShow"
                  mode="date"
                  is-closable
                  @onCancel="signDateShow = false"
                  @onConfirm="signDateConfirm('点价', $event)"
                />
                  </cnd-form-item>-->

                  <cnd-form-item
                    label="点价天数"
                    prop="sSettlement"
                    required
                  >{{ form.sSettlement.value.pricingDays }}</cnd-form-item>

                  <cnd-form-item
                    label="基差"
                    prop="sSettlement"
                    required
                  >{{ form.sSettlement.value.basis }}</cnd-form-item>

                  <cnd-form-item
                    label="提货天数"
                    prop="sSettlement"
                    required
                  >{{ form.sSettlement.value.pickupDays }}</cnd-form-item>

                  <cnd-form-item
                    label="最迟点价日"
                    prop="sSettlement"
                    required
                  >{{ form.sSettlement.value.latestPricingDate }}</cnd-form-item>
                </div>
              </div>
            </el-row>
          </el-row>

          <el-row class="ml-20">
            <div class="weight-size">八、需方指定送达地址及联系方式：</div>
            <div class="ml-20">
              <cnd-form-item label="收件人" prop="sAddressee">
                <el-input v-model="form.sAddressee" input-align="right" placeholder="请输入" />
              </cnd-form-item>

              <cnd-form-item
                class="label-whites"
                label="与需方的关系"
                prop="sNeedSideRelationshipPosition"
              >
                <el-input
                  v-model="form.sNeedSideRelationshipPosition"
                  input-align="right"
                  placeholder="请输入"
                />
              </cnd-form-item>

              <cnd-form-item label="邮递地址" prop="sPostalAddress">
                <el-input v-model="form.sPostalAddress" input-align="right" placeholder="请输入" />
              </cnd-form-item>

              <cnd-form-item label="电话/手机号码" prop="sPhoneNumber">
                <el-input v-model="form.sPhoneNumber" input-align="right" placeholder="请输入" />
              </cnd-form-item>

              <cnd-form-item label="电子邮箱" prop="sEmail">
                <el-input v-model="form.sEmail" input-align="right" placeholder="请输入" />
              </cnd-form-item>

              <cnd-form-item label="传真" prop="sFax">
                <el-input v-model="form.sFax" input-align="right" placeholder="请输入" />
              </cnd-form-item>
            </div>
          </el-row>

          <el-row class="ml-20">
            <div class="weight-size">九、供方指定邮箱：</div>
            <div class="ml-20">
              <cnd-form-item label="指定邮箱" prop="sAppointEmail">
                <el-input v-model="form.sAppointEmail" input-align="right" placeholder="请输入" />
              </cnd-form-item>
            </div>
          </el-row>
          <el-row class="ml-20">
            <div class="weight-size">十、其他</div>
            <div class="ml-20">
              <cnd-form-item label="关联公司" prop="sRelatedCompanies">
                <el-input v-model="form.sRelatedCompanies" input-align="right" placeholder="请输入" />
              </cnd-form-item>
            </div>
          </el-row>

          <el-row class="ml-20">
            <div class="weight-size">一般条款</div>
            <div class="ml-20">
              <div>5.4、货物和账务核对：供方于每月{供方于每月几日前}日前通过合同约定的送达方式或其他方式将库存货物数量及结算单等账目明细清单发送给需方，需方应在每月{需方应在每月几日前}日前审核完毕，若需方未于每月{需方未于每月几日前}日前提出书面异议的，则视为库存货物数量及结算单等账目明细清单无误。</div>
              <div v-if="form.sCommonlyClause.level1 != ''">
                <el-radio-group v-model="form.sCommonlyClause.level1">
                  <div class="disabled-color radio-box">
                    <el-radio :label="1" style="color: #000">
                      <div class="radio-span">
                        <div class="disabled-color-bg" />5.5、对于供方及供方关联公司与需方签订的全部合同项下需方所付的任意款项，需方同意供方及供方关联公司有权按照合同履约情况自行分配回款金额并自行决定每笔款项的用途和冲抵顺序，包括但不限于用于冲抵保证金、追加保证金、
                        货款、超期结算费
                      </div>
                    </el-radio>
                  </div>
                </el-radio-group>
              </div>
              <cnd-form-item label="供方于每月几日前" class="label-whites" prop="premium">
                <el-input
                  v-model="
                    form.sCommonlyClause.value.supplierDeadlineBeforeEndOfMonth
                  "
                  input-align="right"
                  placeholder="请输入"
                />
              </cnd-form-item>

              <cnd-form-item label="需方应在每月几日前" class="label-whites" prop="premium">
                <el-input
                  v-model="
                    form.sCommonlyClause.value.buyerDeadlineBeforeEndOfMonth
                  "
                  input-align="right"
                  placeholder="请输入"
                />
              </cnd-form-item>
              <cnd-form-item label="需方未于每月几日前" class="label-whites" prop="premium">
                <el-input
                  v-model="
                    form.sCommonlyClause.value
                      .buyerMissedDeadlineBeforeEndOfMonth
                  "
                  input-align="right"
                  placeholder="请输入"
                />
              </cnd-form-item>
            </div>
          </el-row>

          <!-- <el-row class="ml-20">
            <div class="weight-size">需方信息</div>
            <div class="ml-20">
              <cnd-form-item label="法定/授权代表人" class="label-whites" prop="premium">
                <el-input
                  v-model="form.sNeedSideRepresentative"
                  input-align="right"
                  placeholder="请输入"
                />
              </cnd-form-item>
              <cnd-form-item label="联系电话" prop="sNeedSidePhoneNumber">
                <el-input
                  v-model="form.sNeedSidePhoneNumber"
                  input-align="right"
                  placeholder="请输入"
                />
              </cnd-form-item>
              <cnd-form-item label="开户银行" prop="sNeedSideBankOfDeposit">
                <el-input
                  v-model="form.sNeedSideBankOfDeposit"
                  input-align="right"
                  placeholder="请输入"
                />
              </cnd-form-item>
              <cnd-form-item label="帐号" prop="sNeedSideAccounts">
                <el-input v-model="form.sNeedSideAccounts" input-align="right" placeholder="请输入" />
              </cnd-form-item>
            </div>
          </el-row>
          <el-row class="ml-20">
            <div class="weight-size">供方信息</div>
            <div class="ml-20">
              <cnd-form-item label="法定/授权代表人" class="label-whites" prop="premium">
                <el-input
                  v-model="form.sSupplySideRepresentative"
                  input-align="right"
                  placeholder="请输入"
                />
              </cnd-form-item>
              <cnd-form-item label="联系电话" prop="premium">
                <el-input
                  v-model="form.sSupplySidePhoneNumber"
                  input-align="right"
                  placeholder="请输入"
                />
              </cnd-form-item>
              <cnd-form-item label="开户银行" prop="premium">
                <el-input
                  v-model="form.sSupplySideBankOfDeposit"
                  input-align="right"
                  placeholder="请输入"
                />
              </cnd-form-item>
              <cnd-form-item label="帐号" prop="premium">
                <el-input v-model="form.sSupplySideAccounts" input-align="right" placeholder="请输入" />
              </cnd-form-item>
            </div>
          </el-row>-->
        </cnd-form-card>
      </cnd-form-card-list>
    </el-form>
  </div>
</template>

<script>
import { getSettlementRateDetail } from '@/api/preContract/preContract'

import { getPreContractDetail } from '@/api/preContract/aggregateMethodContract'

import { getDictet } from '@/api/logistics/saleDelivery/saleorder'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import { getCnDitc } from '@/utils/common'
import { SteelFormat } from 'cnd-horizon-utils'

export default {
  name: 'ClauseInformation',
  components: {
    steelTradeAggrid
  },
  data() {
    return {
      selectList: {
        contractList: [
          {
            name: '显示',
            labelName: '1'
          },
          {
            name: '不显示',
            labelName: '0'
          }
        ],
        // 交货地点
        deliver: [
          {
            name: '交货仓库/码头为：{交货仓库/码头}，需方自提/供方代办运输。',
            labelName: '1'
          },
          {
            name: '交货地点：{交货地点}，供方代办运输。',
            labelName: '2'
          }
        ],
        // 保险
        insurance: [
          {
            name: '不购买保险。',
            labelName: '1'
          },
          {
            name: '保险由供方负责投保，相应的保费预计{保费}元/吨，由需方承担。',
            labelName: '2'
          }
        ],
        // 保险金
        insuranceBenefit: [
          {
            name: '暂定价的{保证金比例}；',
            labelName: '1'
          },
          {
            name: '具体金额{保证金金额}，支付时间：{支付时间}。',
            labelName: '2'
          },
          {
            name: '2.保证金追加：合同签约后，若上海期货交易所{合约}合约期货价格上涨超{上涨比例}（以供方认定为准），需方应追加未点价部分相应涨价金额的保证金,追加保证金比例为涨价金额的{追加保证金比例}；',
            labelName: '3'
          },
          {
            name: '3. 需方应在点价后来款提货，保证金只能作为最后一笔货款冲抵。',
            labelName: '4'
          },
          {
            name: '4.1 若客户先提货后点价，需方应按照供方确认的暂定单价{暂定单价比例}付清提货部分货款后提货。且不可使用前述保证金，并且需要在提货后{提货天数}天内完成点价。',
            labelName: '5'
          },
          {
            name: '4.2 若客户先提货后点价，需方应按照供方确认的暂定单价＋{暂定单价加价金额}元/吨，付清提货部分货款后提货。且不可使用前述保证金，并且需要在提货后{提货天数}天内完成点价。',
            labelName: '6'
          }
        ],
        // 价格条款
        priceClause: [
          {
            name: '本合同价格为现汇锁定一票制价格。',
            labelName: '1'
          },
          {
            name: '本合同价格为银行承兑汇票锁定一票制价格。',
            labelName: '2'
          },
          {
            name: '本合同价格为{付款方式}锁定一票制价格。',
            labelName: '3'
          },
          {
            name: '本合同价格为暂定价，若有运杂费按实结算，最终以供方结算单为准。',
            labelName: '4'
          },
          {
            name: '本合同价格为暂定价，结算时参照网价/{参考网价} 钢厂售价下浮 {销售下浮金额}元进行，最终以供方结算单为准。',
            labelName: '5'
          }
        ],
        // 结算期
        result: [
          {
            name: '需方应于{付款日期}之日起{几天内}天内结算。',
            labelName: '1'
          },
          {
            name: '需方应于货物到交货仓库/码头起{几天内}天内结算。',
            labelName: '2'
          },
          {
            name: '需方应于{付款日期}前付清全部款项。',
            labelName: '3'
          },

          {
            name: '需方应于{付款日期}之日起{几天内}天内以{付款方式}等方式付清{付款比例}%款项， {付款日期2}之日起{几天内2}天内以{付款方式2}等方式付清剩余款项后提清货物（允许分批付款提货），否则视为需方违约，相关违约责任参照商务条款第七条及相关一般条款执行。如果付款时间及金额有调整，以供方确认为准。',
            labelName: '4'
          },
          {
            name: '需方应于{几天内}天内以{付款方式}等方式付清全部货款及费用后提清货物（允许分批付款提货），否则视为需方违约，相关违约责任参照商务条款第七条及相关一般条款执行。',
            labelName: '5'
          }
        ],
        shopDiscount: [
          {
            name: '合同签订后{提前天数}日',
            labelName: '1'
          },
          {
            name: '货物到港后{提前天数}天',
            labelName: '2'
          },
          {
            // 选中这一个，下面还可以选中
            name: '货物进仓后{提前天数}天',
            labelName: '3'
          }
        ],
        shopDiscountSon: [
          {
            name: '标准一：提前提货天数×优惠费率{标准一费率}/日×（总价-保证金）{标准二提前天数止})×{标准二费率}×（总价-保证金），提前提货天数：合同签约后第{标准一提前天数止}-{标准二提前天数止}日至实际提货日的天数间隔；标准二：提前提货天数×优惠费率{标准二费率}/日×（总价-保证金），提前提货天数：实际提货日至合同签约后第{标准一提前天数止}日的天数间隔；',
            labelName: '1'
          },
          {
            name: '标准一： {标准二提前天数止}×优惠费率{标准二费率}/日×（总价-保证金）；标准二：提前付款天数×优惠费率{标准二费率}/日×提前付款金额（不含保证金），提前付款天数：合同签约后第{标准一提前天数止}日至实际付款日的天数间隔；',
            labelName: '2'
          },
          {
            name: '标准一：提前付款天数×优惠费率{标准一费率}/日×提前付款金额+{标准二提前天数止}×{标准二费率}×提前付款金额，提前付款天数：实际付款日至合同签约后第{标准一提前天数止}-{标准二提前天数止}日的天数间隔；标准二：提前付款天数×优惠费率{标准二费率}/日×提前付款金额，提前付款天数：实际付款日至合同签约后第{标准一提前天数止}日的天数间隔；',
            labelName: '3'
          },
          {
            name: '标准一： {标准二提前天数止}×优惠费率{标准二费率}/日×提前付款金额（不含保证金）； 标准二：提前付款天数×优惠费率{标准二费率}/日×提前付款金额（不含保证金），提前付款天数：实际付款日至合同签约后第{标准一提前天数止}日的天数间隔；',
            labelName: '4'
          }
        ],
        // 结算
        settleAccount: [
          {
            name: '按吨位加价（固定日期后结算）',
            labelName: '1'
          },
          {
            name: '分批收保证金多个固定日期分批加价',
            labelName: '2'
          },
          {
            name: '按吨位加价（货后结算）',
            labelName: '3'
          },
          {
            name: '后结算固定加价销售',
            labelName: '4'
          },
          {
            name: '点价结算',
            labelName: '5'
          }
        ]
      },
      headerCount: null,
      footerCount: null,
      searchInfo: null,
      getGridApi: () => {
        return this.$refs.aggrid.gridApi
      },
      activeCollapseName: ['2'],
      options: {
        'msb.deposit.pay.type': '',
        'esc.settle.interest.start': '',
        'base.yes-no': '',
        'msb.return.interest.type': ''
      },
      form: {
        sFreight: {
          level1: '1'
        },
        sDeliveryLocation: {
          level1: '1'
        },
        sInsurance: {
          level1: '1'
        },
        sBond: {
          level1: '1',
          value: {}
        },
        sPriceTerms: {
          level1: '1',
          value: {
            paymentMethod: '',
            referenceOnlinePrice: '',
            salesDecreaseAmount: ''
          }
        },
        sSettlementPeriod: {
          level1: '1',
          value: {
            paymentDate: '',
            referenceOnlinePrice: '',
            salesDecreaseAmount: ''
          }
        },
        sSettlement: {
          level1: '1',
          value: {}
        },
        sCashDiscount: {
          level1: '1',
          value: {
            paymentDate: '',
            referenceOnlinePrice: '',
            salesDecreaseAmount: ''
          }
        },
        sCommercialDiscount: {
          level1: '1',
          level2: '',
          value: {}
        },
        sCommonlyClause: {
          level1: '',
          value: {}
        },
        sNeedSideRepresentative: '',
        sEarnestMoneyPayWay: undefined,
        sEarnestMoneyPercentage: undefined,
        sEarnestMoneyAmount: undefined,
        sInterestStartWay: undefined,
        sDaysOfSettlement: undefined,
        sSettlementInterval: undefined,
        sCashRebate: undefined,
        sCashRebateWay: undefined,
        sCashRebatePercentage: undefined,
        sCashTonRebate: undefined,
        sAcceptanceDiscount: undefined,
        sAcceptanceDiscountWay: undefined,
        sAcceptanceDiscountPercentage: undefined,
        sAcceptanceTonDiscount: undefined
      },
      rowData: [],
      lastNum: 0,
      preNum: 0,
      settlementColumnDefs: [
        // 计息项目
        {
          field: 'sInterestProject',
          headerName: '计息项目',
          pinned: 'left',
          cellStyle: { textAlign: 'left' },
          valueGetter: (params) => {
            return getCnDitc(
              params,
              this.interestProjectList,
              'sInterestProject'
            )
          }
        },
        // 起始天数
        {
          field: 'sDateStart',
          headerName: '提前/逾期天数（起）',
          pinned: 'left',
          cellStyle: { textAlign: 'left' },
          valueFormatter: (params) => {
            return SteelFormat.formatThousandthSign(params.value, 0)
          }
        },
        // 截止天数
        {
          field: 'sDateEnd',
          headerName: '提前/逾期天数（止）',
          pinned: 'left',
          cellStyle: { textAlign: 'left' },
          valueFormatter: (params) => {
            return SteelFormat.formatThousandthSign(params.value, 0)
          }
        },
        // 是否无限期
        {
          field: 'sIndefinite',
          headerName: '是否无限期',
          pinned: 'left',
          cellStyle: { textAlign: 'left' },
          valueGetter: (params) => {
            return getCnDitc(params, this.yesOrNoType, 'sIndefinite')
          }
        },
        // 费率
        {
          field: 'sRate',
          headerName: '费率',
          pinned: 'left',
          cellStyle: { textAlign: 'left' },
          valueFormatter: (params) => {
            const item = SteelFormat.formatThousandthSign(
              parseFloat((params.value * 100).toFixed(8)),
              2
            )
            return item + '%'
          }
        }
        // 加价
        // {
        //   field: 'sPriceRaise',
        //   headerName: '加价',
        //   pinned: 'left',
        //   cellStyle: { textAlign: 'left' },
        //   valueFormatter: params => {
        //     return SteelFormat.formatThousandthSign(params.value, 2)
        //   }
        // }
      ],
      yesOrNoType: [
        {
          sCodeValue: '1',
          sCodeName: '是',
          sSort: '0',
          sFilter: null,
          sisEnabled: '1',
          sLanguage: 'zh_CN'
        },
        {
          sCodeValue: '0',
          sCodeName: '否',
          sSort: '0',
          sFilter: null,
          sisEnabled: '1',
          sLanguage: 'zh_CN'
        }
      ],
      interestProjectList: [
        {
          sCodeValue: '10',
          sCodeName: '保底加价',
          sSort: '0',
          sFilter: null,
          sisEnabled: '1',
          sLanguage: 'zh_CN'
        },
        {
          sCodeValue: '20',
          sCodeName: '利息',
          sSort: '0',
          sFilter: null,
          sisEnabled: '2',
          sLanguage: 'zh_CN'
        },
        {
          sCodeValue: '30',
          sCodeName: '违约金',
          sSort: '0',
          sFilter: null,
          sisEnabled: '3',
          sLanguage: 'zh_CN'
        }
      ],
      fourArr: []
    }
  },
  computed: {
    transportMode() {
      return (this.options['msb.transport.mode'] && this.options['msb.transport.mode'].length) && this.options['msb.transport.mode'].find(item => item.sCodeValue === this.form.sDeliveryLocation.transportMode) ? (this.options['msb.transport.mode'].find(item => item.sCodeValue === this.form.sDeliveryLocation.transportMode)).sCodeName : ''
    },
    logisticsFeesType() {
      let str = ''
      if (this.form.sDeliveryLocation && this.form.sDeliveryLocation.logisticsFeesType && this.form.sDeliveryLocation.logisticsFeesType.length) {
        for (let index = 0; index < this.form.sDeliveryLocation.logisticsFeesType.length; index++) {
          const element = this.form.sDeliveryLocation.logisticsFeesType[index]
          str += this.options['msb.cost.bearing'].find(item => item.sCodeValue === element) ? this.options['msb.cost.bearing'].find(item => item.sCodeValue === element).sCodeName + '、' : ''
        }
      }
      return str.slice(0, -1)
    }
  },
  created() {
    this.getDetail()
    getDictet([
      'msb.deposit.pay.type',
      'esc.settle.interest.start',
      'base.yes-no',
      'msb.return.interest.type',
      'msb.transport.mode',
      'msb.cost.bearing'
    ])
      .then((result) => {
        this.options['msb.deposit.pay.type'] = result.data[0].dicts
        this.options['esc.settle.interest.start'] = result.data[1].dicts
        this.options['base.yes-no'] = result.data[2].dicts
        this.options['msb.return.interest.type'] = result.data[3].dicts
        this.options['msb.transport.mode'] = result.data[4].dicts
        this.options['msb.cost.bearing'] = result.data[5].dicts
      })
      .catch(() => {})
  },
  methods: {
    getValue(value) {
      return this.form.sBond.level1.includes(value)
    },
    fourCheckbox(str) {
      this.fourArr = []
      this.fourArr = str.split(',').map((item) => item.trim())

      this.form.sCashDiscount.value = {}
      // 给第二段赋值
      this.form.sCashDiscount.level1 = this.fourArr[0]
      this.form.sCashDiscount.level2 = this.fourArr[1]
    },
    // parseJsonFields(obj) {
    //   for (const key in obj) {
    //     if (obj && typeof obj === 'object') {
    //       if (obj.prototype.hasOwnProperty.call(obj, key)) {
    //         if (
    //           typeof obj[key] === 'string' &&
    //           obj[key].startsWith('{') &&
    //           obj[key].endsWith('}')
    //         ) {
    //           try {
    //             obj[key] = JSON.parse(obj[key])
    //           } catch (e) {
    //             console.error(`Failed to parse JSON for key: ${key}`, e)
    //           }
    //         }
    //       }
    //     }
    //   }
    //   return obj
    // },
    getDetail() {
      getPreContractDetail({
        id: this.$route.query.Id
      }).then((res) => {
        console.log(
          res.data.msbPreTotalContractClause,
          'res.data.msbPreTotalContractClause'
        )
        const clause = res.data.msbPreTotalContractClause

        this.form = { ...clause }
        this.form.sBond = JSON.parse(clause.sBond)
        this.form.sCashDiscount = JSON.parse(clause.sCashDiscount)
        this.form.sCommercialDiscount = JSON.parse(clause.sCommercialDiscount)
        this.form.sCommonlyClause = JSON.parse(clause.sCommonlyClause)
        this.form.sDeliveryLocation = JSON.parse(clause.sDeliveryLocation)
        this.form.sDeliveryLocationWarehouse = JSON.parse(clause.sDeliveryLocationWarehouse)
        this.form.sFreight = JSON.parse(clause.sFreight)
        this.form.sInsurance = JSON.parse(clause.sInsurance)
        this.form.sPriceTerms = JSON.parse(clause.sPriceTerms)
        this.form.sSettlement = JSON.parse(clause.sSettlement)
        this.form.sSettlementPeriod = JSON.parse(clause.sSettlementPeriod)
        this.form.sSettlement = JSON.parse(clause.sSettlement)
        this.form.sInsurance = JSON.parse(clause.sInsurance)
      })
    },

    loadSettlementData(pagination) {
      return new Promise((resolve, reject) => {
        this.searchInfo = { sContractId: this.$route.query.Id }
        getSettlementRateDetail(
          { sContractId: this.$route.query.Id },
          pagination
        )
          .then((res) => {
            this.rowData = res.data.content.map((item) => {
              item._selected = false
              item._selectedKeys = []
              item.details = []
              return item
            })
            resolve(res.data)
          })
          .catch(() => {
            reject([])
          })
      })
    },
    msbPreTotalContractWarehouse(array) {
      let arr = []
      array.forEach((e) => {
        if (e.warehouseName) {
          arr.push(e.warehouseName)
        }
      })
      arr = arr.join(',')
      return arr
    }
  }
}
</script>

<style scoped lang="scss">
.ml-20 {
  margin-left: 20px;
}
.weight-size {
  font-weight: 800;
  padding-bottom: 5px;
}
.radio-box {
  ::v-deep .el-radio:last-child {
    color: #000;
    display: flex;
    // padding-top: 10px;
    // align-items: center;
  }
  ::v-deep .el-radio__input {
    margin-top: 4px;
  }
  .radio-span {
    line-height: 24px;
    overflow-wrap: break-word;
    overflow: hidden;
    white-space: break-spaces;
  }
}
.disabled-color {
  ::v-deep .el-radio__input.is-disabled + span.el-radio__label {
    color: #000 !important;
  }
  ::v-deep .el-radio__input.is-disabled.is-checked .el-radio__inner::after {
    width: 6px;
    height: 6px;
    background-color: #777777 !important;
  }
}
.d-flex {
  display: flex;
}
.cash-checkbox {
  margin-top: 6px;
  width: 14px;
  min-width: 14px;
  height: 14px;
  margin-right: 6px;
  border-radius: 50%;
  border: 1px solid #e4e7ed;
  position: relative;
}
.cash-checkbox::after {
  content: '';
  position: absolute;
  width: 6px;
  height: 6px;
  top: 3px;
  left: 3px;
  border-radius: 50px;
  background: #777777;
}
.cash-pt {
  padding-left: 18px;
}
.disabled-color-bg {
  width: 6px;
  height: 6px;
  position: absolute;
  border-radius: 20px;
  background: #777777;
  left: 4px;
  top: 8px;
}
</style>
