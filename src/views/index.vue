<template>
  <el-carousel
    id="carousel"
    class="carousel-content"
    autoplay
    indicator-position="none"
  >
    <el-carousel-item v-for="(item,index) in imgList" :key="item">
      <div class="carousel-img" :class="carouselImgClass" :style="`background-image: url('${item.imgUrl}');`">
        <div v-if="index === 0" class="carousel-title carousel-title-fir">
          <p>建发供应链钢贸管家</p>
          <span />
        </div>
        <div v-if="index === 1" class="carousel-title carousel-title-sec">
          <p>新起点 新征程 心服务</p>
          <span />
        </div>
        <div v-if="index === 2" class="carousel-title carousel-title-thi">
          <div class="title-thi">
            <p>整合资源 创造增值</p>
            <span />
          </div>
        </div>
      </div>
    </el-carousel-item>
  </el-carousel>
</template>

<script>
export default {
  data() {
    return {
      imgList: [
        { imgUrl: require('@/assets/layout/main_bg_sec.png') },
        { imgUrl: require('@/assets/layout/main_bg_fir.png') },
        { imgUrl: require('@/assets/layout/main_bg_thi.png') }
      ],
      carouselImgClass: null
    }
  },
  created() {
    window.addEventListener('resize', this.getHeight)
  },
  mounted() {
    this.$nextTick(() => { this.getHeight() })
  },
  destroyed() {
    window.removeEventListener('resize', this.getHeight)
  },
  methods: {
    // 加个节流
    getHeight() {
      const { offsetWidth, offsetHeight } = document.getElementById('carousel')
      this.carouselImgClass = (offsetWidth / offsetHeight) > (1920 / 1000) ? 'carousel-width' : null
    }
  }
}
</script>
<style lang="scss" >
.app-main{
  display: flex;
  flex-direction: column;
}
.carousel-content{
  display: flex;
  flex-direction: column;
  flex: 1 1 auto;
  height: 100vh;
  .el-carousel__container{
    flex: 1 1 auto;
    height: 100vh;
  }
  .carousel-img{
    width: 100%;
    height: 100%;
    background-size:auto 100%;
    background-position:center top;
    background-repeat:no-repeat;
    &.carousel-width{
      background-size:100% auto;
    }
    .carousel-title{
      position: absolute;
      p{
        font-size: 38px;
        margin: 0;
        color: #fff;
        text-shadow: 2px 2px 2px rgba(0,0,0,.5);
      }
      span{
        display: inline-block;
        width:  calc(100% - 70px);
        height: 5px;
        margin-top: 10px;
        border-left:70px solid rgb(231, 17, 15);
        background-color:rgb(53, 106, 161) ;
      }
    }
    .carousel-title-fir{
      right: 10%;
      bottom: 20%;
    }
    .carousel-title-sec{
      top: 40%;
      left: 10%;
    }
    .carousel-title-thi{
      display: flex;
      flex-direction:row-reverse;
      align-items: center;
      width: 100%;
      height: 120px;
      bottom: 30%;
      background-image: linear-gradient(to right,rgba(34, 93, 161,0) 0%, rgb(34, 93, 161) 50%);
      .title-thi{
        margin-right: 8%;
      }
    }
  }
}
</style>
