<template>
  <div class="page-container">
    <cnd-dialog :title="$t('grid.tabs.basicInformation')" :visible="true" @close="onClose">
      <template slot="leftBtn">
        <!-- <el-button type="primary" size="mini" plain icon="el-icon-arrow-left" @click="onClose">返回列表</el-button> -->
        <!-- {{ sSheetStatus }} -->
        <!-- {{ id }} -->
        <!-- v-has:esc_customer_contact_detail_save -->
        <el-button
          v-has:esc_customer_contact_detail_save
          type="primary"
          size="mini"
          :disabled="isBusinessDisabled('save', sSheetStatus)&& id"
          @click="save"
        >{{ $t('btns.save') }}</el-button>
        <el-button
          v-has:esc_customer_contact_detail_submit
          type="primary"
          size="mini"
          :disabled="isBusinessDisabled('submit', sSheetStatus)|| !id"
          @click="submit"
        >{{ $t('grid.others.submit') }}</el-button>
        <el-button
          v-has:esc_customer_contact_detail_delete
          type="danger"
          size="mini"
          :disabled="isBusinessDisabled('remove', sSheetStatus)|| !id"
          @click="remove"
        >{{ $t('btns.delete') }}</el-button>
        <el-button
          v-has:esc_customer_contact_detail_CXSQ
          type="warning"
          size="mini"
          :disabled="isBusinessDisabled('withdraw', sSheetStatus)|| !id"
          @click="onRevoke"
        >{{ $t('grid.others.withdrawRequest') }}</el-button>
        <el-button
          v-has:esc_customer_contact_detail_file
          type="primary"
          size="mini"
          :disabled="isBusinessDisabled('annex', sSheetStatus)|| !id"
          @click="dialogVisible.annex = true"
        >{{ $t('btns.attachmentManagement') }}</el-button>
        <el-button
          v-has:esc_customer_contact_detail_approval
          type="primary"
          size="mini"
          :disabled="isBusinessDisabled('approval', sSheetStatus)||!id"
          @click="dialogVisible.approval = true"
        >{{ $t('grid.others.approvalStatus') }}</el-button>
      </template>
      <template slot="content">
        <el-form
          ref="form"
          class="el-form-w100"
          label-width="90px"
          inline
          :model="form"
          :rules="rules"
          size="small"
          :disabled="isBusinessDisabled('save', sSheetStatus)"
        >
          <cnd-form-card-list :active-panel="activeCollapseName">
            <cnd-form-card class="mb-10" :title="$t('grid.tabs.basicInformation')" name="1">
              <el-row>
                <cnd-form-item
                  label="单号"
                  prop="sExtend6"
                >
                  <el-input v-model="form.sExtend6" disabled />
                </cnd-form-item>
                <cnd-form-item
                  :label="$t('grid.others.customer')"
                  prop="vCustomerName"
                  :error-msg="rules.vCustomerName[0].message"
                >
                  <horizon-search-select
                    v-model="form.vCustomerName"
                    type="customer"
                    @change="handleChangeSelect($event, 'sCustomerId')"
                  />
                </cnd-form-item>
                <!-- <cnd-form-item
                  :label="$t('grid.title.accountingGroup')"
                  prop="vCheckGroupName"
                  :error-msg="rules.vCheckGroupName[0].message"
                >
                  <horizon-search-select
                    v-model="form.vCheckGroupName"
                    type="cost"
                    @change="handleChangeSelect($event, 'sCheckGroupId')"
                  />
                </cnd-form-item> -->
                <cnd-form-item
                  :label="$t('grid.others.effectiveDate')"
                  prop="sStartDate"
                  :error-msg="rules.sStartDate[0].message"
                >
                  <el-date-picker
                    v-model="form.sStartDate"
                    type="date"
                    value-format="yyyy-MM-ddTHH:mm:ss"
                    :picker-options="{
                      disabledDate(time) {
                        return (!form.sInvalidDate ? false : (time.getTime() > new Date(form.sInvalidDate).getTime())) || time.getTime() <= Date.now() - 86400000 }
                    }"
                  />
                </cnd-form-item>
                <cnd-form-item
                  :label="$t('grid.columns.expirationDate')"
                  prop="sInvalidDate"
                  :error-msg="rules.sInvalidDate[0].message"
                >
                  <el-date-picker
                    v-model="form.sInvalidDate"
                    type="date"
                    value-format="yyyy-MM-ddTHH:mm:ss"
                    :picker-options="{
                      disabledDate(time) {
                        return (!form.sStartDate ? false : (time.getTime() < new Date(form.sStartDate).getTime() - 86400000)) || time.getTime() <= Date.now() - 86400000
                      }
                    }"
                  />
                </cnd-form-item>
              </el-row>
            </cnd-form-card>
            <cnd-form-card class="mb-10" :title="$t('grid.others.informationOfTheConsignor')" name="2">
              <el-row>
                <cnd-form-item
                  :label="$t('grid.others.consigneeSName')"
                  prop="sContact"
                  :error-msg="rules.sContact[0].message"
                >
                  <el-input v-model="form.sContact" />
                </cnd-form-item>
                <cnd-form-item
                  :label="$t('grid.others.idCardNumber')"
                  prop="sCertificateNo"
                  :error-msg="rules.sCertificateNo[0].message"
                >
                  <el-input v-model="form.sCertificateNo" />
                </cnd-form-item>
                <cnd-form-item
                  label="车号"
                  prop="sExtend3"
                  :error-msg="rules.sExtend3[0].message"
                >
                  <el-input v-model="form.sExtend3" />
                </cnd-form-item>
                <cnd-form-item
                  :label="$t('grid.others.phoneNumber')"
                  prop="sTel"
                  :error-msg="rules.sTel[0].message"
                >
                  <el-input v-model="form.sTel" />
                </cnd-form-item>
              </el-row>
            </cnd-form-card>
            <cnd-form-card class="mb-10" :title="$t('grid.tabs.systemInformation')" name="3">
              <el-row>
                <cnd-form-item :label="$t('grid.title.createdBy')" prop="vCreatorName">
                  <el-input v-model="form.vCreatorName" disabled clearable />
                </cnd-form-item>
                <cnd-form-item :label="$t('grid.title.createdAt')" prop="sCreateTime">
                  <el-date-picker
                    v-model="form.sCreateTime"
                    format="yyyy-MM-dd HH:mm:ss"
                    type="date"
                    disabled
                    :placeholder="$t('grid.title.createdAt')"
                  />
                </cnd-form-item>
                <cnd-form-item :label="$t('grid.title.modifiedBy')" prop="vModifierName">
                  <el-input v-model="form.vModifierName" disabled clearable />
                </cnd-form-item>
                <cnd-form-item :label="$t('grid.title.modifiedAt')" prop="sModifyTime">
                  <el-date-picker
                    v-model="form.sModifyTime"
                    format="yyyy-MM-dd HH:mm:ss"
                    type="date"
                    disabled
                    :placeholder="$t('grid.title.modifiedAt')"
                  />
                </cnd-form-item>
              </el-row>
              <el-row>
                <cnd-form-item :label="$t('grid.title.status')" prop="sSheetStatus">
                  <el-select v-model="form.sSheetStatus" disabled>
                    <el-option
                      v-for="item in statusList"
                      :key="item.sCodeValue"
                      :label="item.sCodeName"
                      :value="item.sCodeValue"
                    />
                  </el-select>
                </cnd-form-item>
                <cnd-form-item :label="$t('grid.others.effectiveTime')">
                  <el-date-picker
                    v-model="form.sRatifyDate"
                    type="date"
                    disabled
                    :placeholder="$t('grid.others.effectiveTime')"
                  />
                </cnd-form-item>
                <!-- <cnd-form-item :label="$t('grid.title.versionNumber')">
                  <el-input v-model="form.sSheetVersion" disabled />
                </cnd-form-item> -->
              </el-row>
            </cnd-form-card>
          </cnd-form-card-list>
        </el-form>
        <steelAnnexDialog
          :visible="dialogVisible.annex"
          append-to-body
          :biz-id="id"
          :disabled-btn="{ scan: (isBusinessDisabled('save', sSheetStatus) || uploadLimit), del: isBusinessDisabled('save', sSheetStatus) }"
          :allowed-file-types="['pdf']"
          limit-len="1"
          @onSelect="dialogVisible.annex = false"
        />
        <horizon-approval-dialog
          :id="id"
          :solt-btn="true"
          :visible="dialogVisible.approval"
          @handleClose="dialogVisible.approval = false"
        />
        <checkDialog
          :visible="dialogVisible.check"
          :account-id="accountId"
          @close="dialogVisible.check = false"
          @success="checkSuccess"
        />
      </template>
    </cnd-dialog>
  </div>
</template>

<script>
import checkDialog from '@/components/checkEJQDialog/index'
import businessMixin from '@/utils/businessMixin'
import { DictUtil, MessageUtil } from 'cnd-horizon-utils'
import { Validate } from 'cnd-utils'
import { validCarNo } from '@/utils/common'
import steelAnnexDialog from '@/components/steelAnnexDialog'
import {
  addStockDeliverer,
  getStockDelivererDetail,
  submitStockDeliverer,
  removesStockDeliverer,
  stockDelivererWithdraw
} from '@/api/logistics/affairManage'

export default {
  name: 'ConsignorManageDetail',
  components: {
    checkDialog,
    steelAnnexDialog
  },
  filters: {
    formatPaymentType(value, statusList) {
      const status = statusList.filter(item => item.sCodeValue === value)
      return status.length ? status[0].sCodeName : value
    }
  },
  mixins: [businessMixin],
  props: {
    // visible: {
    //   type: Boolean,
    //   default: false
    // },
    // id: {
    //   type: String,
    //   default: null
    // }
  },
  data() {
    const validateCertificateId = (rule, value, cb) => {
      if (!value) {
        return cb()
      }
      if (!Validate.validIdCard(value)) {
        return cb(new Error(this.$t('grid.others.pleaseEnterTheCorrectIdNumber')))
      }
      cb()
    }
    const validateDeliveryPeopelTel = (rule, value, cb) => {
      if (!value) {
        return cb(new Error(this.$t('grid.others.pleaseEnterThePhoneNumber')))
      }
      if (!Validate.validPhone(value) && !Validate.validMobile(value)) {
        return cb(new Error(this.$t('grid.others.pleaseEnterTheCorrectPhoneNumber')))
      }
      cb()
    }
    const validateDeliverysCarNo = (rule, value, cb) => {
      if (!value) {
        return cb()
      }
      if (!validCarNo(value)) {
        return cb(new Error(this.$t('grid.others.pleaseEnterTheCorrectCarNumber')))
      }
      cb()
    }
    return {
      activeCollapseName: ['1', '2', '3'],
      form: {
        vCustomerName: undefined,
        sCustomerId: undefined,
        vCheckGroupName: undefined,
        sCheckGroupId: undefined,
        sStartDate: undefined,
        sInvalidDate: undefined,
        sContact: undefined,
        sCertificateNo: undefined,
        sTel: undefined,
        sExtend3: undefined
      },
      rules: {
        vCustomerName: [
          { required: true, message: this.$t('grid.others.pleaseSelectTheDepartment'), trigger: 'change' }
        ],
        vCheckGroupName: [
          { required: true, message: this.$t('grid.others.pleaseSelectTheCustomer'), trigger: 'change' }
        ],
        sStartDate: [
          { required: true, message: this.$t('grid.others.pleaseSelectTheEffectiveDateTag'), trigger: 'change' }
        ],
        sInvalidDate: [
          { required: true, message: this.$t('grid.others.pleaseSelectTheExpirationDateTag'), trigger: 'change' }
        ],
        sContact: [
          { required: true, message: this.$t('grid.others.pleaseEnterTheNameOfThePicker'), trigger: 'change' }
        ],
        sCertificateNo: [
          { required: false, validator: validateCertificateId, trigger: 'change', message: this.$t('grid.others.pleaseEnterTheCorrectIdNumber') }
        ],
        sExtend3: [
          { required: false, validator: validateDeliverysCarNo, trigger: 'change', message: this.$t('grid.others.pleaseEnterTheCorrectCarNumber') }
        ],
        sTel: [
          { required: true, validator: validateDeliveryPeopelTel, message: this.$t('grid.others.pleaseEnterTheCorrectPhoneNumber'), trigger: 'change' }
        ]
      },
      statusList: [],
      dialogVisible: {
        approval: false,
        annex: false,
        check: false
      },
      id: '',
      accountId: null,
      sCustomerSignId: null,
      uploadLimit: false
    }
  },
  computed: {
    sSheetStatus() {
      return this.form.sSheetStatus
    }
  },
  mounted() {
    this.loadDict()
    this.loadDetail()
  },
  created() {
    this.param = this.$route.query
    this.id = this.param.id
  },
  methods: {
    loadDict() {
      DictUtil.getDict(['dev.common.sheet.status'], res => {
        this.statusList = res[0].dicts
      })
    },
    loadDetail() {
      if (this.id) {
        getStockDelivererDetail({
          sId: this.id
        }).then(res => {
          this.form = res.data
        })
      }
    },
    // 关闭弹窗
    // onClose() {
    //   this.$emit('onClose')
    // },
    save() {
      this.$refs.form.validate(valid => {
        const { sCertificateNo, sExtend3 } = this.form
        if (!sCertificateNo && !sExtend3) {
          return MessageUtil.error('车牌号和身份证号必填一项')
        }
        if (valid) {
          addStockDeliverer(
            Object.assign(
              this.form,
              { sInvalidDate: new Date(new Date(new Date(this.form.sInvalidDate).toLocaleDateString()).getTime() + 24 * 60 * 60 * 1000 - 1) },
              this.id ? { sId: this.id } : {}
            )
          ).then(res => {
            this.$message({
              showClose: true,
              message: this.$t('grid.others.longTermPickerDataSavedSuccessfully'),
              type: 'success'
            })
            if (!this.id) {
              this.$tabDelete(
                `/egl/ConsignorManageDetail/${res.data.sId}?id=${res.data.sId}&status=${res.data.sSheetStatus}&type=edit&name=${this.$t('grid.others.longTermPickerManagement')}【${res.data.sContact}】&activeId=${localStorage.getItem('menuId')}&random=${Math.random()}`
              )
            }
            this.loadDetail()
          })
        }
      })
    },
    onRevoke() {
      this.$confirm(this.$t('grid.tips.whetherToRevokeTheApplication'), this.$t('grid.others.prompt'), {
        confirmButtonText: this.$t('btns.confirm'),
        cancelButtonText: this.$t('btns.cancel'),
        type: 'warning'
      })
        .then(() => {
          stockDelivererWithdraw({
            sId: this.id
          }).then((res) => {
            this.loadDetail()
            this.$message.success(this.$t('grid.others.withdrawalRequestSuccessful'))
          })
        })
        .catch(() => { })
    },
    submit() {
      const { sCustomerSignId } = this.form
      if (sCustomerSignId) {
        this.$confirm('是否发起E建签回签？', this.$t('grid.others.prompt'), {
          distinguishCancelAndClose: true,
          confirmButtonText: '发起',
          cancelButtonText: '不发起',
          type: 'warning'
        }).then(() => {
          this.accountId = sCustomerSignId
          this.dialogVisible.check = true
        }).catch((action) => {
          // 点击了"不发起"按钮
          if (action === 'cancel') {
            this.sCustomerSignId = ''
            this.confirmSubmit()
          }
        })
      } else {
        this.sCustomerSignId = ''
        this.confirmSubmit()
      }
    },
    checkSuccess(payload) {
      this.sCustomerSignId = payload.ejqCustomerId
      this.dialogVisible.check = false
      this.confirmSubmit()
    },
    confirmSubmit() {
      submitStockDeliverer({
        sId: this.id,
        sCustomerSignId: this.sCustomerSignId
      }).then(() => {
        MessageUtil.success(this.$t('tips.submitSuccess'))
        this.loadDetail()
      })
    },
    remove() {
      this.$confirm(this.$t('grid.tips.isTheDeletionConfirmed'), this.$t('grid.others.prompt'), {
        confirmButtonText: this.$t('grid.others.yes'),
        cancelButtonText: this.$t('btns.no'),
        type: 'warning'
      }).then(() => {
        removesStockDeliverer([this.id]).then(res => {
          this.$message({
            showClose: true,
            message: this.$t('grid.tips.deletionSuccess'),
            type: 'success'
          })
          MessageUtil.success(this.$t('tips.deletedSuccessfully'))
          this.$tabDelete()
        })
      })
    },
    handleChange(e) {
      this.activeName = e
    },
    handleChangeSelect(val, key) {
      if (val) {
        this.form[key] = val.sId
      } else {
        this.form[key] = undefined
      }
    }
  }
}
</script>
