<template>
  <div>
    <cnd-dialog
      :title="$t('grid.tabs.basicInformation')"
      :visible="true"
      @close="handleClose"
    >
      <template slot="leftBtn">
        <!-- <el-button
          type="primary"
          size="mini"
          plain
          icon="el-icon-arrow-left"
          @click="handleClose"
        >返回列表</el-button> -->
        <el-button
          key="comm_triagreement_detail_revoke"
          v-has:esc_tsk_stock_receipt_detail_submit
          type="primary"
          size="mini"
          :disabled="isBusinessDisabled('submit',sSheetStatus) || $route.query.disabled === '1'"
          @click="subIntransit"
        >{{ $t('grid.others.submit') }}</el-button>
        <el-button
          key="comm_triagreement_list_delete"
          v-has:esc_tsk_stock_receipt_detail_delete
          type="danger"
          size="mini"
          :disabled="isBusinessDisabled('remove',sSheetStatus)"
          @click="delIntransit"
        >{{ $t('btns.delete') }}</el-button>
        <el-button
          key="comm_triagreement_detail_revoke"
          v-has:esc_tsk_stock_receipt_detail_revoke_apply
          type="warning"
          size="mini"
          :disabled="revDisabled"
          @click="revIntransit"
        >{{ $t('grid.others.withdrawRequest') }}</el-button>
        <el-button
          v-has:esc_stock_onroad_cancelEjqSubmit
          type="warning"
          size="mini"
          :disabled="erevDisabled"
          @click="revIntransit('e')"
        >撤销E建签申请</el-button>
        <template v-if="isVirtual">
          <el-button
            v-has:esc_tsk_stock_receipt_detail_onroad_revoke
            type="warning"
            size="mini"
            :disabled="isBusinessDisabled('revoke',sSheetStatus)"
            @click="revoke"
          >撤单</el-button>
        </template>
        <el-button
          v-has:esc_tsk_stock_receipt_detail_approval_query
          type="primary"
          size="mini"
          :disabled="isBusinessDisabled('approval',sSheetStatus)"
          @click="visibleDialog.approvaldia = true"
        >{{ $t('grid.others.approvalStatus') }}</el-button>
        <el-button
          v-has:esc_tsk_stock_receipt_detail_erp_approval_query
          type="primary"
          size="mini"
          :disabled="isBusinessDisabled('approval',sSheetStatus)"
          @click="visibleDialog.approvaldiaN8 = true"
        >ERP{{ $t('grid.others.approvalInquiry') }}</el-button>
        <el-button
          v-has:esc_tsk_stock_receipt_detail_erp_approval_query_new
          type="primary"
          size="mini"
          :disabled="isBusinessDisabled('approval',sSheetStatus)"
          @click="visibleDialog.approvaldiaV2 = true"
        >ERP{{ $t('grid.others.approvalInquiry') }}</el-button>
        <el-button
          v-has:esc_tsk_stock_receipt_detail__file_query
          type="primary"
          size="mini"
          :disabled="isBusinessDisabled('annex', sSheetStatus)"
          @click="visibleDialog.annex = true"
        >{{ $t('btns.attachmentManagement') }}</el-button>
        <el-button
          v-has:esc_stock_onRoadToArrival_wb_create_oneInWarehouse
          type="primary"
          size="mini"
          :disabled="!(dialogDetail.isInWarehouse === '1')"
          @click="handleWarehouseEntry"
        >一键进仓</el-button>
        <el-button
          v-has:esc_tsk_stock_receipt_detail_modifysIsDetail
          type="primary"
          size="mini"
          :disabled="isBusinessDisabled('revoke', sSheetStatus)"
          @click="modifysIsDetail"
        >修改出仓带明细</el-button>
        <el-button
          v-has:esc_stock_common_force_approval_modify
          type="primary"
          size="mini"
          :disabled="sSheetStatus === '70'"
          @click="visibleDialog.forceApprovalDialog = true"
        >
          强控特批
        </el-button>
        <el-button
          v-has:esc_stock_pur_arrival_revoke_ready
          class="ml-10 mr-10"
          type="primary"
          size="mini"
          :disabled="isBusinessDisabled('withdraw', sSheetStatus)"
          @click="cancelSubmitnoToErp"
        >撤为准备</el-button>
      </template>
      <template slot="content">
        <el-tabs v-if="dialogVisible" v-model="activeName" class="tabs-btn-position" :before-leave="beforeLeave">
          <el-tab-pane :label="$t('grid.tabs.basicInformation')" name="baseInfo">
            <baseInfo
              ref="baseInfo"
              :s-id="sId"
              :info="dialogDetail"
              :warehouse-list="warehouseList"
              @handleSave="saveIntransit('save')"
              @update="getDetail()"
            />
          </el-tab-pane>
          <el-tab-pane :label="$t('grid.tabs.commodityDetails')" name="commodityInfo">
            <factoryInfo
              v-if="logisticsType === '00'&&activeName === 'commodityInfo'"
              ref="factoryInfo"
              :s-id="sId"
              :info="dialogDetail"
            />
            <intransitInfo
              v-if="logisticsType === '20'&&activeName === 'commodityInfo'"
              ref="intransitInfo"
              :s-id="sId"
              :info="dialogDetail"
            />
          </el-tab-pane>
          <el-tab-pane v-if="dialogDetail.sIsDelWarehouse === '0'" label="预提费用" name="accruedExpenses">
            <accruedExpenses
              v-if="activeName === 'accruedExpenses'"
              :id="sId"
              :info="dialogDetail"
            />
          </el-tab-pane>
        </el-tabs>
      </template>
    </cnd-dialog>
    <!-- 弹出层 -->
    <horizon-approval-dialog
      :id="sId"
      :solt-btn="false"
      :visible.sync="visibleDialog.approvaldia"
      @handleClose="visibleDialog.approvaldia = false"
    />
    <horizon-approval-dialog
      :id="sId"
      :type="'n8'"
      :solt-btn="false"
      :visible.sync="visibleDialog.approvaldiaN8"
      @handleClose="visibleDialog.approvaldiaN8 = false"
    />
    <approval-dialog-v2
      :id="sId"
      :solt-btn="false"
      :visible.sync="visibleDialog.approvaldiaV2"
      :sheet-code="dialogDetail.sSheetCode"
      @handleClose="visibleDialog.approvaldiaV2 = false"
    />
    <steel-annex-dialog
      :visible="visibleDialog.annex"
      append-to-body
      :biz-id="sId"
      :disabled-btn="{ scan: isBusinessDisabled('save', sSheetStatus)|| $route.query.disabled === '1', del: isBusinessDisabled('save', sSheetStatus)|| $route.query.disabled === '1' }"
      @onSelect="visibleDialog.annex = false"
    />
    <checkDialog
      :visible="visibleDialog.check"
      :account-id="accountId"
      :show-account="true"
      @close="visibleDialog.check = false"
      @success="checkSuccess"
    />
    <forceApprovalDialog
      :id="sId"
      :visible="visibleDialog.forceApprovalDialog"
      @close="visibleDialog.forceApprovalDialog = false"
      @success="initFn()"
    />
    <submitDialogV2
      :visible="visibleDialog.submitDialog"
      @handleClose="visibleDialog.submitDialog = false"
      @onSure="onSubmit"
    />
  </div>
</template>
<script>
import baseInfo from './baseInfo/index'
import factoryInfo from './factoryInfo/index'
import intransitInfo from './intransitInfo/index'
import accruedExpenses from './accruedExpenses/index'
import businessMixin from '@/utils/businessMixin'
import checkDialog from '@/components/checkEJQDialog/index'
import {
  getStockOnroadDetail,
  updataIntransit,
  submitIntransit,
  submitValidateIntransit,
  removeIntransit,
  withdrawIntransit,
  revokeIntransit,
  getStockOnroadWarehouseList,
  cancelEjqSubmit,
  onroadRevokeready,
  oneInWarehouse
} from '@/api/logistics/purchaseTrack/intransitGoods'
import {
  arrivalModifysIsDetail
} from '@/api/logistics/registrationData/purchaseArrival/onArrival/onArrival.js'
import { MessageUtil } from 'cnd-horizon-utils'
import forceApprovalDialog from '@/components/forceApprovalDialog'
import submitDialogV2 from '@/components/submitDialogV2'
import approvalDialogV2 from '@/components/approvalDialogV2'
export default {
  name: 'IntransitDetail',
  components: {
    baseInfo,
    factoryInfo,
    intransitInfo,
    accruedExpenses,
    checkDialog,
    forceApprovalDialog,
    submitDialogV2,
    approvalDialogV2
  },
  mixins: [businessMixin],
  props: {
    // visible: {
    //   type: Boolean,
    //   default: false
    // },
    // dialogVisible: {
    //   type: Boolean,
    //   default: false
    // },
    // sId: {
    //   type: String,
    //   default: null
    // }
  },
  data() {
    return {
      sId: '',
      accountId: '',
      dialogVisible: true,
      activeName: 'baseInfo',
      visibleDialog: {
        submitDialog: false,
        annex: false,
        approvaldiaN8: false,
        approvaldia: false,
        approvaldiaV2: false,
        check: false,
        forceApprovalDialog: false
      },
      dialogDetail: {},
      oldDetail: null,
      warehouseList: [],
      sIsCoverVesselNo: null,
      checkData: null
    }
  },
  computed: {
    sSheetStatus() {
      return this.dialogDetail.sSheetStatus
    },
    logisticsType() {
      const { dialogVisible, dialogDetail } = this
      if (dialogVisible && dialogDetail) {
        return dialogDetail.sLogisticsType
      }
      return null
    },
    isVirtual() {
      const warehouse = this.warehouseList.find(el => el.sId === this.dialogDetail.sWarehouseId)
      return warehouse?.sType !== '10'
    },
    erevDisabled() {
      const { sSheetStatus, sIsSign } = this.dialogDetail
      return !(sSheetStatus === '30' && sIsSign === '1')
    },
    revDisabled() {
      const { sSheetStatus, sIsSign } = this.dialogDetail
      return !(sSheetStatus === '30' && sIsSign === '0')
    },
    sIsDetail() {
      return this.dialogDetail.sIsDetail
    },
    isXuniWarehouse() {
      return this.dialogDetail.vWarehouseName === '虚拟在途仓' || this.dialogDetail.sWarehouseId === '111111111111544'
    }
  },
  created() {
    this.param = this.$route.query
    this.sId = this.param.id
    this.initFn()
  },
  methods: {
    initFn() {
      this.getDetail()
      this.getWarehouseList()
    },
    getDetail() {
      if (this.sId) {
        getStockOnroadDetail({ sId: this.sId }).then(res => {
          this.dialogDetail = res.data
          this.oldDetail = JSON.stringify(res.data)
        })
      }
    },
    getWarehouseList() {
      getStockOnroadWarehouseList({ sId: this.sId }).then(res => {
        this.warehouseList = res.data.filter(item => item.sId !== '111111111111160')
      })
    },
    handleClose() {
      this.$emit('close')
    },
    async beforeLeave(activeName, oldActiveName) {
      let blFlag = false
      if (activeName === 'baseInfo') {
        this.initFn()
        blFlag = true
      }
      if (!this.isBusinessDisabled('save', this.sSheetStatus)) {
        const flag = new Promise((resolve, reject) => {
          this.$refs.baseInfo.saveForm(async(form, flag) => {
            if (this.sId && flag) {
              if (this.oldDetail !== JSON.stringify(this.dialogDetail)) {
                const flagItem = new Promise((resolve, reject) => {
                  updataIntransit(form).then((res) => {
                    MessageUtil.success(this.$t('grid.others.inTransitOrderSavedSuccessfully'))
                    getStockOnroadDetail({ sId: this.sId }).then(res => {
                      this.dialogDetail = res.data
                      this.oldDetail = JSON.stringify(res.data)
                      resolve(true)
                    }).catch(() => {
                      reject(false)
                    })
                  })
                })
                const isTure = await flagItem
                resolve(isTure)
              } else {
                resolve(true)
              }
            } else {
              reject(false)
            }
          })
        })
        console.log(flag)
        blFlag = await flag
        console.log(blFlag)
      }
      console.log(555, blFlag)
      return blFlag
    },
    // beforeLeave(activeName, oldActiveName) {
    //   if (activeName === 'baseInfo') {
    //     console.log(111)
    //     this.initFn()
    //   }
    //   if (!this.isBusinessDisabled('save', this.sSheetStatus)) {
    //     if (oldActiveName === 'baseInfo') {
    //       this.saveIntransit('leave', (flag) => {
    //         blFlag = flag
    //       })
    //     }
    //     return blFlag
    //   }
    //   return false
    // },
    saveIntransit(type, callback) {
      if (this.sId) {
        this.$refs.baseInfo.saveForm((form, flag) => {
          if (flag) {
            if (this.oldDetail !== JSON.stringify(this.dialogDetail) || type === 'save') {
              form.sRemark = form.sRemark?.trim()
              updataIntransit(form).then((res) => {
                MessageUtil.success(this.$t('grid.others.inTransitOrderSavedSuccessfully'))
                getStockOnroadDetail({ sId: this.sId }).then(res => {
                  this.dialogDetail = res.data
                  this.oldDetail = JSON.stringify(res.data)
                  callback && callback(true)
                })
              }).catch(() => {
                callback && callback(false)
              })
            } else {
              callback && callback(true)
            }
          } else {
            callback && callback(false)
          }
        })
      }
    },
    subIntransit() {
      this.saveIntransit('sumbit', (flag) => {
        if (!flag) return
        this.checkData = {}
        submitValidateIntransit({ sId: this.sId }).then((res) => {
          const { sIsNeedPrompt, sTipsMessage, sIsNeed, ejqCustomerId } = res.data
          const handleSubmission = () => {
            let confirmBtn = this.$t('btns.confirm')
            let cancelBtn = this.$t('btns.cancel')
            let messages = sTipsMessage
            if (sTipsMessage.includes('且项目')) {
              const data = sTipsMessage.split('且项目')
              if (data.length > 1) {
                messages = data[0]
              }
            }
            if (sTipsMessage.includes('是否覆盖子表车船号明细')) {
              confirmBtn = '是'
              cancelBtn = '否'
            }
            this.$confirm(
              messages,
              this.$t('grid.others.prompt'),
              {
                dangerouslyUseHTMLString: true,
                distinguishCancelAndClose: true,
                confirmButtonText: confirmBtn,
                cancelButtonText: cancelBtn,
                type: 'error'
              }
            ).then(() => {
              this.sIsCoverVesselNo = sTipsMessage.includes('是否覆盖子表车船号明细') ? '1' : null
              if (sIsNeed === '1') {
                this.accountId = ejqCustomerId
                this.visibleDialog.check = true
              } else {
                if (this.isXuniWarehouse) {
                  this.onSubmit('')
                } else {
                  this.visibleDialog.submitDialog = true
                }
              }
            }).catch((action) => {
              if (action === 'cancel' && sTipsMessage.includes('是否覆盖子表车船号明细')) {
                this.sIsCoverVesselNo = '0'
                if (sIsNeed === '1') {
                  this.accountId = ejqCustomerId
                  this.visibleDialog.check = true
                } else {
                  if (this.isXuniWarehouse) {
                    this.onSubmit('')
                  } else {
                    this.visibleDialog.submitDialog = true
                  }
                }
              }
            })
          }
          if (sIsNeedPrompt === '1' && sTipsMessage) {
            if (sTipsMessage.includes('销售合同存在附件未确认')) {
              let messages = sTipsMessage
              if (sTipsMessage.includes('且项目')) {
                const data = sTipsMessage.split('且项目')
                if (data.length > 1) {
                  messages = data[1]
                }
              }
              this.$confirm(
                `项目${messages}`,
                this.$t('grid.others.prompt'),
                {
                  confirmButtonText: this.$t('btns.confirm'),
                  cancelButtonText: this.$t('btns.cancel'),
                  type: 'warning'
                }
              ).then(() => {
                handleSubmission()
              }).catch(() => {
              })
            } else {
              handleSubmission()
            }
          } else {
            // confirmSubmission()
            if (sIsNeed === '1') {
              this.accountId = ejqCustomerId
              this.visibleDialog.check = true
            } else {
              if (this.isXuniWarehouse) {
                this.onSubmit('')
              } else {
                this.visibleDialog.submitDialog = true
              }
            }
          }
        })
      })
    },
    async checkSuccess(payload) {
      if (this.isXuniWarehouse) {
        this.checkData = payload
        const isTrue = await this.onSubmit('')
        if (isTrue) {
          this.visibleDialog.check = false
        }
      } else {
        this.checkData = payload
        this.visibleDialog.check = false
        this.visibleDialog.submitDialog = true
      }
    },
    onSubmit(val) {
      return new Promise((resolve, reject) => {
        const { sIsNeed, ejqCustomerId, name, account } = this.checkData || {}
        const data = {
          sIsNeed: sIsNeed || '0',
          ejqCustomerId: ejqCustomerId || '',
          sLinkMan: name || '',
          phone: account || '',
          sIsCoverVesselNo: this.sIsCoverVesselNo,
          param: val
        }
        submitIntransit({ sId: this.sId }, data).then((res) => {
          MessageUtil.success(this.$t('tips.submitSuccess'))
          this.visibleDialog.submitDialog = false
          this.getDetail()
          this.$nextTick(() => {
            this.$refs.factoryInfo && this.$refs.factoryInfo.reload()
            this.$refs.intransitInfo && this.$refs.intransitInfo.reload()
          })
          resolve(true)
        }).catch((err) => {
          reject(err)
        })
      })
    },
    revIntransit(type = null) {
      const postApi = type === 'e' ? cancelEjqSubmit : withdrawIntransit
      this.$confirm(this.$t('grid.tips.isItConfirmedThatThcelledKey'), this.$t('grid.others.prompt'), {
        confirmButtonText: this.$t('btns.confirm'),
        cancelButtonText: this.$t('btns.cancel'),
        type: 'warning'
      }).then(() => {
        postApi({ sId: this.sId }).then((res) => {
          MessageUtil.success(this.$t('tips.withdrawalSuccessTag'))
          this.getDetail()
        })
      })
    },
    revoke() {
      this.$confirm('是否确认撤单？', this.$t('grid.others.prompt'), {
        confirmButtonText: this.$t('btns.confirm'),
        cancelButtonText: this.$t('btns.cancel'),
        type: 'warning'
      }).then(() => {
        revokeIntransit({ sId: this.sId }).then((res) => {
          MessageUtil.success('撤单成功')
          this.getDetail()
        })
      })
    },
    delIntransit() {
      this.$confirm(this.$t('grid.tips.isTheDeletionConfirmed'), this.$t('grid.others.prompt'), {
        confirmButtonText: this.$t('btns.confirm'),
        cancelButtonText: this.$t('btns.cancel'),
        type: 'warning'
      }).then(() => {
        removeIntransit({ sId: this.sId }).then((res) => {
          MessageUtil.success(this.$t('tips.deletedSuccessfully'))
          this.$tabDelete()
        })
      })
    },
    modifysIsDetail() {
      const tips = this.sIsDetail === '1' ? '出仓附件是否带明细 修改 为否' : '出仓附件是否带明细 修改 为是'
      this.$confirm(tips, this.$t('grid.others.prompt'), {
        confirmButtonText: this.$t('btns.confirm'),
        cancelButtonText: this.$t('btns.cancel'),
        type: 'warning'
      })
        .then(() => {
          arrivalModifysIsDetail({
            sId: this.sId,
            sIsDetail: this.sIsDetail === '1' ? '0' : '1'
          }).then(res => {
            if (res.data) {
              this.$message.error(res.data)
            } else {
              this.$message.success(this.$t('tips.operationSuccessful'))
            }
            this.initFn()
          })
        })
        .catch(() => { })
    },
    cancelSubmitnoToErp() {
      this.$confirm('是否确定撤为准备', this.$t('grid.others.prompt'), {
        confirmButtonText: this.$t('btns.confirm'),
        cancelButtonText: this.$t('btns.cancel'),
        type: 'info'
      }).then(() => {
        onroadRevokeready({
          id: this.sId
        }).then(() => {
          MessageUtil.success('撤为准备成功')
          this.getDetail()
        })
      })
    },
    handleWarehouseEntry() {
      oneInWarehouse({ id: this.sId }).then((res) => {
        this.$message.success(' 一键进仓成功')
        // 跳转到货单详情
        this.$router.push({
          path: `/arrivalNoteDetail/${res.data.sId}`,
          query: {
            id: res.data.sId,
            status: res.data.sSheetStatus,
            type: 'edit',
            name: `${this.$t('grid.others.purchaseArrivalOrder')}【${res.data.sCode}】`,
            activeId: localStorage.getItem('menuId')
          }
        })
      })
    }
  }
}
</script>
