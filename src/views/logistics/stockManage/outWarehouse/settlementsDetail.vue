<template>
  <div>
    <div class="auto-page-title">
      <div class="flexV">
        <steelTradeAggrid
          ref="aggrid"
          :column-defs="columnDefs"
          :row-data="rowData"
          :heightinif="200"
          row-key="_sId"
          :load-data="loadData"
          table-selection="single"
          :paginationinif="false"
          @selectedChange="handleSelectedChange"
        />
        <div class="btn-group">
          <div class="text">
            {{ $t('grid.others.allotmentDetails') }}
          </div>
          <div>
            <el-button
              type="primary"
              size="mini"
              style="margin-left: 10px"
              :disabled="rowDataSelected.length == 0 || rowDataSelected.vResidueMatchAmt == 0 || isBusinessDisabled('save',sSheetStatus)"
              @click="paydealAutoUsefun"
            >{{ $t('grid.others.automaticMatching') }}
            </el-button>
            <el-button
              type="primary"
              size="mini"
              style="margin-left: 10px"
              :disabled="rowDataSelected.length == 0 || rowDataSelected.vResidueMatchAmt == 0 || isBusinessDisabled('save',sSheetStatus)"
              @click="allotmentDialogVisiblefun"
            >{{ $t('grid.others.manualAllocation') }}</el-button>
          </div>
        </div>
        <steelTradeAggrid
          ref="allotmentGrid"
          :heightinif="300"
          :column-defs="columnDefsAllotment"
          :row-data="rowDataAllotment"
          :load-data="loadDataAllotment"
          row-key="sId"
          table-selection="multiple"
          :paginationinif="false"
          @selectedChange="allotmentHandleChange"
        />
      </div>
    </div>
    <matchingDialog
      v-if="dialogVisible"
      :id="id"
      :dialog-visible="dialogVisible"
      :select-data="rowDataSelected"
      :info="info"
      @close="setAllotmentDialog"
    />
  </div>
</template>
<script>
import { Moment } from 'cnd-utils'
import { SteelFormat } from 'cnd-horizon-utils'
import {
  getCnDitc,
  getDictet
} from '@/api/logistics/saleDelivery/saleorder'
import {
  adjustDetailSumInfo,
  adjustOffsetQueryPayed,
  adjustOffsetRemove,
  adjustOffsetAutoUse
} from '@/api/logistics/stockManage/outWarehouse'
import { computeCellTotal } from '@/utils/common'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import businessMixin from '@/utils/businessMixin'
import matchingDialog from './dialog/matchingDialog.vue'
export default {
  components: { steelTradeAggrid, matchingDialog },
  mixins: [businessMixin],
  props: {
    id: {
      type: String,
      default: null
    },
    info: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      dialogVisible: false,
      columnDefs: [
        {
          field: 'sProjectCode',
          headerName: '项目号'
        },
        {
          headerName: this.$t('grid.title.salesContractNumber'),
          field: 'sSaleContractCode'
        },
        {
          headerName: this.$t('grid.others.salesOrderNumber'),
          field: 'sNoticeGoodsCode'
        },
        {
          headerName: '调整金额',
          field: 'vAdjustSumAmt',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return SteelFormat.formatPrice(params.value)
          }
        },
        {
          headerName: '待配款金额',
          field: 'vResidueMatchAmt',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return SteelFormat.formatPrice(params.value)
          }
        }
      ],
      columnDefsAllotment: [
        {
          headerName: this.$t('grid.title.salesContractNumber'),
          field: 'sSaleContractCode'
        },
        {
          headerName: this.$t('grid.others.customer'),
          field: 'vCustomerName'
        },
        {
          field: 'sPayDate',
          headerName: this.$t('grid.others.receiptDate'),
          valueFormatter: params => {
            return params.data._hiddenCheckbox ? params.value : Moment.time('YYYY-MM-DD', params.value)
          }
        },
        {
          field: 'sInteractionSubclass',
          headerName: this.$t('grid.others.transactionType'),
          valueGetter: params => {
            return getCnDitc(params, this.selectOps['pay.subtype'], 'sInteractionSubclass')
          }
        },
        {
          field: 'sFundType',
          headerName: this.$t('grid.others.paragraphType'),
          valueGetter: params => {
            return getCnDitc(params, this.selectOps['pay.payment.type'], 'sFundType')
          }
        },
        {
          field: 'sBillCode',
          headerName: this.$t('grid.others.noteNumber')
        }, {
          field: 'sMaturityDate',
          headerName: this.$t('grid.others.maturityDateOfAcceptance'),
          valueFormatter: params => {
            return Moment.time('YYYY-MM-DD', params.value)
          }
        },
        {
          field: 'sUpCode',
          headerName: this.$t('grid.others.originalDocumentNumber')
        },
        {
          field: 'sTaxAmt',
          headerName: this.$t('grid.title.amount'),
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return SteelFormat.formatPrice(params.value)
          }
        }, {
          field: '',
          headerName: this.$t('grid.others.operation'),
          cellStyle: { textAlign: 'center' },
          onCellClicked: (params) => {
            if (!this.isBusinessDisabled('save', this.sSheetStatus) && !params.data._hiddenCheckbox) {
              this.delfuntable(params)
            }
          },
          cellRenderer: (params) => {
            const sHtml = '<span style=color:red;cursor:pointer>' + this.$t('btns.delete') + '</span>'
            if (!this.isBusinessDisabled('save', this.sSheetStatus) && !params.data._hiddenCheckbox) {
              return sHtml
            } else {
              return ''
            }
          }
        }
      ],
      rowData: [],
      rowDataSelected: [],
      rowDataSelectedId: '',
      rowDataAllotment: [],
      selectOps: {
        'pay.payment.type': [],
        'pay.subtype': []
      }
    }
  },
  computed: {
    sSheetStatus() {
      return this.info.sSheetStatus
    }
  },
  created() {
    getDictet([
      'pay.payment.type',
      'pay.subtype'
    ]).then(result => {
      this.selectOps['pay.payment.type'] = result.data[0].dicts
      this.selectOps['pay.subtype'] = result.data[1].dicts
    }).catch(() => {
    })
  },
  methods: {
    allotmentDialogVisiblefun() {
      this.dialogVisible = true
    },
    setAllotmentDialog() {
      this.dialogVisible = false
      this.$refs.aggrid.loadTableData()
      this.$refs.allotmentGrid.loadTableData()
    },
    loadData(pagination) {
      console.log('loadData ')
      return new Promise((resolve, reject) => {
        adjustDetailSumInfo({ sId: this.id, ...pagination }).then(res => {
          this.rowData = res.data.map((item, index) => {
            item._selected = false
            item._sId = index
            if (this.rowDataSelectedId) {
              if (this.rowDataSelectedId === index) {
                item._selected = true
              }
            } else {
              item._selected = false
              res.data[0]._selected = true
            }
            return item
          })
          resolve(res.data)
        }).catch(() => {
          reject(0)
        })
      })
    },
    loadDataAllotment(pagination) {
      return new Promise((resolve, reject) => {
        adjustOffsetQueryPayed({ sUpId: this.id }, {
          sSaleContractId: this.rowDataSelected.sSaleContractId,
          sOldSaleContractId: this.rowDataSelected.sOldSaleContractId,
          sProjectCode: this.rowDataSelected.sProjectCode
        }).then(res => {
          this.rowDataAllotment = res.data.map(item => {
            item._selected = false
            return item
          })
          this.handleCellTotal()
          resolve(res.data)
        }).catch(() => {
          reject(0)
        })
      })
    },
    handleSelectedChange() {
      this.rowDataSelected = []
      this.rowDataSelectedId = ''
      const detail = this.rowData.filter(item => item._selected)
      if (detail.length > 0) {
        this.rowDataSelectedId = detail[0]._sId
        this.rowDataSelected = detail[0]
      } else {
        this.rowDataAllotment = []
        this.rowDataSelectedId = ''
        this.rowDataSelected = []
      }
      if (this.$refs.allotmentGrid) {
        this.$refs.allotmentGrid.reloadTableData()
      }
      console.log('this.rowDataSelected: ', this.rowDataSelected)
    },
    allotmentHandleChange() {
      this.$refs.allotmentGrid.getSelectedData(res => {
        this.handleCellTotal(res.length ? 'selected' : 'all')
      })
    },
    handleCellTotal(type = 'all') {
      setTimeout(() => {
        const totalList = computeCellTotal(
          this.rowDataAllotment,
          {
            sTaxAmt: 0
          },
          {
            sSaleContractCode: type === 'all' ? this.$t('grid.others.amountTo') : `${this.$t('components.selected')} ${this.rowDataAllotment.filter(item => item._selected).length} ${this.$t('pagination.items')}`,
            sMaturityDate: null,
            sFreeExpiryDate: null,
            sMonthRate: null,
            sIrtDay: null,
            sSingleRate: null,
            _selected: false,
            _hiddenCheckbox: true
          },
          type
        )
        if (this.rowDataAllotment.length) {
          this.$refs.allotmentGrid.gridApi.setPinnedBottomRowData([totalList])
        } else {
          this.$refs.allotmentGrid.gridApi.setPinnedBottomRowData([totalList])
        }
      }, 0)
    },
    delfuntable(e) {
      const data = []
      data.push(e.data.sId)
      if (this.sSheetStatus !== '10' && this.sSheetStatus !== '11' && this.sSheetStatus !== '15') {
        this.$message({
          message: this.$t('grid.tips.currentStatusCannotBeDeleted'),
          type: 'error'
        })
        return
      }
      adjustOffsetRemove(data).then(e => {
        this.$message({
          message: this.$t('tips.deletedSuccessfully'),
          type: 'success'
        })
        this.$refs.aggrid.reloadTableData()
        if (this.$refs.allotmentGrid) {
          this.$refs.allotmentGrid.reloadTableData()
        }
      })
    },
    paydealAutoUsefun() {
      if (this.rowDataSelectedId === '' || this.rowDataSelectedId === null) {
        this.$message({
          message: this.$t('grid.others.pleaseSelectAData'),
          type: 'warning'
        })
        return
      }
      const data = []
      data.push({
        sCompanyId: this.rowDataSelected.sCompanyId,
        sCustomerId: this.rowDataSelected.sCustomerId,
        sInteractionSubclass: '110',
        sUpId: this.info.sId,
        // sUpEntity: 'com.chinacnd.horizon.rock.bizkit.stock.delivery.entity.ConNoticeSaleDelivery',
        // sUpSheetCode: 'trade.deliver.sales.domestic',
        sUpCode: this.info.sCode,
        sUpSheetType: '110',
        vSumAmt: this.rowDataSelected.vAdjustSumAmt,
        sTaxAmt: this.rowDataSelected.vResidueMatchAmt,
        vFilterSubclass: '180,190,210',
        sSaleContractCode: this.rowDataSelected.sSaleContractCode,
        sSaleContractId: this.rowDataSelected.sSaleContractId,
        sNoticeGoodsCode: this.rowDataSelected.sNoticeGoodsCode,
        sNoticeGoodsId: this.rowDataSelected.sNoticeGoodsId,
        sProjectCode: this.rowDataSelected.sProjectCode
      })
      adjustOffsetAutoUse(data).then(res => {
        this.$message.success(this.$t('grid.tips.allocationSuccess'))
        this.$refs.aggrid.loadTableData()
        this.$refs.allotmentGrid.loadTableData()
      })
    }
  }
}
</script>
<style lang="scss" scoped>

</style>
