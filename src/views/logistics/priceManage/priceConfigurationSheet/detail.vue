<template>
  <div class="page-container">
    <cnd-dialog
      :title="$t('grid.tabs.basicInformation')"
      :visible="true"
      @close="onClose"
    >
      <template slot="leftBtn">
        <!-- <el-button type="primary" size="mini" plain icon="el-icon-arrow-left" @click="successEmit">返回列表</el-button> -->
        <el-button
          v-has:esc_prc_sheet_detail_submit
          type="primary"
          size="mini"
          :disabled="
            form.sSheetStatus !== '10' &&
              form.sSheetStatus !== '11' &&
              form.sSheetStatus !== '15'
          "
          @click="submit"
        >{{ $t('grid.others.submit') }}</el-button>
        <el-button
          v-has:esc_prc_sheet_detail_delete
          type="danger"
          size="mini"
          :disabled="
            form.sSheetStatus !== '10' &&
              form.sSheetStatus !== '11' &&
              form.sSheetStatus !== '15'
          "
          @click="remove"
        >{{ $t('btns.delete') }}</el-button>
        <el-button
          v-has:esc_prc_sheet_detail_revoke_apply
          type="warning"
          size="mini"
          :disabled="!(form.sSheetStatus === '30')"
          @click="withdraw"
        >{{ $t('grid.others.withdrawRequest') }}</el-button>
        <el-button
          v-has:esc_prc_sheet_detail_file
          type="primary"
          size="mini"
          @click="dialogVisible.annex = true"
        >{{ $t('btns.attachmentManagement') }}</el-button>
        <el-button
          v-has:esc_prc_sheet_detail_approval
          type="primary"
          size="mini"
          @click="openApproval()"
        >{{ $t('grid.others.approvalStatus') }}</el-button>
        <el-button
          v-has:esc_prc_sheet_detail_approvalN8
          type="primary"
          size="mini"
          @click="dialogVisible.approvalN8 = true"
        >ERP{{ $t('grid.others.approvalInquiry') }}</el-button>
        <el-button
          v-has:esc_prc_sheet_detail_ver
          type="primary"
          size="mini"
          :disabled="form.sSheetStatus !== '70'"
          @click="versionBackUp"
        >{{ $t('grid.others.versionMaintenance') }}</el-button>
      </template>
      <template slot="content">
        <el-tabs v-model="activeName" :before-leave="beforeLeave">
          <el-tab-pane :label="$t('grid.tabs.basicInformation')" name="basic">
            <cnd-btn-position top="8" right="10">
              <el-button
                v-has:esc_prc_sheet_detail_save
                type="primary"
                size="mini"
                :disabled="
                  form.sSheetStatus !== '10' &&
                    form.sSheetStatus !== '11' &&
                    form.sSheetStatus !== '15'
                "
                @click="save"
              >{{ $t('btns.save') }}</el-button>
            </cnd-btn-position>
            <BaseInfo
              v-if="activeName === 'basic'"
              :id="activeId"
              ref="basic"
              :form-data="form"
              @success="loadDetail"
            />
          </el-tab-pane>
          <el-tab-pane
            :label="$t('grid.others.priceConfigurationDetails')"
            name="middle"
          >
            <priceConfigDetail
              v-if="activeName === 'middle'"
              :id="activeId"
              ref="middle"
              :form="form"
              :s-sheet-status="form.sSheetStatus || null"
              @reloadDetail="loadDetail"
            />
          </el-tab-pane>
          <el-tab-pane
            label="商品价格配置"
            name="goods"
          >
            <goodsDetail
              v-if="activeName === 'goods'"
              :id="activeId"
              ref="goods"
              :form="form"
              :s-sheet-status="form.sSheetStatus || null"
              @reloadDetail="loadDetail"
            />
          </el-tab-pane>
        </el-tabs>
      </template>
    </cnd-dialog>
    <steel-annex-dialog
      :visible="dialogVisible.annex"
      append-to-body
      :biz-id="activeId"
      :disabled-btn="{
        scan:
          form.sSheetStatus !== '10' &&
          form.sSheetStatus !== '11' &&
          form.sSheetStatus !== '15',
        del:
          form.sSheetStatus !== '10' &&
          form.sSheetStatus !== '11' &&
          form.sSheetStatus !== '15'
      }"
      @onSelect="selectAnnex"
    />
    <!-- <horizon-approval-dialog
      :id="activeId"
      :solt-btn="true"
      :visible="dialogVisible.approval"
      @handleClose="dialogVisible.approval = false"
    /> -->
    <approval-dialog
      :id="activeId"
      :id-keys="approvalKeys"
      :solt-btn="false"
      :visible.sync="dialogVisible.approval"
      @handleClose="dialogVisible.approval = false"
    />
    <horizon-approval-dialog
      :id="activeId"
      :type="'n8'"
      :solt-btn="false"
      :visible.sync="dialogVisible.approvalN8"
      @handleClose="dialogVisible.approvalN8 = false"
    />
    <submitDialog
      :id="activeId"
      :visible="dialogVisible.submitDialog"
      :api-url="`/esc/stock/price/desc/${activeId}`"
      @handleClose="dialogVisible.submitDialog = false"
      @onSure="onSubmit"
    />
  </div>
</template>

<script>
import BaseInfo from './baseInfo'
import businessMixin from '@/utils/businessMixin'
import priceConfigDetail from './priceConfigDetail'
import goodsDetail from './goodsDetail'
import submitDialog from '@/components/submitDialog'
import approvalDialog from '@/components/approvalDialog'
import { MessageUtil } from 'cnd-horizon-utils'
import {
  submitStockPrice,
  remoceStockPrice,
  withdrawStockPrice,
  getStockPriceDetail,
  backupStockPrice,
  modifyStockPrice,
  getOri
} from '@/api/logistics/priceManage'
export default {
  name: 'PriceConfigurationSheetDetail',
  components: {
    BaseInfo,
    priceConfigDetail,
    submitDialog,
    approvalDialog,
    goodsDetail
  },
  mixins: [businessMixin],
  // props: {
  //   visible: {
  //     type: Boolean,
  //     default: false
  //   },
  //   id: {
  //     type: String,
  //     default: ''
  //   }
  // },
  data() {
    return {
      dialogVisible: {
        annex: false,
        submitDialog: false,
        approval: false, // 审批情况
        approvalN8: false
      },
      activeName: 'basic',
      form: {
        sSheetStatus: null
      },
      oldDetail: null,
      activeId: '',
      isUpdata: false,
      approvalKeys: null
    }
  },
  computed: {
    sOriId() {
      return this.form.sOriId
    }
  },
  watch: {
    activeName(val) {
      if (val === 'basic') {
        this.loadDetail()
      }
    }
  },
  mounted() {
    this.loadDetail()
  },
  created() {
    this.param = this.$route.query
    this.activeId = this.param.id
  },
  methods: {
    openApproval() {
      getOri({ id: this.sOriId }).then((res) => {
        const data = [this.activeId]
        this.approvalKeys = res.data.length > 0 ? res.data : data
        this.dialogVisible.approval = true
      })
    },
    beforeLeave(activeName, oldActiveName) {
      if (activeName === 'basic') {
        this.loadDetail()
      }
      let blFlag = true
      if (
        oldActiveName === 'basic' &&
        !this.isBusinessDisabled('save', this.form.sSheetStatus)
      ) {
        if (this.oldDetail !== JSON.stringify(this.form)) {
          if (this.activeId) {
            this.$refs.basic.saveForm((form) => {
              if (!form) {
                blFlag = false
              } else {
                modifyStockPrice(form).then((res) => {
                  this.$message.success(this.$t('tips.saveSuccess'))
                  this.loadDetail()
                  this.isUpdata = false
                })
              }
            })
          }
        }
      }
      return blFlag
    },
    loadDetail() {
      getStockPriceDetail({
        sId: this.activeId
      }).then((res) => {
        this.form = res.data
        this.oldDetail = JSON.stringify(res.data)
      })
    },
    // 关闭弹窗
    onClose() {
      this.$emit('onClose')
    },
    save() {
      this.$refs.basic.save()
    },
    submit() {
      if (this.$refs.middle) {
        this.$refs.middle.$refs.pirceGrid.gridApi.stopEditing()
      }
      this.dialogVisible.submitDialog = true
    },
    onSubmit(value) {
      submitStockPrice(
        {
          sId: this.activeId
        },
        { param: value }
      ).then(() => {
        this.dialogVisible.submitDialog = false
        MessageUtil.success(this.$t('tips.submitSuccess'))
        this.loadDetail()
        if (this.activeName === 'middle' && this.$refs.middle) {
          this.$refs.middle.refresh()
        }
        if (this.activeName === 'goods' && this.$refs.goods) {
          this.$refs.middle.refresh()
        }
      })
    },
    remove() {
      this.$confirm(
        this.$t('tips.isItOkToDelete'),
        this.$t('grid.others.prompt'),
        {
          confirmButtonText: this.$t('btns.confirm'),
          cancelButtonText: this.$t('btns.cancel'),
          type: 'info'
        }
      ).then(() => {
        remoceStockPrice({
          sId: this.activeId
        }).then(() => {
          this.$tabDelete()
        })
      })
    },
    withdraw() {
      this.$confirm(
        this.$t('grid.tips.whetherToRevokeTheApplication'),
        this.$t('grid.others.prompt'),
        {
          confirmButtonText: this.$t('btns.confirm'),
          cancelButtonText: this.$t('btns.cancel'),
          type: 'info'
        }
      ).then(() => {
        withdrawStockPrice({
          sId: this.activeId
        }).then(() => {
          MessageUtil.success(this.$t('tips.withdrawalSuccessTag'))
          this.loadDetail()
        })
      })
    },
    versionBackUp() {
      backupStockPrice({
        sId: this.activeId
      }).then((res) => {
        this.activeId = res.data.sId
        this.activeName = 'middle'
        this.loadDetail()
      })
    },
    selectAnnex() {
      this.dialogVisible.annex = false
    },
    successEmit() {
      this.$emit('success')
      this.onClose()
    }
  }
}
</script>
