<template>
  <div class="auto-page-title">
    <div class="flexV">
      <cnd-btn-position top="8" right="10" />
      <!-- 表格 -->
      <div class="btn-group">
        <div class="text">
          {{ $t('grid.others.salesShipmentDetails') }}
        </div>
        <div>
          <el-button
            v-has:esc_stock_nonparty_goods__del_detail_add_amount_modify
            :disabled="isBusinessDisabled('save',sSheetStatus)"
            type="primary"
            size="mini"
            @click="openModifyDialog('2')"
          >修改总价</el-button>
          <el-button
            v-has:esc_stock_nonparty_goods__del_detail_add_price_modify
            :disabled="isBusinessDisabled('save',sSheetStatus)"
            type="primary"
            size="mini"
            @click="openModifyDialog('1')"
          >修改单价</el-button>
          <el-button key="comm_purcontact_list_add" v-has:esc_stock_nonparty_goods__del_detail_add :disabled="isBusinessDisabled('save',sSheetStatus)" type="primary" size="mini" @click="openPhyInvalidDialog">
            {{ $t('btns.add') }}
          </el-button>
          <el-button key="comm_purcontact_list_del" v-has:esc_stock_nonparty_goods__del_detail_delete :disabled="isBusinessDisabled('save',sSheetStatus)" type="danger" size="mini" @click="agrRemovesfun">
            {{ $t('btns.delete') }}
          </el-button>
          <!-- <el-button key="comm_purcontact_list_del" v-has:esc_stock_nonparty_goods__del_detail_create_settlement :disabled="isBusinessDisabled('save',sSheetStatus)" type="primary" size="mini" @click="addsettle">
            {{ $t('grid.others.generateBillingStatement') }}
          </el-button> -->
        </div>
      </div>
      <steelTradeAggrid
        ref="aggrid"
        :column-defs="columnDefs"
        :heightinif="500"
        :row-data="rowData"
        :load-data="loadData"
        :default-page-size="200"
        table-selection="multiple"
        row-key="sId"
        open-after-filter
        :footer-total="footerCount"
        @selectedChange="handleStockDetailCount"
        @cellValueChanged="cellValueChanged"
        @cellEditingStopped="cellEditingStopped"
        @onPasteEnd="onPasteEnd"
      >
        <!-- <p slot="header">{{ headerCount }}</p> -->
        <!-- <p slot="footer">{{ footerCount }}</p> -->
      </steelTradeAggrid>
    </div>
    <phyInvalidDialog :invoice-id="invoiceId" :type="type" :dialog-visible="phyDialogVisible" @close="closePhyInvalidDialog" />
    <cnd-dialog
      v-if="modifyDialog"
      :visible="modifyDialog"
      :fullscreen="false"
      append-to-body
      :title="modifyType === '1' ? '批量修改' : '修改总价'"
      width="350px"
      height="40px"
      @close="modifyDialog = false"
    >
      <template slot="content">
        <div>
          <el-form
            :model="modifyForm"
            label-width="100px"
            size="small"
            @submit.native.prevent
          >
            <cnd-form-item
              :label="modifyType === '1' ? '此次确认单价' : '总金额'"
              :custom-width="20"
            >
              <cnd-input-number v-model="modifyForm.modifyPrice" type="amount" :decimal-digit="6" clearable :placeholder="$t('components.pleaseEnter')" @keyup.enter.native="modifySubmit" />
            </cnd-form-item>
          </el-form>
        </div>
      </template>
      <template slot="footer">
        <el-button
          size="mini"
          @click="modifyDialog = false"
        >{{ $t('btns.cancel') }}</el-button>
        <el-button
          type="primary"
          size="mini"
          @click="modifySubmit"
        >{{ $t('btns.confirm') }}</el-button>
      </template>
    </cnd-dialog>
  </div>
</template>

<script>
import Vue from 'vue'
import { Middleware } from 'cndinfo-ui'
import businessMixin from '@/utils/businessMixin'
import { SteelFormat } from 'cnd-horizon-utils'
import { getPurPhysicsInvoiceDetail } from '@/api/invoice/purchase'
import agreement from '@/api/agreement'
import {
  detailagrRemoves,
  deliveryshoppage,
  detailupdatePrice,
  commonsettlecreate,
  updatePriceForThird,
  updateAmtForThird,
  updateBatchPriceForThird
} from '@/api/logistics/saleDelivery/nopartysaleorder'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import phyInvalidDialog from '../phytihInfo/phyInvalidDialog.vue'

export default {
  components: { steelTradeAggrid, phyInvalidDialog },
  mixins: [businessMixin],
  props: {
    invoiceId: {
      type: String,
      default: null
    },
    type: {
      type: String,
      default: null
    },
    purInvoiceDetail: {
      type: Object,
      default: null
    }
  },
  data() {
    // const _this = this
    return {
      modifyType: '',
      modifyForm: {
        modifyPrice: null
      },
      modifyDialog: false,
      modifyIds: [],
      columnDefs: [
        {
          headerName: this.$t('grid.others.itemNumberTag'),
          field: 'sProjectCode'
        },
        {
          headerName: '原' + this.$t('grid.title.salesContractNumber'),
          field: 'sOldSaleContractCode'
        },
        {
          headerName: this.$t('grid.title.purchaseContractNumber'),
          field: 'sPurContractCode'
        },
        // {
        //   headerName: this.$t('grid.others.customer'),
        //   field: 'vCustomerName',
        // },
        {
          headerName: this.$t('grid.others.item'),
          field: 'vGoodsDesc'
        },
        {
          headerName: this.$t('grid.title.quantity'),
          field: 'sContractQty',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return SteelFormat.formatThousandthSign(params.value, 4)
          }
        },
        {
          headerName: this.$t('grid.others.numberOfPiecesTag'),
          field: 'sQty',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return SteelFormat.formatThousandthSign(params.value)
          }
        },
        {
          headerName: '单位',
          field: 'vQtyUnitName'
        },
        {
          headerName: this.$t('grid.title.unitPrice'),
          field: 'sTaxPrice',
          cellStyle: { textAlign: 'right' },
          editable: () => {
            return !this.isBusinessDisabled('save', this.sSheetStatus)
          },
          cellEditorFramework: Vue.extend(Middleware.createComponent(
            'CndInputNumber',
            {
              mark: 'sTaxPrice',
              type: 'number',
              decimalDigit: 6,
              autoFocus: true,
              focusSelect: true
            }
          )),
          valueFormatter: (params) => {
            return SteelFormat.formatPrice(params.value)
          }
        },
        {
          headerName: this.$t('grid.title.amount'),
          field: 'sTaxAmt',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return SteelFormat.formatPrice(params.value)
          }
        },
        {
          headerName: this.$t('grid.others.steelCoilNumber'),
          field: 'sExtend4'
        },
        {
          headerName: this.$t('grid.others.carriageNumber'),
          field: 'sExtend5'
        },
        {
          headerName: this.$t('grid.others.originalDocumentNumber'),
          field: 'sUpCode'
        },
        {
          headerName: this.$t('grid.title.accountingGroup'),
          field: 'vCheckGroupName'
        },
        {
          field: 'vDepartmentName',
          headerName: this.$t('grid.others.department')
        },
        {
          headerName: this.$t('grid.title.personnel'),
          field: 'vStaffName'
        },
        {
          headerName: this.$t('grid.title.remarks'),
          field: 'sRemark',
          width: '168px'
        }
      ],
      rowData: [],
      headerCount: '共0条，合计：数量0吨，不含税金额0元，税额0元，含税金额0元',
      footerCount: '',

      phyDialogVisible: false,
      selectOps: {}
    }
  },
  computed: {
    sSheetStatus() {
      return this.purInvoiceDetail.sSheetStatus
    }
  },
  beforeMount() {
    agreement.getDict(['dev.common.sheet.status']).then(result => {
      this.selectOps['dev.common.sheet.status'] = result.data[0].dicts // 状态
    })
  },
  mounted() {
    this.onSearch()
  },
  methods: {

    addsettle(type) {
      commonsettlecreate(this.invoiceId).then(e => {
        this.$message.success(this.$t('grid.others.settlementInformationHasBeenUpdated'))
        if (type) {
          return
        }
        this.$emit('tabs')
      })
    },
    onSearch() {
      this.$refs.aggrid.loadTableData()
    },
    setCount(vCount = 0, vSumContractQty = 0, vSumQty = 0, vSumAmt = 0, flag) {
      this[flag] = [
        { count: vCount, key: 'count' },
        { title: this.$t('grid.title.quantity'), count: SteelFormat.formatThousandthSign(vSumContractQty, 4), unit: this.$t('grid.others.ton') },
        { title: this.$t('grid.others.numberOfPiecesTag'), count: vSumQty, unit: this.$t('grid.others.pieces') },
        { title: this.$t('grid.title.amount'), count: SteelFormat.formatPrice(vSumAmt), unit: this.$t('grid.others.yuan') }
      ]
    },
    handleStockDetailCount(_, details) {
      const vCount = details.length
      let vSumContractQty = 0; let vSumAmt = 0; let vSumQty = 0
      details.forEach(el => {
        vSumContractQty += el.sContractQty
        vSumQty += el.sQty
        vSumAmt += el.sTaxAmt
      })
      this.setCount(vCount, vSumContractQty, vSumQty, vSumAmt, 'footerCount')
    },
    loadData(pagination) {
      return new Promise((resolve, reject) => {
        deliveryshoppage(this.invoiceId, {
          ...pagination
        }).then(res => {
          this.rowData = res.data.content.map(item => {
            item._selected = true
            item._isEdit = false
            return item
          })
          resolve(res.data)
        }).catch(() => {
          reject(0)
        })
      })
    },
    neupdatePricew() {
      detailupdatePrice(this.invoiceId).then(e => {
        this.$message({
          message: this.$t('grid.others.updateSuccessful'),
          type: 'success'
        })
        this.$refs.aggrid.loadTableData()
      })
    },
    loadDetail(data) {
      return new Promise((resolve, reject) => {
        getPurPhysicsInvoiceDetail({
          sInvoiceId: this.invoiceId,
          sInvCode: data.sInvCode,
          sInvNo: data.sInvNo
        }).then(res => {
          resolve(res.data)
        }).catch(() => {
          reject([])
        })
      })
    },
    // 批量删除
    agrRemovesfun() {
      this.$refs.aggrid.getSelectedData(selectList => {
        if (selectList.length === 0) {
          this.$message({
            message: this.$t('grid.others.pleaseSelectAData'),
            type: 'warning'
          })
          return
        }
        const delarr = selectList.map(item => item.sId)
        this.$confirm(this.$t('grid.others.thisActionDeletesThntinueKey'), this.$t('grid.others.prompt'), {
          confirmButtonText: this.$t('btns.confirm'),
          cancelButtonText: this.$t('btns.cancel'),
          type: 'warning'
        })
          .then(() => {
            console.log(delarr)
            detailagrRemoves(delarr).then((result) => {
              this.$message({
                message: this.$t('tips.deletedSuccessfully'),
                type: 'success'
              })
              this.addsettle('stop')
              this.$refs.aggrid.loadTableData()
            })
              .catch(() => { })
          })
      })
    },
    openPhyInvalidDialog() {
      this.phyDialogVisible = true
    },
    closePhyInvalidDialog() {
      this.addsettle('stop')
      this.phyDialogVisible = false
      this.onSearch()
    },
    openModifyDialog(type) {
      this.modifyType = type
      this.modifyIds = []
      this.$refs.aggrid.getSelectedData(selectList => {
        if (selectList.length > 0) {
          this.modifyForm.modifyPrice = null
          this.modifyDialog = true
          selectList.forEach(el => {
            this.modifyIds.push(el.sId)
          })
        } else {
          this.$message.warning(this.$t('grid.others.pleaseCheckTheBox'))
        }
      })
    },
    modifySubmit() {
      if (+this.modifyForm.modifyPrice <= 0) {
        this.$message.error('此次确认单价需大于0')
        return
      }
      if (this.modifyType === '1') {
        updatePriceForThird(
          {
            sId: this.invoiceId,
            price: this.modifyForm.modifyPrice
          },
          this.modifyIds
        ).then(() => {
          this.$refs.aggrid.loadTableData()
          this.$message.success(this.$t('grid.others.modifySuccessfully'))
          this.modifyDialog = false
        })
      } else {
        updateAmtForThird({
          sDetailIds: this.modifyIds,
          sSumPrice: this.modifyForm.modifyPrice
        }).then(() => {
          this.$refs.aggrid.loadTableData()
          this.$message.success(this.$t('grid.others.modifySuccessfully'))
          this.modifyDialog = false
        })
      }
    },
    cellValueChanged(params) {
      params.data._isEdit = true
    },
    onPasteEnd() {
      const list = this.rowData.filter(item => item._isEdit === true)
      console.log('黏贴修改：', JSON.parse(JSON.stringify(list)))
      updateBatchPriceForThird(
        {
          sId: this.invoiceId
        },
        list
      ).then(res => {
        this.$message.success(this.$t('grid.others.modifySuccessfully'))
      }).finally(() => {
        this.$refs.aggrid.reloadTableData()
      })
    },
    cellEditingStopped(params) {
      const { data, oldValue, newValue } = params
      console.log('修改后的数据：', JSON.parse(JSON.stringify(data)))
      if (oldValue !== newValue) {
        updateBatchPriceForThird(
          {
            sId: this.invoiceId
          },
          [data]
        ).then(res => {
          this.$message.success(this.$t('grid.others.modifySuccessfully'))
          this.$refs.aggrid.reloadTableData()
        }).finally(() => {
          this.$refs.aggrid.reloadTableData()
        })
      }
    }
  }
}
</script>
