
<template>
  <cnd-dialog
    v-if="dialogVisible"
    :title="$t('grid.btns.addSalesShipmentDetails')"
    append-to-body
    height="500"
    width="80%"
    :fullscreen="false"
    :visible.sync="dialogVisible"
    @close="handleClose"
  >
    <template slot="content">
      <!-- 搜索条件 -->
      <steel-search-form ref="searchForm" :form-items="formItems" @search="onSearch" />
      <auto-wrap>
        <steelTradeAggrid
          ref="aggrid"
          class="mt-10"
          :column-defs="columnDefs"
          :row-data="rowData"
          row-key="sId"
          :load-data="loadData"
          table-selection="multiple"
        />
      </auto-wrap>
    </template>
    <template slot="footer" class="dialog-footer">
      <el-button size="mini" @click="handleClose">{{ $t('btns.cancel') }}</el-button>
      <el-button type="primary" size="mini" @click="handleSelect">{{ $t('btns.confirm') }}</el-button>
    </template>
  </cnd-dialog>
</template>

<script>

import steelTradeAggrid from '@/components/steelTradeAggrid'
import {
  getDialogPurPhyInvoiceDetail
} from '@/api/invoice/purchase'
import {
  stockToDeliverydetailpage,
  onroadToDeliverydialogconfirm,
  stockOthersget
} from '@/api/logistics/saleDelivery/saleorder'
import agreement from '@/api/agreement'

import { SteelFormat } from 'cnd-horizon-utils'
export default {
  components: { steelTradeAggrid },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    invoiceId: {
      type: String,
      default: null
    }
  },
  data() {
    // const vm = this
    return {
      searchInfo: null,
      purInvoiceDetail: null,
      formItems: [{
        label: this.$t('grid.others.originalDocumentNumber'),
        value: 'vInvNo',
        customWidth: '8',
        type: 'elInput',
        placeholder: this.$t('grid.others.enterOriginalDocumentNumber')
      }, {
        label: this.$t('grid.title.salesContractNumber'),
        value: 'vInvNo',
        customWidth: '8',
        type: 'elInput',
        placeholder: this.$t('grid.others.pleaseEnterTheSalesContractNumber')
      }, {
        label: this.$t('grid.title.personnel'),
        value: 'sStaffId',
        type: 'cndInputDialogItem',
        option: { valueKey: 'sPath' },
        customWidth: '8',
        dialogType: 'staff',
        placeholder: this.$t('grid.others.pleaseSelectPersonnel')
      }],
      columnDefs: [
        {
          headerName: this.$t('grid.others.originalDocumentNumber'),
          field: 'sOriginalCode'
        },
        {
          headerName: this.$t('grid.others.itemNumberTag'),
          field: 'sProjectCode'
        },
        {
          headerName: this.$t('grid.title.quantity'),
          field: 'vCurContractQty',
          editable: true,
          cellEditor: 'TableInput',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            // vTaxAmt/vNetAmt
            return SteelFormat.formatPrice(params.value)
          }
        },
        {
          headerName: this.$t('grid.others.numberOfPiecesTag'),
          field: 'vCurQty',
          editable: true,
          cellEditor: 'TableInput',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return SteelFormat.formatPrice(params.value)
          }
        },
        {
          headerName: this.$t('grid.title.salesContractNumber'),
          field: 'sSaleContractCode'
        },
        {
          headerName: this.$t('grid.title.purchaseContractNumber'),
          field: 'sPurContractCode'
        },
        {
          headerName: this.$t('grid.others.item'),
          field: 'vGoodsDesc'
        },
        {
          headerName: this.$t('grid.others.steelCoilNumber'),
          field: 'sExtend4'
        },
        {
          headerName: this.$t('grid.others.carriageNumber'),
          field: 'sExtend5'
        },
        {
          headerName: this.$t('grid.title.unitPrice'),
          field: 'sVesselNo',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return SteelFormat.formatPrice(params.value)
          }
        },
        // {
        //   headerName: this.$t('grid.title.quantity'),
        //   field: 'vCurContractQty',
        //   editable: true,
        //   cellEditor: 'TableInput',
        //   cellStyle: { textAlign: 'right' },
        //   valueFormatter: (params) => {
        //     // vTaxAmt/vNetAmt
        //     return SteelFormat.formatPrice(params.value)
        //   }
        // },
        // {
        //   headerName: this.$t('grid.others.numberOfPiecesTag'),
        //   field: 'vCurQty',
        //   editable: true,
        //   cellEditor: 'TableInput',
        //   cellStyle: { textAlign: 'right' },
        //   valueFormatter: (params) => {
        //     return SteelFormat.formatPrice(params.value)
        //   }
        // },
        {
          headerName: this.$t('grid.title.accountingGroup'),
          field: 'vCheckGroupName'
        },
        {
          field: 'vDepartmentName',
          headerName: this.$t('grid.others.department')
        },
        {
          headerName: this.$t('grid.title.personnel'),
          field: 'vStaffName'
        }
      ],
      rowData: [],
      headerCount: '共0条，合计：数量0吨，不含税金额0元，税额0元，含税金额0元',
      footerCount: '已选0条，合计：数量0吨，不含税金额0元，税额0元，含税金额0元',
      selectOps: {}
    }
  },
  watch: {
    dialogVisible(newVal) {
      if (newVal) {
        this.$nextTick(() => {
          this.onSearch()
        })
      }
    },
    rowData: {
      deep: true,
      handler(newVal) {
        const details = newVal.filter(item => item._selected)
        if (details && details.length > 0) {
          const vCount = details.length
          let vSumQty = 0; let vSumAmt = 0; let vSumNetAmt = 0; let vSumVatAmt = 0
          details.forEach(el => {
            vSumQty += el.vLeftQty
            vSumNetAmt += el.sNetAmt
            vSumAmt += el.sVatAmt
            vSumVatAmt += el.sTaxAmt
          })
          vSumQty = SteelFormat.formatThousandthSign(vSumQty, 4)
          vSumNetAmt = SteelFormat.formatPrice(vSumNetAmt)
          vSumVatAmt = SteelFormat.formatPrice(vSumVatAmt)
          vSumAmt = SteelFormat.formatPrice(vSumAmt)

          this.handleCount(vCount, vSumAmt, vSumNetAmt, vSumQty, vSumVatAmt, 'footer')
        } else {
          this.handleCount(0, 0, 0, 0, 0, 'footer')
        }
      }
    }
  },
  beforeMount() {
    agreement.getDict(['dev.common.sheet.status']).then(result => {
      this.selectOps['dev.common.sheet.status'] = result.data[0].dicts // 状态
    })
    stockOthersget({ sInvoiceId: this.invoiceId }).then((res) => {
      this.purInvoiceDetail = res.data
      console.log(res)
    })
  },
  methods: {
    onSearch() {
      const searchInfo = this.$refs.searchForm.getSearchData()
      this.searchInfo = searchInfo
      this.$refs.aggrid.loadTableData()
    },
    handleCount(vCount = 0, vSumAmt = 0, vSumNetAmt = 0, vSumQty = 0, vSumVatAmt = 0, partFlag = 'header') {
      const countText = `${vCount}条，合计：数量${SteelFormat.formatThousandthSign(vSumQty, 4)}吨，不含税金额${SteelFormat.formatPrice(vSumNetAmt)}元，税额${SteelFormat.formatPrice(vSumVatAmt)}元，含税金额${SteelFormat.formatPrice(vSumAmt)}元`
      if (partFlag === 'header') {
        this.headerCount = `共${countText}`
      } else {
        this.footerCount = `${this.$t('components.selected')}${countText}`
      }
    },
    loadData(pagination) {
      return new Promise((resolve, reject) => {
        stockToDeliverydetailpage({ ...this.purInvoiceDetail, ...this.$refs.searchForm.getSearchData(), ...{ vIsCor: '0', sNoticeGoodsId: this.invoiceId }}, pagination).then(res => {
          this.rowData = res.data.content.map(item => {
            return {
              ...item,
              _selected: false
            }
          })
          // const { vCount, vSumAmt, vSumNetAmt, vSumQty, vSumVatAmt } = res.data
          // this.handleCount(vCount, vSumAmt, vSumNetAmt, vSumQty, vSumVatAmt)
          resolve(res.data.totalElements)
        }).catch(() => {
          reject([])
        })
      })
    },
    loadDetail(data) {
      return new Promise((resolve, reject) => {
        getDialogPurPhyInvoiceDetail({ sInvCode: data.sInvCode, sInvNo: data.sInvNo }).then(res => {
          resolve(res.data)
        }).catch(() => {
          reject([])
        })
      })
    },
    handleSelect() {
      // const list = this.$refs.aggrid.getSelectedData()
      const list = []
      this.rowData.map(item => {
        if (item._selected === true) {
          list.push(item)
        }
      })
      console.log(list)
      if (list.length > 0) {
        onroadToDeliverydialogconfirm(list, this.invoiceId).then(res => {
          this.$message.success(this.$t('tips.addedSuccessfully'))
          this.$emit('close')
        })
      }
    },
    handleClose() {
      this.$emit('close')
    }
  }
}
</script>
