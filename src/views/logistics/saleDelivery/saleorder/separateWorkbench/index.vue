<template>
  <!-- 采销分离发货工作台 -->
  <div class="page-container">
    <p class="page-title">采销分离发货工作台</p>
    <div class="flexV layout-content auto-page-title">
      <steel-search-form
        ref="searchForm"
        :form-items="formItems"
        @search="onSearch"
      />
      <el-tabs
        v-model="activeName"
        class="tabs-btn-position mt-10"
        @tab-click="tabClick"
      >
        <cnd-btn-position right="10">
          <!-- 采购到货 -->
          <span v-show="caigouBtn">
            <span class="mr-10">
              <el-button
                type="primary"
                size="mini"
                :disabled="!selectedData.length"
                @click="addToCartCg"
              >
                {{ $t('grid.btns.addSalesShipmentGenerated') }}
              </el-button>
            </span>
            <span>
              <el-button
                type="primary"
                size="mini"
                :disabled="!selectedData.length || zhifangDisabled"
                @click="createfun('cg', 30)"
              >
                {{ $t('grid.others.directRelease') }}
              </el-button>
              <el-button
                type="primary"
                size="mini"
                :disabled="!selectedData.length || CGDisabled"
                @click="createfun('cg', 10)"
              >
                {{ $t('grid.title.outbound') }}
              </el-button>
              <el-button
                type="primary"
                size="mini"
                :disabled="!selectedData.length || CGDisabled"
                @click="createfun('cg', 20)"
              >
                {{ $t('grid.others.transfer') }}
              </el-button>
              <el-dropdown>
                <el-button type="primary" size="mini" style="margin-left: 10px">
                  {{ $t('grid.others.importMatch') }}<i class="el-icon-arrow-down el-icon--right" />
                </el-button>
                <el-dropdown-menu
                  size="mini"
                  style="padding: 5px; float: right; margin-right: 14px"
                >
                  <el-dropdown-item
                    size="mini"
                    style="padding: 0 5px; line-height: 15px"
                  >
                    <importBtn
                      style="font-size: 12px; padding: 5px"
                      class=""
                      action="/api/separate/wb/stock/import/10"
                      action-success-url="/separate/wb/stock/importSuccess/10"
                      type="nonebut1"
                      :btn-text="$t('grid.others.OutboundImport')"
                      @success="onupesxfile"
                    />
                  </el-dropdown-item>
                  <el-dropdown-item style="padding: 0 5px; line-height: 15px">
                    <importBtn
                      style="font-size: 12px; padding: 5px"
                      type="nonebut2"
                      class=""
                      action="/api/separate/wb/stock/import/20"
                      action-success-url="/separate/wb/stock/importSuccess/20"
                      :btn-text="$t('grid.others.transferImport')"
                      @success="onupesxfile"
                    />
                  </el-dropdown-item>
                  <el-dropdown-item style="padding: 0 5px; line-height: 15px">
                    <importBtn
                      style="font-size: 12px; padding: 5px"
                      type="nonebut5"
                      class=""
                      action="/api/separate/wb/stock/import/30"
                      action-success-url="/separate/wb/stock/importSuccess/30"
                      btn-text="直放导入"
                      @success="onupesxfile"
                    />
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </span>
          </span>
          <!-- 在途货物 -->
          <span v-show="zaituBtn">
            <span class="mr-10">
              <el-button
                type="primary"
                size="mini"
                :disabled="!selectedData.length"
                @click="addToCart"
              >
                {{ $t('grid.btns.addSalesShipmentGenerated') }}
              </el-button>
            </span>
            <span>
              <el-button
                type="primary"
                size="mini"
                :disabled="ZTZFDisabled"
                @click="createfun('zt', 30)"
              >
                {{ $t('grid.others.directRelease') }}
              </el-button>
              <el-button
                type="primary"
                size="mini"
                :disabled="ZTCCDisabled"
                @click="createfun('zt', 10)"
              >
                {{ $t('grid.title.outbound') }}
              </el-button>
              <el-button
                type="primary"
                size="mini"
                :disabled="ZTCCDisabled"
                @click="createfun('zt', 20)"
              >
                {{ $t('grid.others.transfer') }}
              </el-button>
              <el-dropdown>
                <el-button type="primary" size="mini" style="margin-left: 10px">
                  {{ $t('grid.others.importMatch') }}<i class="el-icon-arrow-down el-icon--right" />
                </el-button>
                <el-dropdown-menu
                  size="mini"
                  style="padding: 5px; float: right; margin-right: 14px"
                >
                  <el-dropdown-item
                    size="mini"
                    style="padding: 0 5px; line-height: 15px"
                  >
                    <importBtn
                      style="font-size: 12px; padding: 5px"
                      class=""
                      action="/api/separate/wb/onRoad/import/10"
                      action-success-url="/separate/wb/onRoad/importSuccess/10"
                      type="nonebut11"
                      :btn-text="$t('grid.others.OutboundImport')"
                      @success="onupesxfile"
                    />
                  </el-dropdown-item>
                  <el-dropdown-item style="padding: 0 5px; line-height: 15px">
                    <importBtn
                      style="font-size: 12px; padding: 5px"
                      type="nonebut12"
                      class=""
                      action="/api/separate/wb/onRoad/import/20"
                      action-success-url="/separate/wb/onRoad/importSuccess/20"
                      :btn-text="$t('grid.others.transferImport')"
                      @success="onupesxfile"
                    />
                  </el-dropdown-item>
                  <el-dropdown-item
                    size="mini"
                    style="padding: 0 5px; line-height: 15px"
                  >
                    <importBtn
                      style="font-size: 12px; padding: 5px"
                      class=""
                      :action="'/api/separate/wb/onRoad/import/30'"
                      :action-success-url="'/separate/wb/onRoad/importSuccess/30'"
                      type="nonebut5"
                      btn-text="直放导入"
                      @success="onupesxfile"
                    />
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </span>
          </span>
          <!-- 钢厂可直放清单 -->
          <span v-show="zhifangBtn">
            <span class="mr-10">
              <el-button
                type="primary"
                size="mini"
                :disabled="!selectedData.length"
                @click="addToCartZf"
              >
                {{ $t('grid.btns.addSalesShipmentGenerated') }}
              </el-button>
            </span>
            <span>
              <el-button
                type="primary"
                size="mini"
                :disabled="!selectedData.length"
                @click="createfun('zf')"
              >
                {{ $t('grid.others.directRelease') }}
              </el-button>
              <importBtn
                class="mr-10 ml-10"
                :action="'/api/separate/wb/third/import'"
                :action-success-url="'/separate/wb/third/importSuccess'"
                :title="$t('excel.import')"
                btn-text="直放导入"
                @success="onupesxfile"
              />
            </span>
          </span>
          <!-- 直放合同发贷 -->
          <span v-show="fadaiBtn">
            <span class="mr-10">
              <el-button
                type="primary"
                size="mini"
                :disabled="!selectedData.length"
                @click="addToCartZfht"
              >
                {{ $t('grid.btns.addSalesShipmentGenerated') }}
              </el-button>
            </span>
            <span>
              <el-button
                type="primary"
                size="mini"
                :disabled="!selectedData.length"
                @click="createfun('zfht')"
              >
                {{ $t('grid.others.directRelease') }}
              </el-button>
            </span>
          </span>
        </cnd-btn-position>
        <el-tab-pane :label="$t('grid.others.purchaseArrival')" name="fourth">
          <div class="h100 flexV">
            <steelTradeAggrid
              v-if="activeName == 'fourth'"
              ref="aggrid"
              :column-defs="cgColumnDefs"
              :row-data="cgrowData"
              :load-data="cgloadData"
              :header-total="headerCount"
              :footer-total="footerCount"
              :auto-load-data="false"
              table-selection="multiple"
              row-key="sId"
              full-row-type="parent"
              @selectedChange="handleFooterCount"
            />
            <template v-if="activeName == 'fourth'">
              <cndShoppingCart
                ref="shoppingCart"
                :title="$t('grid.others.salesPendingShipmentDetail')"
                :submit-text="$t('grid.others.transfer')"
                row-key="sId"
                full-row-type="parent"
                :submit-disabled="CGDisabled"
                :is-subtable="false"
                :cur-config="shopCurConfig"
                :button-title="$t('grid.others.pendingSalesOrderGeneration')"
                :columns="arrivalshoppingCartColumn"
                :merge-config="shoppingCartMerge"
                :validate-key="validateKeyArrival"
                :count-list="shoppingCartCountList"
                @selectedChange="handleShoppingCartCount"
                @submit="submit($event,'cg', 20)"
              >
                <div slot="opeartions">
                  <el-button
                    type="primary"
                    size="small"
                    :disabled="shoparr.length == 0 || zhifangDisabled"
                    @click="submit('','cg', 30)"
                  >
                    直放
                  </el-button>
                  <el-button
                    type="primary"
                    size="small"
                    :disabled="shoparr.length == 0 || CGDisabled"
                    @click="submit('','cg', 10)"
                  >
                    出仓
                  </el-button>
                </div>
              </cndShoppingCart>
            </template>
          </div>
        </el-tab-pane>
        <el-tab-pane :label="$t('grid.others.goodsInTransit')" name="third">
          <div class="h100 flexV">
            <steelTradeAggrid
              v-if="activeName == 'third'"
              ref="aggrid"
              :column-defs="ztColumnDefs"
              :row-data="ztrowData"
              :load-data="ztloadData"
              :header-total="headerCount"
              :footer-total="footerCount"
              :auto-load-data="false"
              table-selection="multiple"
              row-key="sId"
              full-row-type="parent"
              @selectedChange="handleFooterCount($event, 'zt')"
            />
            <template v-if="activeName == 'third'">
              <cndShoppingCart
                ref="shoppingCart"
                :title="$t('grid.others.salesPendingShipmentDetail')"
                :submit-text="$t('grid.others.transfer')"
                :submit-disabled="ZTCCDisabled"
                row-key="sId"
                full-row-type="parent"
                :is-subtable="false"
                :cur-config="shopCurConfig"
                :button-title="$t('grid.others.pendingSalesOrderGeneration')"
                :columns="shoppingCartColumns"
                :merge-config="shoppingCartMerge"
                :validate-key="validateKeyTransit"
                :count-list="shoppingCartCountList"
                @selectedChange="handleShoppingCartCount"
                @submit="submit($event,'zt', 20)"
              >
                <div slot="opeartions">
                  <el-button
                    type="primary"
                    size="small"
                    :disabled="ZTZFDisabled"
                    @click="submit('','zt', 30)"
                  >
                    直放
                  </el-button>
                  <el-button
                    type="primary"
                    size="small"
                    :disabled="ZTCCDisabled"
                    @click="submit('','zt', 10)"
                  >
                    出仓
                  </el-button>
                </div>
              </cndShoppingCart>
            </template>
          </div>
        </el-tab-pane>
        <el-tab-pane :label="$t('grid.others.steelFactoryDirectReleaseList')" name="second">
          <div class="h100 flexV">
            <steelTradeAggrid
              v-if="activeName == 'second'"
              ref="aggrid"
              :column-defs="zfColumnDefs"
              :row-data="zfrowData"
              :load-data="zfloadData"
              :header-total="headerCount"
              :footer-total="footerCount"
              :auto-load-data="false"
              table-selection="multiple"
              row-key="sId"
              full-row-type="parent"
              @selectedChange="handleFooterCount($event, 'zf')"
            />
            <template v-if="activeName == 'second'">
              <cndShoppingCart
                ref="shoppingCart"
                :title="$t('grid.others.salesPendingShipmentDetail')"
                submit-text="直放"
                row-key="sId"
                full-row-type="parent"
                :is-subtable="false"
                :cur-config="zfshopCurConfig"
                :button-title="$t('grid.others.pendingSalesOrderGeneration')"
                :columns="zfshoppingCartColumns"
                :merge-config="zfshoppingCartMerge"
                :validate-key="validateKeyZf"
                :count-list="shoppingCartCountList"
                @selectedChange="handleShoppingCartCount"
                @submit="submit($event,'zf')"
              />
            </template>
          </div>
        </el-tab-pane>
        <el-tab-pane label="直放合同发货" name="fifth">
          <div class="h100 flexV">
            <steelTradeAggrid
              v-if="activeName == 'fifth'"
              ref="aggrid"
              :column-defs="zfhtcolumnDefs"
              :row-data="zfhtrowData"
              :load-data="zfhtloadData"
              :auto-load-data="false"
              table-selection="multiple"
              row-key="sCountId"
              full-row-type="parent"
              :header-total="headerCount"
              :footer-total="footerCount"
              @selectedChange="handleFooterCount($event, 'zfht')"
            />
            <template v-if="activeName == 'fifth'">
              <cndShoppingCart
                ref="shoppingCart"
                :title="$t('grid.others.salesPendingShipmentDetail')"
                submit-text="直放"
                row-key="sCountId"
                full-row-type="parent"
                :is-subtable="false"
                :button-title="$t('grid.others.pendingSalesOrderGeneration')"
                :columns="zfhtShoppingCartColumns"
                :validate-key="validateKeyZf"
                :count-list="shoppingCartCountList"
                @selectedChange="zfhtShoppingCartCount"
                @submit="submit($event,'zfht')"
              />
            </template>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
    <selectContract
      v-if="selectContractVisible"
      :dialog-visible="selectContractVisible"
      :selected-data="dialogData"
      :select-type="selectType"
      :out-type="outType"
      @close="selectContractVisible = false"
    />
  </div>
</template>
<script>
import {
  separateWbstock,
  separateWbonRoad,
  directWbdirect,
  separateOnRoadCreate,
  separateStockCreate,
  separateWbThird,
  separateWbstockSum,
  separateWbonRoadSum
} from '@/api/logistics/saleDelivery/separate'
import {
  stockdeliveryget,
  thirdToDeliverywbbut
} from '@/api/logistics/saleDelivery/saleorder'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import cndShoppingCart from '@/components/cndShoppingCart/index'
import importBtn from '@/components/importBtn'
import selectContract from './selectContract.vue'
import tableData from './tabledata.js'
import { SteelFormat } from 'cnd-horizon-utils'
import curMixin from '@/utils/curMixin'

export default {
  name: 'SeparateWorkbench',
  components: { steelTradeAggrid, cndShoppingCart, selectContract, importBtn },
  mixins: [tableData, curMixin],
  data() {
    return {
      searchInfo: null,
      selectedData: [],
      zhifangDisabled: true,
      CGDisabled: true,
      ZTZFDisabled: true,
      ZTCCDisabled: true,
      outType: null,
      selectType: null,
      formItems: [
        {
          label: this.$t('grid.others.logisticsOrderNumber'),
          value: 'sStockReceiptCode',
          type: 'elInput',
          placeholder: this.$t('grid.others.pleaseEnterTheLogisticBillNumber')
        },
        {
          label: '采购合同号',
          value: 'sPurContractCode',
          type: 'elInput',
          placeholder: '请输入采购合同号'
        },
        {
          label: '销售合同号',
          value: 'sSaleContractCode',
          type: 'elInput',
          placeholder: '请输入销售合同号'
        },
        {
          label: this.$t('grid.title.company'),
          value: 'sCompanyId',
          type: 'cndInputDialog',
          dialogType: 'company',
          placeholder: this.$t('grid.others.pleaseSelectCompany')
        },
        {
          label: this.$t('grid.others.itemNumberTag'),
          value: 'sProjectCode',
          type: 'elInput',
          placeholder: this.$t('grid.others.pleaseEnterTheItemNumberTag')
        },
        {
          label: this.$t('grid.others.warehouse'),
          value: 'sWarehouseId',
          type: 'cndInputDialogItem',
          defaultUrl: '/esc/warehouse/info/dialog/v2/page-dialog',
          option: { valueKey: 'sPath' },
          placeholder: this.$t('grid.others.pleaseSelectAWarehouse')
        },
        {
          label: this.$t('grid.others.carriageNumber'),
          value: 'sVesselNo',
          type: 'elInput'
        },
        {
          label: '钢卷号',
          value: 'sSteelNo',
          type: 'elInput'
        },
        {
          label: this.$t('grid.others.supplier'),
          value: 'sSupplierId',
          type: 'cndInputDialogItem',
          dialogType: 'customer',
          defaultUrl: '/esc/customer/page',
          option: { valueKey: 'sPath' },
          placeholder: this.$t('grid.others.pleaseSelectSupplier')
        },
        {
          label: this.$t('grid.title.accountingGroup'),
          value: 'sCheckGroupId',
          type: 'cndInputDialog',
          dialogType: 'cost',
          placeholder: this.$t('grid.others.pleaseSelectAccountingGroup')
        },
        {
          label: this.$t('grid.others.department'),
          value: 'sDepartmentId',
          type: 'cndInputDialog',
          dialogType: 'depart',
          placeholder: this.$t('grid.others.pleaseSelectTheDepartment')
        },
        {
          label: this.$t('grid.title.personnel'),
          value: 'sStaffId',
          type: 'cndInputDialogItem',
          option: { valueKey: 'sPath' },
          itemType: 'occultation',
          dialogType: 'staff',
          placeholder: this.$t('grid.others.pleaseSelectPersonnel')
        },
        {
          label: this.$t('grid.others.accountingFilter'),
          value: 'vIsCor',
          type: 'elSelect',
          itemType: 'occultation',
          default: '0',
          dict: 'dev.common.verify.finish.type',
          placeholder: this.$t('grid.others.pleaseSelectTheUnderwritingFilter')
        },
        {
          label: '品名',
          value: 'sArtName',
          type: 'elInput'
        },
        {
          label: this.$t('grid.others.customer'),
          value: 'sCustomerId',
          type: 'cndInputDialogItem',
          dialogType: 'customer',
          defaultUrl: '/esc/customer/page',
          option: { valueKey: 'sPath' }
        },
        {
          label: '经营单位',
          value: 'sManagementId',
          type: 'cndInputDialog',
          dialogType: 'escOrg',
          itemType: 'occultation'
        },
        {
          label: this.$t('grid.title.createdAt'),
          value: ['sCreateTime', 'vCreateTimeTo'],
          placeholder: [this.$t('grid.others.pleaseSelectTheDate'), this.$t('grid.others.pleaseSelectTheDate')],
          itemType: 'occultation',
          unlinkPanels: true,
          type: 'elDatePicker'
        },
        {
          label: '作业平台',
          value: 'sOperaPlat',
          type: 'elSelect',
          dict: 'opera.plat.value',
          placeholder: this.$t('components.pleaseSelect')
        }
      ],
      shoppingCartMerge: [
        {
          field: 'sCurQtx',
          mode: 'cover',
          max: 'sLeftQtx'
        },
        {
          field: 'sCurQty',
          mode: 'cover',
          max: 'sLeftQty'
        }
      ],
      shopCurConfig: {
        vCurQty: 'sCurQtx',
        vCurPkgQty: 'sCurQty',
        vLeftQty: 'sLeftQtx',
        vLeftPkgQty: 'sLeftQty'
      },
      zfshopCurConfig: {
        vCurQty: 'vCurContractQty',
        vCurPkgQty: 'vCurQty',
        vLeftQty: 'vLeftQty',
        vLeftPkgQty: 'vLeftPkgQty'
      },
      zfshoppingCartMerge: [
        {
          field: 'vCurContractQty',
          mode: 'cover',
          max: 'vLeftQty'
        },
        {
          field: 'vCurQty',
          mode: 'cover',
          max: 'vLeftPkgQty'
        }
      ],
      shoppingCartCountList: [],
      cgrowData: [],
      ztrowData: [],
      zfrowData: [],
      zfhtrowData: [],
      headerCount: null,
      footerCount: null,
      activeName: 'fourth',
      shoparr: [],
      selectContractVisible: false,
      dialogData: [],
      validateKeyArrival: ['sCompanyId', 'sSupplierId', 'sWarehouseId'],
      validateKeyTransit: [
        'sCompanyId',
        'sSupplierId',
        'sWarehouseId'
      ],
      validateKeyZf: [
        'sCompanyId',
        'sSupplierId',
        'sWarehouseId'
      ]
    }
  },
  computed: {
    caigouBtn() {
      return this.activeName === 'fourth'
    },
    zaituBtn() {
      return this.activeName === 'third'
    },
    zhifangBtn() {
      return this.activeName === 'second'
    },
    fadaiBtn() {
      return this.activeName === 'fifth'
    }
  },
  mounted() {
    this.onSearch(false)
  },
  methods: {
    onSearch(load = true) {
      const searchInfo = this.$refs.searchForm.getSearchData()
      this.searchInfo = searchInfo
      if (load) {
        this.$refs.aggrid.loadTableData()
      }
    },
    tabClick() {
      this.headerCount = null
      this.footerCount = null
      this.cgrowData = []
      this.ztrowData = []
      this.zfhtrowData = []
      this.zfrowData = []
    },
    cgloadData(pagination = { page: 0, limit: 20 }) {
      return new Promise((resolve, reject) => {
        separateWbstock(
          pagination,
          {
            ...this.searchInfo
          }
        )
          .then((res) => {
            console.log(12345, res)
            this.cgrowData = res.data.page.content.map((item) => {
              item._selected = false
              return item
            })
            const vCount = res.data.page.totalElements
            separateWbstockSum(this.searchInfo).then(v => {
              const { vSumLeftQtx, vSumLeftQty } = v.data
              this.setCount(vCount, vSumLeftQtx, vSumLeftQty, 'headerCount')
            })
            resolve(res.data.page)
          })
          .catch(() => {
            reject(0)
          })
      })
    },
    ztloadData(pagination = { page: 0, limit: 20 }) {
      return new Promise((resolve, reject) => {
        separateWbonRoad(
          pagination,
          {
            ...this.searchInfo
          }
        )
          .then((res) => {
            console.log(12345, res)
            this.ztrowData = res.data.page.content.map((item) => {
              item._selected = false
              return item
            })
            const vCount = res.data.page.totalElements
            separateWbonRoadSum(this.searchInfo).then(v => {
              const { vSumLeftQtx, vSumLeftQty } = v.data
              this.setCount(vCount, vSumLeftQtx, vSumLeftQty, 'headerCount')
            })
            resolve(res.data.page)
          })
          .catch(() => {
            reject(0)
          })
      })
    },
    zfloadData(pagination = { page: 0, limit: 20 }) {
      return new Promise((resolve, reject) => {
        separateWbThird(
          pagination,
          {
            ...this.searchInfo
          }
        )
          .then((res) => {
            this.zfrowData = res.data.page.content.map((item) => {
              item._selected = false
              return item
            })
            const vCount = res.data.page.totalElements
            this.setCount(vCount, res.data.vSumLeftQtx, res.data.vSumLeftQty, 'headerCount')
            resolve(res.data.page.totalElements)
          })
          .catch(() => {
            reject(0)
          })
      })
    },
    zfhtloadData(pagination = { page: 0, limit: 20 }) {
      return new Promise((resolve, reject) => {
        directWbdirect(
          pagination,
          {
            ...this.searchInfo
          }
        )
          .then((res) => {
            this.zfhtrowData = res.data.page.content.map((item) => {
              item._selected = false
              return item
            })
            const vCount = res.data.page.totalElements
            this.setCount(vCount, res.data.vSumLeftQtx, 0, 'headerCount', 'zfht')
            resolve(res.data.page.totalElements)
          })
          .catch(() => {
            reject(0)
          })
      })
    },
    handleFooterCount(e, type) {
      this.zhifangDisabled = true
      this.ZTZFDisabled = true
      this.ZTCCDisabled = true
      if (type === 'zt') {
        this.$refs.aggrid.getSelectedData(res => {
          if (res.length > 0) {
            this.ZTZFDisabled = !res.every(el => el.sWarehouseType === '30' && el.sCustomType === '20')
            this.ZTCCDisabled = !res.every(el => el.sCustomType === '10' || el.sIsZeroStock === '1')
          }
        })
      }
      this.$refs.aggrid.getSelectedData((res) => {
        this.selectedData = res || []
        if (res.length > 0) {
          this.zhifangDisabled = !res.every((el) => el.sIsNullStockType === true)
          this.CGDisabled = !res.every((el) => el.sIsNullStockType === false)
        }
        const vCount = res.length
        let vSumQty = 0
        let vSumQtx = 0

        if (type === 'zfht') {
          res.forEach((el) => {
            vSumQty += Number(el.sLeftQtx) || 0
          })
        } else if (type === 'zf') {
          res.forEach((el) => {
            vSumQty += Number(el.vCurContractQty) || 0
            vSumQtx += Number(el.vCurQty) || 0
          })
        } else {
          res.forEach((el) => {
            vSumQty += Number(el.sCurQtx) || 0
            vSumQtx += Number(el.sCurQty) || 0
          })
        }
        this.setCounts(vCount, vSumQty, vSumQtx, 'footerCount', type)
      })
    },
    setCount(vCount = 0, vSumQty = 0, vSumQtx = 0, flag, type) {
      if (type === 'zfht') {
        this[flag] = [
          { count: vCount, key: 'count' },
          {
            title: this.$t('grid.others.remainingQuantity'),
            count: SteelFormat.formatThousandthSign(vSumQty, 4),
            unit: this.$t('grid.others.ton')
          }
        ]
      } else {
        this[flag] = [
          { count: vCount, key: 'count' },
          {
            title: this.$t('grid.others.remainingQuantity'),
            count: SteelFormat.formatThousandthSign(vSumQty, 4),
            unit: this.$t('grid.others.ton')
          },
          {
            title: this.$t('grid.others.remainingPieces'),
            count: SteelFormat.formatThousandthSign(vSumQtx, 0),
            unit: this.$t('grid.others.pieces')
          }
        ]
      }
      console.log(this[flag])
    },
    setCounts(vCount = 0, vSumQty = 0, vSumQtx = 0, flag, type) {
      if (type === 'zfht') {
        this[flag] = [
          { count: vCount, key: 'count' },
          {
            title: this.$t('grid.others.remainingQuantity'),
            count: SteelFormat.formatThousandthSign(vSumQty, 4),
            unit: this.$t('grid.others.ton')
          }
        ]
      } else {
        this[flag] = [
          { count: vCount, key: 'count' },
          {
            title: this.$t('grid.others.quantityConfirmedThisTime'),
            count: SteelFormat.formatThousandthSign(vSumQty, 4),
            unit: this.$t('grid.others.ton')
          },
          {
            title: this.$t('grid.others.theNumberOfConfirmedPieces'),
            count: SteelFormat.formatThousandthSign(vSumQtx, 0),
            unit: this.$t('grid.others.pieces')
          }
        ]
      }
      console.log(this[flag])
    },
    // 购物车事件
    handleShoppingCartCount(list) {
      let sCurQtxSum = 0
      let sCurQtySum = 0
      const details = list.filter((item) => item._selected)
      const vCount = details.length
      details.forEach((el) => {
        sCurQtxSum += Number(el.sCurQtx)
        sCurQtySum += Number(el.sCurQty)
      })
      this.setCountshop(
        vCount,
        sCurQtxSum,
        sCurQtySum,
        'shoppingCartCountList'
      )
      this.shoparr = list
    },
    zfhtShoppingCartCount(list) {
      let sCurQtxSum = 0
      const details = list.filter((item) => item._selected)
      const vCount = details.length
      details.forEach((el) => {
        sCurQtxSum += Number(el.sLeftQtx)
      })
      this.shoppingCartCountList = [
        { count: vCount, key: 'count' },
        {
          title: this.$t('grid.title.quantity'),
          count: SteelFormat.formatThousandthSign(sCurQtxSum, 4),
          unit: this.$t('grid.others.ton')
        }
      ]
      this.shoparr = list
    },
    // 在途购物车加入销售购物车
    addToCart() {
      this.$refs.aggrid.gridApi.stopEditing()
      this.$refs.aggrid.getSelectedData((list) => {
        const iszero = list.every((item) => item.sCurQtx > 0)
        const operaPlat = list.some((item) => item.sOperaPlat === '20')
        if (!iszero) {
          this.$message.error('本次确认数量必须大于0且件数必须大于或等于0!')
          return
        }
        if (operaPlat) {
          this.$message.error('买家作业销售合同不允许生成单据!')
          return
        }
        this.$refs.shoppingCart.addToCart(list)
      })
    },
    addToCartZf() {
      this.$refs.aggrid.gridApi.stopEditing()
      this.$refs.aggrid.getSelectedData((list) => {
        const iszero = list.every((item) => item.vCurContractQty > 0)
        const operaPlat = list.some((item) => item.sOperaPlat === '20')
        if (!iszero) {
          this.$message.error('本次确认数量必须大于0且件数必须大于或等于0!')
          return
        }
        if (operaPlat) {
          this.$message.error('买家作业销售合同不允许生成单据!')
          return
        }
        this.$refs.shoppingCart.addToCart(list)
      })
    },
    addToCartZfht() {
      this.$refs.aggrid.gridApi.stopEditing()
      this.$refs.aggrid.getSelectedData((list) => {
        this.$refs.shoppingCart.addToCart(list)
      })
    },
    addToCartCg() {
      this.$refs.aggrid.gridApi.stopEditing()
      this.$refs.aggrid.getSelectedData((list) => {
        const iszero = list.every((item) => item.sCurQtx > 0)
        const operaPlat = list.some((item) => item.sOperaPlat === '20')
        if (!iszero) {
          this.$message.error('本次确认数量必须大于0且件数必须大于或等于0!')
          return
        }
        if (operaPlat) {
          this.$message.error('买家作业销售合同不允许生成单据!')
          return
        }
        this.$refs.shoppingCart.addToCart(list)
      })
    },
    setCountshop(vCount = 0, sCurQtx = 0, sCurQty = 0, flag) {
      this[flag] = [
        { count: vCount, key: 'count' },
        {
          title: this.$t('grid.title.quantity'),
          count: SteelFormat.formatThousandthSign(sCurQtx, 4),
          unit: this.$t('grid.others.ton')
        },
        {
          title: this.$t('grid.others.numberOfPiecesTag'),
          count: SteelFormat.formatThousandthSign(sCurQty, 0),
          unit: this.$t('grid.others.pieces')
        }
      ]
    },
    createfun(type, outType) {
      this.$refs.aggrid.stopChildEditing()
      this.$refs.aggrid.getSelectedData((selectList) => {
        if (selectList.length > 0) {
          if (type === 'cg' || type === 'zt') {
            if (!this.validateData(selectList, ['sCompanyId', 'sSupplierId', 'sWarehouseId'])) {
              return false
            }
          }
          if (type === 'zfht') {
            if (!this.validateData(selectList, ['sCompanyId', 'sSupplierId'])) {
              return false
            }
          }
          if (type === 'zfht') {
            this.selectContractVisible = true
            this.selectType = 'zfht'
            this.dialogData = this.selectedData
            return
          }
          if (type === 'zf') {
            this.createZfSeparate(selectList)
            return
          }
          const allSame = this.arePurContractCodesSame(selectList)
          if (allSame === 'allNull') {
            this.selectContractVisible = true
            this.dialogData = this.selectedData
            this.selectType = type
            this.outType = outType
          } else if (allSame) {
            const api = type === 'zt' ? separateOnRoadCreate : separateStockCreate
            api(selectList, outType).then(res => {
              console.log('vzt', res)
              this.$message.success(this.$t('tips.addedSuccessfully'))
              this.$router.push({
                path: `/salesdeliveryDialog/${res.data.sId}`,
                query: {
                  Id: res.data.sId,
                  status: res.data.sSheetStatus,
                  type: 'edit',
                  name: `${this.$t('grid.others.salesOrder')}【${
                    res.data.sCode
                  }】`,
                  activeId: localStorage.getItem('menuId')
                }
              })
            })
          } else {
            this.$message.error(this.$t('多个销售合同出仓，不可选择包含空销售合同数据！'))
          }
        } else {
          this.$message.warning('请选择至少一项')
        }
      })
    },
    /**
     * 校验需一致字段
     * @param {Array} 选中数据
     * @return {Boolean} 是否校验通过
     */
    validateData(selectList, validateKey) {
      const errorDict = {
        'sCompanyId': this.$t('grid.title.company'),
        'sSupplierId': this.$t('grid.others.supplier'),
        'sWarehouseId': this.$t('grid.others.warehouse')
      }

      const errorList = []
      selectList.forEach(prod => {
        validateKey.forEach(key => {
          if (prod[key] !== selectList[0][key]) {
            if (!errorList.includes(key)) {
              errorList.push(key)
            }
          }
        })
      })

      if (errorList.length) {
        this.$message({
          type: 'error',
          message: errorList.reduce((prev, next, index) => {
            prev = prev + `${errorDict[next]}、`
            if (index + 1 === errorList.length) {
              prev = prev.substr(0, prev.length - 1) + this.$t('grid.others.inconsistentPleaseSelectAgain')
            }
            return prev
          }, '')
        })
        return false
      } else {
        return true
      }
    },
    createZfSeparate(list) {
      this.$refs.aggrid.stopChildEditing()
      this.$refs.aggrid.gridApi.clearFocusedCell()
      thirdToDeliverywbbut(list).then(res => {
        console.log('res', res)
        this.$message.success(this.$t('tips.addedSuccessfully'))
        this.$router.push({
          path: `/salesdeliveryDialog/${res.data.sId}`,
          query: {
            Id: res.data.sId,
            status: res.data.sSheetStatus,
            type: 'edit',
            name: `${this.$t('grid.others.salesOrder')}【${
              res.data.sCode
            }】`,
            activeId: localStorage.getItem('menuId')
          }
        })
      })
    },
    arePurContractCodesSame(selectList) {
      const hasEmptySaleContractCode = selectList.every(item => item.sSaleContractCode !== null)
      if (hasEmptySaleContractCode) {
        return true
      }
      const filteredData = selectList.filter(item => item.sSaleContractCode)
      if (filteredData.length === 0) {
        return 'allNull'
      }
      const firstCode = filteredData[0].sSaleContractCode
      return filteredData.every(item => item.sSaleContractCode === firstCode)
    },
    submit(list, name, outType) {
      console.log(111, list, name)
      if (list === '') {
        const data = this.$refs.shoppingCart
          .getCartData()
          .filter((row) => row._selected)
        this.shoparr = data
        list = data
        console.log(222, this.shoparr, list)
      }
      if (!list.length) {
        this.$message.warning('请选择至少一项')
        return
      }
      if (name === 'zf') {
        this.createZfSeparate(list)
        return
      }
      if (name === 'zfht') {
        this.selectContractVisible = true
        this.selectType = 'zfht'
        this.dialogData = list
        return
      }
      const allSame = this.arePurContractCodesSame(list)
      if (allSame === 'allNull') {
        this.selectContractVisible = true
        this.dialogData = list
        this.selectType = name
        this.outType = outType
      } else if (allSame) {
        const api = name === 'zt' ? separateOnRoadCreate : separateStockCreate
        api(list, outType).then(res => {
          console.log('vzt', res)
          this.$message.success(this.$t('tips.addedSuccessfully'))
          this.$router.push({
            path: `/salesdeliveryDialog/${res.data.sId}`,
            query: {
              Id: res.data.sId,
              status: res.data.sSheetStatus,
              type: 'edit',
              name: `${this.$t('grid.others.salesOrder')}【${
                res.data.sCode
              }】`,
              activeId: localStorage.getItem('menuId')
            }
          })
        })
      } else {
        this.$message.error(this.$t('多个销售合同出仓，不可选择包含空销售合同数据！'))
      }
    },
    onupesxfile(v) {
      stockdeliveryget({ sInvoiceId: v.data.sId }).then((res) => {
        this.$router.push({
          path: `/salesdeliveryDialog/${res.data.sId}`,
          query: {
            Id: res.data.sId,
            status: res.data.sSheetStatus,
            type: 'edit',
            name: `${this.$t('grid.others.salesOrder')}【${res.data.sCode}】`,
            activeId: localStorage.getItem('menuId')
          }
        })
        this.$emit('hidebody', res.data.sId)
      })
    }
  }
}
</script>
<style>
</style>
