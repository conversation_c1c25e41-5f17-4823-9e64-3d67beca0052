<template>
  <div>
    <cnd-dialog
      :title="$t('grid.tabs.basicInformation')"
      :visible="true"
      @close="handleClose"
    >
      <template slot="leftBtn">
        <el-button
          v-has:esc_mall_retail_detail_submit
          type="primary"
          size="mini"
          :disabled="isBusinessDisabled('submit', sSheetStatus) || isDisabledByDispatch"
          @click="subPurInvoice"
        >{{ $t('grid.others.submit') }}</el-button>
        <el-button
          v-has:esc_mall_retail_detail_delete
          type="danger"
          size="mini"
          :disabled="isBusinessDisabled('remove', sSheetStatus) || isDisabledByDispatch"
          @click="delPurInvoice"
        >{{ $t('btns.delete') }}</el-button>
        <!-- <el-button
          type="warning"
          size="mini"
          :disabled="isBusinessDisabled('withdraw',sSheetStatus)"
          @click="revPurInvoice"
        >{{$t('grid.others.withdrawRequest')}}</el-button> -->
        <el-button
          v-has:esc_mall_retail_detail_withdraw
          type="warning"
          size="mini"
          :disabled="eWithdrawDisabled || isDisabledByDispatch || mallIsSigned"
          @click="withdraw"
        >撤销申请</el-button>
        <template v-if="purInvoiceDetail.isAbleToRevokeDelivery === '1'">
          <el-button
            v-has:esc_mall_retail_detail_revoke
            type="warning"
            size="mini"
            :disabled="isBusinessDisabled('submit', sSheetStatus)"
            @click="mallWithdraw"
          >配送撤销</el-button>
        </template>
        <el-button
          v-has:esc_mall_retail_detail_file
          type="primary"
          size="mini"
          :disabled="isBusinessDisabled('annex', sSheetStatus) || isDisabledByDispatch"
          @click="visibleDialog.annex = true"
        >{{ $t('btns.attachmentManagement') }}</el-button>
        <el-button
          v-has:esc_mall_retail_detail_approval
          type="primary"
          size="mini"
          :disabled="isBusinessDisabled('approval', sSheetStatus) || isDisabledByDispatch"
          @click="visibleDialog.approvaldia = true"
        >{{ $t('grid.others.approvalStatus') }}</el-button>
        <el-button
          v-has:esc_mall_retail_detail_approval_N8
          type="primary"
          size="mini"
          :disabled="isBusinessDisabled('approval', sSheetStatus) || isDisabledByDispatch"
          @click="visibleDialog.approvaldiaN8 = true"
        >ERP{{ $t('grid.others.approvalInquiry') }}</el-button>
        <template v-if="showRemarkBtn">
          <el-button
            v-has:esc_mall_retail_detail_remark_modify
            :disabled="modifyRemarkDisabled"
            type="primary"
            size="mini"
            @click="visibleDialog.modifyDialog = true"
          >
            修改备注
          </el-button>
        </template>
      </template>
      <template slot="content">
        <el-tabs
          v-if="dialogVisible"
          v-model="activeName"
          class="tabs-btn-position"
          :before-leave="beforeLeave"
        >
          <el-tab-pane :label="$t('grid.tabs.basicInformation')" name="0">
            <div>
              <cnd-btn-position top="7" right="10">
                <el-button
                  v-has:esc_mall_retail_detail_save
                  type="primary"
                  size="mini"
                  :disabled="isBusinessDisabled('save', sSheetStatus) || isDisabledByDispatch"
                  @click="savePurInvoice()"
                >{{ $t('btns.save') }}</el-button>
              </cnd-btn-position>
            </div>
            <baseInfo
              v-if="dialogVisible"
              :id="invoiceId"
              ref="basic"
              :info="purInvoiceDetail"
            />
          </el-tab-pane>
          <el-tab-pane :label="$t('grid.tabs.commodityDetails')" name="1">
            <phytihInfo
              v-if="dialogVisible && activeName == '1'"
              ref="phytihInfo"
              :type="purInvoiceDetail.sLogisticsType"
              :invoice-id="invoiceId"
              :pur-invoice-detail="purInvoiceDetail"
              @tabs="tabsqh"
              @updata="updata"
            />
          </el-tab-pane>
          <el-tab-pane :label="$t('grid.tabs.settlementInformation')" name="2">
            <invalidInfo
              v-if="dialogVisible && invalidInfoShow && activeName === '2'"
              :info="purInvoiceDetail"
              :invoice-id="invoiceId"
            />
          </el-tab-pane>
        </el-tabs>
      </template>
    </cnd-dialog>
    <!-- 弹出层 -->
    <horizon-approval-dialog
      :id="invoiceId"
      :solt-btn="false"
      :visible.sync="visibleDialog.approvaldia"
      @handleClose="handleCloseApproval"
    />
    <horizon-approval-dialog
      :id="invoiceId"
      :type="'n8'"
      :solt-btn="false"
      :visible.sync="visibleDialog.approvaldiaN8"
      @handleClose="visibleDialog.approvaldiaN8 = false"
    />
    <steelAnnexDialog
      :visible="visibleDialog.annex"
      append-to-body
      :biz-id="invoiceId"
      :upload-option="{ sIsAuto: '0' }"
      :disabled-btn="{
        scan: isBusinessDisabled('save', sSheetStatus) || isDisabledByDispatch,
        del: isBusinessDisabled('save', sSheetStatus) || isDisabledByDispatch
      }"
      :option="
        sSheetStatus !== '70'
          ? { vNotEqualUserDefine: 'FHDFJ,00,JSX_GZ' }
          : { vNotEqualUserDefine: '00,JSX_GZ' }
      "
      @onSelect="visibleDialog.annex = false"
    />
    <AnnexDialog
      :biz-id="invoiceId"
      :row-data="AnnexRowData"
      :visible="visibleDialog.annex2"
      :title="AnnexTitle"
      append-to-body
      @onSelect="visibleDialog.annex2 = false"
    />
    <submitDialog
      :id="invoiceId"
      :visible="visibleDialog.submitDialog"
      :api-url="`/esc/stock/delivery/third/desc/${invoiceId}`"
      @handleClose="visibleDialog.submitDialog = false"
      @onSure="onSubmitPurstock"
    />
    <checkDialog
      :visible="visibleDialog.check"
      :params="ejqParams"
      @close="visibleDialog.check = false"
      @success="checkSuccess"
    />
    <formDialog
      :visible="visibleDialog.modifyDialog"
      :form-items="formDialogItems"
      width="350px"
      height="60px"
      title="修改备注"
      @close="visibleDialog.modifyDialog = false"
      @success="onSuccess"
    />
  </div>
</template>
<script>
import baseInfo from './baseInfo/index'
import phytihInfo from './phytihInfo/index'
import invalidInfo from './invalidInfo/index'
import businessMixin from '@/utils/businessMixin'
import AnnexDialog from './AnnexDialog/index.vue'
import steelAnnexDialog from '@/components/steelAnnexDialog'
import submitDialog from '@/components/submitDialog'
import checkDialog from '@/components/checkEJQDialogV2/index'
import {
  stockdeliveryget,
  updataPurstock,
  submitPurstock,
  submitNewCheck,
  submitNewCheckDialog,
  checkAnnexExist,
  revokePurstock,
  commonsettlecreate,
  settlecommonpage,
  submitCheckRetail
} from '@/api/logistics/saleDelivery/retailDelivery'
import { retailMallDelete, retailMallCancel, retailMallRevoke, modifyRemark } from '@/api/logistics/saleDelivery/retailMall'
import { createPurInvoice } from '@/api/invoice/purchase.js'
import formDialog from '@/components/formDialog'

export default {
  name: 'MallRetailOrderDetail',
  components: {
    baseInfo,
    phytihInfo,
    invalidInfo,
    AnnexDialog,
    steelAnnexDialog,
    submitDialog,
    checkDialog,
    formDialog
  },
  mixins: [businessMixin],
  props: {},
  data() {
    return {
      dialogVisible: false,
      invoiceId: null,
      visibleDialog: {
        annex: false,
        approvaldiaN8: false,
        approvaldia: false,
        annex2: false,
        submitDialog: false,
        check: false,
        modifyDialog: false
      },
      signInfoVoList: [],
      invalidInfoShow: true,
      purInvoiceDetail: {},
      oldDetail: null,
      activeName: '0',
      AnnexTitle: '',
      AnnexRowData: null,
      ejqParams: {},
      formDialogItems: [
        {
          label: '备注',
          value: 'sRemark',
          type: 'elInputTextArea',
          customWidth: 22,
          required: true,
          errorMessage: this.$t('components.pleaseEnter')
        }
      ]
    }
  },
  computed: {
    sSheetStatus() {
      return this.purInvoiceDetail.sSheetStatus
    },
    eWithdrawDisabled() {
      const { sSheetStatus, sExtend16, sSheetType } = this.purInvoiceDetail
      return !(sSheetStatus === '30' && sExtend16 === '20' && sSheetType === '130')
    },
    isDisabledByDispatch() {
      const { sIsShowDispatchingFields, sExtend48, sSheetStatus } = this.purInvoiceDetail
      return sIsShowDispatchingFields === '1' && sExtend48 === '1' && sSheetStatus === '15'
    },
    showRemarkBtn() {
      const { sExtend29, sSheetStatus } = this.purInvoiceDetail
      return sExtend29 === '20' && sSheetStatus === '70'
    },
    modifyRemarkDisabled() {
      const { sSheetStatus, isExistSignReceipt } = this.purInvoiceDetail
      return !(sSheetStatus === '70' && isExistSignReceipt === '0')
    },
    mallIsSigned() {
      const { sExtend50, sExtend29 } = this.purInvoiceDetail
      return sExtend50 === '1' && sExtend29 === '20'
    }
  },
  watch: {
    activeName(newVal, oldVal) {
      if (newVal === '2') {
        this.invalidInfoShow = false
        if (
          this.purInvoiceDetail.sSheetStatus === '10' ||
          this.purInvoiceDetail.sSheetStatus === '11' ||
          this.purInvoiceDetail.sSheetStatus === '15'
        ) {
          settlecommonpage({
            sInvoiceId: this.invoiceId,
            limit: 30,
            page: 0
          }).then((res) => {
            if (res.data.content.length <= 0) {
              commonsettlecreate(this.invoiceId).then((e) => {
                this.invalidInfoShow = true
              })
            } else {
              this.invalidInfoShow = true
            }
          })
        } else {
          this.invalidInfoShow = true
        }
      }
    }
  },
  created() {
    this.invoiceId = this.$route.query.Id
    if (this.invoiceId) {
      this.dialogVisible = true
    }
  },
  mounted() {
    this.initFn()
  },
  methods: {
    async beforeLeave(activeName, oldActiveName) {
      let blFlag = false
      if (activeName === '0') {
        this.initFn()
        blFlag = true
      }
      if (
        oldActiveName === '0' &&
        !this.isBusinessDisabled('save', this.sSheetStatus)
      ) {
        const flag = new Promise((resolve, reject) => {
          this.$refs.basic.arrivalModify(async(form) => {
            if (!form) {
              reject(false)
              return
            } else {
              if (this.oldDetail === JSON.stringify(this.purInvoiceDetail)) {
                resolve(true)
                return
              }
              const api = this.invoiceId ? updataPurstock : createPurInvoice
              const res = await api(form)
              if (res.code === '0000') {
                this.$message.success(this.$t('tips.saveSuccess'))
                resolve(true)
              } else {
                reject(false)
              }
            }
          })
        })
        blFlag = await flag
      }
      return blFlag
    },
    tabsqh() {
      this.activeName = '2'
    },
    initFn(updataPhy) {
      console.log('initFn------------: ')
      return new Promise((resolve, reject) => {
        if (this.invoiceId) {
          stockdeliveryget({ sInvoiceId: this.invoiceId }).then((res) => {
            if (res.data) {
              res.data.sReceiver = res.data.escCustomerReceiver.sReceiver
              res.data.sTel = res.data.escCustomerReceiver.sTel
              res.data.sRelation = res.data.escCustomerReceiver.sRelation
              res.data.sPostalAddress = res.data.escCustomerReceiver.sPostalAddress
              res.data.sEmail = res.data.escCustomerReceiver.sEmail
              res.data.sFax = res.data.escCustomerReceiver.sFax
            }
            this.purInvoiceDetail = res.data
            this.oldDetail = JSON.stringify(res.data)
            updataPhy && this.$refs.phytihInfo?.onSearch()
            resolve(true)
          }).catch(() => {
            reject()
          })
        }
      })
    },
    savePurInvoice(type = 'save') {
      return new Promise((resolve, reject) => {
        this.$refs.basic.arrivalModify((form) => {
          if (form) {
            if (type === 'submit' && this.oldDetail === JSON.stringify(this.purInvoiceDetail)) {
              resolve(true)
              return
            }
            const api = this.invoiceId ? updataPurstock : createPurInvoice
            api(form)
              .then(async(res) => {
                this.$message.success(this.$t('tips.saveSuccess'))
                await this.initFn()
                resolve(true)
              })
              .catch(() => {
                reject(false)
              })
          } else {
            reject(false)
          }
        }
        )
      })
    },
    async subPurInvoice() {
      if (!this.purInvoiceDetail.sOutType) {
        this.$message.error(
          this.$t('grid.others.theWayOutOfTheWarehBeEmptyKey')
        )
        return
      }
      let saveDone = true
      saveDone = await this.savePurInvoice('submit')
      if (saveDone) {
        this.signInfoVoList = []
        submitNewCheck({ sInvoiceId: this.invoiceId }).then(async(res) => {
          if (res.code === '0000') {
            this.visibleDialog.submitDialog = true

            // if (this.purInvoiceDetail.sLogisticsType === '10') {
            //   await this.checkSubmit()
            // }
            // if (this.purInvoiceDetail.sCustomerSignId || this.purInvoiceDetail.sOriCustomerSignId) {
            //   this.$confirm('是否发起E建签回签？', this.$t('grid.others.prompt'), {
            //     distinguishCancelAndClose: true,
            //     confirmButtonText: '发起',
            //     cancelButtonText: '不发起',
            //     type: 'warning'
            //   })
            //     .then(() => {
            //       this.ejqParams = {
            //         sSheetCode: this.purInvoiceDetail.sSheetCode,
            //         sId: this.purInvoiceDetail.sId
            //       }
            //       this.visibleDialog.check = true
            //     })
            //     .catch((action) => {
            //       if (action === 'cancel') {
            //         this.hanldeSubmitCheckFile()
            //       }
            //     })
            // } else {
            //   this.hanldeSubmitCheckFile()
            // }
          }
        })
      }
    },
    checkSubmit() {
      return new Promise((resolve, reject) => {
        submitNewCheckDialog({ sInvoiceId: this.invoiceId })
          .then((res) => {
            resolve()
          })
          .catch((err) => {
            this.$message.closeAll()
            this.$confirm(err.message, this.$t('grid.others.prompt'), {
              confirmButtonText: this.$t('btns.confirm'),
              cancelButtonText: this.$t('btns.cancel'),
              type: 'warning'
            }).then(() => {
              resolve()
            })
          })
      })
    },
    checkSuccess(payload) {
      submitCheckRetail(
        { sInvoiceId: this.invoiceId },
        {
          signInfoVoList: payload
        }
      ).then((res) => {
        this.visibleDialog.check = false
        this.signInfoVoList = payload
        this.visibleDialog.submitDialog = true
      })
    },
    hanldeSubmitCheckFile() {
      checkAnnexExist({ sInvoiceId: this.invoiceId }).then((res) => {
        if (res.data === true) {
          this.$confirm(
            this.$t('tips.isSubmissionConfirmed'),
            this.$t('grid.others.prompt'),
            {
              confirmButtonText: this.$t('btns.confirm'),
              cancelButtonText: this.$t('btns.cancel'),
              type: 'warning'
            }
          ).then(() => {
            this.visibleDialog.submitDialog = true
          })
        } else {
          this.$confirm(
            '未上传附件，是否提交？',
            this.$t('grid.others.prompt'),
            {
              confirmButtonText: this.$t('btns.confirm'),
              cancelButtonText: this.$t('btns.cancel'),
              type: 'warning'
            }
          ).then(() => {
            this.visibleDialog.submitDialog = true
          })
        }
      })
    },
    onSubmitPurstock(value) {
      submitPurstock(
        { sInvoiceId: this.invoiceId },
        {
          param: value,
          signInfoVoList: this.signInfoVoList
        }
      ).then((res) => {
        this.$message.success(this.$t('tips.submitSuccess'))
        this.visibleDialog.submitDialog = false
        this.initFn()
      })
    },
    revPurInvoice() {
      this.$confirm(
        this.$t('grid.tips.isItConfirmedThatThcelledKey'),
        this.$t('grid.others.prompt'),
        {
          confirmButtonText: this.$t('btns.confirm'),
          cancelButtonText: this.$t('btns.cancel'),
          type: 'warning'
        }
      ).then(() => {
        revokePurstock({ id: this.invoiceId }).then((res) => {
          this.$message.success(this.$t('tips.withdrawalSuccessTag'))
          this.initFn()
        })
      })
    },
    withdraw() {
      this.$confirm(
        this.$t('grid.tips.isItConfirmedThatThcelledKey'),
        this.$t('grid.others.prompt'),
        {
          confirmButtonText: this.$t('btns.confirm'),
          cancelButtonText: this.$t('btns.cancel'),
          type: 'warning'
        }
      ).then(() => {
        retailMallCancel({ id: this.invoiceId }).then((res) => {
          this.$message.success(this.$t('tips.withdrawalSuccessTag'))
          this.initFn()
        })
      })
    },
    mallWithdraw() {
      this.$confirm(
        '是否确认配送撤销？',
        this.$t('grid.others.prompt'),
        {
          confirmButtonText: this.$t('btns.confirm'),
          cancelButtonText: this.$t('btns.cancel'),
          type: 'warning'
        }
      ).then(() => {
        retailMallRevoke({ id: this.invoiceId }).then((res) => {
          this.$message.success(this.$t('tips.withdrawalSuccessTag'))
          this.$tabDelete()
        })
      })
    },
    delPurInvoice() {
      this.$confirm(
        this.$t('grid.tips.isTheDeletionConfirmed'),
        this.$t('grid.others.prompt'),
        {
          confirmButtonText: this.$t('btns.confirm'),
          cancelButtonText: this.$t('btns.cancel'),
          type: 'warning'
        }
      ).then(() => {
        retailMallDelete([this.invoiceId]).then((res) => {
          this.$message.success(this.$t('tips.deletedSuccessfully'))
          this.$tabDelete()
        })
      })
    },

    handleCloseApproval() {
      this.visibleDialog.approvaldia = false
    },
    updata(e) {
      const form = { ...this.purInvoiceDetail }
      form.sExtend29 = e
      updataPurstock(form)
        .then(async(res) => {
          this.$message.success(this.$t('tips.saveSuccess'))
          await this.initFn(1)
        })
        .catch(() => {})
    },
    onSuccess({ sRemark }) {
      modifyRemark(this.invoiceId, {
        param: sRemark
      }).then(res => {
        this.$message.success(this.$t('grid.others.modifySuccessfully'))
        this.visibleDialog.modifyDialog = false
        this.initFn()
      })
    }
  }
}
</script>
