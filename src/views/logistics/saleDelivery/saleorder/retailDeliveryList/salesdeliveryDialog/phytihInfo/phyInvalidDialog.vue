
<template>
  <cnd-dialog
    v-if="dialogVisible"
    :title="$t('grid.btns.addSalesShipmentDetails')"
    append-to-body
    height="500"
    width="80%"
    :fullscreen="false"
    :visible.sync="dialogVisible"
    @close="handleClose"
  >
    <template slot="content">
      <!-- 搜索条件 -->
      <steel-search-form ref="searchForm" :params-button="false" :form-items="formItems" @search="onSearch" />
      <auto-wrap class="mt-10">
        <steelTradeAggrid
          ref="aggrid"
          :column-defs="columnDefs"
          :row-data="rowData"
          row-key="sId"
          :load-data="loadData"
          :auto-load-data="false"
          table-selection="multiple"
          :footer-total="footerTotal"
          @selectedChange="handleChangeGoods"
        />
      </auto-wrap>
      <cnd-dialog
        v-if="modifyDialog"
        :visible="modifyDialog"
        :fullscreen="false"
        append-to-body
        :title="$t('grid.others.batchModify')"
        width="350px"
        height="40px"
        @close="modifyDialog = false"
      >
        <template slot="content">
          <div>
            <el-form
              :model="modifyForm"
              label-width="100px"
              size="small"
              @submit.native.prevent
            >
              <cnd-form-item
                label="此次确认单价"
                :custom-width="20"
              >
                <cnd-input-number v-model="modifyForm.modifyPrice" type="amount" :decimal-digit="6" clearable :placeholder="$t('components.pleaseEnter')" @keyup.enter.native="modifySubmit" />
              </cnd-form-item>
            </el-form>
          </div>
        </template>
        <template slot="footer">
          <el-button
            size="mini"
            @click="modifyDialog = false"
          >{{ $t('btns.cancel') }}</el-button>
          <el-button
            type="primary"
            size="mini"
            @click="modifySubmit"
          >{{ $t('btns.confirm') }}</el-button>
        </template>
      </cnd-dialog>
    </template>
    <template slot="footer" class="dialog-footer">
      <el-button type="primary" size="mini" @click="openModifyDialog">批量修改单价</el-button>
      <el-button size="mini" @click="handleClose">{{ $t('btns.cancel') }}</el-button>
      <el-button type="primary" size="mini" @click="handleSelect">{{ $t('btns.confirm') }}</el-button>
    </template>
  </cnd-dialog>
</template>

<script>
var Decimal = window.Decimal

import Vue from 'vue'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import {
  getDialogPurPhyInvoiceDetail
} from '@/api/invoice/purchase'
import {
  onroadToDeliverydetailpage,
  onroadToDeliverydialogconfirm,
  stockToDeliverydetailpage,
  stockToDeliveryconfirm,
  thirdToDeliverypage,
  thirdToDeliveryconfirm
} from '@/api/logistics/saleDelivery/retailDelivery'
import agreement from '@/api/agreement'
import { Moment } from 'cnd-utils'
import { SteelFormat } from 'cnd-horizon-utils'
import { Middleware } from 'cndinfo-ui'
export default {
  components: { steelTradeAggrid },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    invoiceId: {
      type: String,
      default: null
    },
    type: {
      type: String,
      default: null
    },
    purInvoiceDetail: {
      type: Object,
      default: null
    }
  },
  data() {
    // const vm = this
    return {
      searchInfo: null,
      formItems: [{
        label: this.$t('grid.others.originalDocumentNumber'),
        value: 'sCode',
        type: 'elInput'
      }, {
        label: this.$t('grid.others.itemNumberTag'),
        value: 'sProjectCode',
        type: 'elInput'
      }, {
        label: this.$t('grid.title.personnel'),
        value: 'sStaffId',
        type: 'cndInputDialogItem',
        option: { valueKey: 'sPath' },
        dialogType: 'staff'
      },
      {
        label: this.$t('grid.others.item'),
        value: 'vGoodsDesc',
        type: 'elInput'
      },
      {
        label: this.$t('grid.others.carriageNumber'),
        value: 'sExtend5',
        type: 'elInput'
      },
      {
        label: '粗品名',
        value: 'sGoodsId',
        type: 'cndInputDialogItem',
        defaultUrl: '/esc/goods/ext/goodsDesc',
        option: { valueKey: 'sName' },
        multiple: true,
        reserveKeyword: false
      },
      {
        label: '钢卷号',
        value: 'sSteelNo',
        type: 'elInput'
      }
      ],
      columnDefs: [
        {
          headerName: this.$t('grid.others.originalDocumentNumber'),
          field: 'sOriginalCode'
        },
        {
          headerName: this.$t('grid.others.itemNumberTag'),
          field: 'sProjectCode'
        },
        {
          headerName: this.$t('grid.others.item'),
          field: 'vGoodsDesc'
        },
        {
          headerName: this.$t('grid.others.quantityConfirmedThisTime'),
          field: 'vCurContractQty',
          editable: true,
          cellEditorFramework: Vue.extend(
            Middleware.createComponent(
              'CndInputNumber',
              {
                mark: 'vCurContractQty',
                type: 'number',
                decimalDigit: 4,
                autoFocus: true,
                focusSelect: true
              },
              {
                blur: ({ event, rowData, middleware }) => {
                  if (middleware.params.data.sExtend18 === '0') {
                    if (event.target.value > rowData.data.vLeftContractQty) {
                      rowData.data.vCurQty = rowData.data.vLeftContractQty
                      console.log(rowData.data.vCurQty)
                      middleware.rendered.vCurContractQty.setValue(rowData.data.vLeftContractQty)
                      rowData.data.vCurContractQty = rowData.data.vLeftContractQty
                    } else {
                      console.log(rowData)
                      rowData.data.vCurQty = event.target.value
                    }
                    // middleware.rendered.vCurQty.setValue(event.target.value)
                    // this.rowData[1].detailVos[0].vCurQty = 999
                  }
                }
              }
            )),
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatThousandthSign(params.value, 4)
          }
        },
        {
          headerName: this.$t('grid.others.theNumberOfConfirmedPieces'),
          field: 'vCurQty',
          editable: (event) => {
            console.log(event.data.sExtend18 && event.data.sExtend18 === '0')
            return !(event.data.sExtend18 && event.data.sExtend18 === '0')
          },
          cellEditorFramework: Vue.extend(
            Middleware.createComponent(
              'CndInputNumber',
              {
                mark: 'vCurQty',
                type: 'number',
                decimalDigit: 0,
                focusSelect: true
              }
            )
          ),
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatThousandthSign(params.value, 0)
          }
        },
        {
          headerName: '此次确认单价',
          field: 'sCurTaxPrice',
          editable: true,
          cellEditorFramework: Vue.extend(
            Middleware.createComponent(
              'CndInputNumber',
              {
                mark: 'sCurTaxPrice',
                type: 'number',
                decimalDigit: 2
              }
            )
          ),
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatPrice(params.value)
          }
        },
        {
          headerName: this.$t('grid.title.purchaseContractNumber'),
          field: 'sPurContractCode'
        },
        {
          headerName: this.$t('grid.others.steelCoilNumber'),
          field: 'sExtend4'
        },
        {
          headerName: this.$t('grid.others.carriageNumber'),
          field: 'sExtend5'
        },
        {
          headerName: this.$t('grid.title.unitPrice'),
          field: 'sTaxPrice',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return SteelFormat.formatPrice(params.value)
          }
        },
        // {
        //   headerName: this.$t('grid.title.quantity'),
        //   field: 'vCurContractQty',
        //   editable: true,
        //   cellEditor: 'TableInput',
        //   cellStyle: { textAlign: 'right' },
        //   valueFormatter: (params) => {
        //     // vTaxAmt/vNetAmt
        //     return SteelFormat.formatPrice(params.value)
        //   }
        // },
        // {
        //   headerName: this.$t('grid.others.numberOfPiecesTag'),
        //   field: 'vCurQty',
        //   editable: true,
        //   cellEditor: 'TableInput',
        //   cellStyle: { textAlign: 'right' },
        //   valueFormatter: (params) => {
        //     return SteelFormat.formatPrice(params.value)
        //   }
        // },
        {
          headerName: this.$t('grid.title.accountingGroup'),
          field: 'vCheckGroupName'
        },
        {
          field: 'vDepartmentName',
          headerName: this.$t('grid.others.department')
        },
        {
          headerName: this.$t('grid.title.personnel'),
          field: 'vStaffName'
        },
        {
          headerName: this.$t('grid.title.remarks'),
          field: 'sRemark',
          width: 150
        },
        {
          headerName: this.$t('grid.title.createdAt'),
          field: 'sCreateTime',
          minWidth: 150,
          valueFormatter(params) {
            return Moment.time('YYYY-MM-DD HH:mm:ss', params.data.sCreateTime)
          }
        }
      ],
      rowData: [],
      footerTotal: null,
      selectOps: {},
      modifyForm: {
        modifyPrice: null
      },
      modifyData: [],
      modifyDialog: false
    }
  },
  watch: {
    dialogVisible(newVal) {
      if (newVal) {
        this.$nextTick(() => {
          this.onSearch()
        })
      }
    }
  },
  beforeMount() {
    agreement.getDict(['dev.common.sheet.status']).then(result => {
      this.selectOps['dev.common.sheet.status'] = result.data[0].dicts // 状态
    })
  },
  methods: {
    openModifyDialog() {
      this.$refs.aggrid.getSelectedData(selectList => {
        if (selectList.length > 0) {
          this.modifyForm.modifyPrice = null
          this.modifyDialog = true
          this.modifyData = selectList
        } else {
          this.$message.warning(this.$t('grid.others.pleaseCheckTheBox'))
        }
      })
    },
    modifySubmit() {
      const newValue = this.modifyForm.modifyPrice
      setTimeout(() => {
        this.rowData.forEach(item => {
          this.modifyData.forEach(el => {
            if (item.sId === el.sId) {
              item.sCurTaxPrice = +newValue
            }
          })
        })
        this.$message.closeAll()
        this.$refs.aggrid.refreshTable()
        this.$message.success(this.$t('grid.others.modifySuccessfully'))
        this.modifyDialog = false
      }, 0)
    },
    handleChangeGoods(rowData) {
      const details = rowData.filter(item => item._selected)
      const vCount = details.length
      let vCurContractQty = new Decimal(0)
      let vCurQty = new Decimal(0)
      details.forEach(el => {
        vCurContractQty = vCurContractQty.add(el.vCurContractQty)
        vCurQty = vCurQty.add(el.vCurQty)
      })
      this.footerTotal = [
        { count: vCount, key: 'count' },
        { title: this.$t('grid.title.quantity'), count: SteelFormat.formatThousandthSign(vCurContractQty, 4), unit: this.$t('grid.others.ton') },
        { title: this.$t('grid.others.numberOfPiecesTag'), count: vCurQty, unit: this.$t('grid.others.pieces') }
      ]
      this.$refs.aggrid.refreshTable()
    },
    onSearch() {
      const searchInfo = this.$refs.searchForm.getSearchData()
      this.searchInfo = searchInfo
      this.$refs.aggrid.loadTableData()
    },
    loadData(pagination) {
      return new Promise((resolve, reject) => {
        console.log('this.type: ', this.type)
        if (this.type === '20') {
          onroadToDeliverydetailpage({ ...this.purInvoiceDetail, ...this.$refs.searchForm.getSearchData(), ...{ vIsCor: '0', sNoticeGoodsId: this.invoiceId }}, pagination).then(res => {
            this.rowData = res.data.content.map(item => {
              return {
                ...item,
                _selected: false
              }
            })
            resolve(res.data)
          }).catch(() => {
            reject([])
          })
        } else if (this.type === '30') {
          stockToDeliverydetailpage({ ...this.purInvoiceDetail, ...this.$refs.searchForm.getSearchData(), ...{ vIsCor: '0', sNoticeGoodsId: this.invoiceId }}, pagination).then(res => {
            this.rowData = res.data.content.map(item => {
              return {
                ...item,
                _selected: false
              }
            })
            resolve(res.data)
          }).catch(() => {
            reject([])
          })
        } else if (this.type === '10') {
          thirdToDeliverypage({ ...this.purInvoiceDetail, ...this.$refs.searchForm.getSearchData(), ...{ vIsCor: '0', sNoticeGoodsId: this.invoiceId }}, pagination).then(res => {
            this.rowData = res.data.content.map(item => {
              return {
                ...item,
                _selected: false
              }
            })
            resolve(res.data)
          }).catch(() => {
            reject([])
          })
        }
      })
    },
    rowValueChanged(params) {
      console.log(params)
      const { data, rowIndex } = params
      if (params.data.sExtend18 === '0') {
        if (data.vCurContractQty > params.data.vLeftContractQty) {
          data.vCurQty = params.data.vLeftContractQty
        }
      }
      if (data.sExtend18) {
        if (data.sExtend18 === '0') {
          this.rowData[rowIndex] = data
          this.$refs.aggrid.refreshTable()
          return
        }
      }
    },
    loadDetail(data) {
      return new Promise((resolve, reject) => {
        getDialogPurPhyInvoiceDetail({ sInvCode: data.sInvCode, sInvNo: data.sInvNo }).then(res => {
          resolve(res.data)
        }).catch(() => {
          reject([])
        })
      })
    },
    handleSelect() {
      this.$refs.aggrid.gridApi.stopEditing()
      console.log(this.type)
      // const list = this.$refs.aggrid.getSelectedData()
      const list = []
      this.rowData.map(item => {
        if (item._selected === true) {
          list.push(item)
        }
      })
      if (list.length > 0) {
        if (this.type === '20') {
          onroadToDeliverydialogconfirm(list, this.invoiceId).then(res => {
            this.$message.success(this.$t('tips.addedSuccessfully'))
            this.$emit('close')
          })
        } else if (this.type === '30') {
          stockToDeliveryconfirm(list, this.invoiceId).then(res => {
            this.$message.success(this.$t('tips.addedSuccessfully'))
            this.$emit('close')
          })
        } else if (this.type === '10') {
          thirdToDeliveryconfirm(list, this.invoiceId).then(res => {
            this.$message.success(this.$t('tips.addedSuccessfully'))
            this.$emit('close')
          })
        }
      } else {
        this.$message.error(this.$t('grid.others.pleaseSelectData'))
      }
    },
    handleClose() {
      this.$emit('close')
    }
  }
}
</script>
