<template>
  <div>
    <el-form ref="baseForm" :rules="rules" class="el-form-w100" label-width="110px" :inline="true" :model="baseForm" size="small">
      <cnd-form-card-list ref="cndFormCardList" :active-panel="activeCollapseName" :error-position="true">
        <cnd-form-card :title="$t('grid.tabs.basicInformation')" name="1">
          <el-row>
            <!-- 发票登记号、公司、经营单位、供应商 -->
            <cnd-form-item :label="$t('grid.others.salesOrderNumber')">
              <el-input v-model="baseForm.sCode" disabled />
            </cnd-form-item>
            <cnd-form-item :label="$t('grid.others.supplier')">
              <el-input v-model="baseForm.vSupplierName" disabled />
            </cnd-form-item>
            <cnd-form-item :label="$t('grid.title.company')">
              <el-input v-model="baseForm.vCompanyName" disabled />
            </cnd-form-item>
            <cnd-form-item :label="$t('grid.others.warehouse')" prop="sWarehouseId" :error-msg="rules.sWarehouseId[0].message">
              <el-input v-if="!preaAgreement" v-model="baseForm.vWarehouseName" disabled />
              <el-select
                v-if="preaAgreement"
                v-model="baseForm.sWarehouseId"
                :disabled="isBusinessDisabled('save', baseForm.sSheetStatus)"
                :placeholder="$t('components.pleaseSelect')"
                @change="changeWarehouse"
              >
                <el-option
                  v-for="item in warehouseList"
                  :key="item.sId"
                  :label="item.sName"
                  :value="item.sId"
                />
              </el-select>
            </cnd-form-item>
            <cnd-form-item
              v-if="baseForm.sExtend32 ==='1'"
              label="运费承担"
              prop="sCarriageType"
              :error-msg="$t('components.pleaseSelect')"
              :rules="[
                { required: baseForm.sExtend32 ==='1', trigger: 'change' }
              ]"
            >
              <el-select v-model="baseForm.sCarriageType" :disabled="isDisabled" :placeholder="$t('components.pleaseSelect')" @change="handleUpdata">
                <el-option
                  v-for="item in selectOps['trade.stock.freight.undertake']"
                  :key="item.sCodeValue"
                  :label="item.sCodeName"
                  :value="item.sCodeValue"
                />
              </el-select>
            </cnd-form-item>
            <cnd-form-item :label="$t('grid.others.pickupCustomer')">
              <el-input v-model="baseForm.vDeliCustomerName" disabled />
            </cnd-form-item>
            <cnd-form-item :label="$t('grid.others.originalContractCustomer')">
              <el-input v-model="baseForm.vOriCustmerName" disabled />
            </cnd-form-item>
            <cnd-form-item :label="$t('grid.title.transferOfCargoRights')" prop="sOutGoodsType" :error-msg="rules.sOutGoodsType[0].message">
              <el-select v-model="baseForm.sOutGoodsType" :disabled="isPreagreement" :placeholder="$t('components.pleaseSelect')" @change="handleOutGoodsType">
                <el-option
                  v-for="item in selectOps['stock.out.goods.type']"
                  :key="item.sCodeValue"
                  :label="item.sCodeName"
                  :value="item.sCodeValue"
                />
              </el-select>
            </cnd-form-item>
            <cnd-form-item v-if="baseForm.sOutGoodsType === '30'" :label="$t('grid.title.directReleaseMethod')" prop="sNullStockType" :error-msg="rules.sNullStockType[0].message">
              <el-select v-model="baseForm.sNullStockType" :disabled="directRelease" :placeholder="$t('components.pleaseSelect')">
                <el-option
                  v-for="item in selectOps['stock.null.type']"
                  :key="item.sCodeValue"
                  :label="item.sCodeName"
                  :value="item.sCodeValue"
                />
              </el-select>
            </cnd-form-item>
            <cnd-form-item :label="$t('grid.others.orderMethod')" prop="sOutType" :error-msg="rules.sOutType[0].message">
              <el-select
                v-model="baseForm.sOutType"
                :disabled="isDisabled"
                clearable
                :placeholder="$t('components.pleaseSelect')"
                @change="handleUpdata"
              >
                <el-option
                  v-for="item in selectOps['stock.delivery.type']"
                  :key="item.sCodeValue"
                  :label="item.sCodeName"
                  :value="item.sCodeValue"
                />
              </el-select>
            </cnd-form-item>
            <cnd-form-item :label="$t('grid.others.settlementOutOfPosition')" :prop="sExtend6Editable ? 'sExtend6' : ''" :error-msg="rules.sExtend6[0].message">
              <el-select
                v-model="baseForm.sExtend6"
                :disabled="sExtend6Editable ? isDisabled : true"
                :placeholder="$t('components.pleaseSelect')"
                @change="handleUpdataExtend6"
              >
                <el-option
                  v-for="item in selectOps['base.yes-no']"
                  :key="item.sCodeValue"
                  :label="item.sCodeName"
                  :value="item.sCodeValue"
                />
              </el-select>
            </cnd-form-item>
            <cnd-form-item v-if="!['10','15'].includes(baseForm.sSheetStatus) && baseForm.sOperaPlat === '10'" label="是否E建签" prop="sExtend15">
              <el-select v-model="baseForm.sExtend15" clearable disabled :placeholder="$t('components.pleaseSelect')" @change="handleUpdata">
                <el-option
                  v-for="item in selectOps['base.yes-no']"
                  :key="item.sCodeValue"
                  :label="item.sCodeName"
                  :value="item.sCodeValue"
                />
              </el-select>
            </cnd-form-item>
            <cnd-form-item v-if="!['10','15'].includes(baseForm.sSheetStatus) && baseForm.sExtend15 === '1'" :label="$t('grid.others.contractingStatus')" prop="sSignStatus">
              <el-select v-model="baseForm.sSignStatus" disabled :placeholder="$t('components.pleaseSelect')" @change="handleUpdata">
                <el-option
                  v-for="item in selectOps['customer.sign.status']"
                  :key="item.sCodeValue"
                  :label="item.sCodeName"
                  :value="item.sCodeValue"
                />
              </el-select>
            </cnd-form-item>
            <cnd-form-item v-if="baseForm.sExtend6=='1'" :label="$t('grid.others.statementNumberKey')" :prop="baseForm.sExtend6=='1'?'sExtend1':''" :error-msg="rules.sExtend1[0].message">
              <el-select v-model="baseForm.sExtend1" :disabled="isDisabled" clearable :placeholder="$t('grid.others.selectCorrespondingStatementNumber')" @change="handleUpdata">
                <el-option
                  v-for="item in settlepaydata"
                  :key="item.sId"
                  :label="item.sCode"
                  :value="item.sId"
                />
              </el-select>
            </cnd-form-item>
            <template v-if="baseForm.sOperaPlat === '10'">
              <cnd-form-item v-if="!['10','15'].includes(baseForm.sSheetStatus)" label="是否赊销" prop="sExtend21">
                <el-select v-model="baseForm.sExtend21" disabled :placeholder="$t('components.pleaseSelect')" @change="handleUpdata($event,'sExtend21')">
                  <el-option
                    v-for="item in selectOps['base.yes-no']"
                    :key="item.sCodeValue"
                    :label="item.sCodeName"
                    :value="item.sCodeValue"
                  />
                </el-select>
              </cnd-form-item>
              <cnd-form-item v-if="showExtend24" label="是否单批发货" prop="sExtend24" :error-msg="rules.sExtend24[0].message">
                <el-select v-model="baseForm.sExtend24" :disabled="isDisabled" :placeholder="$t('components.pleaseSelect')" @change="handleUpdata">
                  <el-option
                    v-for="item in selectOps['base.yes-no']"
                    :key="item.sCodeValue"
                    :label="item.sCodeName"
                    :value="item.sCodeValue"
                  />
                </el-select>
              </cnd-form-item>
              <!-- <cnd-form-item v-if="showExtend25" label="出库费承担人" prop="sExtend25" :error-msg="rules.sExtend25[0].message">
                <el-select v-model="baseForm.sExtend25" :disabled="isDisabled" :placeholder="$t('components.pleaseSelect')" @change="handleUpdata">
                  <el-option
                    v-for="item in expenseBearer"
                    :key="item.sCodeValue"
                    :label="item.sCodeName"
                    :value="item.sCodeValue"
                  />
                </el-select>
              </cnd-form-item> -->
              <!-- <cnd-form-item label="是否工程" prop="sExtend23" :error-msg="rules.sExtend23[0].message">
                  <el-select v-model="baseForm.sExtend23" :disabled="isDisabled" :placeholder="$t('components.pleaseSelect')" @change="handleUpdata">
                    <el-option
                      v-for="item in selectOps['base.yes-no']"
                      :key="item.sCodeValue"
                      :label="item.sCodeName"
                      :value="item.sCodeValue"
                    />
                  </el-select>
                </cnd-form-item> -->
              <cnd-form-item v-if="showExtend11" label="仓库装卸费" :error-msg="rules.sExtend11[0].message" prop="sExtend11">
                <el-select
                  v-model="baseForm.sExtend11"
                  placeholder="请选择"
                  :disabled="isDisabled"
                >
                  <el-option
                    v-for="item in selectOps['logistic.fee.payer']"
                    :key="item.sCodeName"
                    :label="item.sCodeName"
                    :value="item.sCodeName"
                  />
                </el-select>
              </cnd-form-item>
            </template>
            <cnd-form-item v-if="baseForm.sExtend32 ==='1'" label="件数" prop="sExtend18" :error-msg="rules.sExtend18[0].message">
              <el-select v-model="baseForm.sExtend18" :disabled="isDisabled" :placeholder="$t('components.pleaseSelect')" @change="handleUpdata">
                <el-option
                  v-for="item in selectOps['base.yes-no']"
                  :key="item.sCodeValue"
                  :label="item.sCodeName"
                  :value="item.sCodeValue"
                />
              </el-select>
            </cnd-form-item>
            <template v-if="showExtend35">
              <cnd-form-item
                label="是否自动发单"
                prop="sExtend35"
                :error-msg="$t('components.pleaseSelect')"
                :rules="[
                  { required: true, trigger: 'change' }
                ]"
              >
                <el-select v-model="baseForm.sExtend35" :disabled="isDisabled" :placeholder="$t('components.pleaseSelect')" @change="handleUpdata($event,'sExtend35')">
                  <el-option
                    v-for="item in selectOps['base.yes-no']"
                    :key="item.sCodeValue"
                    :label="item.sCodeName"
                    :value="item.sCodeValue"
                  />
                </el-select>
              </cnd-form-item>
            </template>
            <template v-if="showExtend22">
              <cnd-form-item
                label="上下家是否关联"
                prop="sExtend22"
                :error-msg="$t('components.pleaseEnter')"
                :rules="[
                  { required: showExtend22, trigger: 'blur' }
                ]"
              >
                <el-select v-model="baseForm.sExtend22" :disabled="isDisabled" :placeholder="$t('components.pleaseSelect')" @change="handleUpdata($event,'sExtend22')">
                  <el-option
                    v-for="item in selectOps['base.yes-no']"
                    :key="item.sCodeValue"
                    :label="item.sCodeName"
                    :value="item.sCodeValue"
                  />
                </el-select>
              </cnd-form-item>
            </template>
            <template v-if="showExtend43">
              <cnd-form-item
                label="货物存放地"
                prop="sExtend43"
                :error-msg="$t('components.pleaseSelect')"
                :rules="[{ required: showExtend43, trigger: ['blur,change']}]"
              >
                <el-select v-model="baseForm.sExtend43" :disabled="isDisabled || baseForm.sExtend32 !== '1'">
                  <el-option
                    v-for="item in selectOps['esc.url_type']"
                    :key="item.sCodeValue"
                    :label="item.sCodeName"
                    :value="item.sCodeValue"
                  />
                </el-select>
              </cnd-form-item>
            </template>
            <template v-if="baseForm.sExtend22 === '1' && baseForm.sOperaPlat === '10'">
              <cnd-form-item
                label="业务披露关联客商说明"
                prop="sExtend30"
                :error-msg="$t('components.pleaseEnter')"
                :rules="[
                  { required: baseForm.sExtend22 === '1'&& baseForm.sOperaPlat === '10', trigger: 'blur' }
                ]"
              >
                <el-input
                  v-model="baseForm.sExtend30"
                  :disabled="isDisabled"
                  type="textarea"
                  :rows="2"
                  maxlength="1024"
                  :placeholder="$t('components.pleaseEnter')"
                  @change="handleUpdata"
                />
              </cnd-form-item>
            </template>
            <template v-if="baseForm.sIsShowDispatchingFields === '1'">
              <cnd-form-item
                label="流向"
                prop="sExtend39"
                :error-msg="$t('components.pleaseEnter')"
                :rules="[
                  { required: baseForm.sIsShowDispatchingFields === '1', trigger: 'blur' }
                ]"
              >
                <el-select v-model="baseForm.sExtend39" :disabled="isDisabled" :placeholder="$t('components.pleaseSelect')">
                  <el-option
                    v-for="item in sFlowDirectionList"
                    :key="item"
                    :label="item"
                    :value="item"
                  />
                </el-select>
              </cnd-form-item>
              <cnd-form-item
                label="提货方式"
                prop="sExtend40"
                :error-msg="$t('components.pleaseEnter')"
                :rules="[
                  { required: baseForm.sIsShowDispatchingFields === '1', trigger: 'blur' }
                ]"
              >
                <el-select v-model="baseForm.sExtend40" :disabled="isDisabled" :placeholder="$t('components.pleaseSelect')">
                  <el-option
                    v-for="item in sDeliveryMethodList"
                    :key="item"
                    :label="item"
                    :value="item"
                  />
                </el-select>
              </cnd-form-item>
              <cnd-form-item
                label="定价方式"
                prop="sExtend41"
                :error-msg="$t('components.pleaseEnter')"
                :rules="[
                  { required: baseForm.sIsShowDispatchingFields === '1', trigger: 'blur' }
                ]"
              >
                <el-select v-model="baseForm.sExtend41" :disabled="isDisabled" :placeholder="$t('components.pleaseSelect')">
                  <el-option
                    v-for="item in sPricingMethodList"
                    :key="item"
                    :label="item"
                    :value="item"
                  />
                </el-select>
              </cnd-form-item>
              <cnd-form-item
                label="户头"
                prop="sExtend45"
                :error-msg="$t('components.pleaseEnter')"
                :rules="[
                  { required: baseForm.sIsShowDispatchingFields === '1', trigger: 'blur' }
                ]"
              >
                <el-select v-model="baseForm.sExtend45" :disabled="isDisabled" :placeholder="$t('components.pleaseSelect')">
                  <el-option
                    v-for="item in sAccountMethodList"
                    :key="item"
                    :label="item"
                    :value="item"
                  />
                </el-select>
              </cnd-form-item>
            </template>
            <!-- <cnd-form-item v-if="baseForm.sOutGoodsType === '30' && baseForm.sLogisticsType === '30'" label="送货地址" :prop="isDisabled ? '' : 'sDeliveryAddress'" :error-msg="rules.sDeliveryAddress[0].message">
              <el-input
                v-model="baseForm.sDeliveryAddress"
                :disabled="isDisabled"
                type="textarea"
                :rows="2"
                placeholder="请填写送货地址"
                @change="handleUpdata('sDeliveryAddress')"
              />
            </cnd-form-item> -->
            <cnd-form-item v-if="baseForm.sIsShowBatches === '1'" label="是否分批签收" prop="sIsBatches" :error-msg="rules.sIsBatches[0].message">
              <el-select v-model="baseForm.sIsBatches" :disabled="isDisabled" :placeholder="$t('components.pleaseSelect')" @change="handleUpdata">
                <el-option
                  v-for="item in selectOps['base.yes-no']"
                  :key="item.sCodeValue"
                  :label="item.sCodeName"
                  :value="item.sCodeValue"
                />
              </el-select>
            </cnd-form-item>
            <cnd-form-item label="是否采销分离">
              <el-select
                v-model="baseForm.vContractSeparate"
                disabled
              >
                <el-option
                  v-for="item in selectOps['base.yes-no']"
                  :key="item.sCodeValue"
                  :label="item.sCodeName"
                  :value="item.sCodeValue"
                />
              </el-select>
            </cnd-form-item>
            <cnd-form-item
              v-if="showConsignee"
              label="收货人"
              prop="sConsignee"
              :error-msg="rules.sConsignee[0].message"
            >
              <el-select
                v-model="baseForm.sConsignee"
                :disabled="isRemarkDisabled || baseForm.sExtend16 === '20'"
              >
                <el-option
                  v-for="item in selectOps['direct.consignee']"
                  :key="item.sCodeValue"
                  :label="item.sCodeName"
                  :value="item.sCodeValue"
                />
              </el-select>
            </cnd-form-item>
            <cnd-form-item :label="$t('grid.title.remarks')">
              <el-input
                v-model="baseForm.sRemark"
                :disabled="isRemarkDisabled"
                type="textarea"
                :rows="2"
                :placeholder="$t('grid.others.pleaseEnterANote')"
                @change="handleUpdata('sRemark')"
              />
              <!-- <el-input v-model="baseForm.sRemark" clearable :placeholder="$t('grid.others.pleaseEnterANote')" @change="handleUpdata" /> -->
            </cnd-form-item>

          </el-row>
        </cnd-form-card>
        <cnd-form-card v-if="sParams.sSteelMillType === '1'" title="钢厂信息" name="5">
          <el-row>
            <cnd-form-item label="定价方式">
              <el-select v-model="sParams.sPriceType" disabled>
                <el-option
                  v-for="item in selectOps['steel.mill.price.type']"
                  :key="item.sCodeValue"
                  :label="item.sCodeName"
                  :value="item.sCodeValue"
                />
              </el-select>
            </cnd-form-item>
            <cnd-form-item label="账户标识">
              <el-input v-model="sParams.sAccountId" disabled />
            </cnd-form-item>
          </el-row>
        </cnd-form-card>
        <cnd-form-card v-if="['30'].includes(baseForm.sOutGoodsType) && (['20'].includes(baseForm.sLogisticsType) || ['20','30'].includes(baseForm.sNullStockType))" :title="$t('grid.others.shipArrivalDirectPickupLetter')" name="2">
          <el-row v-if="baseForm.vWarehouseName === '钢厂代运火运仓'">
            <cnd-form-item label="目的站">
              <!-- <el-input v-model="baseForm.sWharfName" :disabled="isPreagreement" /> -->
              <horizon-search-select-item
                v-model="baseForm.sWharfName"
                :default-url="`/esc/portCity/page`"
                :option="{
                  seledLabel: 'sName',
                  valueKey: 'sId',
                  label: 'sPath'
                }"
                :other-options="{
                  sManagementId: baseForm.sManagementId
                }"
                disabled
                @change="handleChange($event, 'sWharfId', 'sWharfName')"
              />
            </cnd-form-item>
            <cnd-form-item v-if="judgeManage(baseForm.sManagementCode, 'JFJS')" label="到站日期">
              <el-date-picker
                v-model="baseForm.sShipDate"
                type="date"
                :placeholder="$t('grid.others.selectDate')"
                disabled
              />
            </cnd-form-item>
          </el-row>
          <el-row v-else>
            <cnd-form-item label="目的站">
              <!-- <el-input
                v-model="baseForm.sWharfName"
              /> -->
              <horizon-search-select-item
                v-model="baseForm.sWharfName"
                :default-url="`/esc/portCity/page`"
                :option="{
                  seledLabel: 'sName',
                  valueKey: 'sId',
                  label: 'sPath'
                }"
                :other-options="{
                  sManagementId: baseForm.sManagementId
                }"
                disabled
                @change="handleChange($event, 'sWharfId', 'sWharfName')"
              />
            </cnd-form-item>
            <cnd-form-item :label="$t('grid.others.shipName')">
              <el-input v-model="baseForm.sVesselName" :placeholder="$t('grid.others.pleaseEnterTheNameOfTheShip')" disabled @change="handleUpdata" />
            </cnd-form-item>
            <cnd-form-item :label="$t('grid.others.shipArrivalDate')">
              <el-date-picker v-model="baseForm.sShipDate" type="date" :placeholder="$t('grid.others.selectDate')" disabled @change="handleUpdata" />
            </cnd-form-item>
            <cnd-form-item v-if="baseForm.sOutGoodsType=='30'" :label="$t('grid.others.directPickupLetterTemplate')">
              <el-input v-model="tymb" disabled />
            </cnd-form-item>
            <cnd-form-item v-if="judgeManage(baseForm.sManagementCode, 'JFJS')" label="是否控船">
              <el-select
                v-model="baseForm.sIsShip"
                :placeholder="$t('components.pleaseSelect')"
                disabled
              >
                <el-option
                  v-for="item in selectOps['base.yes-no']"
                  :key="item.sCodeValue"
                  :label="item.sCodeName"
                  :value="item.sCodeValue"
                />
              </el-select>
            </cnd-form-item>
          </el-row>
        </cnd-form-card>
        <cnd-form-card :title="$t('grid.others.amountInformation')" name="3">
          <el-row>
            <cnd-form-item :label="$t('grid.title.originalCurrency')">
              <el-select v-model="baseForm.sOriginalCurrency" disabled :placeholder="$t('components.pleaseSelect')" @change="handleUpdata">
                <el-option
                  v-for="item in selectOps['currencylist']"
                  :key="item.sId"
                  :label="item.sCnName"
                  :value="item.sId"
                />
              </el-select>
            </cnd-form-item>
            <cnd-form-item :label="$t('grid.others.originalCurrencyAmount')">
              <cnd-input-number v-model="baseForm.sOriginalAmt" type="amount" disabled :placeholder="$t('components.pleaseEnter')" @change="handleUpdata" />
              <!-- <el-input v-model="baseForm.sOriginalAmt" disabled /> -->
            </cnd-form-item>
          </el-row>
        </cnd-form-card>
        <cnd-form-card :title="$t('grid.tabs.systemInformation')" name="4">
          <el-row>
            <cnd-form-item :label="$t('grid.title.createdBy')">
              <el-input v-model="baseForm.vCreatorName" disabled />
            </cnd-form-item>
            <cnd-form-item :label="$t('grid.title.createdAt')">
              <el-input :value="handleTime(baseForm.sCreateTime)" disabled />
            </cnd-form-item>
            <cnd-form-item :label="$t('grid.title.modifiedBy')">
              <el-input v-model="baseForm.vModifierName" disabled />
            </cnd-form-item>
            <cnd-form-item :label="$t('grid.title.modifiedAt')">
              <el-input :value="handleTime(baseForm.sModifyTime)" disabled />
            </cnd-form-item>
          </el-row>
          <el-row>
            <cnd-form-item :label="$t('grid.title.status')">
              <el-select v-model="baseForm.sSheetStatus" disabled>
                <el-option
                  v-for="item in selectOps['dev.common.sheet.status']"
                  :key="item.sCodeValue"
                  :label="item.sCodeName"
                  :value="item.sCodeValue"
                />
              </el-select>
            </cnd-form-item>
            <cnd-form-item :label="$t('grid.others.effectiveTime')">
              <el-input :value="handleTime(baseForm.sRatifyDate)" disabled />
            </cnd-form-item>
          </el-row>
        </cnd-form-card>
      </cnd-form-card-list>
    </el-form>
  </div>
</template>
<script>
import agreement from '@/api/agreement'
import { SteelFormat } from 'cnd-horizon-utils'
import { Moment } from 'cnd-utils'
import businessMixin from '@/utils/businessMixin'
import { expenseBearer } from '@/utils/dict'
import { judgeManage, filterString } from '@/utils/common'
import {
  settlepaypage,
  doChecksExtend6,
  preagreementWarehouse
} from '@/api/logistics/saleDelivery/saleorder'
import { setSupplierSteelTemplateConfig } from '../../../comm/index'
export default {
  mixins: [businessMixin],
  props: {
    info: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      judgeManage: judgeManage,
      expenseBearer: expenseBearer,
      activeCollapseName: ['1', '2', '3', '4', '5'],
      sParams: {
        sSteelMillType: '',
        sPriceType: '',
        sAccountId: ''
      },
      settlepaydata: [],
      baseForm: {
        sOutType: '',
        sNullStockType: ''
      },
      previousSExtend6: '',
      tymb: '通用模版',
      selectOps: {
        'stock.delivery.type': [],
        'stock.null.type': [],
        'dev.common.sheet.status': [],
        'base.yes-no': [],
        'stock.out.goods.type': [],
        'currencylist': [],
        'customer.sign.status': [],
        'trade.stock.freight.undertake': [],
        'esc.url_type': [],
        'logistic.fee.payer': [],
        'direct.consignee': [],
        'steel.mill.price.type': []
      },
      rules: {
        sOutType: [
          { required: true, message: this.$t('grid.others.pleaseChooseTheWayOutOfTheWarehouse'), trigger: 'change' }
        ],
        sCarriageType: [
          { required: true, message: this.$t('components.pleaseSelect'), trigger: 'change' }
        ],
        sExtend6: [
          { required: true, message: this.$t('grid.others.selectWhetherToSettrehouseKey'), trigger: 'change' }
        ],
        sExtend1: [
          { required: true, message: this.$t('components.pleaseSelect'), trigger: 'change' }
        ],
        // sWharfId: [
        //   { required: true, message: this.$t('components.pleaseSelect'), trigger: 'change' }
        // ],
        sVesselName: [
          { required: true, message: this.$t('grid.others.pleaseSelectTheShipName'), trigger: 'change' }
        ],
        sShipDate: [
          { required: true, message: this.$t('grid.others.pleaseSelectTheShipArrivalDate'), trigger: 'change' }
        ],
        sDeliveryAddress: [
          { required: true, message: '请填写收货地址', trigger: 'change' }
        ],
        // sExtend21: [
        //   { required: true, message: this.$t('components.pleaseSelect'), trigger: 'change' }
        // ],
        sExtend22: [
          { required: true, message: this.$t('components.pleaseSelect'), trigger: 'change' }
        ],
        sExtend23: [
          { required: true, message: this.$t('components.pleaseSelect'), trigger: 'change' }
        ],
        sExtend24: [
          { required: true, message: this.$t('components.pleaseSelect'), trigger: 'change' }
        ],
        sExtend25: [
          { required: true, message: this.$t('components.pleaseSelect'), trigger: 'change' }
        ],
        sExtend18: [
          { required: true, message: this.$t('components.pleaseSelect'), trigger: 'change' }
        ],
        sExtend35: [
          { required: true, message: this.$t('components.pleaseSelect'), trigger: 'change' }
        ],
        sIsBatches: [
          { required: true, message: this.$t('components.pleaseSelect'), trigger: 'change' }
        ],
        sExtend11: [
          { required: true, message: '请选择仓库装卸费', trigger: 'change' }
        ],
        sNullStockType: [
          { required: true, message: '请选择直放方式', trigger: 'change' }
        ],
        sOutGoodsType: [
          { required: true, message: '请选择货权转移方式', trigger: 'change' }
        ],
        sWarehouseId: [
          { required: true, message: '请选择仓库', trigger: 'change' }
        ],
        sConsignee: [
          { required: true, message: '请选择收货人', trigger: 'change' }
        ]
        // vAmt: [
        //   { required: true, message: '请输入原币金额', trigger: 'change' }
        // ]
      },
      sFlowDirectionList: [],
      sDeliveryMethodList: [],
      sPricingMethodList: [],
      sAccountMethodList: [],
      warehouseList: []
    }
  },
  computed: {
    isDisabled() {
      return this.deliveryDisabled || this.isBusinessDisabled('save', this.baseForm.sSheetStatus)
    },
    isRemarkDisabled() {
      return this.isBusinessDisabled('save', this.baseForm.sSheetStatus)
    },
    deliveryDisabled() {
      const { sSheetStatus, sExtend29, sOperaPlat, sExternalId } = this.baseForm
      return sOperaPlat === '20' && ['20', '30'].includes(sExtend29) && ['10', '15'].includes(sSheetStatus) && sExternalId
    },
    showExtend24() {
      // 是否单批发货：是否赊销=是  && 上海物资
      const { sExtend21, sManagementCode, sExtend32 } = this.baseForm
      if (sExtend32 === '1') {
        return sExtend21 === '1'
      } else {
        return sExtend21 === '1' && judgeManage(sManagementCode, 'SHWZ')
      }
    },
    showExtend25() {
      // 出库费承担人：
      // 一：上海物资 && 库存直放 (sLogisticsType=20 || 30) && 在途仓sScodeType=30 && 剔除库存 否sIsDelWarehouse=0
      // 二：上海物资 && 库存业务sLogisticsType=30
      const { sManagementCode, sLogisticsType, sScodeType, sIsDelWarehouse } = this.baseForm
      if (judgeManage(sManagementCode, 'SHWZ') && ['20', '30'].includes(sLogisticsType)) {
        if (sScodeType === '30') {
          return sIsDelWarehouse === '0'
        } else {
          return sLogisticsType === '30'
        }
      } else {
        return false
      }
    },
    showExtend35() {
      const { sManagementCode, sNullStockType, sOutGoodsType } = this.baseForm
      // 上海物资 && 出仓，货转，直放（钢厂直放）
      if (judgeManage(sManagementCode, 'SHWZ')) {
        if (sOutGoodsType === '30') {
          return sNullStockType === '10'
        } else {
          return true
        }
      } else {
        return false
      }
    },
    showExtend22() {
      const { sShipType, sIsCross, sOutGoodsType, sOperaPlat } = this.baseForm
      return (sShipType === '10' || sShipType === '20' && sOutGoodsType === '30' && sIsCross === '0') && sOperaPlat === '10'
    },
    showExtend43() {
      const { sSupplierId, sNullStockType } = this.baseForm
      return sSupplierId === '1187678613356625921' && sNullStockType === '10'
    },
    sExtend6Editable() {
      const { sOperaPlat, sManagementCode, sExtend16, sSheetType } = this.baseForm
      return sOperaPlat === '10' && judgeManage(sManagementCode, 'JFJS') && sExtend16 === '10' && sSheetType === '110'
    },
    preaAgreement() {
      const { sIsPreagreement } = this.baseForm
      return sIsPreagreement === '1'
    },
    isPreagreement() {
      return !(this.baseForm.sIsPreagreement === '1') || this.isBusinessDisabled('save', this.baseForm.sSheetStatus)
    },
    directRelease() {
      return this.isPreagreement || (this.baseForm.sOutGoodsType === '30' && this.baseForm.sWarehouseId === '111111111111112')
    },
    showExtend11() {
      const { sIsMark, sExtend16, sScodeType } = this.baseForm
      return sIsMark === '1' && sExtend16 === '10' && (sScodeType === '20' || sScodeType === '30')
    },
    showConsignee() {
      const { sConsignee, vManagementCode } = this.baseForm
      return (sConsignee !== '' && sConsignee !== null) && !judgeManage(vManagementCode, 'JFJS')
    }
  },
  watch: {
    info: {
      handler(val) {
        this.$refs.baseForm && this.$refs.baseForm.resetFields()
        this.baseForm = val
        this.sParams = val.sParams
          ? JSON.parse(val.sParams)
          : {
            sSteelMillType: '',
            sPriceType: '',
            sAccountId: ''
          }
        this.previousSExtend6 = val.sExtend6
        const datas = {
          page: 0,
          limit: 999,
          sNoticeId: val.sId
        }
        if (this.baseForm.sIsShowDispatchingFields === '1') {
          setSupplierSteelTemplateConfig({
            sSupplierId: this.baseForm.sSupplierId,
            sManagementId: this.baseForm.sManagementId,
            sCustomerId: this.baseForm.sCustomerId
          }).then(res => {
            this.sFlowDirectionList = res.sFlowDirectionList
            this.sDeliveryMethodList = res.sDeliveryMethodList
            this.sPricingMethodList = res.sPricingMethodList
            this.sAccountMethodList = res.sAccountMethodList
          })
        }
        settlepaypage(datas).then(e => {
          this.settlepaydata = e.data.content
        })
        if (this.baseForm.sIsPreagreement === '1') {
          preagreementWarehouse(this.baseForm.sId).then(res => {
            this.warehouseList = res.data
          })
        }
      }
    }
  },
  beforeMount() {
    agreement.getDict([
      'stock.null.type',
      'stock.delivery.type',
      'dev.common.sheet.status',
      'base.yes-no',
      'stock.out.goods.type',
      'customer.sign.status',
      'trade.stock.freight.undertake',
      'esc.url_type',
      'logistic.fee.payer',
      'direct.consignee',
      'steel.mill.price.type'
    ]).then(result => {
      this.selectOps['stock.null.type'] = result.data[0].dicts
      this.selectOps['stock.delivery.type'] = result.data[1].dicts
      this.selectOps['dev.common.sheet.status'] = result.data[2].dicts // 状态
      this.selectOps['base.yes-no'] = result.data[3].dicts // 是否结算
      this.selectOps['stock.out.goods.type'] = result.data[4].dicts // 货权转移方式
      this.selectOps['customer.sign.status'] = result.data[5].dicts
      this.selectOps['trade.stock.freight.undertake'] = result.data[6].dicts
      this.selectOps['esc.url_type'] = result.data[7].dicts
      this.selectOps['logistic.fee.payer'] = result.data[8].dicts
      this.selectOps['direct.consignee'] = result.data[9].dicts
      this.selectOps['steel.mill.price.type'] = result.data[10].dicts
    })
    // 币种
    agreement.currencylist().then(result => {
      this.selectOps['currencylist'] = result.data
    })
  },
  mounted() {
    // console.log(this.info)
    this.baseForm = this.info
  },
  methods: {
    handleChange(val, key, key2) {
      if (key2) {
        this.baseForm[key2] = val ? val.sName : null
      }
      this.baseForm[key] = val?.sId ?? null
      this.$emit('handleUpdata')
    },
    handleTime(time) {
      return Moment.time('YYYY-MM-DD HH:mm:ss', time)
    },
    numinif(e) {
      return SteelFormat.formatThousandthSign(e, 4)
    },
    handleUpdata(v, k) {
      if (['sRemark', 'sDeliveryAddress'].includes(k)) {
        this.baseForm[k] = filterString(this.baseForm[k])
      }
      if (k === 'sExtend22') {
        if (v === '1') {
          this.baseForm.sExtend30 = null
        }
      }
      this.$emit('handleUpdata')
    },
    handleUpdataExtend6(v) {
      doChecksExtend6({
        sId: this.baseForm.sId,
        sExtend6: v
      }).then(res => {
        if (res.data) {
          this.settlepaydata = [res.data]
          this.baseForm.sExtend1 = res.data.sId
          this.previousSExtend6 = v
        } else {
          this.baseForm.sExtend6 = '0'
        }
      }).catch(() => {
        this.$nextTick(() => {
          this.baseForm.sExtend6 = this.previousSExtend6
        })
      }).finally(() => {
        this.$emit('handleUpdata')
      })
    },
    handleOutGoodsType(value) {
      if (this.baseForm.sIsPreagreement === '1' && this.baseForm.sWarehouseId === '111111111111112' && value === '30') {
        this.baseForm.sNullStockType = '10'
      }
    },
    arrivalModify(fn, type) {
      this.$refs.baseForm.validate((valid) => {
        if (valid) {
          if (this.baseForm.sExtend6 === '0') {
            this.baseForm.sExtend1 = null
          }
          fn && fn(this.baseForm)
        } else {
          fn && fn(false)
          return false
        }
      })
    },
    changeWarehouse(value) {
      const selectObj = this.warehouseList.find(item => item.sId === value)
      if (selectObj) {
        const { sName, sIsMark, sType } = selectObj
        this.baseForm.vWarehouseName = sName
        this.baseForm.sIsMark = sIsMark
        this.baseForm.sScodeType = sType
      } else {
        this.baseForm.vWarehouseName = null
      }
      // if 协议做单-情况船到码头直提函可编辑信息
      if (this.baseForm.sIsPreagreement === '1') {
        this.baseForm.sWharfName = ''
        this.baseForm.sShipDate = ''
        this.baseForm.sVesselName = ''
        this.baseForm.sIsShip = ''
        if (this.baseForm.sOutGoodsType === '30' && this.baseForm.sWarehouseId === '111111111111112') {
          this.baseForm.sNullStockType = '10'
        } else {
          this.baseForm.sNullStockType = ''
        }
      }
    }
  }
}
</script>
