<template>
  <div class="auto-page-title">
    <div class="flexV">
      <cnd-btn-position top="7" right="10">
        <el-radio-group
          v-model="sDeliveryType"
          size="mini"
          :disabled="
            isBusinessDisabled('save', sSheetStatus) || deliveryDisabled
          "
          @change="changeRadio"
        >
          <el-radio-button label="10">自提</el-radio-button>
          <el-radio-button label="20">{{
            isGCZF ? '建发配送' : '配送'
          }}</el-radio-button>
          <el-radio-button v-if="isGCZF" label="30">钢厂配送</el-radio-button>
        </el-radio-group>
      </cnd-btn-position>
      <div class="btn-group" style="border-bottom: 1px solid #dfe4ed">
        <div class="text">
          <!-- 提货人信息 -->
          {{ $t('grid.others.informationOfTheConsignor') }}
          <el-checkbox
            v-if="isAppointSend"
            v-model="checkStatus"
            true-label="1"
            style="margin-left: 20px"
            :disabled="isBusinessDisabled('save', sSheetStatus)"
            @change="handleCheckChange"
          >是否指定配送</el-checkbox>
        </div>
        <div>
          <template v-if="purInvoiceDetail.sOperaPlat === '10'">
            <el-dropdown size="mini" type="primary" style="margin: 0 10px;" split-button>
              选择提货人
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item v-if="isHistroy">
                  <el-button
                    v-has:esc_stock_receipt_goods__shipper_choice
                    :disabled="isBusinessDisabled('save', sSheetStatus) || deliveryDisabled"
                    type="text"
                    class="dropdown-item-button"
                    @click="openlinkmanlistbox('history')"
                  >历史提货人</el-button>
                </el-dropdown-item>
                <el-dropdown-item v-if="isLongterm">
                  <el-button
                    v-has:esc_stock_receipt_goods__shipper_choice_V2
                    :disabled="isBusinessDisabled('save', sSheetStatus) || deliveryDisabled"
                    type="text"
                    class="dropdown-item-button"
                    @click="openlinkmanlistbox('longterm')"
                  >长期提货人</el-button>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
          <el-button
            v-if="isHistroy"
            v-has:esc_stock_receipt_goods__shipper_add
            :disabled="
              isBusinessDisabled('save', sSheetStatus) || deliveryDisabled
            "
            type="primary"
            size="mini"
            @click="addlinkman"
          >
            {{ $t('btns.add') }}
          </el-button>
          <el-button
            v-has:esc_stock_receipt_goods__shipper_modify
            :disabled="
              isBusinessDisabled('save', sSheetStatus) || deliveryDisabled
            "
            type="primary"
            size="mini"
            @click="editlinkman"
          >
            {{ $t('grid.others.modify') }}
          </el-button>
          <el-button
            v-has:esc_stock_receipt_goods__shipper_delete
            :disabled="
              isBusinessDisabled('save', sSheetStatus) || deliveryDisabled
            "
            type="danger"
            size="mini"
            @click="dellinkman"
          >
            {{ $t('btns.delete') }}
          </el-button>
        </div>
      </div>
      <div
        v-if="checkListdata && checkListdata.length > 0"
        class="checkListgroup"
      >
        <el-checkbox-group v-model="checkList" class="checkListdatabox">
          <div v-for="(item, index) in checkListdata" :key="index">
            <el-checkbox :label="item.sId">
              <span class="spanheadsh">
                <span
                  v-if="item.sCarNo"
                  class="checkListdataspanmin"
                >{{ $t('grid.columns.carNumber') }}：{{ item.sCarNo }}
                </span>
                <span
                  v-if="item.sEmissionStandard"
                  class="checkListdataspanmin"
                >排放标准：{{ formatPaymentType(item.sEmissionStandard, emissionStand) }}
                </span>
                <span
                  v-if="item.sStandardLoad"
                  class="checkListdataspanmin"
                >标载：{{ item.sStandardLoad }}
                </span>
                <span
                  v-if="item.sCarColor"
                  class="checkListdataspanmin"
                >车辆颜色：{{ formatPaymentType(item.sCarColor, carColor) }}
                </span>
                <span
                  v-if="item.sLinkman"
                  class="checkListdataspanmin"
                  :title="item.sLinkman"
                >提货人/收货人：{{ item.sLinkman }}
                </span>
                <span
                  v-if="item.sTel"
                  class="checkListdataspan"
                >{{ $t('grid.others.contactInformation') }}：{{ item.sTel }}
                </span>
                <span
                  v-if="item.sCertificateId"
                  class="checkListdataspan checkListdataspansfz"
                >
                  {{ $t('grid.others.id') }}：{{ item.sCertificateId }}</span>
                <span
                  v-if="item.sLogisticsCompany"
                  class="checkListdataspan checkListdataspansfz"
                >
                  送货地址：{{ item.sLogisticsCompany }}</span>
                <span
                  v-if="item.sRemark && purInvoiceDetail.sIsShowDispatchingFields === '1'"
                  class="checkListdataspan checkListdataspansfz"
                >
                  备注：{{ item.sRemark }}</span>
                <span
                  v-if="item.sIsLongTerm === 1"
                  class="checkListdataspan"
                >{{ $t('grid.others.longTermPickupPerson') }}
                </span>
              </span>
              <div v-if="item.relVos.length > 0">
                <div
                  v-for="(items, indexs) in item.relVos"
                  :key="indexs"
                  class="debody"
                >
                  <div>{{ items.vGoodsDesc }}</div>
                  <div>{{ items.sQty }} {{ $t('grid.others.pieces') }}</div>
                  <div>
                    {{ initnum(items.sContractQty) }}
                    {{ $t('grid.others.ton') }}
                  </div>
                  <div>{{ items.sExtend4 ? items.sExtend4 : '' }}</div>
                  <div>{{ items.sExtend5 ? items.sExtend5 : '' }}</div>
                </div>
              </div>
              <cnd-btn-position top="4" right="10">
                <el-button
                  v-if="item.updateList && item.updateList.length > 0"
                  type="text"
                  size="mini"
                  @click="showHistoryModal(item.updateList)"
                >
                  换车历史
                </el-button>
              </cnd-btn-position>
            </el-checkbox>
          </div>
        </el-checkbox-group>
      </div>
      <div
        v-if="checkListdata && checkListdata.length == 0"
        class="btn-group"
        style="border-bottom: 1px solid #dfe4ed; border-top: none"
      >
        <div style="width: 100%; text-align: center; color: #999">
          {{ $t('grid.others.noData') }}
        </div>
      </div>
      <!-- 合同明细 -->
      <contractdetail
        v-if="purInvoiceDetail.sExtend32 === '1'"
        ref="contractDetail"
        :invoice-id="invoiceId"
        :pur-invoice-detail="purInvoiceDetail"
        @selectedChange="contractSelectedChange"
        @removeSuccess="onSearch"
      />
      <!-- 销售发货明细 -->
      <div class="btn-group" style="border-top: 1px solid #dfe4ed">
        <div class="text">
          {{ $t('grid.others.salesShipmentDetails') }}
        </div>
        <div>
          <el-button
            v-has:esc_stock_receipt_goods__del_detail_add_amount_modify
            :disabled="
              isBusinessDisabled('save', sSheetStatus) || deliveryDisabled
            "
            type="primary"
            size="mini"
            @click="openModifyDialog('2')"
          >修改总价</el-button>
          <el-button
            v-has:esc_stock_receipt_goods__del_detail_add_price_modify
            :disabled="
              isBusinessDisabled('save', sSheetStatus) || deliveryDisabled
            "
            type="primary"
            size="mini"
            @click="openModifyDialog('1')"
          >修改单价</el-button>
          <el-button
            type="primary"
            size="mini"
            :disabled="
              isBusinessDisabled('save', sSheetStatus) || deliveryDisabled
            "
            @click="neupdatePricew"
          >
            {{ $t('grid.others.updateUnitPrice') }}
          </el-button>
          <el-button
            :disabled="
              isBusinessDisabled('save', sSheetStatus) || deliveryDisabled
            "
            type="primary"
            size="mini"
            @click="openPhyInvalidDialog"
          >
            {{ $t('btns.add') }}
          </el-button>
          <el-button
            :disabled="
              isBusinessDisabled('save', sSheetStatus) || deliveryDisabled
            "
            type="danger"
            size="mini"
            @click="agrRemovesfun"
          >
            {{ $t('btns.delete') }}
          </el-button>
          <el-button
            type="primary"
            size="mini"
            :disabled="deliveryDisabled"
            @click="openlinkmanlist"
          >
            {{ $t('grid.others.matchingPickupTruckNumber') }}
          </el-button>
          <!-- <el-button key="comm_purcontact_list_del" :disabled="isBusinessDisabled('save',sSheetStatus)" type="primary" size="mini" @click="addsettle">
            {{ $t('grid.others.generateBillingStatement') }}
          </el-button> -->
        </div>
      </div>
      <steelTradeAggrid
        ref="aggrid"
        :column-defs="
          purInvoiceDetail.sExtend32 === '1' ? columnDefssV2 : columnDefss
        "
        :heightinif="400"
        :row-data="rowData"
        :load-data="loadData"
        :auto-load-data="false"
        :default-page-size="200"
        table-selection="multiple"
        :footer-total="footerCount"
        row-key="sId"
        open-after-filter
        :full-row-type="fullRowType"
        @rowValueChanged="rowValueChanged"
        @rowEditingStopped="rowEditingStopped"
        @selectedChange="handleStockDetailCount"
        @onPasteEnd="onPasteEnd"
      />
    </div>
    <phyInvalidDialog
      :invoice-id="invoiceId"
      :type="type"
      :dialog-visible="phyDialogVisible"
      @close="closePhyInvalidDialog"
    />
    <phyInvalidDialogV2
      :invoice-id="invoiceId"
      :info="selectData"
      :dialog-visible="phyDialogVisibleV2"
      @close="closePhyInvalidDialog"
    />
    <phyInvalidDialogV3
      :invoice-id="invoiceId"
      :type="type"
      :pur-invoice-detail="purInvoiceDetail"
      :dialog-visible="phyDialogVisibleV3"
      @close="closePhyInvalidDialog"
    />
    <addlinkmanDialog
      v-if="dialogVisible.linkmanDialogVisible"
      :invoice-id="invoiceId"
      :dialog-visible="dialogVisible.linkmanDialogVisible"
      :pur-invoice-detail="purInvoiceDetail"
      @close="closeaddlinkmanDialog"
    />
    <addlinkmanDialog2
      v-if="dialogVisible.linkmanDialogVisible2"
      :invoice-id="invoiceId"
      :dialog-visible="dialogVisible.linkmanDialogVisible2"
      :pur-invoice-detail="purInvoiceDetail"
      @close="closeaddlinkmanDialog"
    />
    <editlinkmanDialog
      v-if="dialogVisible.editlinkmanDialogVisible"
      :invoice-id="invoiceId"
      :dataedit="dataedit"
      :dialog-visible="dialogVisible.editlinkmanDialogVisible"
      :pur-invoice-detail="purInvoiceDetail"
      @close="closeaddlinkmanDialog"
    />
    <editlinkmanDialog2
      v-if="dialogVisible.editlinkmanDialogVisible2"
      :invoice-id="invoiceId"
      :dataedit="dataedit"
      :dialog-visible="dialogVisible.editlinkmanDialogVisible2"
      :pur-invoice-detail="purInvoiceDetail"
      @close="closeaddlinkmanDialog"
    />
    <linkmanlist
      v-if="dialogVisible.linkmanlistDialogVisible"
      :invoice-id="invoiceId"
      :dataedit="dataedit"
      :pur-invoice-detail="purInvoiceDetail"
      :is-disabled="
        isBusinessDisabled('save', sSheetStatus) || deliveryDisabled
      "
      :dialog-visible="dialogVisible.linkmanlistDialogVisible"
      @close="closeaddlinkmanDialog"
    />
    <dialog-table
      v-if="dialogVisible.linkmanlistbox"
      ref="linkmanlistbox"
      :title="titleLinkman"
      :visible="dialogVisible.linkmanlistbox"
      :column-defs="columnDefs"
      :req-config="reqConfig"
      :show-pagination="false"
      :radio="false"
      :append-to-body="true"
      :show-delete="showDelete"
      @onSelect="onSelect"
    />
    <cnd-dialog
      v-if="modifyDialog"
      :visible="modifyDialog"
      :fullscreen="false"
      append-to-body
      :title="modifyType === '1' ? '批量修改' : '修改总价'"
      width="350px"
      height="40px"
      @close="modifyDialog = false"
    >
      <template slot="content">
        <div>
          <el-form
            :model="modifyForm"
            label-width="100px"
            size="small"
            @submit.native.prevent
          >
            <cnd-form-item
              :label="modifyType === '1' ? '此次确认单价' : '总金额'"
              :custom-width="20"
            >
              <cnd-input-number
                v-model="modifyForm.modifyPrice"
                type="amount"
                :decimal-digit="6"
                clearable
                :placeholder="$t('components.pleaseEnter')"
                @keyup.enter.native="modifySubmit"
              />
            </cnd-form-item>
          </el-form>
        </div>
      </template>
      <template slot="footer">
        <el-button size="mini" @click="modifyDialog = false">{{
          $t('btns.cancel')
        }}</el-button>
        <el-button type="primary" size="mini" @click="modifySubmit">{{
          $t('btns.confirm')
        }}</el-button>
      </template>
    </cnd-dialog>
    <carHistoryDialog
      :visible="dialogVisible.historyDialog"
      :row-data="historyData"
      :is-liuan="purInvoiceDetail.liuAnRpa"
      @close="dialogVisible.historyDialog = false"
    />
  </div>
</template>

<script>
var Decimal = window.Decimal
import Vue from 'vue'
import { Middleware } from 'cndinfo-ui'
import businessMixin from '@/utils/businessMixin'
import { SteelFormat } from 'cnd-horizon-utils'
import agreement from '@/api/agreement'
import { deliveryshoppage } from '@/api/logistics/saleDelivery/saleorder'
import {
  deliverylinkmanlists,
  commonsettlecreate,
  detailagrRemoves,
  detailupdatePrice,
  // 用新接口createLinkMan
  // linkmanadds,
  linkmanremoves,
  thirdContractDeliveryModify,
  thirdContractDeliveryDetail,
  thirdContractDeliveryRemoves
} from '@/api/logistics/saleDelivery/saleorder'
import {
  updatePriceForThird,
  updateAmtForThird,
  historyPickerRemoves
} from '@/api/logistics/saleDelivery/nopartysaleorder'
import {
  createLinkMan
} from '@/api/logistics/saleDelivery/pickup.js'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import DialogTable from '@/components/DialogTable'
import phyInvalidDialog from './phyInvalidDialog.vue'
import addlinkmanDialog from './addlinkmanDialog.vue'
import addlinkmanDialog2 from './addlinkmanDialog2.vue'
import editlinkmanDialog from './editlinkmanDialog.vue'
import editlinkmanDialog2 from './editlinkmanDialog2.vue'
import phyInvalidDialogV2 from './phyInvalidDialogV2.vue'
import phyInvalidDialogV3 from './phyInvalidDialogV3.vue'
import contractdetail from './contractdetail/index'
import linkmanlist from './linkmanlist.vue'
import { getUnitNameList } from '@/api/logistics/registrationData/steelPlantList'
import carHistoryDialog from '../../../componets/carHistoryDialog'
import { DictUtil } from 'cnd-horizon-utils'
export default {
  components: {
    linkmanlist,
    steelTradeAggrid,
    phyInvalidDialog,
    addlinkmanDialog,
    addlinkmanDialog2,
    editlinkmanDialog,
    editlinkmanDialog2,
    phyInvalidDialogV2,
    contractdetail,
    carHistoryDialog,
    DialogTable,
    phyInvalidDialogV3
  },
  mixins: [businessMixin],
  props: {
    invoiceId: {
      type: String,
      default: null
    },
    type: {
      type: String,
      default: null
    },
    purInvoiceDetail: {
      type: Object,
      default: null
    }
  },
  data() {
    // const _this = this
    return {
      selectData: '',
      modifyType: '',
      modifyForm: {
        modifyPrice: null
      },
      modifyDialog: false,
      modifyIds: [],
      dialogVisible: {
        linkmanlistbox: false, // 长期提货人弹窗
        linkmanDialogVisible: false, // 新增提货人弹窗
        linkmanDialogVisible2: false, // 新增提货人弹窗-六安钢厂
        editlinkmanDialogVisible: false, // 编辑提货人弹窗-六安钢厂
        editlinkmanDialogVisible2: false,
        linkmanlistDialogVisible: false,
        historyDialog: false
      },
      columnDefs: [],
      checkList: [],
      checkListdata: [],
      dataedit: {},
      columnDefss: [
        {
          headerName: this.$t('grid.others.itemNumberTag'),
          field: 'sProjectCode'
        },
        {
          headerName: this.$t('grid.title.salesContractNumber'),
          field: 'sSaleContractCode'
        },
        {
          headerName: this.$t('grid.title.purchaseContractNumber'),
          field: 'sPurContractCode'
        },
        // {
        //   headerName: this.$t('grid.others.customer'),
        //   field: 'vCustomerName',
        // },
        {
          headerName: this.$t('grid.others.item'),
          field: 'vGoodsDesc'
        },
        {
          headerName: this.$t('grid.title.quantity'),
          field: 'sContractQty',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return SteelFormat.formatThousandthSign(params.value, 4)
          }
        },
        {
          headerName: this.$t('grid.others.numberOfPiecesTag'),
          field: 'sQty',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return SteelFormat.formatThousandthSign(params.value)
          }
        },
        {
          headerName: '单位',
          field: 'vQtyUnitName'
        },
        {
          headerName: this.$t('grid.title.unitPrice'),
          field: 'sTaxPrice',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return SteelFormat.formatPrice(params.value)
          }
        },
        {
          headerName: this.$t('grid.title.amount'),
          field: 'sTaxAmt',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return SteelFormat.formatPrice(params.value)
          }
        },
        {
          headerName: this.$t('grid.others.steelCoilNumber'),
          field: 'sExtend4'
        },
        {
          headerName: this.$t('grid.others.carriageNumber'),
          field: 'sExtend5'
        },
        {
          headerName: this.$t('grid.others.originalDocumentNumber'),
          field: 'sUpCode'
        },
        {
          headerName: this.$t('grid.title.accountingGroup'),
          field: 'vCheckGroupName'
        },
        {
          field: 'vDepartmentName',
          headerName: this.$t('grid.others.department')
        },
        {
          headerName: this.$t('grid.title.personnel'),
          field: 'vStaffName'
        },
        {
          headerName: this.$t('grid.title.remarks'),
          field: 'sRemark',
          width: '168px'
        }
      ],
      columnDefssV2: [
        {
          headerName: this.$t('grid.title.purchaseContractNumber'),
          field: 'sPurContractCode'
        },
        {
          headerName: this.$t('grid.others.itemNumberTag'),
          field: 'sProjectCode'
        },
        {
          headerName: this.$t('grid.title.salesContractNumber'),
          field: 'sSaleContractCode'
        },
        { headerName: this.$t('grid.others.item'), field: 'vArtName' },
        {
          headerName: this.$t('grid.title.quantity'),
          field: 'vCurContractQty',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return SteelFormat.formatThousandthSign(params.value, 4)
          },
          editable: () => {
            return !this.isBusinessDisabled('save', this.sSheetStatus)
          },
          cellEditorFramework: Vue.extend(
            Middleware.createComponent(
              'CndInputNumber',
              {
                mark: 'vCurContractQty',
                type: 'number',
                decimalDigit: 4,
                autoFocus: true,
                focusSelect: true
              },
              {
                blur: ({ event, rowData, middleware }) => {
                  if (this.sExtend18 !== '1') {
                    rowData.data.vCurQty = +event.target.value
                    this.$refs.aggrid.gridApi.refreshCells(rowData)
                    return
                  }
                }
              }
            )
          )
        },
        {
          headerName: this.$t('grid.others.numberOfPiecesTag'),
          field: 'vCurQty',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return SteelFormat.formatThousandthSign(params.value)
          },
          editable: () => {
            return (
              !this.isBusinessDisabled('save', this.sSheetStatus) &&
              this.sExtend18 === '1'
            )
          },
          cellEditorFramework: Vue.extend(
            Middleware.createComponent(
              'CndInputNumber',
              {
                mark: 'vCurQty',
                type: 'number',
                decimalDigit: 0,
                focusSelect: true
              },
              {}
            )
          )
        },
        {
          headerName: this.$t('grid.title.unitPrice'),
          field: 'sTaxPrice',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return SteelFormat.formatPrice(params.value)
          },
          // editable: () => {
          //   return !this.isBusinessDisabled('save', this.sSheetStatus)
          // },
          cellEditorFramework: Vue.extend(
            Middleware.createComponent(
              'CndInputNumber',
              {
                mark: 'sTaxPrice',
                type: 'number',
                decimalDigit: 2,
                focusSelect: true
              },
              {}
            )
          )
        },
        // {
        //   headerName: '单位',
        //   field: 'vQtyUnitName',
        //   editable: () => {
        //     return !this.isBusinessDisabled('save', this.sSheetStatus)
        //   },
        //   cellEditorFramework: Vue.extend(Middleware.createComponent(
        //     'AgGridSelect',
        //     {
        //       mark: 'vQtyUnitName',
        //       config: {
        //         label: 'sCnName',
        //         value: 'sCnName'
        //       },
        //       queryMethod: this.getUnitNameList
        //     },
        //     {
        //       getOption: (option, params) => {
        //         this.rowData[params.rowIndex].sQtyUnit = option.sId
        //       }
        //     }
        //   ))
        // },
        {
          headerName: this.$t('grid.title.amount'),
          field: 'sTaxAmt',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return SteelFormat.formatPrice(params.value)
          }
        },
        {
          headerName: this.$t('grid.others.steelCoilNumber'),
          field: 'sExtend4',
          editable: () => {
            return !this.isBusinessDisabled('save', this.sSheetStatus)
          }
        },
        {
          headerName: this.$t('grid.others.carriageNumber'),
          field: 'sExtend5',
          editable: () => {
            return !this.isBusinessDisabled('save', this.sSheetStatus)
          }
        },
        {
          headerName: '备注',
          field: 'sRemark',
          editable: () => {
            return !this.isBusinessDisabled('save', this.sSheetStatus)
          }
        }
      ],
      rowData: [],
      phyDialogVisible: false,
      phyDialogVisibleV2: false,
      phyDialogVisibleV3: false,
      selectOps: {},
      footerCount: null,
      sDeliveryType: '10',
      historyData: [],
      checkStatus: '0',
      emissionStand: [],
      carColor: [],
      // history、longterm
      typeLinkman: 'history',
      showDelete: true,
      titleLinkman: '选择历史提货人'
    }
  },
  computed: {
    fullRowType() {
      return !this.isBusinessDisabled('save', this.sSheetStatus)
        ? 'parent'
        : null
    },
    deliveryDisabled() {
      const { sSheetStatus, sExtend29, sOperaPlat, sExternalId } =
        this.purInvoiceDetail
      return (
        sOperaPlat === '20' &&
        ['20', '30'].includes(sExtend29) &&
        ['10', '15'].includes(sSheetStatus) &&
        sExternalId
      )
    },
    sSheetStatus() {
      return this.purInvoiceDetail.sSheetStatus
    },
    sExtend18() {
      return this.purInvoiceDetail.sExtend18 || '0'
    },
    isGCZF() {
      const { sOutGoodsType, sNullStockType } = this.purInvoiceDetail
      return sOutGoodsType === '30' && sNullStockType === '10'
    },
    reqConfig() {
      return {
        method: 'POST',
        url: `/esc/stock/deliverer/listValid`,
        params: {
          sheetType: this.typeLinkman === 'history' ? '1' : '2',
          sCustomerId: this.purInvoiceDetail.sCustomerId,
          sheetId: this.invoiceId,
          sheetEntityType: '1'
        }
      }
    },
    isAppointSend() {
      const { sExtend29, sIsJianke, sExtend16 } = this.purInvoiceDetail
      return sExtend29 === '20' && sIsJianke === '1' && sExtend16 === '10'
    },
    isHistroy() {
      if (this.checkListdata.length === 0) {
        return true
      }
      if (this.checkListdata.length > 0 && (this.checkListdata[0]?.sIsLongTerm === '0' || this.checkListdata[0]?.sIsLongTerm === null)) {
        return true
      }
      return false
    },
    isLongterm() {
      const { liuAnRpa, sExtend29 } = this.purInvoiceDetail
      if (liuAnRpa || sExtend29 !== '10') {
        return false
      }
      if ((this.checkListdata.length > 0 && this.checkListdata[0]?.sIsLongTerm === '1') || this.checkListdata.length === 0) {
        return true
      }
      return false
    }
  },
  watch: {
    purInvoiceDetail: {
      deep: true,
      immediate: true,
      handler(newVal) {
        this.sDeliveryType = newVal.sExtend29 || '10'
        this.checkStatus = newVal.sExtend42 || '0'
      }
    }
  },
  beforeMount() {
    agreement.getDict(['dev.common.sheet.status']).then((result) => {
      this.selectOps['dev.common.sheet.status'] = result.data[0].dicts // 状态
    })
  },
  mounted() {
    this.onSearch()
    // 字典
    this.loadDict()
  },
  methods: {
    // 字典
    loadDict() {
      DictUtil.getDict(
        [
          'emission.standard',
          'car.color'
        ],
        (res) => {
          this.emissionStand = res[0].dicts
          this.carColor = res[1].dicts
        }
      )
    },
    formatPaymentType(value, statusList) {
      const status = statusList.filter(item => item.sCodeValue === value)
      return status.length ? status[0].sCodeName : value
    },
    getUnitNameList() {
      return new Promise((resolve, reject) => {
        getUnitNameList()
          .then((res) => {
            resolve(res.data)
          })
          .catch((err) => {
            console.log(err)
          })
      })
    },
    initnum(e) {
      return SteelFormat.formatThousandthSign(e, 4)
    },
    dellinkman() {
      // 删除提货人
      console.log(this.checkList)
      if (this.checkList.length === 0) {
        this.$message.error(
          this.$t('grid.others.pleaseSelectAPickerInformation')
        )
        return
      }
      linkmanremoves(this.checkList).then((e) => {
        this.$message.success(this.$t('tips.deletedSuccessfully'))
        this.onSearch()
      })
    },
    editlinkman() {
      if (this.checkList.length === 0) {
        this.$message.error(
          this.$t('grid.others.pleaseSelectAPickerInformation')
        )
        return
      }
      if (this.checkList.length > 1) {
        this.$message.error(this.$t('grid.others.onlyOneDataCanBeEdited'))
        return
      }
      const thss = this
      this.checkListdata.map(function(item, index) {
        if (item.sId === thss.checkList[0]) {
          thss.dataedit = item
        }
      })
      if (this.purInvoiceDetail.liuAnRpa) {
        this.dialogVisible.editlinkmanDialogVisible2 = true
      } else {
        this.dialogVisible.editlinkmanDialogVisible = true
      }
    },
    onSelect(option, type) {
      if (option.length > 0 && type === 'confirm') {
        option.map((e) => {
          e.sLinkman = e.sContact
          if (this.sDeliveryType === '10') {
            e.sCertificateId = e.sCertificateNo
            e.sCarNo = e.sExtend3
          } else {
            e.sLogisticsCompany = e.sExtend2
          }
        })
        if (this.typeLinkman === 'longterm') {
          option = option.map((item) => {
            return {
              ...item,
              sIsLongTerm: '1'
            }
          })
        }
        createLinkMan({
          sheetId: this.invoiceId,
          sDeliveryType: this.sDeliveryType || '10',
          sheetEntityType: '1',
          escDeliveryLinkmanVos: option
        }).then((e) => {
          this.$message({
            message: this.$t('tips.addSuccess'),
            type: 'success'
          })
          this.onSearch()
          this.checkList = []
        })
      }
      if (option.length > 0 && type === 'delete') {
        const sId = []
        option.map((e) => {
          sId.push(e.sId)
        })
        historyPickerRemoves({
          sNoticeSaleId: this.invoiceId,
          sCustomerId: this.purInvoiceDetail.sCustomerId
        }, sId).then((res) => {
          if (res.code === '0000') {
            this.$message({
              message: '删除成功',
              type: 'success'
            })
            this.$refs.linkmanlistbox.queryTableList()
          }
        })
        return
      }
      this.dialogVisible.linkmanlistbox = false
    },
    addlinkman(e) {
      // 新增提货人
      if (this.purInvoiceDetail.liuAnRpa) {
        this.dialogVisible.linkmanDialogVisible2 = true
      } else {
        this.dialogVisible.linkmanDialogVisible = true
      }
    },
    addsettle(type) {
      commonsettlecreate(this.invoiceId).then((e) => {
        this.$message.success(
          this.$t('grid.others.settlementInformationHasBeenUpdated')
        )
        if (type) {
          return
        }
        this.$emit('tabs')
      })
    },
    onSearch() {
      deliverylinkmanlists({ sDeliveryId: this.invoiceId }).then((e) => {
        this.checkListdata = e.data || []
        if (this.checkListdata.length > 0 && this.checkListdata[0]?.sIsLongTerm === '1') {
          this.$emit('updataCheckList', true)
        }
        if (this.checkListdata.length > 0 && (this.checkListdata[0]?.sIsLongTerm === '0' || this.checkListdata[0]?.sIsLongTerm === null)) {
          this.$emit('updataCheckList', false)
        }
        this.checkList = []
      })
      this.$refs.aggrid.loadTableData()
    },
    setCount(vCount = 0, vSumContractQty = 0, vSumQty = 0, vSumAmt = 0, flag) {
      this[flag] = [
        { count: vCount, key: 'count' },
        {
          title: this.$t('grid.title.quantity'),
          count: SteelFormat.formatThousandthSign(vSumContractQty, 4),
          unit: this.$t('grid.others.ton')
        },
        {
          title: this.$t('grid.others.numberOfPiecesTag'),
          count: vSumQty,
          unit: this.$t('grid.others.pieces')
        },
        {
          title: this.$t('grid.title.amount'),
          count: SteelFormat.formatPrice(vSumAmt),
          unit: this.$t('grid.others.yuan')
        }
      ]
    },
    handleStockDetailCount(_, details) {
      const vCount = details.length
      let vSumContractQty = new Decimal(0)
      let vSumAmt = new Decimal(0)
      let vSumQty = new Decimal(0)
      if (this.purInvoiceDetail.sExtend32 === '1') {
        details.forEach(el => {
          vSumContractQty = vSumContractQty.add(el.vCurContractQty)
          vSumQty = vSumQty.add(el.vCurQty)
          vSumAmt = vSumAmt.add(el.sTaxAmt)
        })
      } else {
        details.forEach(el => {
          vSumContractQty = vSumContractQty.add(el.sContractQty)
          vSumQty = vSumQty.add(el.sQty)
          vSumAmt = vSumAmt.add(el.sTaxAmt)
        })
      }
      this.setCount(vCount, vSumContractQty, vSumQty, vSumAmt, 'footerCount')
    },
    neupdatePricew() {
      detailupdatePrice(this.invoiceId).then((e) => {
        this.$message({
          message: this.$t('grid.others.updateSuccessful'),
          type: 'success'
        })
        this.$refs.aggrid.loadTableData()
      })
    },
    contractSelectedChange(res) {
      this.selectData = res
      this.rowData = []
      this.$refs.aggrid.loadTableData()
    },
    loadData(pagination) {
      return new Promise((resolve, reject) => {
        if (this.purInvoiceDetail.sExtend32 === '1') {
          if (this.selectData && this.selectData.sId) {
            thirdContractDeliveryDetail({
              sDeliveryId: this.invoiceId,
              sContractDeliveryId: this.selectData.sId,
              ...pagination
            })
              .then((res) => {
                this.rowData = res.data.content.map((item) => {
                  item._selected = true
                  item._isEdit = false
                  return item
                })
                resolve(res.data)
              })
              .catch(() => {
                reject(0)
              })
          } else {
            this.rowData = []
          }
        } else {
          deliveryshoppage(this.invoiceId, { ...pagination })
            .then((res) => {
              this.rowData = res.data.content.map((item) => {
                item._selected = true
                return item
              })
              resolve(res.data)
            })
            .catch(() => {
              reject(0)
            })
        }
      })
    },
    rowValueChanged(params) {
      params.data._isEdit = true
    },
    rowEditingStopped(params) {
      this.modifiesRowData([params.data])
    },
    onPasteEnd() {
      const list = this.rowData.filter((item) => item._isEdit === true)
      this.modifiesRowData(list)
    },
    modifiesRowData(list) {
      thirdContractDeliveryModify(list, this.invoiceId)
        .then((res) => {
          this.$refs.contractDetail && this.$refs.contractDetail.$refs.aggrid.reloadTableData()
          this.$refs.aggrid.loadTableData()
        })
        .catch(() => {
          this.$refs.aggrid.loadTableData()
        })
    },
    // 批量删除
    agrRemovesfun() {
      this.$refs.aggrid.getSelectedData(selectList => {
        if (selectList.length === 0) {
          this.$message({
            message: this.$t('grid.others.pleaseSelectAData'),
            type: 'warning'
          })
          return
        }
        const delarr = selectList.map(item => item.sId)
        this.$confirm(
          this.$t('grid.others.thisActionDeletesThntinueKey'),
          this.$t('grid.others.prompt'),
          {
            confirmButtonText: this.$t('btns.confirm'),
            cancelButtonText: this.$t('btns.cancel'),
            type: 'warning'
          }
        ).then(() => {
          const api = this.purInvoiceDetail.sExtend32 === '1' ? thirdContractDeliveryRemoves : detailagrRemoves
          api(delarr).then(() => {
            this.$message({
              message: this.$t('tips.deletedSuccessfully'),
              type: 'success'
            })
            this.$refs.contractDetail && this.$refs.contractDetail.$refs.aggrid.reloadTableData()
            this.onSearch()
            this.addsettle('stop')
          })
            .catch(() => {})
        })
      })
    },
    openPhyInvalidDialog() {
      if (this.purInvoiceDetail.sExtend32 === '1') {
        if (this.selectData?.sId) {
          this.phyDialogVisibleV2 = true
        } else {
          this.$message.warning('请选择一条合同明细')
        }
      } else {
        if (this.purInvoiceDetail.vContractSeparate === '1') {
          this.phyDialogVisibleV3 = true
        } else {
          this.phyDialogVisible = true
        }
      }
    },
    openlinkmanlist() {
      if (this.checkListdata.length === 0) {
        this.$message({
          message: this.$t('grid.others.pleaseAddPickupInformation'),
          type: 'warning'
        })
        return
      }
      this.dialogVisible.linkmanlistDialogVisible = true
    },
    closePhyInvalidDialog() {
      this.phyDialogVisible = false
      this.phyDialogVisibleV2 = false
      this.phyDialogVisibleV3 = false
      this.addsettle('stop')
      this.onSearch()
    },
    closeaddlinkmanDialog() {
      this.dialogVisible.linkmanDialogVisible = false
      this.dialogVisible.linkmanDialogVisible2 = false
      this.dialogVisible.editlinkmanDialogVisible = false
      this.dialogVisible.editlinkmanDialogVisible2 = false
      this.dialogVisible.linkmanlistDialogVisible = false
      this.onSearch()
    },
    changeRadio(e) {
      this.$emit('updata', e)
    },
    openModifyDialog(type) {
      this.modifyType = type
      this.modifyIds = []
      this.$refs.aggrid.getSelectedData((selectList) => {
        if (selectList.length > 0) {
          this.modifyForm.modifyPrice = null
          this.modifyDialog = true
          selectList.forEach((el) => {
            this.modifyIds.push(el.sId)
          })
        } else {
          this.$message.warning(this.$t('grid.others.pleaseCheckTheBox'))
        }
      })
    },
    modifySubmit() {
      if (+this.modifyForm.modifyPrice <= 0) {
        this.$message.error('此次确认单价需大于0')
        return
      }
      if (this.modifyType === '1') {
        updatePriceForThird(
          {
            sId: this.invoiceId,
            price: this.modifyForm.modifyPrice
          },
          this.modifyIds
        ).then(() => {
          this.$refs.aggrid.loadTableData()
          this.$message.success(this.$t('grid.others.modifySuccessfully'))
          this.modifyDialog = false
        })
      } else {
        updateAmtForThird({
          sDetailIds: this.modifyIds,
          sSumPrice: this.modifyForm.modifyPrice
        }).then(() => {
          this.$refs.aggrid.loadTableData()
          this.$message.success(this.$t('grid.others.modifySuccessfully'))
          this.modifyDialog = false
        })
      }
    },
    showHistoryModal(data) {
      this.historyData = data
      this.dialogVisible.historyDialog = true
    },
    openlinkmanlistbox(type) {
      this.showDelete = type === 'history'
      this.typeLinkman = type
      this.titleLinkman = type === 'history' ? '选择历史提货人' : '选择长期提货人'
      if (this.sDeliveryType === '10' && this.purInvoiceDetail.liuAnRpa) {
        this.columnDefs = [
          { field: 'sExtend3', headerName: '车号', width: 80 },
          {
            field: 'sEmissionStandard',
            headerName: '排放标准',
            width: 100,
            valueGetter: params => {
              const filterItem = this.emissionStand.filter(item => item.sCodeValue === params.data.sEmissionStandard)
              return filterItem.length ? filterItem[0].sCodeName : params.data.sEmissionStandard
            }
          },
          { field: 'sStandardLoad', headerName: '标载', width: 80 },
          {
            field: 'sCarColor',
            headerName: '车辆颜色',
            width: 100,
            valueGetter: params => {
              const filterItem = this.carColor.filter(item => item.sCodeValue === params.data.sCarColor)
              return filterItem.length ? filterItem[0].sCodeName : params.data.sCarColor
            }
          },
          { field: 'sContact', headerName: '提货人', width: 100 },
          { field: 'sTel', headerName: '联系方式', width: 100 },
          {
            field: 'sCertificateNo',
            headerName: this.$t('grid.others.idCardNumber'),
            width: 160
          }
        ]
      } else if (this.sDeliveryType === '10' && !this.purInvoiceDetail.liuAnRpa) {
        this.columnDefs = [
          { field: 'sExtend3', headerName: '车号', width: 80 },
          { field: 'sContact', headerName: '提货人', width: 80 },
          { field: 'sTel', headerName: '联系方式', width: 100 },
          {
            field: 'sCertificateNo',
            headerName: this.$t('grid.others.idCardNumber'),
            width: 160
          }
        ]
      } else {
        this.columnDefs = [
          { field: 'sContact', headerName: '提货人', width: 80 },
          { field: 'sTel', headerName: '联系方式', width: 100 },
          {
            field: 'sExtend2',
            headerName: '送货地址',
            width: 160
          }
        ]
      }
      this.dialogVisible.linkmanlistbox = true
      this.$nextTick(() => {
        this.$refs?.linkmanlistbox?.queryTableList()
      })
    },
    handleCheckChange(e) {
      this.$emit('updataAppoint', e)
    }
  }
}
</script>
<style lang="scss" scoped>
.checkListgroup {
  line-height: 28px;

  font-size: 12px;
  width: 100%;
  background-color: #ffffff;
  .checkListdatabox {
    line-height: 16px;
  }
  .checkListdatabox .checkListdataspan {
    margin-right: 15px;
    display: inline-block;
    min-width: 150px;
  }
  .checkListdatabox .checkListdataspansfz {
    min-width: 200px;
  }
  .checkListdataspansdd {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    margin-right: 15px;
    display: inline-block;
    min-width: 300px;
  }
  /* .dialog-wrap .dialog-body{
    padding-bottom: 10px;
  } */
  .spanheadsh {
    display: inline-block;
    line-height: 30px;
    padding-left: 5px;
    white-space: normal;
    vertical-align: middle;
  }
  .checkListdatabox .checkListdataspanmin {
    margin-right: 15px;
    display: inline-block;
    min-width: 150px;
  }
  .checkListdatas {
    margin-left: 5px;
    margin-right: 5px;
  }
  .debody {
    display: flex;
    div {
      padding: 5px 0;
      flex: 1;
      border: 1px solid #e6e8eb;
      text-align: center;
      margin: -1px;
      background: #ffffff;
    }
  }
}
.dropdown-item-button {
  width: 100%;
  height: 100%;
  padding: 0;
  margin: 0;
  line-height: 18px;
  text-align: text;
  font-size: 12px;
  border: none;
  color: #606266;
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif
}

/* 悬停状态 */
.dropdown-item-button:not(.is-disabled):hover {
  background-color: #ecf5ff;
  color: #66b1ff;
}

/* 禁用状态 */
.dropdown-item-button.is-disabled {
  color: #c0c4cc;
  cursor: not-allowed;
  background-color: transparent !important;
}
</style>
<style scoped>
::v-deep .checkListdatabox .el-checkbox__label {
  display: inline !important;
  width: 100% !important;
}
::v-deep .checkListdatabox .el-checkbox {
  width: 100% !important;
  background-color: #fafafa;
  user-select: auto !important;
  border-bottom: 1px solid #e6e8eb;
}
::v-deep .checkListdatabox .el-checkbox__input {
  margin-left: 10px;
}
::v-deep
  .checkListdatabox
  .el-checkbox__input.is-checked
  + .el-checkbox__label {
  color: #606266 !important;
}

::v-deep
  .el-radio-button__orig-radio:disabled:checked
  + .el-radio-button__inner {
  color: #fff !important;
  background-color: #a0cfff !important;
  border-color: #a0cfff !important;
}
::v-deep .el-radio-button--mini .el-radio-button__inner {
  padding: 6px 15px;
}
</style>
