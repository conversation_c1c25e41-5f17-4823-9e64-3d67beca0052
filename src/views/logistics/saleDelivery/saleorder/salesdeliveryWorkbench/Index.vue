<template>
  <!-- 销售待发货工作台 -->
  <div class="page-container">
    <p v-show="dtype !== 'oa'" class="page-title">
      {{ $t('grid.others.salesPendingShipmentWorkbench') }}
    </p>
    <div
      class="flexV"
      :class="dtype === 'oa' ? '' : 'layout-content auto-page-title'"
      :style="dtype === 'oa' ? 'height:92%' : ''"
    >
      <steel-search-form
        ref="searchForm"
        :form-items="formItems"
        @search="onSearch"
      />
      <el-tabs
        v-model="activeName"
        class="tabs-btn-position mt-10"
        @tab-click="tabClick"
      >
        <cnd-btn-position right="10">
          <!-- 采购到货 -->
          <span v-show="caigouBtn">
            <span v-show="addToCartBtn" class="mr-10">
              <el-button
                v-has:esc_stock_receipt_workbench_esc_tsk_stock_pur_receipt_join
                type="primary"
                size="mini"
                :disabled="!selectedData.length"
                @click="addToCart"
              >
                {{ $t('grid.btns.addSalesShipmentGenerated') }}
              </el-button>
            </span>
            <span v-show="zhuanshouBtn">
              <el-button
                v-has:esc_stock_receipt_workbench_esc_tsk_stock_pur_receipt_resale
                class="mr-10"
                type="primary"
                size="mini"
                :disabled="!selectedData.length"
                @click="tablezsfun"
              >
                转售
              </el-button>
            </span>
            <span v-show="otherBtn">
              <el-button
                v-has:esc_stock_receipt_workbench_esc_tsk_stock_pur_receipt_out
                type="primary"
                size="mini"
                :disabled="!selectedData.length || zhifangDisabled"
                @click="Outwarehouse('直放')"
              >
                {{ $t('grid.others.directRelease') }}
              </el-button>
              <el-button
                v-has:esc_stock_receipt_workbench_esc_tsk_stock_pur_receipt_delivery
                type="primary"
                size="mini"
                :disabled="!selectedData.length || !zhifangDisabled"
                @click="Outwarehouse('出仓')"
              >
                <!-- 出仓 -->
                {{ $t('grid.title.outbound') }}
              </el-button>
              <el-button
                v-has:esc_stock_receipt_workbench_esc_tsk_stock_pur_receipt_HZ
                type="primary"
                size="mini"
                :disabled="!selectedData.length || !zhifangDisabled"
                @click="Outwarehouse('货转')"
              >
                <!-- 货转 -->
                {{ $t('grid.others.transfer') }}
              </el-button>
              <el-dropdown
                v-has:esc_stock_receipt_workbench_esc_tsk_stock_pur_receipt_import
              >
                <el-button type="primary" size="mini" style="margin-left: 10px">
                  {{ $t('grid.others.importMatch') }}<i class="el-icon-arrow-down el-icon--right" />
                </el-button>
                <el-dropdown-menu
                  size="mini"
                  style="padding: 5px; float: right; margin-right: 14px"
                >
                  <el-dropdown-item
                    size="mini"
                    style="padding: 0 5px; line-height: 15px"
                  >
                    <importBtn
                      style="font-size: 12px; padding: 5px"
                      class=""
                      action="/api/esc/stock/stockToDelivery/wb/import/10"
                      action-success-url="/esc/stock/stockToDelivery/wb/importSuccessData/10"
                      type="nonebut1"
                      :query-params="{ isIntercept: '1' }"
                      :btn-text="$t('grid.others.OutboundImport')"
                      @success="onupesxfile"
                    />
                  </el-dropdown-item>
                  <el-dropdown-item style="padding: 0 5px; line-height: 15px">
                    <importBtn
                      style="font-size: 12px; padding: 5px"
                      type="nonebut2"
                      class=""
                      action="/api/esc/stock/stockToDelivery/wb/import/20"
                      action-success-url="/esc/stock/stockToDelivery/wb/importSuccessData/20"
                      :query-params="{ isIntercept: '1' }"
                      :btn-text="$t('grid.others.transferImport')"
                      @success="onupesxfile"
                    />
                  </el-dropdown-item>
                  <el-dropdown-item style="padding: 0 5px; line-height: 15px">
                    <importBtn
                      style="font-size: 12px; padding: 5px"
                      type="nonebut3"
                      class=""
                      action="/api/esc/stock/others/StockToOthers/import"
                      action-success-url="/esc/stock/others/StockToOthers/importSuccessData"
                      btn-text="转售直接导入"
                      @success="zsImport"
                    />
                  </el-dropdown-item>
                  <el-dropdown-item style="padding: 0 5px; line-height: 15px">
                    <importBtn
                      style="font-size: 12px; padding: 5px"
                      type="nonebut4"
                      class=""
                      is-sync
                      action="/api/esc/stock/others/StockToOthers/syn/import"
                      btn-text="转售后台导入"
                    />
                  </el-dropdown-item>
                  <el-dropdown-item style="padding: 0 5px; line-height: 15px">
                    <importBtn
                      style="font-size: 12px; padding: 5px"
                      type="nonebut5"
                      class=""
                      action="/api/esc/stock/stockToDelivery/wb/import/30"
                      action-success-url="/esc/stock/stockToDelivery/wb/importSuccessData/30"
                      :query-params="{ isIntercept: '1' }"
                      btn-text="直放导入"
                      @success="onupesxfile"
                    />
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </span>
          </span>
          <!-- 在途货物 -->
          <span v-show="zaituBtn">
            <span v-show="addToCartBtn" class="mr-10">
              <el-button
                v-has:esc_stock_receipt_workbench_esc_tsk_stock_receipt_join
                type="primary"
                size="mini"
                :disabled="!selectedData.length"
                @click="addToCart"
              >
                {{ $t('grid.btns.addSalesShipmentGenerated') }}
              </el-button>
            </span>
            <span v-show="zhuanshouBtn">
              <el-button
                v-has:esc_stock_receipt_workbench_esc_tsk_stock_receipt__resale
                class="mr-10"
                type="primary"
                size="mini"
                :disabled="!selectedData.length"
                @click="tablezsfun"
              >
                转售
              </el-button>
            </span>
            <span v-show="otherBtn">
              <el-button
                v-has:esc_stock_receipt_workbench_esc_tsk_stock_receipt__out
                type="primary"
                size="mini"
                :disabled="ZTZFDisabled"
                @click="tablezffun"
              >
                {{ $t('grid.others.directRelease') }}
              </el-button>
              <el-button
                v-has:esc_stock_receipt_workbench_esc_tsk_stock_receipt_delivery
                type="primary"
                size="mini"
                :disabled="ZTCCDisabled"
                @click="ZTOutwarehouse('出仓')"
              >
                <!-- 出仓 -->
                {{ $t('grid.title.outbound') }}
              </el-button>
              <el-button
                v-has:esc_stock_receipt_workbench_esc_tsk_stock_receipt_HZ
                type="primary"
                size="mini"
                :disabled="ZTCCDisabled"
                @click="ZTOutwarehouse('货转')"
              >
                <!-- 货转 -->
                {{ $t('grid.others.transfer') }}
              </el-button>
              <!-- <importBtn
                v-has:esc_stock_receipt_workbench_esc_tsk_stock_receipt_import
                class="ml-10"
                :action="activeName=='second'?'/api/esc/stock/thirdToDelivery/wb/import/30':'/api/esc/stock/onroadToDelivery/wb/import/30'"
                :action-success-url="activeName=='second'?'/esc/stock/thirdToDelivery/wb/importSuccessData/30':'/esc/stock/onroadToDelivery/wb/importSuccessData/30'"
                :btn-text="$t('grid.others.importMatch')"
                @success="onupesxfile"
              /> -->
              <el-dropdown
                v-has:esc_stock_receipt_workbench_esc_tsk_stock_receipt_import
              >
                <el-button type="primary" size="mini" style="margin-left: 10px">
                  {{ $t('grid.others.importMatch') }}<i class="el-icon-arrow-down el-icon--right" />
                </el-button>
                <el-dropdown-menu
                  size="mini"
                  style="padding: 5px; float: right; margin-right: 14px"
                >
                  <el-dropdown-item
                    size="mini"
                    style="padding: 0 5px; line-height: 15px"
                  >
                    <importBtn
                      style="font-size: 12px; padding: 5px"
                      class=""
                      action="/api/esc/stock/onroadToDelivery/wb/import/10"
                      action-success-url="/esc/stock/onroadToDelivery/wb/importSuccessData/10"
                      type="nonebut11"
                      :query-params="{ isIntercept: '1' }"
                      :btn-text="$t('grid.others.OutboundImport')"
                      @success="onupesxfile"
                    />
                  </el-dropdown-item>
                  <el-dropdown-item style="padding: 0 5px; line-height: 15px">
                    <importBtn
                      style="font-size: 12px; padding: 5px"
                      type="nonebut12"
                      class=""
                      action="/api/esc/stock/onroadToDelivery/wb/import/20"
                      action-success-url="/esc/stock/onroadToDelivery/wb/importSuccessData/20"
                      :query-params="{ isIntercept: '1' }"
                      :btn-text="$t('grid.others.transferImport')"
                      @success="onupesxfile"
                    />
                  </el-dropdown-item>
                  <el-dropdown-item
                    size="mini"
                    style="padding: 0 5px; line-height: 15px"
                  >
                    <importBtn
                      style="font-size: 12px; padding: 5px"
                      class=""
                      :action="'/api/esc/stock/onroadToDelivery/wb/import/30'"
                      :action-success-url="'/esc/stock/onroadToDelivery/wb/importSuccessData/30'"
                      type="nonebut5"
                      :query-params="{ isIntercept: '1' }"
                      btn-text="直放导入"
                      @success="onupesxfile"
                    />
                  </el-dropdown-item>
                  <el-dropdown-item style="padding: 0 5px; line-height: 15px">
                    <importBtn
                      style="font-size: 12px; padding: 5px"
                      type="nonebut6"
                      class=""
                      action="/api/esc/stock/others/OnRoadToOthers/import"
                      action-success-url="/esc/stock/others/OnRoadToOthers/importSuccessData"
                      btn-text="转售直接导入"
                      @success="zsImport"
                    />
                  </el-dropdown-item>
                  <el-dropdown-item style="padding: 0 5px; line-height: 15px">
                    <importBtn
                      style="font-size: 12px; padding: 5px"
                      type="nonebut7"
                      class=""
                      is-sync
                      action="/api/esc/stock/others/OnRoadToOthers/syn/import"
                      btn-text="转售后台导入"
                    />
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </span>
          </span>
          <!-- 钢厂可直放 -->
          <span v-show="zhifangBtn">
            <span v-show="addToCartBtn" class="mr-10">
              <el-button
                v-has:esc_stock_receipt_workbench_esc_con_third_delivery_list_join
                type="primary"
                size="mini"
                :disabled="!selectedData.length"
                @click="addToCart"
              >
                {{ $t('grid.btns.addSalesShipmentGenerated') }}
              </el-button>
            </span>
            <span v-show="zhuanshouBtn">
              <el-button
                v-has:esc_stock_receipt_workbench_esc_con_third_delivery_list__resale
                class="mr-10"
                type="primary"
                size="mini"
                :disabled="!selectedData.length"
                @click="tablezsfun"
              >
                转售
              </el-button>
            </span>
            <span v-show="otherBtn">
              <el-button
                v-has:esc_stock_receipt_workbench_esc_con_third_delivery_list__out
                type="primary"
                size="mini"
                :disabled="!selectedData.length"
                @click="tablezffun"
              >
                {{ $t('grid.others.directRelease') }}
              </el-button>
              <!-- <importBtn
                v-has:esc_stock_receipt_workbench_esc_con_third_delivery_list_import
                class="ml-10"
                :action="activeName=='second'?'/api/esc/stock/thirdToDelivery/wb/import/30':'/api/esc/stock/onroadToDelivery/wb/import/30'"
                :action-success-url="activeName=='second'?'/esc/stock/thirdToDelivery/wb/importSuccessData/30':'/esc/stock/onroadToDelivery/wb/importSuccessData/30'"
                :btn-text="$t('grid.others.importMatch')"
                @success="onupesxfile"
              /> -->
              <el-dropdown
                v-has:esc_stock_receipt_workbench_esc_con_third_delivery_list_import
              >
                <el-button type="primary" size="mini" style="margin-left: 10px">
                  {{ $t('grid.others.importMatch') }}<i class="el-icon-arrow-down el-icon--right" />
                </el-button>
                <el-dropdown-menu
                  size="mini"
                  style="padding: 5px; float: right; margin-right: 14px"
                >
                  <el-dropdown-item
                    size="mini"
                    style="padding: 0 5px; line-height: 15px"
                  >
                    <importBtn
                      style="font-size: 12px; padding: 5px"
                      class=""
                      :action="'/api/esc/stock/thirdToDelivery/wb/import/30'"
                      :action-success-url="'/esc/stock/thirdToDelivery/wb/importSuccessData/30'"
                      type="nonebut8"
                      :query-params="{ isIntercept: '1' }"
                      btn-text="直放导入"
                      @success="onupesxfile"
                    />
                  </el-dropdown-item>
                  <el-dropdown-item style="padding: 0 5px; line-height: 15px">
                    <importBtn
                      style="font-size: 12px; padding: 5px"
                      type="nonebut9"
                      class=""
                      action="/api/esc/stock/others/ThirdToOthers/import"
                      action-success-url="/esc/stock/others/ThirdToOthers/importSuccessData"
                      btn-text="转售直接导入"
                      @success="zsImport"
                    />
                  </el-dropdown-item>
                  <el-dropdown-item style="padding: 0 5px; line-height: 15px">
                    <importBtn
                      style="font-size: 12px; padding: 5px"
                      type="nonebut10"
                      class=""
                      is-sync
                      action="/api/esc/stock/others/ThirdToOthers/syn/import"
                      btn-text="转售后台导入"
                    />
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </span>
          </span>
          <span v-show="activeName=='fifth'">
            <span v-show="otherBtn">
              <el-button
                type="primary"
                size="mini"
                :disabled="!selectedData.length"
                @click="zfhtfun"
              >
                {{ $t('grid.others.directRelease') }}
              </el-button>
            </span>
          </span></cnd-btn-position>
        <el-tab-pane :label="$t('grid.others.purchaseArrival')" name="fourth">
          <div class="h100 flexV">
            <steelTradeAggrid
              v-if="activeName == 'fourth'"
              ref="aggrid"
              class="tabstablebor"
              :heightinif="heightinif"
              :column-defs="stockToDelivery"
              :child-column-defs="childstockToDelivery"
              :row-data="rowDatacg"
              :load-data="stockToDeliverywb"
              :load-detail="stockToDeliverywbDetail"
              :header-total="headerCount"
              :footer-total="footerCount"
              :auto-load-data="false"
              :show-header-select="false"
              table-selection="multiple"
              full-row-type="child"
              child-row-key="sId"
              row-key="sId"
              children-list-key="detailVos"
              is-subtable
              @selectedChange="handleFooterCount"
              @childCellValueChanged="childCellValueChangedfun"
            />
            <template v-if="dtype !== 'oa' && activeName == 'fourth'">
              <cndShoppingCart
                ref="shoppingCart"
                v-has:esc_stock_receipt_workbench_esc_tsk_stock_pur_receipt_join
                btn-key="esc_stock_receipt_workbench_esc_tsk_stock_pur_receipt_HZ"
                :title="$t('grid.others.salesPendingShipmentDetail')"
                :submit-text="$t('grid.others.transfer')"
                row-key="sId"
                :button-title="$t('grid.others.pendingSalesOrderGeneration')"
                child-row-key="sId"
                full-row-type="parent"
                children-list-key="detailVos"
                :submit-disabled="!!shoparr.every((el) => el.sIsNullStockType === true)"
                :columns="arrivalshoppingCartColumn"
                :merge-config="shoppingCartMerge"
                :count-list="shoppingCartCountList"
                :validate-key="[
                  'sCompanyId',
                  'sSupplierId',
                  'sWarehouseId',
                  'sCustomerId'
                ]"
                @selectedChange="handleShoppingCartCount"
                @submit="submit($event,$t('grid.others.transfer'))"
              >
                <div slot="opeartions">
                  <el-button
                    v-has:esc_stock_receipt_workbench_esc_tsk_stock_pur_receipt_resale
                    type="primary"
                    size="small"
                    :disabled="shoparr.length == 0"
                    @click="zsshopcart"
                  >
                    转售
                  </el-button>
                  <el-button
                    v-has:esc_stock_receipt_workbench_esc_tsk_stock_pur_receipt_out
                    type="primary"
                    size="small"
                    :disabled="shoparr.length == 0 || !shoparr.every((el) => el.sIsNullStockType === true)"
                    @click="outshopcart(30)"
                  >
                    直放
                  </el-button>
                  <el-button
                    v-has:esc_stock_receipt_workbench_esc_tsk_stock_pur_receipt_delivery
                    size="small"
                    type="primary"
                    :disabled="shoparr.length == 0 || !!shoparr.every((el) => el.sIsNullStockType === true)"
                    @click="outshopcart(10)"
                  >
                    {{ $t('grid.title.outbound') }}
                  </el-button>
                </div>
              </cndShoppingCart>
            </template>
          </div>
        </el-tab-pane>
        <el-tab-pane :label="$t('grid.others.goodsInTransit')" name="third">
          <div class="h100 flexV">
            <steelTradeAggrid
              v-if="activeName == 'third'"
              ref="aggrid"
              class="tabstablebor"
              :heightinif="heightinif"
              :column-defs="mainColumnDefs"
              :child-column-defs="mainChildColumnDefs"
              :row-data="rowDatazt"
              :load-data="onroadToDelivery"
              :load-detail="onroadToDeliveryDetail"
              full-row-type="child"
              :header-total="headerCount"
              :footer-total="footerCount"
              :auto-load-data="false"
              :show-header-select="false"
              table-selection="multiple"
              row-key="sId"
              child-row-key="sId"
              children-list-key="wbVos"
              is-subtable
              @selectedChange="handleFooterCount($event,'zaitu')"
              @childCellValueChanged="childCellValueChangedfun"
            />
            <template v-if="dtype !== 'oa' && activeName == 'third'">
              <cndShoppingCart
                ref="shoppingCart"
                v-has:esc_stock_receipt_workbench_esc_tsk_stock_receipt_join
                btn-key="esc_stock_receipt_workbench_esc_tsk_stock_receipt_HZ"
                :title="$t('grid.others.salesPendingShipmentDetail')"
                :submit-text="$t('grid.others.transfer')"
                row-key="sId"
                :button-title="$t('grid.others.pendingSalesOrderGeneration')"
                child-row-key="sId"
                full-row-type="parent"
                :validate-key="[
                  'sCompanyId',
                  'sSupplierId',
                  'sWarehouseId',
                  'sCustomerId'
                ]"
                children-list-key="wbVos"
                :submit-disabled="!shoparr.every(el => el.sWarehouseType === '30' && el.sCustomType === '10')"
                :columns="shoppingCartColumns"
                :merge-config="shoppingCartMerge"
                :count-list="shoppingCartCountList"
                @selectedChange="handleShoppingCartCount"
                @submit="zfshopcart('货转')"
              >
                <div slot="opeartions">
                  <el-button
                    v-has:esc_stock_receipt_workbench_esc_tsk_stock_receipt__resale
                    type="primary"
                    size="small"
                    :disabled="shoparr.length == 0"
                    @click="zsshopcart"
                  >
                    转售
                  </el-button>
                  <el-button
                    v-has:esc_stock_receipt_workbench_esc_tsk_stock_receipt__out
                    type="primary"
                    size="small"
                    :disabled="shoparr.length == 0 || !shoparr.every(el => el.sWarehouseType === '30' && el.sCustomType === '20')"
                    @click="zfshopcart('直放')"
                  >
                    直放
                  </el-button>
                  <el-button
                    v-has:esc_stock_receipt_workbench_esc_tsk_stock_receipt_delivery
                    type="primary"
                    size="small"
                    :disabled="shoparr.length == 0 || !shoparr.every(el => el.sWarehouseType === '30' && el.sCustomType === '10')"
                    @click="zfshopcart('出仓')"
                  >
                    <!-- 出仓 -->
                    {{ $t('grid.title.outbound') }}
                  </el-button>
                </div>
              </cndShoppingCart>
            </template>
          </div>
        </el-tab-pane>
        <el-tab-pane :label="$t('grid.others.steelFactoryDirectReleaseList')" name="second">
          <div class="h100 flexV">
            <steelTradeAggrid
              v-if="activeName == 'second'"
              ref="aggrid"
              class="tabstablebor"
              :heightinif="heightinif"
              full-row-type="child"
              :column-defs="columnDefs"
              :child-column-defs="childColumnDefs"
              :row-data="rowData"
              :load-data="loadData"
              :load-detail="loadDetail"
              :header-total="headerCount"
              :footer-total="footerCount"
              :auto-load-data="false"
              :show-header-select="false"
              table-selection="multiple"
              row-key="sId"
              child-row-key="sId"
              children-list-key="detailVos"
              is-subtable
              @childRowValueChanged="
                rowValueChanged(
                  $event,
                  'aggrid',
                  'rowData',
                  'detailVos',
                  'handleFooterCount',
                  'vCurContractQty',
                  'vCurQty',
                  'vLeftContractQty',
                  'vLeftQty'
                )
              "
              @selectedChange="handleFooterCount"
              @childCellValueChanged="childCellValueChangedfun"
            />
            <template v-if="dtype !== 'oa' && activeName == 'second'">
              <cndShoppingCart
                ref="shoppingCart"
                v-has:esc_stock_receipt_workbench_esc_con_third_delivery_list_join
                btn-key="esc_stock_receipt_workbench_esc_con_third_delivery_list__out"
                :title="$t('grid.others.salesPendingShipmentDetail')"
                :submit-text="$t('grid.others.directRelease')"
                row-key="sId"
                :button-title="$t('grid.others.pendingSalesOrderGeneration')"
                full-row-type="parent"
                child-row-key="sId"
                :cur-config="curConfig"
                children-list-key="detailVos"
                :columns="shoppingCartColumn"
                :merge-config="shoppingCartMerge"
                :validate-key="[
                  'sCompanyId',
                  'sSupplierId',
                  'sWarehouseId',
                  'sCustomerId',
                  'sIsDisplay',
                  'sExtend6'
                ]"
                :error-dict="{
                  'sExtend6':'货物存放地',
                }"
                :count-list="shoppingCartCountList"
                @selectedChange="handleShoppingCartCount"
                @submit="submit($event,$t('grid.others.directRelease'))"
              >
                <div slot="opeartions">
                  <el-button
                    v-has:esc_stock_receipt_workbench_esc_con_third_delivery_list__resale
                    type="primary"
                    size="small"
                    :disabled="shoparr.length == 0"
                    @click="zsshopcart"
                  >
                    转售
                  </el-button>
                </div>
              </cndShoppingCart>
            </template>
          </div>
        </el-tab-pane>
        <el-tab-pane label="直放合同发货" name="fifth">
          <div class="h100 flexV ">
            <steelTradeAggrid
              v-if="activeName=='fifth'"
              ref="aggrid"
              class="tabstablebor"
              :heightinif="heightinif"
              :column-defs="zfhtcolumnDefs"
              :row-data="rowDatazfht"
              :load-data="zfhtloadData"
              :auto-load-data="false"
              table-selection="multiple"
              row-key="sId"
              @selectedChange="handleFooterCount($event,'zfht')"
            />
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>
<script>
import {
  stockthirdToDelivery,
  stockthirddetaillist,
  onroadToDeliverypage,
  onroadToDeliverypagelist,
  stockToDeliverywbpage,
  stockToDeliverywbpageSum,
  stockToDeliverywbpagelist,
  onroadToDeliverywbbut,
  thirdToDeliverywbbut,
  stockToDeliverywbcerate,
  ThirdToOthersCreate,
  OnRoadToOthersCreate,
  StockToOthersCreate,
  stockdeliveryget,
  stockOthersget,
  thirdContractDelivery,
  thirdContractDeliveryWbCreate,
  thirdContractDeliveryWbCreateValidate
} from '@/api/logistics/saleDelivery/saleorder'
import {
  getParameterLimit
} from '@/api/logistics/saleDelivery/common'
import cndShoppingCart from '@/components/cndShoppingCart/index'
import importBtn from '@/components/importBtn'
import curMixin from '@/utils/curMixin'

// import { mainColumnDefs, mainChildColumnDefs, shoppingCartColumns, arrivalshoppingCartColumn, shoppingCartColumn, childColumnDefs, columnDefs, childstockToDelivery, stockToDelivery } from './tabledata.js'
import tableData from './tabledata.js'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import { SteelFormat } from 'cnd-horizon-utils'

export default {
  name: 'SalesdeliveryWorkbench',
  components: { steelTradeAggrid, cndShoppingCart, importBtn },
  mixins: [curMixin, tableData],
  props: {
    heightinif: {
      type: [Number, String],
      default: ''
    },
    dtype: {
      type: String,
      default: ''
    },
    zsBtn: {
      type: Boolean,
      default: true
    },
    otherBtn: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      searchInfo: null,
      selectedData: [],
      zhifangDisabled: true,
      ZTZFDisabled: true,
      ZTCCDisabled: true,
      curConfig: {
        vCurQty: 'vCurContractQty',
        vCurPkgQty: 'vCurQty',
        vLeftQty: 'vLeftQty',
        vLeftPkgQty: 'vLeftPkgQty'
      },
      formItems: [
        {
          label: this.$t('grid.others.logisticsOrderNumber'),
          value: 'sCode',
          type: 'elInput',
          placeholder: this.$t('grid.others.pleaseEnterTheLogisticBillNumber')
        },
        {
          label: this.$t('grid.title.salesContractNumber'),
          value: 'vSaleContractCode',
          type: 'elInput',
          placeholder: this.$t('grid.others.pleaseEnterTheSalesContractNumber')
        },
        {
          label: this.$t('grid.others.customer'),
          value: 'sCustomerId',
          type: 'cndInputDialogItem',
          dialogType: 'customer',
          defaultUrl: '/esc/customer/page',
          option: { valueKey: 'sPath' },
          placeholder: this.$t('grid.others.pleaseSelectTheCustomer')
        },
        {
          label: this.$t('grid.title.company'),
          value: 'sCompanyId',
          type: 'cndInputDialog',
          dialogType: 'company',
          placeholder: this.$t('grid.others.pleaseSelectCompany')
        },
        {
          label: this.$t('grid.others.warehouse'),
          value: 'sWarehouseId',
          type: 'cndInputDialogItem',
          defaultUrl: '/esc/warehouse/info/dialog/v2/page-dialog',
          option: { valueKey: 'sPath' },
          placeholder: this.$t('grid.others.pleaseSelectAWarehouse')
        },
        {
          label: this.$t('grid.others.carriageNumber'),
          value: 'sExtend5',
          type: 'elInput'
        },
        {
          label: this.$t('grid.title.purchaseContractNumber'),
          value: 'sPurContractCode',
          type: 'elInput',
          placeholder: this.$t('grid.others.enterPurchaseContractNumber')
        },
        {
          label: this.$t('grid.others.itemNumberTag'),
          value: 'sProjectCode',
          type: 'elInput',
          placeholder: this.$t('grid.others.pleaseEnterTheItemNumberTag')
        },
        {
          label: this.$t('grid.title.accountingGroup'),
          value: 'sCheckGroupId',
          type: 'cndInputDialog',
          dialogType: 'cost',
          placeholder: this.$t('grid.others.pleaseSelectAccountingGroup')
        },
        {
          label: this.$t('grid.others.department'),
          value: 'sDepartmentId',
          type: 'cndInputDialog',
          dialogType: 'depart',
          placeholder: this.$t('grid.others.pleaseSelectTheDepartment')
        },
        {
          label: this.$t('grid.title.personnel'),
          value: 'sStaffId',
          type: 'cndInputDialogItem',
          option: { valueKey: 'sPath' },
          itemType: 'occultation',
          dialogType: 'staff',
          placeholder: this.$t('grid.others.pleaseSelectPersonnel')
        },
        {
          label: this.$t('grid.others.accountingFilter'),
          value: 'vIsCor',
          type: 'elSelect',
          itemType: 'occultation',
          default: '0',
          dict: 'dev.common.verify.finish.type',
          placeholder: this.$t('grid.others.pleaseSelectTheUnderwritingFilter')
        },
        {
          label: '经营单位',
          value: 'sManagementId',
          type: 'cndInputDialog',
          dialogType: 'escOrg',
          itemType: 'occultation'
        },
        {
          label: '作业平台',
          value: 'sOperaPlat',
          // default: this.dtype === 'oa' ? '10' : '',
          // hidden: this.dtype === 'oa',
          type: 'elSelect',
          dict: 'opera.plat.value',
          placeholder: this.$t('components.pleaseSelect')
        },
        {
          label: this.$t('grid.others.steelCoilNumber'),
          value: 'sExtend4',
          type: 'elInput'
        },
        {
          label: this.$t('grid.title.createdAt'),
          value: ['sCreateTime', 'vCreateTimeTo'],
          placeholder: [this.$t('grid.others.pleaseSelectTheDate'), this.$t('grid.others.pleaseSelectTheDate')],
          itemType: 'occultation',
          unlinkPanels: true,
          type: 'elDatePicker'
        }
      ],
      shoparr: [],
      rowData: [],
      rowDatazt: [],
      rowDatacg: [],
      shoppingCartMerge: [
        {
          field: 'vCurContractQty',
          mode: 'cover'
        },
        {
          field: 'vCurQty',
          mode: 'cover'
        }
      ],
      shoppingCartCountList: [],
      headerCount: null,
      footerCount: null,
      detailDialogVisible: false,
      // childstockToDelivery,
      // stockToDelivery,
      selectedId: null,
      activeName: 'fourth',
      rowDatazfht: [],
      compare: false,
      allowLength: 200
    }
  },
  computed: {
    addToCartBtn() {
      return this.dtype !== 'oa'
    },
    caigouBtn() {
      return this.activeName === 'fourth'
    },
    zaituBtn() {
      return this.activeName === 'third'
    },
    zhifangBtn() {
      return this.activeName === 'second'
    },
    zhuanshouBtn() {
      return this.zsBtn
    }
  },
  mounted() {
    this.onSearch(false)
    this.getAllowLength()
  },
  methods: {
    // 出仓条数做成可配置
    getAllowLength() {
      getParameterLimit({
        code: 'FHDMX-LIMIT'
      }).then(res => {
        if (res.data.value) {
          this.allowLength = Number(res.data.value)
        }
      })
    },
    tabClick() {
      this.headerCount = null
      this.footerCount = null
      this.rowDatazt = []
      this.rowDatacg = []
      this.rowData = []
      this.rowDatazfht = []
    },
    // 出仓
    onSearch(load = true) {
      const searchInfo = this.$refs.searchForm.getSearchData()
      this.searchInfo = searchInfo
      if (load) {
        this.$refs.aggrid.loadTableData()
      }
    },
    onupesxfile(v) {
      if (v.data) {
        // this.$parentRouter.push({ path: '/egl/salesdeliveryList', query: { id: res.data.sId }})
        stockdeliveryget({ sInvoiceId: v.data.sId }).then((res) => {
          this.$router.push({
            path: `/salesdeliveryDialog/${res.data.sId}`,
            query: {
              Id: res.data.sId,
              status: res.data.sSheetStatus,
              type: 'edit',
              name: `${this.$t('grid.others.salesOrder')}【${res.data.sCode}】`,
              activeId: localStorage.getItem('menuId')
            }
          })
          this.$emit('hidebody', res.data.sId)
        })
      }
    },
    zsImport(v) {
      if (v.data) {
        // this.$parentRouter.push({ path: '/egl/salesdeliveryList', query: { id: res.data.sId }})
        stockOthersget({ sInvoiceId: v.data.sId }).then((res) => {
          this.$router.push({
            path: `/salesdeliveryResaleDialog/${res.data.sId}`,
            query: {
              Id: res.data.sId,
              status: res.data.sSheetStatus,
              type: 'edit',
              name: `转售单【${res.data.sCode}】`,
              activeId: localStorage.getItem('menuId')
            }
          })
          this.$emit('hidebody', res.data.sId)
        })
      }
    },
    outshopcart(type = 10) {
      const data = this.$refs.shoppingCart
        .getCartData()
        .filter((row) => row._selected)
      this.shoparr = data
      const tips = {
        10: this.$t('grid.others.thisActionWillBeOutntinueKey'),
        20: '此操作将货转销售发货单, 是否继续?',
        30: this.$t('grid.others.thisActionWillPlayTntinueKey')
      }
      this.$confirm(
        tips[type],
        this.$t('grid.others.prompt'),
        {
          confirmButtonText: this.$t('btns.confirm'),
          cancelButtonText: this.$t('btns.cancel'),
          type: 'warning'
        }
      ).then(() => {
        this.$refs.aggrid.gridApi.clearFocusedCell()
        if (data.length > 0) {
          stockToDeliverywbcerate(type, data).then((res) => {
            this.$message.success(this.$t('tips.addedSuccessfully'))
            this.$router.push({
              path: `/salesdeliveryDialog/${res.data.sId}`,
              query: {
                Id: res.data.sId,
                status: res.data.sSheetStatus,
                type: 'edit',
                name: `${this.$t('grid.others.salesOrder')}【${
                  res.data.sCode
                }】`,
                activeId: localStorage.getItem('menuId')
              }
            })
            this.$emit('hidebody', res.data.sId)
          })
        } else {
          this.$message.warning(this.$t('grid.others.pleaseCheckTheBox'))
        }
      })
    },
    zfshopcart(e) {
      const data = this.$refs.shoppingCart
        .getCartData()
        .filter((row) => row._selected)
      this.shoparr = data
      this.$confirm(`此操作将${e}此数据, 是否继续?`, this.$t('grid.others.prompt'), {
        confirmButtonText: this.$t('btns.confirm'),
        cancelButtonText: this.$t('btns.cancel'),
        type: 'warning'
      }
      ).then(() => {
        this.$refs.aggrid.gridApi.clearFocusedCell()
        const type = {
          '出仓': '10',
          '货转': '20',
          '直放': '30'
        }
        if (data.length > 0) {
          onroadToDeliverywbbut(data, type[e]).then((res) => {
            this.$message.success(
              this.$t('grid.others.successfulDirectRelease')
            )
            this.$router.push({
              path: `/salesdeliveryDialog/${res.data.sId}`,
              query: {
                Id: res.data.sId,
                status: res.data.sSheetStatus,
                type: 'edit',
                name: `${this.$t('grid.others.salesOrder')}【${
                  res.data.sCode
                }】`,
                activeId: localStorage.getItem('menuId')
              }
            })
            this.$emit('hidebody', res.data.sId)
          })
        } else {
          this.$message.warning(this.$t('grid.others.pleaseCheckTheBox'))
        }
      })
    },
    zsshopcart() {
      const data = this.$refs.shoppingCart
        .getCartData()
        .filter((row) => row._selected)
      const apilist = {
        second: ThirdToOthersCreate,
        third: OnRoadToOthersCreate,
        fourth: StockToOthersCreate
      }
      console.log(apilist[this.activeName])
      this.shoparr = data
      this.$confirm(
        '此操作将转售此数据, 是否继续?',
        this.$t('grid.others.prompt'),
        {
          confirmButtonText: this.$t('btns.confirm'),
          cancelButtonText: this.$t('btns.cancel'),
          type: 'warning'
        }
      ).then(() => {
        this.$refs.aggrid.gridApi.clearFocusedCell()
        if (data.length > 0) {
          apilist[this.activeName](data).then((res) => {
            this.$message.success(this.$t('tips.addedSuccessfully'))
            this.$router.push({
              path: `/salesdeliveryResaleDialog/${res.data.sId}`,
              query: {
                Id: res.data.sId,
                status: res.data.sSheetStatus,
                type: 'edit',
                name: `转售单【${res.data.sCode}】`,
                activeId: localStorage.getItem('menuId')
              }
            })
            this.$emit('hidebody', res.data.sId)
          })
        } else {
          this.$message.warning(this.$t('grid.others.pleaseCheckTheBox'))
        }
      })
    },

    // 表格点击
    onRowDoubleClicked(params) {
      this.$router.push({
        path: `/salesdeliveryDialog/${params.data.sId}`,
        query: {
          Id: params.data.sId,
          status: params.data.sSheetStatus,
          type: 'edit',
          name: `${this.$t('grid.others.salesOrder')}【${params.data.sCode}】`,
          activeId: localStorage.getItem('menuId')
        }
      })
    },
    // 购物车事件
    handleShoppingCartCount(list) {
      let vCurContractQty = 0
      let vCurQty = 0
      const details = list.filter((item) => item._selected)
      const vCount = details.length
      details.forEach((el) => {
        vCurContractQty += Number(el.vCurContractQty)
        vCurQty += Number(el.vCurQty)
      })
      // this.setCount(vCount, vSumQty, vSumAmt, 'footerCount')
      this.setCountshop(
        vCount,
        vCurContractQty,
        vCurQty,
        'shoppingCartCountList'
      )
      this.shoparr = list
    },
    // 购物车事件
    // 在途购物车加入销售购物车
    addToCart() {
      this.$refs.aggrid.stopChildEditing()
      this.$refs.aggrid.getSelectedData((list) => {
        if (this.activeName === 'second') {
          this.$refs.shoppingCart.$refs.aggrid.columnApi.setColumnVisible('sExtend6', list[0].sSupplierId === '1187678613356625921')
        }
        list.map((item) => {
          item.vCurQty = item.vLeftQty
          item.vCurContractQty = item.vLeftContractQty
        })
        this.$refs.shoppingCart.addToCart(list)
        this.shoparr = list
      })
    },
    // 在途购物车直放
    submit(list, name) {
      this.$confirm(
        `此操作将${name}销售发货单, 是否继续?`,
        this.$t('grid.others.prompt'),
        {
          confirmButtonText: this.$t('btns.confirm'),
          cancelButtonText: this.$t('btns.cancel'),
          type: 'warning'
        }
      ).then(() => {
        this.$refs.aggrid.gridApi.clearFocusedCell()
        if (this.activeName === 'second') {
          thirdToDeliverywbbut(list).then((res) => {
            this.$message.success(
              this.$t('grid.others.successfulDirectRelease')
            )
            this.$refs.shoppingCart.clearCart()
            this.$refs.shoppingCart.closeDrawer()
            this.$router.push({
              path: `/salesdeliveryDialog/${res.data.sId}`,
              query: {
                Id: res.data.sId,
                status: res.data.sSheetStatus,
                type: 'edit',
                name: `${this.$t('grid.others.salesOrder')}【${
                  res.data.sCode
                }】`,
                activeId: localStorage.getItem('menuId')
              }
            })
            this.$emit('hidebody', res.data.sId)
          })
        } else if (this.activeName === 'third') {
          stockToDeliverywbcerate(20, list).then((res) => {
            this.$message.success(this.$t('tips.addedSuccessfully'))
            this.$refs.shoppingCart.clearCart()
            this.$refs.shoppingCart.closeDrawer()
            this.$router.push({
              path: `/salesdeliveryDialog/${res.data.sId}`,
              query: {
                Id: res.data.sId,
                status: res.data.sSheetStatus,
                type: 'edit',
                name: `${this.$t('grid.others.salesOrder')}【${
                  res.data.sCode
                }】`,
                activeId: localStorage.getItem('menuId')
              }
            })
            this.$emit('hidebody', res.data.sId)
          })
        } else if (this.activeName === 'fourth') {
          stockToDeliverywbcerate(20, list).then((res) => {
            this.$message.success(this.$t('tips.addedSuccessfully'))
            this.$refs.shoppingCart.clearCart()
            this.$refs.shoppingCart.closeDrawer()
            this.$router.push({
              path: `/salesdeliveryDialog/${res.data.sId}`,
              query: {
                Id: res.data.sId,
                status: res.data.sSheetStatus,
                type: 'edit',
                name: `${this.$t('grid.others.salesOrder')}【${
                  res.data.sCode
                }】`,
                activeId: localStorage.getItem('menuId')
              }
            })
            this.$emit('hidebody', res.data.sId)
          })
        }
      })
    },
    childCellValueChangedfun(params) {
      const { data, colDef, newValue, oldValue } = params
      let updateTarget
      if (newValue === oldValue) {
        return
      }
      if (this.activeName === 'third') {
        updateTarget = this.rowDatazt[data.parentId].wbVos.find(
          (item) => item.sId === data.sId
        )
      } else if (this.activeName === 'fourth') {
        updateTarget = this.rowDatacg[data.parentId].detailVos.find(
          (item) => item.sId === data.sId
        )
      } else if (this.activeName === 'second') {
        updateTarget = this.rowData[data.parentId].detailVos.find(
          (item) => item.sId === data.sId
        )
        if (updateTarget.sIsDisplay === '0') {
          if (newValue > updateTarget.vLeftContractQty) {
            updateTarget.vCurQty = newValue
            updateTarget.vCurContractQty = newValue
          } else {
            updateTarget.vCurQty = newValue
          }
        }
      }
      updateTarget[colDef.field] = newValue
    },
    // 钢厂可直放子表
    loadDetail(data) {
      return new Promise((resolve, reject) => {
        stockthirddetaillist(
          {
            sDeliveryId: data.sId
          },
          this.searchInfo
        )
          .then((res) => {
            resolve(res.data)
          })
          .catch(() => {
            reject([])
          })
      })
    },

    // 在途子表
    onroadToDeliveryDetail(data) {
      return new Promise((resolve, reject) => {
        onroadToDeliverypagelist(
          {
            id: data.sId
          },
          this.searchInfo
        )
          .then((res) => {
            const newData = res.data.map(v => {
              return {
                ...v,
                sWarehouseType: data.sWarehouseType,
                sCustomType: data.sCustomType,
                sIsZeroStock: data.sIsZeroStock
              }
            })
            resolve(newData)
          })
          .catch(() => {
            reject([])
          })
      })
    },
    // 采购子表
    stockToDeliverywbDetail(data) {
      return new Promise((resolve, reject) => {
        stockToDeliverywbpagelist(
          {
            id: data.sId
          },
          this.searchInfo
        )
          .then((res) => {
            resolve(res.data)
          })
          .catch(() => {
            reject([])
          })
      })
    },
    loadData(pagination = { page: 0, limit: 20 }) {
      return new Promise((resolve, reject) => {
        stockthirdToDelivery(
          { ...pagination },
          {
            ...this.searchInfo
          }
        )
          .then((res) => {
            this.rowData = res.data.thirdPage.content.map((item) => {
              item._selected = false
              item._selectedKeys = []
              item.detailVos = []
              return item
            })
            const { vCount, vSumAmt, vSumQty, vSumContractQty } = res.data
            this.setCount(
              vCount,
              vSumContractQty,
              vSumQty,
              vSumAmt,
              'headerCount'
            )
            resolve(res.data.thirdPage.totalElements)
            console.log(res)
          })
          .catch(() => {
            this.totalRowData = ''
            reject(0)
          })
      })
    },
    onroadToDelivery(pagination = { page: 0, limit: 20 }) {
      return new Promise((resolve, reject) => {
        onroadToDeliverypage(
          { ...pagination },
          {
            ...this.searchInfo
          }
        )
          .then((res) => {
            if (res.data.onroadPage === null) {
              this.rowDatazt = []
              resolve(0)
            }
            this.rowDatazt = res.data.onroadPage.content.map((item) => {
              item._selected = false
              item._selectedKeys = []
              item.wbVos = []
              return item
            })
            const { vCount, vSumAmt, vSumQty, vSumContractQty } = res.data
            // this.totalRowData = `共${vCount}条，合计：发货数量${vSumContractQty}吨，发货件数${vSumQty}件，发货金额${vSumAmt}元`
            // const { vCount, vSumAmt, vSumNetAmt, vSumQty, vSumVatAmt } = res.data
            this.setCount(
              vCount,
              vSumContractQty,
              vSumQty,
              vSumAmt,
              'headerCount'
            )
            resolve(res.data.onroadPage.totalElements)
            console.log(res)
          })
          .catch(() => {
            this.totalRowData = ''
            reject(0)
          })
      })
    },
    zfhtloadData(pagination) {
      return new Promise((resolve, reject) => {
        thirdContractDelivery({ ...pagination }, {
          ...this.searchInfo
        }).then(res => {
          this.rowDatazfht = res.data.thirdContractDeliveryPage.content.map(item => {
            item._selected = false
            item._selectedKeys = []
            return item
          })
          resolve(res.data.thirdContractDeliveryPage.totalElements)
        }).catch(() => {
          reject(0)
        })
      })
    },
    setCountshop(vCount = 0, vCurContractQty = 0, vCurQty = 0, flag) {
      this[flag] = [
        { count: vCount, key: 'count' },
        {
          title: this.$t('grid.title.quantity'),
          count: SteelFormat.formatThousandthSign(vCurContractQty, 4),
          unit: this.$t('grid.others.ton')
        },
        {
          title: this.$t('grid.others.numberOfPiecesTag'),
          count: SteelFormat.formatThousandthSign(vCurQty, 0),
          unit: this.$t('grid.others.pieces')
        }
      ]
      console.log(this[flag])
    },
    setCount(vCount = 0, vSumContractQty = 0, vSumQty = 0, vSumAmt = 0, flag) {
      this[flag] = [
        { count: vCount, key: 'count' },
        {
          title: this.$t('grid.others.remainingQuantity'),
          count: SteelFormat.formatThousandthSign(vSumContractQty, 4),
          unit: this.$t('grid.others.ton')
        },
        {
          title: this.$t('grid.others.remainingPieces'),
          count: SteelFormat.formatThousandthSign(vSumQty, 0),
          unit: this.$t('grid.others.pieces')
        }
      ]
      console.log(this[flag])
    },
    setCounts(vCount = 0, vSumContractQty = 0, vSumQty = 0, vSumAmt = 0, flag) {
      this[flag] = [
        { count: vCount, key: 'count' },
        {
          title: this.$t('grid.others.quantityConfirmedThisTime'),
          count: SteelFormat.formatThousandthSign(vSumContractQty, 4),
          unit: this.$t('grid.others.ton')
        },
        {
          title: this.$t('grid.others.theNumberOfConfirmedPieces'),
          count: SteelFormat.formatThousandthSign(vSumQty, 0),
          unit: this.$t('grid.others.pieces')
        }
      ]
      console.log(this[flag])
    },
    stockToDeliverywb(pagination = { page: 0, limit: 20 }) {
      return new Promise((resolve, reject) => {
        stockToDeliverywbpage(
          { ...pagination },
          {
            ...this.searchInfo
          }
        )
          .then((res) => {
            this.rowDatacg = res.data.stockPage.content.map((item) => {
              item._selected = false
              item._selectedKeys = []
              item.detailVos = []
              return item
            })
            const vCount = res.data.vCount
            stockToDeliverywbpageSum(
              { ...pagination },
              {
                ...this.searchInfo
              }
            ).then((v) => {
              const { vSumAmt, vSumQty, vSumContractQty } = v.data
              this.setCount(
                vCount,
                vSumContractQty,
                vSumQty,
                vSumAmt,
                'headerCount'
              )
            })
            resolve(res.data.stockPage.totalElements)
            console.log(res)
          })
          .catch(() => {
            this.totalRowData = ''
            reject(0)
          })
      })
    },
    checkLength(data) {
      return new Promise((resolve, reject) => {
        if (data.length > this.allowLength) {
          this.$confirm(`数据超过${this.allowLength}条，系统将自动截取前${this.allowLength}条数据，是否同意`, this.$t('grid.others.prompt'), {
            confirmButtonText: this.$t('btns.confirm'),
            cancelButtonText: this.$t('btns.cancel'),
            type: 'warning'
          }).then(() => {
            const slicedArr = data.slice(0, this.allowLength)
            resolve(slicedArr)
          })
        } else {
          resolve(data)
        }
      })
    },
    // 出仓 huozhuan
    Outwarehouse(e) {
      this.$refs.aggrid.stopChildEditing()
      this.$refs.aggrid.gridApi.clearFocusedCell()
      this.$confirm(`此操作将${e}此数据, 是否继续?`, this.$t('grid.others.prompt'), {
        confirmButtonText: this.$t('btns.confirm'),
        cancelButtonText: this.$t('btns.cancel'),
        type: 'warning'
      }).then(() => {
        this.$refs.aggrid.getSelectedData(async(selectList) => {
          if (selectList.length > 0) {
            const checkedData = await this.checkLength(selectList)
            const type = {
              '出仓': 10,
              '直放': 30,
              '货转': 20
            }
            stockToDeliverywbcerate(type[e], checkedData).then((res) => {
              this.$message.success(this.$t('tips.addedSuccessfully'))
              this.$router.push({
                path: `/salesdeliveryDialog/${res.data.sId}`,
                query: {
                  Id: res.data.sId,
                  status: res.data.sSheetStatus,
                  type: 'edit',
                  name: `${this.$t('grid.others.salesOrder')}【${
                    res.data.sCode
                  }】`,
                  activeId: localStorage.getItem('menuId')
                }
              })
              this.$emit('hidebody', res.data.sId)
            })
          } else {
            this.$message.warning(this.$t('grid.others.pleaseCheckTheBox'))
          }
        }, 'margeChild')
      })
        .catch(() => {})
    },
    ZTOutwarehouse(e) {
      this.$refs.aggrid.gridApi.clearFocusedCell()
      this.$refs.aggrid.stopChildEditing()
      this.$confirm(`此操作将${e}此数据, 是否继续?`, this.$t('grid.others.prompt'), {
        confirmButtonText: this.$t('btns.confirm'),
        cancelButtonText: this.$t('btns.cancel'),
        type: 'warning'
      }).then(() => {
        this.$refs.aggrid.getSelectedData(async(selectList) => {
          if (selectList.length > 0) {
            const checkedData = await this.checkLength(selectList)
            // if (selectList.some(e => e.sSaleContractCode !== selectList[0].sSaleContractCode)) {
            //   this.$message.error('合同需一致，请重新选择')
            //   return false
            // }
            const type = {
              '出仓': '10',
              '货转': '20'
            }
            onroadToDeliverywbbut(checkedData, type[e]).then(res => {
              this.$message.success(this.$t('tips.addedSuccessfully'))
              this.$router.push({
                path: `/salesdeliveryDialog/${res.data.sId}`,
                query: {
                  Id: res.data.sId,
                  status: res.data.sSheetStatus,
                  type: 'edit',
                  name: `${this.$t('grid.others.salesOrder')}【${res.data.sCode}】`,
                  activeId: localStorage.getItem('menuId')
                }
              })
              this.$emit('hidebody', res.data.sId)
            })
          } else {
            this.$message.warning(this.$t('grid.others.pleaseCheckTheBox'))
          }
        }, 'margeChild')
      })
        .catch(() => { })
    },
    handleFooterCount(rowData, type) {
      this.zhifangDisabled = true
      this.ZTZFDisabled = true
      this.ZTCCDisabled = true
      this.ZFHTDisabled = true
      if (type === 'zfht') {
        this.$refs.aggrid.getSelectedData(res => {
          this.selectedData = res || []
        })
        return
      }
      if (type === 'zaitu') {
        this.$refs.aggrid.getSelectedData(res => {
          if (res.length > 0) {
            this.ZTZFDisabled = !res.every(el => el.sWarehouseType === '30' && el.sCustomType === '20')
            this.ZTCCDisabled = !res.every(el => el.sCustomType === '10' || el.sIsZeroStock === '1')
          }
        })
      }
      this.$refs.aggrid.getSelectedData((res) => {
        this.selectedData = res || []
        if (res.length > 0) {
          this.zhifangDisabled = !res.every((el) => el.sIsNullStockType === true)
        }
        const vCount = res.length
        let vSumContractQty = 0
        const vSumAmt = 0
        let vSumQty = 0

        res.forEach((el) => {
          vSumContractQty += Number(el.vCurContractQty) || 0
          vSumQty += Number(el.vCurQty) || 0
        })
        this.setCounts(vCount, vSumContractQty, vSumQty, vSumAmt, 'footerCount')
      }, 'margeChild')
    },
    // 直放按钮
    tablezffun() {
      this.$refs.aggrid.stopChildEditing()
      this.$refs.aggrid.gridApi.clearFocusedCell()
      this.$confirm(
        this.$t('grid.others.thisActionWillPlayTntinueKey'),
        this.$t('grid.others.prompt'),
        {
          confirmButtonText: this.$t('btns.confirm'),
          cancelButtonText: this.$t('btns.cancel'),
          type: 'warning'
        }
      )
        .then(() => {
          this.$refs.aggrid.getSelectedData(async(selectList) => {
            if (selectList.length > 0) {
              const checkedData = await this.checkLength(selectList)
              if (this.activeName === 'second') {
                // 销售发货工作台钢厂直放按钮
                thirdToDeliverywbbut(checkedData).then((res) => {
                  this.$message.success(this.$t('tips.addedSuccessfully'))
                  this.$router.push({
                    path: `/salesdeliveryDialog/${res.data.sId}`,
                    query: {
                      Id: res.data.sId,
                      status: res.data.sSheetStatus,
                      type: 'edit',
                      name: `${this.$t('grid.others.salesOrder')}【${
                        res.data.sCode
                      }】`,
                      activeId: localStorage.getItem('menuId')
                    }
                  })
                  this.$emit('hidebody', res.data.sId)
                })
              } else if (this.activeName === 'third') {
                onroadToDeliverywbbut(checkedData, 30).then((res) => {
                  this.$message.success(this.$t('tips.addedSuccessfully'))
                  this.$router.push({
                    path: `/salesdeliveryDialog/${res.data.sId}`,
                    query: {
                      Id: res.data.sId,
                      status: res.data.sSheetStatus,
                      type: 'edit',
                      name: `${this.$t('grid.others.salesOrder')}【${
                        res.data.sCode
                      }】`,
                      activeId: localStorage.getItem('menuId')
                    }
                  })
                  this.$emit('hidebody', res.data.sId)
                })
              }
            } else {
              this.$message.warning(this.$t('grid.others.pleaseCheckTheBox'))
            }
          }, 'margeChild')
        })
        .catch(() => {})
    },
    async zfhtfun() {
      await this.zfhtfunV2()
      this.$refs.aggrid.getSelectedData(async(selectList) => {
        thirdContractDeliveryWbCreate(selectList, 30).then(res => {
          this.$message.success(this.$t('tips.addedSuccessfully'))
          this.$router.push({
            path: `/salesdeliveryDialog/${res.data.sId}`,
            query: {
              Id: res.data.sId,
              status: res.data.sSheetStatus,
              type: 'edit',
              name: `${this.$t('grid.others.salesOrder')}【${
                res.data.sCode
              }】`,
              activeId: localStorage.getItem('menuId')
            }
          })
          this.$emit('hidebody', res.data.sId)
        })
      })
    },
    zfhtfunV2() {
      return new Promise((resolve, reject) => {
        this.$refs.aggrid.getSelectedData(async(selectList) => {
          thirdContractDeliveryWbCreateValidate(selectList).then(res => {
            if (res.code === '0000') {
              this.$confirm(res.data || this.$t('grid.others.thisActionWillPlayTntinueKey'), this.$t('grid.others.prompt'), {
                confirmButtonText: this.$t('btns.confirm'),
                cancelButtonText: this.$t('btns.cancel'),
                type: 'warning'
              }).then(() => {
                resolve()
              })
            }
          })
        })
      })
    },
    tablezsfun() {
      this.$refs.aggrid.stopChildEditing()
      this.$refs.aggrid.gridApi.clearFocusedCell()
      this.$confirm(
        '此操作将转售此数据, 是否继续?',
        this.$t('grid.others.prompt'),
        {
          confirmButtonText: this.$t('btns.confirm'),
          cancelButtonText: this.$t('btns.cancel'),
          type: 'warning'
        }
      )
        .then(() => {
          if (this.activeName === 'second') {
            this.$refs.aggrid.getSelectedData(async(selectList) => {
              if (selectList.length > 0) {
                // const checkedData = await this.checkLength(selectList)
                ThirdToOthersCreate(selectList).then((res) => {
                  this.$message.success(this.$t('tips.addedSuccessfully'))
                  this.$router.push({
                    path: `/salesdeliveryResaleDialog/${res.data.sId}`,
                    query: {
                      Id: res.data.sId,
                      status: res.data.sSheetStatus,
                      type: 'edit',
                      name: `转售单【${res.data.sCode}】`,
                      activeId: localStorage.getItem('menuId')
                    }
                  })
                  this.$emit('hidebody', res.data.sId)
                })
              } else {
                this.$message.warning(this.$t('grid.others.pleaseCheckTheBox'))
              }
            }, 'margeChild')
          } else if (this.activeName === 'third') {
            this.$refs.aggrid.getSelectedData(async(selectList) => {
              if (selectList.length > 0) {
                // const checkedData = await this.checkLength(selectList)
                OnRoadToOthersCreate(selectList).then((res) => {
                  this.$message.success(this.$t('tips.addedSuccessfully'))
                  this.$router.push({
                    path: `/salesdeliveryResaleDialog/${res.data.sId}`,
                    query: {
                      Id: res.data.sId,
                      status: res.data.sSheetStatus,
                      type: 'edit',
                      name: `转售单【${res.data.sCode}】`,
                      activeId: localStorage.getItem('menuId')
                    }
                  })
                  this.$emit('hidebody', res.data.sId)
                })
              } else {
                this.$message.warning(this.$t('grid.others.pleaseCheckTheBox'))
              }
            }, 'margeChild')
          } else {
            this.$refs.aggrid.getSelectedData(async(selectList) => {
              if (selectList.length > 0) {
                // const checkedData = await this.checkLength(selectList)
                StockToOthersCreate(selectList).then((res) => {
                  this.$message.success(this.$t('tips.addedSuccessfully'))
                  this.$router.push({
                    path: `/salesdeliveryResaleDialog/${res.data.sId}`,
                    query: {
                      Id: res.data.sId,
                      status: res.data.sSheetStatus,
                      type: 'edit',
                      name: `转售单【${res.data.sCode}】`,
                      activeId: localStorage.getItem('menuId')
                    }
                  })
                  this.$emit('hidebody', res.data.sId)
                })
              } else {
                this.$message.warning(this.$t('grid.others.pleaseCheckTheBox'))
              }
            }, 'margeChild')
          }
        })
        .catch(() => {})
    }
  }
}
</script>
<style>

.tabstablebor .steel-trade-aggrid-row,
.tabstablebor .ag-theme-balham .ag-root-wrapper,
.tabstablebor .steel-trade-aggrid .cnd-pagination {
  border-left: none;
  border-right: none;
}
.tabstablebor .steel-trade-aggrid .cnd-pagination {
  border-bottom: none;
}
.el-tabs__content .tabstablebor .cnd-pagination {
  border: none;
}
</style>
