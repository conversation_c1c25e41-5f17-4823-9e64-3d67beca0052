/*
 * @Description:
 */// 接口
import {
  getArrivalList,
  getArrivalListCount,
  getArrivalRemove,
  getArrivalDetailList,
  getArrivalExport,
  getArrivalGet,
  confirmRelations,
  downloadBatch
} from '@/api/logistics/registrationData/purchaseArrival/onArrival/onArrival.js'
import {
  getContractSubmitControls
} from '@/api/contract'
import { handleDict } from '@/utils/common'
import exportBtn from '@/components/exportBtnV2'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import {
  getDictet
} from '@/api/logistics/saleDelivery/saleorder'
// import { columnDefs, childColumnDefs } from './ColumnDefs'
import { saveAs } from 'file-saver'
import OnArrival from './OnArrival.vue'
import Detail from './Detail.vue'
import { SteelFormat, MessageUtil } from 'cnd-horizon-utils'
import { Moment } from 'cnd-utils'
import { convertRes2Blob } from '@/utils/common'
// let _status = []

export default {
  name: 'ArrivalNoteList',
  components: {
    exportBtn,
    steelTradeAggrid,
    Detail,
    OnArrival
  },
  data() {
    return {
      s: [
        {
          'sCodeValue': '10',
          'sCodeName': this.$t('grid.others.ready'),
          'sSort': 11,
          'sFilter': null,
          'sIsEnabled': '1',
          'sLanguage': 'zh_CN'
        },
        {
          'sCodeValue': '15',
          'sCodeName': '驳回',
          'sSort': 22,
          'sFilter': null,
          'sIsEnabled': '1',
          'sLanguage': 'zh_CN'
        },
        {
          'sCodeValue': '30',
          'sCodeName': '待审',
          'sSort': 22,
          'sFilter': null,
          'sIsEnabled': '1',
          'sLanguage': 'zh_CN'
        },
        {
          'sCodeValue': '70',
          'sCodeName': '已审核',
          'sSort': 33,
          'sFilter': null,
          'sIsEnabled': '1',
          'sLanguage': 'zh_CN'
        }
      ],
      disRemove: true,
      options: {
        'dev.common.sheet.status': ''
      },
      activeName: 'main',
      // 表头定义 start
      columnDefs: [
        {
          headerName: this.$t('grid.others.purchaseOrderNumber'),
          field: 'sCode'
        },
        {
          headerName: this.$t('grid.title.status'),
          field: 'sSheetStatus',
          valueGetter: params => {
            let str = ''
            this.s.map(item => {
              if (item.sCodeValue === params.data.sSheetStatus) {
                str = item.sCodeName
              }
            })
            return str
          }
        },
        // {
        //   headerName: this.$t('grid.others.currentApprover'),
        //   field: 'vCurTaskUser'
        // },
        // {
        //   headerName: this.$t('grid.others.approvalComments'),
        //   field: 'vComment',
        //   cellStyle: { textAlign: 'right' }
        // },
        {
          headerName: this.$t('grid.others.quantityNumberOfPieces'),
          field: 'vQtx',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            const data = params.data
            return `${SteelFormat.formatThousandthSign(data.vQtx, 4)}/${SteelFormat.formatThousandthSign(data.vQty)}`
          }
        },
        {
          headerName: this.$t('grid.title.amount'),
          field: 'vAmt',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatPrice(params.value)
          }
        },
        {
          headerName: this.$t('grid.others.supplier'), // 供应商,
          field: 'vSupplierName',
          cellStyle: { textAlign: 'left' }
        },
        {
          headerName: this.$t('grid.others.warehouse'),
          field: 'vWarehouseName',
          cellStyle: { textAlign: 'left' }
        },
        {
          headerName: this.$t('grid.title.company'),
          field: 'vCompanyName',
          cellStyle: { textAlign: 'left' }
        },
        {
          headerName: this.$t('grid.title.createdAt'),
          field: 'sCreateTime',
          cellStyle: { textAlign: 'right' },
          minWidth: 150,
          valueFormatter(params) {
            return Moment.time('YYYY-MM-DD HH:mm:ss', params.value)
          }
        },
        {
          headerName: this.$t('grid.title.createdBy'),
          field: 'vCreatorName',
          cellStyle: { textAlign: 'right' }
        },
        {
          field: 'vManagementName',
          headerName: '经营单位'
        }
      ],
      childColumnDefs: [
        {
          headerName: this.$t('grid.others.itemNumberTag'),
          field: 'sProjectCode'
        },
        {
          headerName: this.$t('grid.title.purchaseContractNumber'),
          field: 'sContractCode'
        },
        {
          headerName: this.$t('grid.title.salesContractNumber'),
          field: 'sSaleContractCode'
        },
        {
          headerName: '物流单号',
          field: 'sUpCode'
        },
        {
          headerName: this.$t('grid.others.item'),
          field: 'vArtName'
        },
        {
          headerName: this.$t('grid.others.steelCoilNumber'),
          field: 'sExtend4'
        },
        {
          headerName: this.$t('grid.others.carriageNumber'),
          field: 'sExtend5'
        },
        {
          headerName: this.$t('grid.others.quantityNumberOfPieces'),
          field: 'sQtx',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            const data = params.data
            return `${SteelFormat.formatThousandthSign(data.sQtx, 4)}/${SteelFormat.formatThousandthSign(data.sQty)}`
          }
        },
        {
          headerName: this.$t('grid.title.unitPrice'),
          field: 'vPrice',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatPrice(params.value)
          }
        },
        {
          headerName: this.$t('grid.title.amount'),
          field: 'vAmt',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatPrice(params.value)
          }
        },
        {
          headerName: this.$t('grid.title.accountingGroup'),
          field: 'vCheckGroupName'
        },
        {
          field: 'vDepartmentName',
          headerName: this.$t('grid.others.department')
        },
        {
          headerName: this.$t('grid.title.personnel'),
          field: 'vStaffName'
        },
        {
          headerName: this.$t('grid.title.remarks'),
          field: 'sRemark'
        }
      ],
      // 表头定义 end
      rowData: [],
      totalRowData: '',
      dialogVisible: {
        detail: false,
        arrivalNode: false,
        onArrival: false
      },
      headerCount: null,
      footerCount: null,
      searchInfos: null,
      detail: null,
      formItems: [
        {
          label: this.$t('grid.others.purchaseOrderNumber'),
          value: 'sCode',
          type: 'elInput',
          placeholder: this.$t('grid.others.pleaseEnterThePurchaseOrderNumber')
        },
        {
          label: this.$t('grid.title.purchaseContractNumber'),
          value: 'sPurContractCode',
          type: 'elInput',
          placeholder: this.$t('grid.others.enterPurchaseContractNumber')
        },
        {
          label: this.$t('grid.others.supplier'), // 供应商,
          value: 'sSupplierId',
          type: 'cndInputDialogItem',
          dialogType: 'customer',
          placeholder: this.$t('grid.others.pleaseSelectSupplier')
        },
        {
          label: this.$t('grid.others.carriageNumber'),
          value: 'sExtend5',
          type: 'elInput',
          placeholder: this.$t('grid.others.pleaseInputTheCarAndBoatNumber')
        },
        {
          label: this.$t('grid.title.company'),
          value: 'sCompanyId',
          type: 'cndInputDialog',
          dialogType: 'company',
          placeholder: this.$t('grid.others.pleaseSelectCompany')
        },
        {
          label: this.$t('grid.title.accountingGroup'),
          value: 'sCheckGroupId',
          type: 'cndInputDialog',
          dialogType: 'cost',
          placeholder: this.$t('grid.others.pleaseSelectAccountingGroup')
        },
        {
          label: this.$t('grid.others.department'),
          value: 'sDepartmentId',
          type: 'cndInputDialog',
          dialogType: 'depart',
          placeholder: this.$t('grid.others.pleaseSelectTheDepartment')
        },
        {
          label: this.$t('grid.title.personnel'),
          value: 'sStaffId',
          type: 'cndInputDialogItem',
          option: { valueKey: 'sPath' },
          dialogType: 'staff',
          placeholder: this.$t('grid.others.pleaseSelectPersonnel')
        },
        {
          label: this.$t('grid.title.createdBy'),
          value: 'sCreator',
          type: 'cndInputDialog',
          dialogType: 'applicant',
          itemType: 'occultation',
          placeholder: this.$t('grid.others.pleaseSelectTheCreator')
        },
        {
          label: this.$t('grid.title.status'),
          value: 'sSheetStatus',
          type: 'elSelect',
          dict: [
            {
              'sCodeValue': '10',
              'sCodeName': this.$t('grid.others.ready'),
              'sSort': 11,
              'sFilter': null,
              'sIsEnabled': '1',
              'sLanguage': 'zh_CN'
            },
            {
              'sCodeValue': '15',
              'sCodeName': '驳回',
              'sSort': 22,
              'sFilter': null,
              'sIsEnabled': '1',
              'sLanguage': 'zh_CN'
            },
            {
              'sCodeValue': '30',
              'sCodeName': '待审',
              'sSort': 22,
              'sFilter': null,
              'sIsEnabled': '1',
              'sLanguage': 'zh_CN'
            },
            {
              'sCodeValue': '70',
              'sCodeName': '已审核',
              'sSort': 33,
              'sFilter': null,
              'sIsEnabled': '1',
              'sLanguage': 'zh_CN'
            }
          ],
          itemType: 'occultation'
        },
        {
          label: this.$t('grid.others.warehouse'),
          value: 'sWarehouseId',
          type: 'cndInputDialogItem',
          defaultUrl: '/esc/warehouse/info/dialog/v2/page-dialog',
          option: { valueKey: 'sPath' },
          placeholder: this.$t('grid.others.pleaseSelectAWarehouse'),
          itemType: 'occultation'
        },
        {
          label: this.$t('grid.title.createdAt'),
          value: ['sCreateTime', 'vCreateTimeTo'],
          placeholder: [this.$t('grid.others.pleaseSelectTheDate'), this.$t('grid.others.pleaseSelectTheDate')],
          type: 'elDatePicker',
          unlinkPanels: true,
          itemType: 'occultation'
        },
        {
          label: '经营单位',
          value: 'sManagementId',
          type: 'cndInputDialog',
          dialogType: 'escOrg',
          itemType: 'occultation'
        },
        {
          label: this.$t('grid.others.itemNumberTag'),
          value: 'sProjectCode',
          type: 'elInput',
          placeholder: this.$t('grid.others.pleaseEnterTheItemNumberTag')
        }
      ],
      selId: '',
      pageName: '',
      exportConfig: [
        {
          label: this.$t('grid.others.itemNumberTag'),
          value: 'sProjectCode'
        },
        {
          label: this.$t('grid.title.purchaseContractNumber'),
          value: 'sContractCode'
        },
        {
          label: this.$t('grid.title.salesContractNumber'),
          value: 'sSaleContractCode'
        },
        {
          label: '物流单号',
          value: 'sUpCode'
        },
        // {
        //   label: this.$t('grid.others.item'),
        //   value: 'vArtName'
        // },
        { label: this.$t('grid.others.item'), value: 'goodsName' },
        { label: this.$t('grid.others.material'), value: 'mateName' },
        { label: this.$t('grid.others.specification'), value: 'specName' },
        {
          label: this.$t('grid.others.steelCoilNumber'),
          value: 'sExtend4'
        },
        {
          label: this.$t('grid.others.carriageNumber'),
          value: 'sExtend5'
        },
        {
          label: this.$t('grid.title.quantity'),
          value: 'sQtx',
          setValue: (value) => {
            return Number((+value).toFixed(4))
          }
        },
        {
          label: this.$t('grid.others.numberOfPiecesTag'),
          value: 'sQty'
          // setValue: (value) => {
          //   return SteelFormat.formatThousandthSign(value)
          // }
        },
        {
          label: this.$t('grid.title.unitPrice'),
          value: 'vPrice',
          setValue: value => { return Number((+value).toFixed(2)) }
        },
        {
          label: this.$t('grid.title.amount'),
          value: 'vAmt',
          setValue: value => { return Number((+value).toFixed(2)) }
        },
        {
          label: this.$t('grid.title.accountingGroup'),
          value: 'vCheckGroupName'
        },
        {
          label: this.$t('grid.others.department'),
          value: 'vDepartmentName'
        },
        {
          label: this.$t('grid.title.personnel'),
          value: 'vStaffName'
        },
        {
          label: this.$t('grid.title.remarks'),
          value: 'sRemark'
        },
        { label: this.$t('grid.others.purchaseOrderNumber'), value: 'sCode' },
        { label: this.$t('grid.title.status'), value: 'sSheetStatus',
          setValue: (value) => {
            return handleDict(value, this.options['dev.common.sheet.status'])
          }
        },
        // { label: this.$t('grid.others.currentApprover'), value: 'vCurTaskUser' },
        // { label: this.$t('grid.others.approvalComments'), value: 'vComment' },
        { label: this.$t('grid.others.supplier'), value: 'vSupplierName' },
        { label: this.$t('grid.others.warehouse'), value: 'vWarehouseName' },
        { label: this.$t('grid.title.company'), value: 'vCompanyName' },
        { label: this.$t('grid.title.createdBy'), value: 'vCreatorName' },
        { label: this.$t('grid.title.createdAt'), value: 'sCreateTime',
          minWidth: 150,
          setValue: (value) => {
            return Moment.time('YYYY-MM-DD HH:mm:ss', value)
          }
        },
        { label: '经营单位', value: 'vManagementName' }
      ],
      getGridApi: () => {
        return this.$refs.aggrid.gridApi
      }
    }
  },
  computed: {
    searchInfo() {
      return this.$refs.searchForm ? this.$refs.searchForm.searchInfo : {}
    },
    detailParams() {
      const sIds = []
      this.rowData.forEach(item => {
        sIds.push(item.sId)
      })
      return {
        sIds: sIds.toString()
      }
    }
  },
  created() {
    this.pageName = this.$route.query.pageName
    // this.getDef()
    getDictet([
      'dev.common.sheet.status', 'stock.null.type', 'stock.delivery.type'
    ]).then(result => {
      this.options['dev.common.sheet.status'] = result.data[0].dicts
      console.log(this.options['dev.common.sheet.status'])
    }).catch(() => {
    })
    // DictUtil.getDict(['dev.common.sheet.status'], (res) => {
    //   _status = res[0].dicts
    // })
  },
  mounted() {
    this.onSearch(false)
  },
  methods: {
    // getDef() {
    //   const id = this.$route.query.id
    //   if (id) {
    //     this.selId = id
    //     this.dialogVisible.detail = true
    //   }
    // },
    setCount(vCount = 0, vSumAmt = 0, vSumNetAmt = 0, vAmtcount = 0, flag) {
      this[flag] = [
        { count: vCount, key: 'count' },
        { title: this.$t('grid.title.quantity'), count: SteelFormat.formatThousandthSign(vSumAmt, 4), unit: this.$t('grid.others.ton') },
        { title: this.$t('grid.others.numberOfPiecesTag'), count: SteelFormat.formatThousandthSign(vSumNetAmt), unit: this.$t('grid.others.pieces') },
        { title: this.$t('grid.title.amount'), count: SteelFormat.formatPrice(vAmtcount), unit: this.$t('grid.others.yuan') }
      ]
    },
    handleFooterCount(rowData) {
      const details = rowData.filter(item => item._selected)
      this.disRemove = true
      this.$refs.aggrid.getSelectedData(res => {
        if (res.masterData && res.masterData.length > 0) {
          if (res.childData && res.childData.length === 0) {
            this.disRemove = !details.every(val => val.sSheetStatus === '10')
          }
        }
      }, 'delete')
      this.$refs.aggrid.getSelectedData(res => {
        console.log(res)
        const vCount = res.length
        let sQtxcount = 0
        let sQtycount = 0
        let vAmtcount = 0
        res.forEach(el => {
          sQtxcount += Number(el.sQtx || 0)
          sQtycount += Number(el.sQty || 0)
          vAmtcount += Number(el.vAmt || 0)
        })
        this.setCount(vCount, sQtxcount, sQtycount, vAmtcount, 'footerCount')
      }, 'margeChild')
    },
    loadData(pagination = { page: 0, limit: 30 }) {
      return new Promise((resolve, reject) => {
        this.$refs.searchForm.validate().then((valid) => {
          if (valid) {
            getArrivalList(
              this.$refs.searchForm.getSearchData(),
              pagination
            )
              .then((res) => {
                const { vCount } = res.data
                this.rowData = res.data.pageVo.content.map((item) => {
                  item._selected = false
                  item._selectedKeys = []
                  item.detailList = []
                  return item
                })
                getArrivalListCount(this.$refs.searchForm.getSearchData()).then(v => {
                  const { vSumAmt, vSumQtx, vSumQty } = v.data
                  this.setCount(vCount, vSumQtx, vSumQty, vSumAmt, 'headerCount')
                })
                resolve(res.data.pageVo)
              })
              .catch(() => {
                reject(0)
              })
          } else {
            resolve(0)
          }
        })
      })
    },
    loadDetail(data) {
      return new Promise((resolve, reject) => {
        getArrivalDetailList(data.sId, this.$refs.searchForm.getSearchData()).then(res => {
          resolve(res.data)
        }).catch(() => {
          reject([])
        })
      })
    },
    // 导出
    exportfun() {
      getArrivalExport(this.$refs.searchForm.getSearchData()).then((e) => {
        saveAs(e, this.$t('grid.others.purchaseOrderManagement'))
      }).catch((e) => {
        saveAs(e, this.$t('grid.others.purchaseOrderManagement'))
      })
    },
    onSearch(load = true) {
      const searchInfo = this.$refs.searchForm.getSearchData()
      this.searchInfos = searchInfo
      if (load) {
        this.$refs.aggrid.loadTableData()
      }
    },
    deleteData() {
      this.$refs.aggrid.getSelectedData(selData => {
        console.log(selData)
        let isTrue = false
        if (selData.masterData.length === 0) {
          this.$message.warning(this.$t('grid.others.pleaseSelectData'))
          return
        }
        const isd = []
        selData.masterData.map(item => {
          isd.push(item.sId)
          if (item.sSheetStatus !== '10' && !isTrue) {
            isTrue = true
          }
        })
        if (isTrue) {
          this.$message.warning(this.$t('grid.others.theDocumentIsNotReadyToBeDeleted'))
          return
        }
        this.$confirm(this.$t('grid.tips.isTheDeletionConfirmed'), this.$t('grid.others.prompt'), {
          confirmButtonText: this.$t('btns.confirm'),
          cancelButtonText: this.$t('btns.cancel'),
          type: 'warning'
        })
          .then(() => {
            getArrivalRemove(isd).then(res => {
              if (res.code === '0000') {
                this.$message.success(this.$t('tips.deletedSuccessfully'))
                this.$refs.aggrid.reloadTableData()
              } else {
                this.$message.error(res.message)
              }
            })
          })
          .catch(() => { })
      }, 'delete')

      // const selData = this.rowData.filter(item => item._selected)
      // let isTrue = false
      // if (selData.length === 0) {
      //   this.$message.warning(this.$t('grid.others.pleaseSelectData'))
      //   return
      // }
      // const isd = []
      // selData.map(item => {
      //   isd.push(item.sId)
      //   if (item.sSheetStatus !== '10' && !isTrue) {
      //     isTrue = true
      //   }
      // })
      // if (isTrue) {
      //   this.$message.warning(this.$t('grid.others.theDocumentIsNotReadyToBeDeleted'))
      //   return
      // }
      // this.$confirm(this.$t('grid.tips.isTheDeletionConfirmed'), this.$t('grid.others.prompt'), {
      //   confirmButtonText: this.$t('btns.confirm'),
      //   cancelButtonText: this.$t('btns.cancel'),
      //   type: 'warning'
      // })
      //   .then(() => {
      //     getArrivalRemoves(isd).then(res => {
      //       if (res.code === '0000') {
      //         this.$message.success(this.$t('tips.deletedSuccessfully'))
      //         this.$refs.aggrid.reloadTableData()
      //       } else {
      //         this.$message.error(res.message)
      //       }
      //     })
      //   })
      //   .catch(() => { })
    },
    onArrivalClose() {
      this.dialogVisible.onArrival = false
      this.$refs.aggrid.reloadTableData()
    },
    jumpTo() {
      this.dialogVisible.onArrival = true
      // this.$router.push({ path: '/arrivalNoteDetail/add', query: { type: 'add', name: '采购到货单新增', activeId: localStorage.getItem('menuId') }})
      // this.$parentRouter.push({
      //   path: '/egl/onArrivalList'
      // })
    },
    showdetail(id) {
      this.dialogVisible.onArrival = false
      this.selId = id
      this.dialogVisible.detail = true
    },
    async rowDoubleClicked(e) {
      let disabled = '0'
      if (e.data.detailList[0]) {
        const sSaleContractIds = []
        const sPurContractIds = []
        e.data.detailList.forEach(e => {
          if (e.sSaleContractId) {
            sSaleContractIds.push(e.sSaleContractId)
          }
          if (e.sContractId) {
            sPurContractIds.push(e.sContractId)
          }
        })
        const res = await getContractSubmitControls({
          sSaleContractIds: sSaleContractIds.length ? sSaleContractIds : null,
          sPurContractIds: sPurContractIds.length ? sPurContractIds : null
        })
        disabled = res.data === false ? '1' : '0'
      }
      getArrivalGet({ id: e.data.sId }).then(res => {
        if (res.data) {
          this.$router.push({ path: '/arrivalNoteDetail/' + e.data.sId, query: { id: e.data.sId, status: e.data.sSheetStatus, type: 'edit', name: this.$t('grid.others.purchaseArrivalOrder') + '【' + e.data.sCode + '】', activeId: localStorage.getItem('menuId'), disabled: disabled }})
        } else {
          MessageUtil.error(this.$t('grid.others.recordDoesNotExist'))
        }
      })
      // this.dialogVisible.detail = true
      // this.selId = d.data.sId
    },
    onDownloadBatch() {
      this.$refs.aggrid.getSelectedData(res => {
        if (res.length === 0) {
          this.$message.warning(this.$t('grid.others.pleaseSelectData'))
          return
        }
        const sIds = res.map(item => item.sId)
        confirmRelations(sIds).then(async(e) => {
          if (e.code === '0000') {
            const res = await downloadBatch(e.data)
            if (e.message !== '') {
              MessageUtil.error(e.message)
            }
            convertRes2Blob(res, '导出附件', 'application/zip')
          } else {
            MessageUtil.error(e.message)
          }
        })
      })
    }
  }
}
