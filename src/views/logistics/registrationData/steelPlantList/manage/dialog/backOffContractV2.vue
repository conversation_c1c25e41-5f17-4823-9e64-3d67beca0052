
<template>
  <cnd-dialog
    :title="$t('grid.others.returnDetails')"
    append-to-body
    width="800px"
    height="500"
    :fullscreen="false"
    :visible.sync="dialogVisible"
    @close="handleClose"
  >
    <template slot="content">
      <auto-wrap>
        <steelTradeAggrid
          ref="aggrid"
          :column-defs="columnDefs"
          :row-data="rowData"
          :load-data="loadData"
          :paginationinif="false"
          full-row-type="parent"
          table-selection="multiple"
          row-key="sId"
          @rowValueChanged="rowValueChanged"
        />
      </auto-wrap>
    </template>
    <template slot="footer">
      <el-button size="mini" @click="handleClose">{{ $t('btns.cancel') }}</el-button>
      <el-button type="primary" size="mini" @click="submit">{{ $t('btns.confirm') }}</el-button>
    </template>
  </cnd-dialog>
</template>

<script>
import Vue from 'vue'
import { SteelFormat } from 'cnd-horizon-utils'
import { Middleware } from 'cndinfo-ui'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import curMixin from '@/utils/curMixin'
import {
  getBackOffstockThirdDeliveryList,
  backAddsStockThirdDeliveryMultiple
} from '@/api/logistics/registrationData/steelPlantList'
import { Message } from 'element-ui'
export default {
  components: { steelTradeAggrid },
  mixins: [curMixin],
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    sIsDisplayQty: {
      type: String,
      default: ''
    },
    urlData: {
      type: Array,
      default: () => []
    },
    typeBackOff: {
      type: String,
      default: ''
    }
  },
  data() {
    const _this = this
    return {
      columnDefs: [
        {
          headerName: this.$t('grid.title.purchaseContractNumber'),
          field: 'sPurContractCode'
        },
        {
          headerName: this.$t('grid.others.itemNumberTag'),
          field: 'sProjectCode'
        },
        {
          headerName: this.$t('grid.title.salesContractNumber'),
          field: 'sSaleContractCode'
        },
        {
          headerName: this.$t('grid.others.item'),
          field: 'vArtName'
        },
        {
          headerName: this.$t('grid.others.quantityNumberOfPieces'),
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return `${SteelFormat.formatThousandthSign(params.data.vSumFallbackQtx, 4)}/${SteelFormat.formatThousandthSign(params.data.vSumFallbackQty)}`
          }
        },
        {
          headerName: this.$t('grid.others.returnedQuantityNd'),
          field: 'vSumBackQtx',
          editable: true,
          cellEditorFramework: Vue.extend(
            Middleware.createComponent(
              'CndInputNumber',
              {
                mark: 'vSumBackQtx',
                type: 'number',
                decimalDigit: 4,
                autoFocus: true,
                focusSelect: true
              },
              {
                blur: ({ event, rowData, middleware }) => {
                  if (_this.sIsDisplayQty === '1') {
                    _this.setCurQty(event, rowData, middleware)
                  } else {
                    rowData.data.vSumBackQty = SteelFormat.formatThousandthSign(event.target.value)
                    _this.$refs.aggrid.gridApi.refreshCells(rowData)
                    return
                  }
                }
              }
            )
          ),
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return `${SteelFormat.formatThousandthSign(params.value, 4)}`
          }
        },
        {
          headerName: this.$t('grid.others.returnedPieces'),
          field: 'vSumBackQty',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return `${SteelFormat.formatThousandthSign(params.value)}`
          },
          editable: () => {
            return this.sIsDisplayQty === '1'
          },
          cellEditorFramework: Vue.extend(
            Middleware.createComponent(
              'CndInputNumber',
              {
                mark: 'vSumBackQty',
                type: 'number',
                decimalDigit: 0,
                focusSelect: true
              },
              {
                blur: ({ event, rowData, middleware }) => {
                  _this.setCurPkgQty(event, rowData, middleware)
                }
              }
            )
          )
        },
        {
          headerName: this.$t('grid.others.steelCoilNumber'),
          field: 'sExtend4'
        },
        {
          headerName: this.$t('grid.others.carriageNumber'),
          field: 'sExtend5'
        }
      ],
      curConfig: {
        vCurQty: 'vSumBackQtx',
        vCurPkgQty: 'vSumBackQty',
        vLeftQty: 'vSumFallbackQtx',
        vLeftPkgQty: 'vSumFallbackQty'
      },
      rowData: []
    }
  },
  methods: {
    loadData() {
      return new Promise((resolve, reject) => {
        if (this.typeBackOff === 'direct') {
          this.rowData = this.urlData.map(item => {
            item._selected = false
            item.vSumBackQty = item.vSumFallbackQty || 0
            item.vSumBackQtx = item.vSumFallbackQtx || 0
            return item
          })
        } else {
          getBackOffstockThirdDeliveryList(
            this.urlData
          ).then(res => {
            this.rowData = res.data.map(item => {
              item._selected = false
              item.vSumBackQty = item.vSumFallbackQty || 0
              item.vSumBackQtx = item.vSumFallbackQtx || 0
              return item
            })
          })
        }
      })
    },
    rowValueChanged(params) {
      const { data, rowIndex } = params
      const { vCurQty, vCurPkgQty, vLeftQty, vLeftPkgQty } = this.curConfig
      if (+data[vCurQty] === +data[vLeftQty]) {
        if (+data[vCurPkgQty] !== +data[vLeftPkgQty]) {
          Message.closeAll()
          Message({ message: this.$t('grid.others.ifTheRemainingQuanteUsedUpKey'), type: 'warning' })
        }
      }

      if (+data[vCurPkgQty] === +data[vLeftPkgQty]) {
        if (+data[vCurQty] !== +data[vLeftQty]) {
          Message.closeAll()
          Message({ message: this.$t('grid.others.theRemainingPiecesAeUsedUpKey'), type: 'warning' })
        }
      }
      this.rowData[rowIndex] = data
    },
    submit() {
      this.$refs.aggrid.gridApi.clearFocusedCell()
      this.$refs.aggrid.getSelectedData(selected => {
        if (!selected.length) {
          Message.error(this.$t('grid.tips.youHaveNotSelectedTontractKey'))
          return
        }
        backAddsStockThirdDeliveryMultiple(selected).then(res => {
          this.handleClose()
          this.$message.success(this.$t('tips.operationSuccessful'))
          this.$emit('success')
        })
      })
    },
    handleClose() {
      this.$emit('close')
    }
  }
}
</script>
