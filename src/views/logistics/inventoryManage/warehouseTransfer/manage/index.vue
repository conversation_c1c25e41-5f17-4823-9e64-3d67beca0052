<template>
  <div class="page-container">
    <p class="page-title">{{ $t('grid.others.transferOrderManagement') }}</p>
    <div class="layout-content auto-page-title flexV">
      <steel-search-form
        ref="searchForm"
        :form-items="formItems"
        @search="onSearch"
      />

      <div class="btn-group mt-10">
        <div class="text">
          {{ $t('router.transferOrder') }}{{ $t('grid.others.list') }}
        </div>
        <div>
          <export-btn
            v-has:esc_stock_move_exportAllot
            class="ml-10 mr-10"
            :file-name="$t('router.transferOrder')"
            api-url="/esc/stock/move/exportAllot"
            :post-params="searchInfo"
            :isload-detail="true"
            :detail-params="detailParams"
          />
          <el-button
            v-has:esc_allot_download_attachment
            type="primary"
            size="mini"
            :disabled="fileDisabled"
            @click="fileDownload"
          >附件下载</el-button>
          <el-button
            v-has:esc_allot_list_updatesExtend9
            type="primary"
            size="mini"
            @click="updatesExtend"
          >{{ $t('grid.others.shippedOrders') }}</el-button>
          <el-button
            key="comm_puragreement_list_add"
            v-has:esc_allot_list_add
            type="primary"
            size="mini"
            @click="openWorkbenchDialog"
          >{{ $t('btns.add') }}</el-button>
          <el-button
            v-has:esc_allot_detail_delete
            type="danger"
            size="mini"
            :disabled="delDisable"
            @click="handleDelBtn"
          >{{ $t('btns.delete') }}</el-button>
        </div>
      </div>
      <!-- 移仓调拨单管理 -->
      <steelTradeAggrid
        ref="aggrid"
        :column-defs="columnDefs"
        :row-data="rowData"
        :load-data="loadData"
        :header-total="headerTotal"
        :footer-total="footerTotal"
        :auto-load-data="false"
        table-selection="multiple"
        row-key="sId"
        @selectedChange="handleFooterTotal"
        @rowDoubleClicked="openDetailDialog"
      />
    </div>
    <!-- <detailDialog
      :s-id="sId"
      :dialog-visible="dialogVisible"
      @close="closeDetailDialog"
    /> -->
    <workbenchDialog
      :dialog-visible="workbenchVisible"
      @close="closeWorkbenchDialog"
    />
  </div>
</template>

<script>
import steelTradeAggrid from '@/components/steelTradeAggrid'
// import detailDialog from '../detailDialog'
import workbenchDialog from './workbenchDialog'
import { SteelFormat } from 'cnd-horizon-utils'
import { Moment } from 'cnd-utils'
import { getCnDitc } from '@/utils/common'
import { statusDict } from '@/utils/dict'
import agreement from '@/api/agreement'
import exportBtn from '@/components/exportBtnV2'
import { convertRes2Blob } from '@/utils/common'
import { deliveryDownLoadBatch } from '@/api/logistics/saleDelivery/saleorder'
import {
  getStockMovePage,
  getStockMovePageSum,
  removeStockMoves,
  getStockMoveDetail,
  moveUpdatesExtend9
} from '@/api/logistics/inventoryManage.js'
import { getContractSubmitControls } from '@/api/contract'
export default {
  name: 'WarehouseTransferManage',
  components: { steelTradeAggrid, workbenchDialog, exportBtn },
  data() {
    const _this = this
    // const sendStatusDict = [{
    //   'sCodeValue': '0',
    //   'sCodeName': '未发送'
    // }, {
    //   'sCodeValue': '1',
    //   'sCodeName': '已发送'
    // }]
    return {
      fileDisabled: true,
      delDisable: true,
      searchInfo: null,
      formItems: [
        {
          label: this.$t('grid.others.transferOrderNumber'),
          value: 'sCode',
          type: 'elInput',
          placeholder: this.$t('grid.others.pleaseEnterTheTransferOrderNumber')
        },
        {
          label: this.$t('grid.others.moveOutWarehouse'),
          value: 'sWarehouseId',
          type: 'cndInputDialogItem',
          defaultUrl: '/esc/warehouse/info/dialog/v2/page-dialog',
          option: { valueKey: 'sPath' },
          placeholder: this.$t('grid.others.selectWarehouseToBeMovedOutOf')
        },
        {
          label: this.$t('grid.others.moveOutCompany'),
          value: 'sCompanyId',
          type: 'cndInputDialog',
          dialogType: 'company',
          placeholder: this.$t('grid.others.pleaseSelectTheMovingOutCompany')
        },
        {
          label: this.$t('grid.others.moveOutOperator'),
          value: 'sStaffId',
          type: 'cndInputDialog',
          dialogType: 'staff',
          placeholder: this.$t('grid.others.pleaseSelectTheOperator')
        },
        {
          label: this.$t('grid.others.moveOutAccountingGroup'),
          value: 'sCheckGroupId',
          type: 'cndInputDialog',
          dialogType: 'cost',
          placeholder: this.$t('grid.others.selectAccountingGroupToMoveOut')
        },
        {
          label: this.$t('grid.title.purchaseContractNumber'),
          value: 'vContractCode',
          type: 'elInput',
          placeholder: this.$t('grid.others.enterPurchaseContractNumber')
        },
        {
          label: this.$t('grid.title.moveToWarehouse'),
          value: 'sWarehouseInId',
          type: 'cndInputDialogItem',
          defaultUrl: '/esc/warehouse/info/dialog/v2/page-dialog',
          option: { valueKey: 'sPath' },
          placeholder: this.$t('grid.others.pleaseSelectTheWarehouseToMoveInto')
        },
        {
          label: this.$t('grid.others.moveToCompany'),
          value: 'sCompanyInId',
          type: 'cndInputDialog',
          dialogType: 'company',
          placeholder: this.$t('grid.others.pleaseSelectTheCompanyToMoveInto')
        },
        {
          label: '是否跨货权',
          value: 'sIsCrossGoodsRight',
          type: 'elSelect',
          dict: 'base.yes-no',
          itemType: 'occultation'
        },
        {
          label: this.$t('grid.others.dateOfTransferTag'),
          value: ['sAllotDate', 'sAllotDateTo'],
          placeholder: [
            this.$t('grid.others.startDate'),
            this.$t('grid.others.endDate')
          ],
          unlinkPanels: true,
          type: 'elDatePicker',
          itemType: 'occultation'
        },
        {
          label: this.$t('grid.title.createdAt'),
          value: ['sCreateTime', 'vCreateTimeTo'],
          placeholder: [
            this.$t('grid.others.startDate'),
            this.$t('grid.others.endDate')
          ],
          unlinkPanels: true,
          type: 'elDatePicker',
          itemType: 'occultation'
        },
        {
          label: this.$t('grid.title.status'),
          value: 'sSheetStatus',
          type: 'elSelect',
          dict: statusDict,
          itemType: 'occultation'
        },
        {
          label: '经营单位',
          value: 'sManagementId',
          type: 'cndInputDialog',
          dialogType: 'escOrg',
          itemType: 'occultation'
        },
        {
          label: this.$t('grid.title.salesContractNumber'),
          value: 'vSaleContractCode',
          type: 'elInput',
          placeholder: this.$t('grid.others.pleaseEnterTheSalesContractNumber')
        },
        {
          label: '创建人',
          value: 'sCreator',
          type: 'cndInputDialog',
          itemType: 'occultation',
          placeholder: this.$t('grid.others.pleaseSelectTheCreator'),
          dialogType: 'applicant'
        }
        // {
        //   label: this.$t('grid.others.issuingStatus'),
        //   value: 'sExtend9',
        //   type: 'elSelect',
        //   dict: sendStatusDict,
        //   itemType: 'occultation'
        // }
      ],
      columnDefs: [
        {
          headerName: this.$t('grid.others.transferOrderNumber'),
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'sCode',
              headerClass: 'c-header_child'
            }
          ]
        },
        {
          headerName: this.$t('grid.title.status'),
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'sSheetStatus',
              headerClass: 'c-header_child',
              valueGetter: (params) => {
                return getCnDitc(
                  params,
                  _this.selectOps['dev.common.sheet.status'],
                  'sSheetStatus'
                )
              }
            }
          ]
        },
        {
          headerName: this.$t('grid.others.dateOfTransferTag'),
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'sAllotDate',
              headerClass: 'c-header_child',
              valueFormatter: (params) => {
                return Moment.time('YYYY-MM-DD', params.value)
              }
            }
          ]
        },
        {
          headerName: this.$t('grid.others.transferOut'),
          children: [
            {
              headerName: this.$t('grid.others.warehouse'),
              field: 'vWarehouseName'
            },
            {
              headerName: this.$t('grid.title.company'),
              field: 'vCompanyName'
            },
            {
              headerName: this.$t('grid.title.operatedBy'),
              field: 'vStaffName'
            },
            {
              headerName: this.$t('grid.title.accountingGroup'),
              field: 'vCheckGroupName',
              width: '100px'
            },
            {
              headerName: this.$t('grid.others.department'),
              field: 'vDepartmentName',
              width: '100px'
            }
          ]
        },
        {
          headerName: this.$t('grid.others.quantityNumberOfPieces'),
          field: 'vQtx',
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'vQtx',
              headerClass: 'c-header_child',
              cellStyle: { textAlign: 'right' },
              valueFormatter: (params) => {
                // vQtx/vQty
                return `${SteelFormat.formatThousandthSign(
                  params.data.vQtx,
                  4
                )}/${params.data.vQty}`
              }
            }
          ]
        },
        {
          headerName: this.$t('grid.title.purchaseContractNumber'),
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'sPurContractCode',
              headerClass: 'c-header_child'
            }
          ]
        },
        {
          headerName: this.$t('grid.others.moveIn'),
          children: [
            {
              headerName: this.$t('grid.others.warehouse'),
              field: 'vWarehouseInName'
            },
            {
              headerName: this.$t('grid.title.company'),
              field: 'vCompanyInName'
            },
            {
              headerName: this.$t('grid.title.accountingGroup'),
              field: 'vCheckGroupInName',
              width: '100px'
            },
            {
              headerName: this.$t('grid.others.department'),
              field: 'vDepartmentInName',
              width: '100px'
            }
          ]
        },
        {
          headerName: this.$t('grid.title.salesContractNumber'),
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'vSaleContractCode',
              headerClass: 'c-header_child'
            }
          ]
        },
        {
          headerName: this.$t('grid.title.createdBy'),
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'vCreatorName',
              headerClass: 'c-header_child'
            }
          ]
        },
        {
          headerName: this.$t('grid.title.createdAt'),
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'sCreateTime',
              headerClass: 'c-header_child',
              minWidth: 150,
              valueFormatter: (params) => {
                return Moment.time('YYYY-MM-DD HH:mm:ss', params.value)
              }
            }
          ]
        },
        {
          headerName: '经营单位',
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'vManagementName',
              headerClass: 'c-header_child'
            }
          ]
        }
        // {
        //   headerName: this.$t('grid.others.issuingStatus'),
        //   headerClass: 'c-header_parent',
        //   children: [
        //     {
        //       headerName: '',
        //       field: 'sExtend9',
        //       headerClass: 'c-header_child',
        //       minWidth: 106,
        //       valueFormatter: (params) => {
        //         return getCnDitc(params, sendStatusDict, 'sExtend9')
        //       }
        //     }
        //   ]
        // }
      ],
      rowData: [],
      headerTotal: null,
      footerTotal: null,

      sId: null,
      dialogVisible: false,
      workbenchVisible: false,
      selectOps: {
        'dev.common.sheet.status': null
      }
    }
  },
  computed: {
    detailParams() {
      const sIds = []
      const selectIds = []
      this.rowData.forEach(item => {
        sIds.push(item.sId)
        if (item && item._selected) {
          selectIds.push(item.sId)
        }
      })
      return {
        sIds: sIds.toString(),
        selectIds: selectIds.toString()
      }
    }
  },
  beforeMount() {
    agreement.getDict(['dev.common.sheet.status']).then((result) => {
      this.selectOps['dev.common.sheet.status'] = result.data[0].dicts // 状态
    })
  },
  mounted() {
    // this.getDef()
    this.onSearch(false)
  },
  methods: {
    onSearch(load = true) {
      const searchInfo = this.$refs.searchForm.getSearchData()
      this.searchInfo = searchInfo
      if (load) {
        this.$refs.aggrid.loadTableData()
      }
    },
    setTotal(vCount = 0, vSumQtx = 0, vSumQty = 0, flag) {
      this[flag] = [
        { count: vCount, key: 'count' },
        {
          title: this.$t('grid.title.quantity'),
          count: SteelFormat.formatThousandthSign(vSumQtx, 4),
          unit: this.$t('grid.others.ton')
        },
        {
          title: this.$t('grid.others.numberOfPiecesTag'),
          count: vSumQty,
          unit: this.$t('grid.others.pieces')
        }
      ]
    },
    handleDelBtn() {
      const ids = []
      let isTrue = false

      const selRows = this.rowData.filter((item) => item._selected)
      if (selRows.length === 0) {
        this.$message.warning(
          this.$t('grid.others.pleaseSelectTheDataToBeDeleted')
        )
        return
      }
      selRows.map((item) => {
        ids.push(item.sId)
        if (item.sSheetStatus !== '10' && !isTrue) {
          isTrue = true
        }
      })
      if (isTrue) {
        this.$message.warning(
          this.$t('grid.others.theDocumentIsNotReadyToBeDeleted')
        )
        return
      }
      this.$confirm(
        this.$t('grid.tips.isTheDeletionConfirmed'),
        this.$t('grid.others.prompt'),
        {
          confirmButtonText: this.$t('btns.confirm'),
          cancelButtonText: this.$t('btns.cancel'),
          type: 'warning'
        }
      )
        .then(() => {
          removeStockMoves(ids).then((res) => {
            if (res.code === '0000') {
              this.$message.success(this.$t('tips.deletedSuccessfully'))
              this.$refs.aggrid.reloadTableData()
            } else {
              this.$message.success(this.$t('grid.others.failedToDelete'))
            }
          })
        })
        .catch(() => {})
    },
    handleFooterTotal(rowData) {
      const details = rowData.filter((item) => item._selected)
      this.delDisable = true
      this.fileDisabled = true
      if (details.length) {
        this.delDisable = !details.every((val) => val.sSheetStatus === '10')
        this.fileDisabled = false
      }
      const vCount = details.length
      let vSumQtx = 0
      let vSumQty = 0
      details.forEach((el) => {
        vSumQtx += el.vQtx
        vSumQty += el.vQty
      })
      this.setTotal(vCount, vSumQtx, vSumQty, 'footerTotal')
    },
    loadData(pagination) {
      return new Promise((resolve, reject) => {
        getStockMovePage(this.searchInfo, pagination)
          .then((res) => {
            this.rowData = res.data.page.content.map((item) => {
              item._selected = false
              item._selectedKeys = []
              return item
            })
            const { vCount } = res.data
            getStockMovePageSum(this.searchInfo).then(v => {
              const { vSumQtx, vSumQty } = v.data
              this.setTotal(vCount, vSumQtx, vSumQty, 'headerTotal')
            })
            resolve(res.data.page)
          })
          .catch(() => {
            reject([])
          })
      })
    },
    // getDef() {
    //   const id = this.$route.query.id
    //   if (id) {
    //     this.sId = id
    //     this.dialogVisible = true
    //   }
    // },
    async openDetailDialog(params) {
      let disabled = '0'
      if (params.data.details[0]) {
        const sSaleContractIds = []
        const sPurContractIds = []
        params.data.details.forEach((e) => {
          if (e.sSaleContractId) {
            sSaleContractIds.push(e.sSaleContractId)
          }
          if (e.sContractId) {
            sPurContractIds.push(e.sContractId)
          }
        })
        const res = await getContractSubmitControls({
          sSaleContractIds: sSaleContractIds.length ? sSaleContractIds : null,
          sPurContractIds: sPurContractIds.length ? sPurContractIds : null
        })
        disabled = res.data === false ? '1' : '0'
      }
      getStockMoveDetail({ sId: params.data.sId }).then((res) => {
        if (res.data.sId) {
          this.$router.push({
            path: `/warehouseTransferManagedetailDialog/${params.data.sId}`,
            query: {
              Id: params.data.sId,
              status: params.data.sSheetStatus,
              type: 'edit',
              name: `${this.$t('router.transferOrder')}【${
                params.data.sCode
              }】`,
              disabled: disabled,
              activeId: localStorage.getItem('menuId')
            }
          })
        } else {
          this.$message.error(this.$t('grid.others.recordDoesNotExist'))
        }
      })
    },
    closeDetailDialog() {
      this.$parentRouter.replace({
        query: {}
      })
      this.dialogVisible = false
      this.$refs.aggrid.loadTableData()
    },
    openWorkbenchDialog() {
      this.workbenchVisible = true
    },
    closeWorkbenchDialog() {
      this.workbenchVisible = false
      this.$refs.aggrid.loadTableData()
    },
    updatesExtend() {
      const delarr = []
      const vm = this
      this.rowData.map((item) => {
        if (item._selected === true) {
          delarr.push(item.sId)
        }
      })
      if (delarr.length === 0) {
        this.$message({
          message: this.$t('grid.others.pleaseSelectAData'),
          type: 'warning'
        })
        return
      }

      this.$confirm(
        this.$t('grid.tips.isTheStatusOfTheOrdToSentKey'),
        this.$t('grid.others.prompt'),
        {
          confirmButtonText: this.$t('btns.confirm'),
          cancelButtonText: this.$t('btns.cancel'),
          type: 'warning'
        }
      ).then(() => {
        moveUpdatesExtend9(delarr)
          .then((result) => {
            this.$message({
              message: this.$t('grid.others.successfulOperation'),
              type: 'success'
            })
            vm.$refs.aggrid.loadTableData()
          })
          .catch(() => {})
      })
    },
    fileDownload() {
      const details = this.rowData.filter(item => item._selected)
      const sIds = details.map(item => item.sId)
      const vBusIds = sIds.toString()
      deliveryDownLoadBatch({ vBusIds }).then(v => {
        if (v.type === 'application/json') {
          const fileReader = new FileReader()
          fileReader.readAsText(v, 'utf-8')
          fileReader.onload = () => {
            const result = JSON.parse(fileReader.result)
            if (!result.data && result.message === 'ok') {
              this.$message.error('暂无附件')
            } else {
              this.$message.error(result.message)
            }
          }
        } else {
          convertRes2Blob(v, '移动调拨单', 'application/zip')
          this.$nextTick(() => {
            this.$message.success(this.$t('tips.downloadSuccessful'))
          })
        }
      })
    }
  }
}
</script>
