<template>
  <div class="flexV" style="height:500px">
    <div class="btn-group">
      <div>
        <!-- <el-button type="primary" size="mini" @click="addRowData">{{$t('btns.add')}}</el-button> -->
        <el-button type="primary" size="mini" :disabled="sDatas.sSheetStatus==70||sData.sSheetStatus==30" @click="outerVisible = true">付款选择往来</el-button>
        <el-button type="primary" size="mini" :disabled="sDatas.sSheetStatus==70||sData.sSheetStatus==30" @click="saveRowData">{{ $t('btns.save') }}</el-button>
        <el-button type="danger" size="mini" :disabled="!curSelRowData||sDatas.sSheetStatus==70||sData.sSheetStatus==30" @click="removeRowData">{{ $t('btns.delete') }}</el-button>
      </div>
    </div>
    <ag-grid-vue edit-type="fullRow" class="ag-theme-balham grid-class" :row-drag-managed="true" :grid-options="gridData.gridOptions" :row-data="gridData.gridData" :modules="aGGridAllModules" @selection-changed="gridOnSelectionChanged" @grid-ready="query" />
    <cnd-pagination v-if="gridData && gridData.pagination" :total="pagination.total" :page="pagination.pageNo" :limit="pagination.pageSize" :event="pageChange" />
    <RequestDetailDialog v-if="outerVisible" :outer-visible.sync="outerVisible" :s-payment-id.sync="sPaymentId" :s-data.sync="sData" />
    <TableSelect v-if="false" />
    <TableDate v-if="false" />
  </div>
</template>
<script>
import Vue from 'vue'
import payment from '@/api/payment'
import { SteelFormat } from 'cnd-horizon-utils'
import { Moment } from 'cnd-utils'
import { GridPageMixin } from '@/utils/page-mixins'
import RequestDetailDialog from './RequestDetailDialog'
import TableSelect from '@/components/TableSelect.vue'
import TableDate from '@/components/TableDate.vue'
export default {
  components: {
    TableSelect,
    TableDate,
    RequestDetailDialog
  },
  mixins: [GridPageMixin],
  props: [
    'purchaseAddType',
    'sData'
  ],
  data() {
    return {
      outerVisible: false,
      detaildialogvisible: false,
      options: [
        'currencylist',
        'trade.settlement.type',
        'trade.pay.mode',
        'pay.subtype',
        'base.yes-no',
        'contract.type'
      ],
      form: {
        sPaymentSubType: '',
        sIsReject: '',
        vPaymentCurrencyName: '',
        sPaymentAmt: '',
        sProjectCode: '',
        sContractCode: '',
        sContractType: '',
        sRemark: '',
        sCreator: '',
        sCreateTime: '',
        sModifier: '',
        sModifyTime: ''
      },
      pagination: {
        pageNo: 0,
        pageSize: 20
      }
    }
  },
  computed: {
    sPaymentId() {
      return this.purchaseAddType
    },
    sDatas() {
      return this.sData
    }
  },
  watch: {
    outerVisible(val) {
      if (!val) {
        this.query()
      }
    }
  },
  created() {
    this.inif()
  },
  mounted() {
  },
  methods: {
    // 切换分页数据
    pageChange(obj) {
      this.pagination.pageNo = obj.page
      this.pagination.pageSize = obj.limit
      this.query()
    },
    // 获取grid配置参数（列的定义....）
    getDefaultGridOptions() {
      const vm = this
      const gridOptions = {
        columnDefs: [
          {
            headerName: '',
            minWidth: 50,
            maxWidth: 50,
            checkboxSelection: true,
            editable: false,
            headerCheckboxSelection: true,
            pinned: 'left'
          },
          {
            field: 'sPaymentSubType',
            headerName: this.$t('grid.columns.paymentSubtype'),
            cellStyle: { textAlign: 'left' },
            valueGetter(params) {
              return vm.getCnDitc(params, vm.options['pay.subtype'], 'sPaymentSubType', 'sCodeValue', 'sCodeName')
            },
            cellEditorFramework: Vue.extend(TableSelect),
            filterList: () => {
              return {
                options: vm.options['pay.subtype'],
                skey: 'sCodeValue',
                sName: 'sCodeName'
              }
            }
          },
          {
            field: 'sIsReject',
            cellStyle: { textAlign: 'left' },
            headerName: '是否退货',
            valueFormatter(params) {
              return vm.getCnDitc(params, vm.options['base.yes-no'], 'sIsReject', 'sCodeValue', 'sCodeName')
            },
            cellEditorFramework: Vue.extend(TableSelect),
            filterList: () => {
              return {
                options: vm.options['base.yes-no'],
                skey: 'sCodeValue',
                sName: 'sCodeName'
              }
            }
          },
          {
            field: 'sOriginalCurrency',
            cellStyle: { textAlign: 'left' },
            headerName: this.$t('grid.others.currency'),
            valueGetter(params) {
              return vm.getCnDitc(params, vm.options['currencylist'], 'sOriginalCurrency', 'sId', 'sCnName')
            },
            cellEditorFramework: Vue.extend(TableSelect),
            filterList: () => {
              return {
                options: vm.options['currencylist'],
                skey: 'sId',
                sName: 'sCnName'
              }
            }
          },
          {
            field: 'sTaxAmt',
            cellStyle: { textAlign: 'left' },
            headerName: this.$t('grid.others.originalCurrencyAmount'),
            valueFormatter(params) {
              return SteelFormat.formatPrice(params.data.sTaxAmt)
            }
          },
          {
            field: 'sProjectCode',
            cellStyle: { textAlign: 'left' },
            headerName: this.$t('grid.others.itemNumberTag')
          },
          {
            field: 'sContractCode',
            cellStyle: { textAlign: 'left' },
            headerName: this.$t('grid.others.contractNumber') // '合同号'
          },
          {
            field: 'sContractType',
            cellStyle: { textAlign: 'left' },
            headerName: this.$t('grid.others.contractType'),
            valueGetter(params) {
              return vm.getCnDitc(params, vm.options['contract.type'], 'sContractType', 'sCodeValue', 'sCodeName')
            },
            cellEditorFramework: Vue.extend(TableSelect),
            filterList: () => {
              return {
                options: vm.options['contract.type'],
                skey: 'sCodeValue',
                sName: 'sCodeName'
              }
            }
          },
          {
            field: 'sRemark',
            cellStyle: { textAlign: 'left' },
            headerName: this.$t('grid.title.remarks')
          },
          {
            field: 'vCreatorName',
            cellStyle: { textAlign: 'left' },
            editable: false,
            headerName: this.$t('grid.title.createdBy')
          },
          {
            field: 'sCreateTime',
            editable: false,
            cellStyle: { textAlign: 'left' },
            headerName: this.$t('grid.title.createdAt'),
            valueFormatter(params) {
              return Moment.time('YYYY-MM-DD HH:mm:ss', params.data.sCreateTime)
            },
            cellEditorFramework: Vue.extend(TableDate)
          },
          {
            field: 'vModifierName',
            editable: false,
            cellStyle: { textAlign: 'left' },
            headerName: this.$t('grid.title.modifiedBy')
          },
          {
            field: 'sModifyTime',
            editable: false,
            cellStyle: { textAlign: 'left' },
            headerName: this.$t('grid.title.modifiedAt'),
            valueFormatter(params) {
              return Moment.time('YYYY-MM-DD HH:mm:ss', params.data.sModifyTime)
            },
            cellEditorFramework: Vue.extend(TableDate)
          }
        ],
        rowSelection: 'multiple',
        defaultColDef: {
          resizable: true,
          editable: true
        },
        onRowDoubleClicked: (e) => {
        }
      }
      return gridOptions
    },
    addRowData() {
      if (!this.gridData?.gridData) {
        this.gridData.gridData = []
      }
      const rowTemplate = {
        sPaymentId: this.purchaseAddType,
        sPaymentCode: this.sCode,
        sPaymentSubType: '',
        sIsReject: '',
        vPaymentCurrencyName: '',
        sPaymentAmt: '',
        sProjectCode: '',
        sContractCode: '',
        sContractType: '',
        sRemark: '',
        sCreator: '',
        sCreateTime: '',
        sModifier: '',
        sModifyTime: ''
      }
      this.gridData.gridData.push(rowTemplate)
    },
    saveRowData() {
      this.gridData.gridOptions.api.clearFocusedCell()
      payment.paydetailmodifies(this.gridData.gridData).then((e) => {
        this.tipMessage(e)
      })
    },
    tipMessage(res) {
      if (res.code === '0000') {
        this.$message({
          message: this.$t('grid.others.successfulOperation'),
          type: 'success'
        })
        this.query()
      }
    },
    inif() {
      payment.List(['trade.settlement.type', 'trade.pay.mode', 'pay.subtype', 'base.yes-no', 'contract.type']).then((result) => {
        this.options['trade.settlement.type'] = result.data[0].dicts // 经营类型
        this.options['trade.pay.mode'] = result.data[1].dicts // 经营类型
        this.options['pay.subtype'] = result.data[2].dicts // 下拉-款项类型
        this.options['base.yes-no'] = result.data[3].dicts // 下拉-是否退货
        this.options['contract.type'] = result.data[4].dicts // 下拉-合同类型
      })
      payment.currencylist().then((result) => {
        this.options['currencylist'] = result.data
      })
    },
    // 查询
    query(refresh = false) {
      this.pagination.sPaymentId = this.purchaseAddType
      payment.paydetailpage(
        this.pagination
      ).then((result) => {
        this.setGridResult(result)
        this.pagination.total = result.data.totalElements
        this.clearSelRowData()
      })
    },
    removeRowData() {
      const row = this.gridData.gridOptions.api.getSelectedNodes().map(item => item.data)[0]
      if (row.sId) {
        this.$confirm(this.$t('grid.others.thisActionDeletesThntinueKey'), this.$t('grid.others.prompt'), {
          confirmButtonText: this.$t('btns.confirm'),
          cancelButtonText: this.$t('btns.cancel'),
          type: 'warning'
        })
          .then(() => {
            payment.paydetailremoves(this.curSelRowData.map(item => item['sId'])).then((result) => {
              this.$message({
                message: this.$t('tips.deletedSuccessfully'),
                type: 'success'
              })
              this.query()
            })
          })
          .catch(() => { })
        return
      }
      const idx = this.gridData.gridData.findIndex(item => {
        let itIs = true
        for (const tempKey in row) {
          if (row[tempKey] !== item[tempKey]) {
            itIs = false
            break
          }
        }
        if (itIs && Object.keys(item).length === Object.keys(row).length) {
          return true
        }
        return false
      })
      this.gridData.gridData.splice(idx, 1)
    }
  }
}
</script>
