<template>
  <div
    class="login-container"
    :style="style"
  >
    <div class="logo-title">
      <img src="@/assets/login/horizon.png" alt>
    </div>
    <div class="layout-class">
      <div class="logo-desc">
        <!-- <img src="@/assets/login/login_img.png" alt> -->
      </div>
      <el-form
        ref="loginForm"
        :model="loginForm"
        :rules="loginRules"
        :class="['login-form', { 'login-form-special': isSpecialHost }]"
        auto-complete="on"
        label-position="left"
      >
        <div class="title-container">
          <span v-if="!isSpecialHost" class="small-rect" />
          <h3 v-if="!isSpecialHost" class="title">
            {{ $t('login.accountPasswordLogin') }}
          </h3>
          <img v-if="isSpecialHost" src="@/assets/login/loadtitle.png" alt class="loadtitle">
          <!-- <lang-select class="set-language" /> -->
        </div>

        <el-form-item prop="username">
          <el-input
            ref="username"
            v-model="loginForm.username"
            name="username"
            type="text"
            :placeholder="$t('grid.others.accountNumber')"
            auto-complete="on"
          />
        </el-form-item>

        <el-form-item prop="credential">
          <el-input
            ref="password"
            :key="passwordType"
            v-model="loginForm.credential"
            name="password"
            :type="passwordType"
            :placeholder="$t('grid.others.password')"
            auto-complete="on"
            @keyup.enter.native="handleLogin"
          />
          <span class="show-pwd" @click="showPwd">
            <cnd-icon v-show="passwordType === 'password'" name="cnd-eyes-close" />
            <cnd-icon v-show="passwordType !== 'password'" name="cnd-eyes" />
          </span>
        </el-form-item>
        <router-link to="/forget" class="login-forget">{{ $t('login.forgotPassword') }}</router-link>
        <el-button
          type="primary"
          style="width:100%;margin:20px 0 10px 0;"
          :loading="loading"
          :disabled="loading"
          @click.native.prevent="handleLogin"
        >{{ $t('login.login') }}</el-button>
        <!-- 忘记密码 -->
        <!-- <div class="forget-pass">忘记密码</div> -->

        <!-- <div style="position:relative">
          <div class="tips">
            <span>账号 : admin</span>
            <span>密码 : 随便填</span>
          </div>
          <div class="tips">
            <span style="margin-right:18px;">账号 : editor</span>
            <span>密码 : 随便填</span>
          </div>
        </div>-->
        <div class="form-footer">
          <p>
            <span class="copyright">版权所有 ©2021- 2024 厦门建发钢铁集团有限公司</span>
          </p>
          <div class="beian">
            <img class="beian-img" src="@/assets/login/beianlogo.png">
            <a class="tolink" href="http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=**************" target="_blank">闽公网安备**************号</a>
            <span class="tolink" @click="toBeian">闽ICP备15007080号-4</span>
          </div>
        </div>
      </el-form>
    </div>
    <!-- <div class="company">
      <p>{{$t('grid.others.copyrightCD')}}</p>
      <p class="tolink" @click="toBeian">闽ICP备15007080号-4</p>
      <div class="beian">
        <img class="beian-img" src="@/assets/login/beianlogo.png">
        <a class="tolink" href="http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=**************" target="_blank">闽公网安备**************号</a>
      </div>
    </div> -->
    <!-- <el-dialog title="第三方登录" :visible.sync="showDialog">
      本地不能模拟，请结合自己业务进行模拟！！！
      <br />
      <br />
      <br />
      <social-sign/>
    </el-dialog> -->

    <!-- 添加验证码弹窗 -->
    <verification-dialog
      :visible.sync="verificationVisible"
      :account="loginForm.username"
      :device="loginForm.device"
      @verify="handleVerify"
    />
  </div>
</template>

<script>
// import { Validate } from '@/utils/common'
// import LangSelect from '@/components/LangSelect'
import FingerprintJS from '@fingerprintjs/fingerprintjs'

import { mapActions } from 'vuex'
import { isIE } from '@/utils/index'
import VerificationDialog from './verificationDialog.vue'
// import { Message } from 'element-ui'
export default {
  name: 'Login',
  components: {
    VerificationDialog
  },
  data() {
    return {
      loginForm: {
        username: '',
        credential: '',
        device: ''
        // username: '',
        // credential: ''
      },
      passwordType: 'password',
      loading: false,
      showDialog: false,
      redirect: undefined,
      verificationVisible: false // 验证码弹窗显示状态
    }
  },
  computed: {
    isSpecialHost() {
      const host = window.location.hostname
      return host === 'suptest.esteellink.com' || host === 'support.esteellink.com'
    },
    loginRules() {
      return {
        username: [
          { required: true, trigger: 'blur', message: this.$t('grid.others.accountNumberCannotBeEmpty') }
        ],
        credential: [
          { required: true, trigger: 'blur', message: this.$t('grid.others.passwordCanNotBeEmpty') }
        ]
      }
    },

    style() {
      let bg = require('@/assets/login/newbg.jpg')
      if (this.isSpecialHost) {
        bg = require('@/assets/login/subbg.png')
      }
      return {
        backgroundImage: `url('${bg}')`
      }
    }
  },
  watch: {
    $route: {
      handler: function(route) {
        this.redirect = route.query.redirect
      },
      immediate: true
    }
  },
  mounted() {
    if (this.loginForm.username === '') {
      this.$refs.username.focus()
    } else if (this.loginForm.credential === '') {
      this.$refs.password.focus()
    }
    if (isIE()) {
      this.$confirm(this.$t('tips.isIE'), '确认信息', {
        confirmButtonText: this.$t('btns.confirmKey'),
        showCancelButton: false,
        customClass: 'ie-tips'
      })
    }
    this.initDeviceFingerprint()
  },
  methods: {
    ...mapActions(
      'user', ['login']
    ),
    // 初始化设备指纹
    async initDeviceFingerprint() {
      try {
        const fp = await FingerprintJS.load()
        const result = await fp.get()

        const requiredComponents = [
          'architecture', // CPU架构
          'deviceMemory', // 设备内存
          'hardwareConcurrency', // CPU核心数
          'platform', // 操作系统平台
          'canvas', // 画布
          'webGlBasics', // WebGL 渲染获取 GPU 型号、驱动版本
          'webGlExtensions',
          'plugins' // 浏览器插件
        ]
        // 收集稳定的组件信息
        const stableComponents = requiredComponents.reduce((acc, key) => {
          const component = result.components[key]
          if (component) {
            acc[key] = component
          } else {
            console.warn(`组件 ${key} 不支持`)
          }
          return acc
        }, {})

        const fingerprintId = FingerprintJS.hashComponents(stableComponents)
        this.loginForm.device = fingerprintId
        window.configs.VUE_APP_STABLE_COMPONENTS = stableComponents
        window.configs.VUE_APP_DEVICE = this.loginForm.device
        console.log('test')
      } catch (error) {
        console.error('获取设备指纹失败:', error)
        this.loginForm.device = 'fallback_' + Math.random().toString(36).substr(2, 9)
        window.configs.VUE_APP_DEVICE = this.loginForm.device
      }
    },
    getCharCount(str, char) {
      const regex = new RegExp(char, 'g')
      const result = str.match(regex)
      const count = !result ? 0 : result.length
      return count
    },
    showPwd() {
      if (this.passwordType === 'password') {
        this.passwordType = ''
      } else {
        this.passwordType = 'password'
      }
      this.$nextTick(() => {
        this.$refs.password.focus()
      })
    },
    // 处理实际的登录逻辑
    handleLoginSubmit(formData, callback) {
      this.loading = true
      this.login(formData)
        .then((res) => {
          if (callback) {
            callback()
          }
          this.verificationVisible = false
          this.$router.push({ path: this.redirect ? this.redirect : res.path ? res.path : '/navbar' })
        })
        .catch((err) => {
          if (callback) {
            callback(err.message)
          } else {
            if (err.message === '请获取验证码') {
              this.$message.closeAll()
              this.verificationVisible = true
            }
          }
        })
        .finally(() => {
          this.loading = false
        })
    },

    handleLogin() {
      this.$refs.loginForm.validate(valid => {
        if (valid) {
          this.loginForm.username = this.loginForm.username.trim()
          const formData = new FormData()
          Object.keys(this.loginForm).forEach(k => {
            formData.append(k, this.loginForm[k])
          })
          this.handleLoginSubmit(formData)
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },

    toBeian() {
      window.open('https://beian.miit.gov.cn', '_blank')
    },

    // 验证码验证
    handleVerify({ code, remember, callback }) {
      const formData = new FormData()
      Object.keys(this.loginForm).forEach(k => {
        formData.append(k, this.loginForm[k])
      })
      // 添加验证码和设备相关信息
      formData.append('code', code)
      formData.append('remember', remember)

      // 调用登录接口
      this.handleLoginSubmit(formData, callback)
    }
  }
}
</script>
