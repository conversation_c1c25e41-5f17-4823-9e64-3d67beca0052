
<template>
  <cnd-dialog
    title="新增销售退货明细"
    append-to-body
    width="80%"
    height="520"
    :fullscreen="false"
    :visible.sync="dialogVisible"
    @close="handleClose"
  >
    <template v-if="dialogVisible" slot="content">
      <steel-search-form ref="searchForm" :form-items="formItems" @search="onSearch" />
      <auto-wrap>
        <steelTradeAggrid
          ref="aggrid"
          class="mt-10"
          :column-defs="columnDefs"
          :row-data="rowData"
          :load-data="loadData"
          :footer-total="footerTotal"
          :auto-load-data="false"
          table-selection="multiple"
          row-key="sId"
          full-row-type="parent"
          @selectedChange="selectedChange"
          @rowValueChanged="rowValueChangeds"
        />
      </auto-wrap>
    </template>
    <template slot="footer" class="dialog-footer">
      <el-button size="mini" @click="handleClose">{{ $t('btns.cancel') }}</el-button>
      <el-button type="primary" size="mini" @click="handleSelect">{{ $t('btns.confirm') }}</el-button>
    </template>
  </cnd-dialog>
</template>

<script>
var Decimal = window.Decimal
import Vue from 'vue'
import { Middleware } from 'cndinfo-ui'
import { SteelFormat } from 'cnd-horizon-utils'
import { Moment } from 'cnd-utils'
import {
  saleWorkbenchPage,
  saleDetailCreate
} from '@/api/returnManage/index.js'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import curMixin from '@/utils/curMixin'
import {
  getDictet, getCnDitc
} from '@/api/logistics/saleDelivery/saleorder'
export default {
  components: { steelTradeAggrid },
  mixins: [curMixin],

  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    id: {
      type: String,
      default: null
    },
    info: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      searchInfo: null,
      curConfig: {
        vCurQty: 'curActualQtx',
        vCurPkgQty: 'curActualQty',
        vLeftQty: 'sActualQtx',
        vLeftPkgQty: 'sActualQty'
      },
      formItems: [
        {
          label: '发货单号',
          value: 'vSaleNoticeCode',
          type: 'elInput'
        },
        {
          label: this.$t('grid.others.itemNumberTag'),
          value: 'sProjectCode',
          type: 'elInput'
        },
        {
          label: this.$t('grid.title.purchaseContractNumber'),
          value: 'sPurContractCode',
          type: 'elInput'
        },
        {
          label: this.$t('grid.title.salesContractNumber'),
          value: 'sSaleContractCode',
          type: 'elInput'
        },
        {
          label: this.$t('grid.others.steelCoilNumber'),
          value: 'sSteelNo',
          type: 'elInput'
        },
        {
          label: this.$t('grid.others.carriageNumber'),
          value: 'sVesselNo',
          type: 'elInput'
        },
        {
          label: this.$t('grid.title.accountingGroup'),
          value: 'sCheckGroupId',
          type: 'cndInputDialog',
          dialogType: 'cost'
        },
        {
          label: '发货日期',
          value: ['sDeliveryNotifyDateFrom', 'sDeliveryNotifyDateTo'],
          placeholder: [this.$t('grid.others.pleaseSelectTheDate'), this.$t('grid.others.pleaseSelectTheDate')],
          type: 'elDatePicker',
          unlinkPanels: true,
          itemType: 'occultation'
        }
      ],
      columnDefs: [
        {
          headerName: '发货单号',
          field: 'sSaleNoticeCode'
        },
        {
          headerName: this.$t('grid.others.customer'),
          field: 'vCustomerName'
        },
        {
          headerName: this.$t('grid.others.warehouse'),
          field: 'vWarehouseName'
        },
        {
          headerName: '发货类型',
          field: 'sOutGoodsType',
          valueGetter: (params) => {
            return getCnDitc(params, this.options['stock.out.goods.type'], 'sOutGoodsType')
          }
        },
        {
          headerName: this.$t('grid.others.item'),
          field: 'sGoodsDesc'
        },
        {
          headerName: this.$t('grid.others.steelCoilNumber'),
          field: 'sSteelNo'
        },
        {
          headerName: this.$t('grid.others.carriageNumber'),
          field: 'sVesselNo'
        },
        {
          headerName: '实发数量/件数',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return `${SteelFormat.formatThousandthSign(params.data.sActualQtx, 4)}/${params.data.sActualQty}`
          }
        },
        {
          headerName: '发货金额',
          field: 'sTaxAmt',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatPrice(params.data.sTaxAmt)
          }
        },
        {
          headerName: this.$t('grid.others.quantityConfirmedThisTime'),
          field: 'curActualQtx',
          editable: true,
          cellEditorFramework: Vue.extend(
            Middleware.createComponent(
              'CndInputNumber',
              {
                mark: 'curActualQtx',
                type: 'number',
                decimalDigit: 4,
                autoFocus: true,
                focusSelect: true
              },
              {
                blur: ({ event, rowData, middleware }) => {
                  let curActualQtx = +event.target.value
                  const sTaxPrice = rowData.data.sTaxPrice || 0
                  if (curActualQtx <= 0) {
                    this.$message.warning('数量不能为0')
                    return
                  }
                  if (curActualQtx > rowData.data.sActualQtx) {
                    curActualQtx = rowData.data.sActualQtx
                    rowData.data.vSumAmt = Number(this.$toFixed(rowData.data.sActualQtx * sTaxPrice))
                  } else {
                    rowData.data.vSumAmt = Number(this.$toFixed(curActualQtx * sTaxPrice))
                  }
                  this.$refs.aggrid.gridApi.refreshCells(rowData)
                  this.setCurQty(event, rowData, middleware)
                }
              }
            )
          ),
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatThousandthSign(params.value, 4)
          }
        },
        {
          headerName: this.$t('grid.others.theNumberOfConfirmedPieces'),
          field: 'curActualQty',
          editable: true,
          cellEditorFramework: Vue.extend(
            Middleware.createComponent(
              'CndInputNumber',
              {
                mark: 'curActualQty',
                type: 'number',
                decimalDigit: 0,
                focusSelect: true
              },
              {
                blur: ({ event, rowData, middleware }) => {
                  // this.setCurPkgQty(event, rowData, middleware)
                }
              }
            )
          ),
          cellStyle: { textAlign: 'right' }
        },
        {
          headerName: '退货金额',
          field: 'vSumAmt',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatPrice(params.data.vSumAmt)
          }
        },
        {
          headerName: this.$t('grid.others.contractingStatus'),
          field: 'vSignStatus',
          valueGetter: (params) => {
            return getCnDitc(params, this.options['customer.sign.status'], 'vSignStatus')
          }
        },
        {
          headerName: this.$t('grid.others.itemNumberTag'),
          field: 'sProjectCode'
        },
        {
          headerName: this.$t('grid.title.purchaseContractNumber'),
          field: 'sPurContractCode'
        },
        {
          headerName: this.$t('grid.title.salesContractNumber'),
          field: 'sSaleContractCode'
        },
        {
          headerName: this.$t('grid.title.company'),
          field: 'vCompanyName'
        },
        {
          headerName: '发货日期',
          field: 'sStockReceiptDate',
          valueFormatter(params) {
            return Moment.time('YYYY-MM-DD', params.data.sStockReceiptDate)
          }
        },
        {
          headerName: this.$t('grid.others.originalDocumentNumber'),
          field: 'sOriUpCode'
        },
        {
          headerName: this.$t('grid.title.accountingGroup'),
          field: 'vCheckGroupName'
        },
        {
          headerName: this.$t('grid.title.personnel'),
          field: 'vStaffName'
        },
        {
          headerName: '经营单位',
          field: 'vManagementName'
        }
      ],
      rowData: [],
      footerTotal: null,
      options: {
        'stock.out.goods.type': [],
        'customer.sign.status': []
      }
    }
  },
  watch: {
    dialogVisible(val) {
      if (val) {
        this.$nextTick(() => {
          this.onSearch()
        })
      }
    }
  },
  beforeCreate() {
    getDictet([
      'stock.out.goods.type',
      'customer.sign.status'
    ]).then(result => {
      this.options['stock.out.goods.type'] = result.data[0].dicts || []
      this.options['customer.sign.status'] = result.data[1].dicts || []
    }).catch(() => {
    })
  },
  methods: {
    onSearch(load = true) {
      const searchInfo = this.$refs.searchForm.getSearchData()
      searchInfo.sCustomerId = this.info.sCustomerId
      searchInfo.sCompanyId = this.info.sCompanyId
      this.searchInfo = searchInfo
      if (load) {
        this.$refs.aggrid.loadTableData()
      }
    },
    loadData(pagination) {
      return new Promise((resolve, reject) => {
        saleWorkbenchPage(this.searchInfo, pagination).then(res => {
          this.rowData = res.data.content.map(item => {
            item._selected = false
            return item
          })
          resolve(res.data)
        }).catch(() => {
          reject([])
        })
      })
    },
    setTotal(vCount = 0, vSumLeftQtx = 0, vSumLeftQty = 0, vSumLeftTaxAmt = 0, flag) {
      this[flag] = [
        { count: vCount, key: 'count' },
        { title: '退货数量', count: SteelFormat.formatThousandthSign(vSumLeftQtx, 4), unit: this.$t('grid.others.ton') },
        { title: '退货件数', count: SteelFormat.formatThousandthSign(vSumLeftQty, 0), unit: this.$t('grid.others.pieces') },
        { title: '退货金额', count: SteelFormat.formatPrice(vSumLeftTaxAmt, 0), unit: this.$t('grid.others.yuan') }
      ]
    },
    selectedChange(list) {
      this.$refs.aggrid.getSelectedData(res => {
        console.log('res: ', res)
        const vCount = res.length
        let vSumLeftQtx = 0
        let vSumLeftQty = 0
        let vSumLeftTaxAmt = 0
        res.forEach(el => {
          vSumLeftQtx = +new Decimal(vSumLeftQtx).add(+el.sActualQtx)
          vSumLeftQty = +new Decimal(vSumLeftQty).add(+el.sActualQty)
          vSumLeftTaxAmt = +new Decimal(vSumLeftTaxAmt).add(+el.vSumAmt)
        })
        this.setTotal(vCount, vSumLeftQtx, vSumLeftQty, vSumLeftTaxAmt, 'footerTotal')
      })
    },
    rowValueChangeds(rowData) {
      const { curActualQtx, sActualQtx, curActualQty, sActualQty } = rowData.data
      if (curActualQtx <= 0) {
        this.$message.closeAll()
        this.$message.warning('数量不能为0')
        return
      }
      if (curActualQtx > sActualQtx) {
        this.$message.closeAll()
        this.$message.warning('此次确认数量不能大于实发数量')
        return
      }
      if (curActualQty > sActualQty) {
        this.$message.closeAll()
        this.$message.warning('此次确认件数不能大于实发件数')
        return
      }
    },
    handleClose() {
      this.$emit('close')
    },
    handleSelect() {
      this.$refs.aggrid.gridApi.stopEditing()
      this.$refs.aggrid.getSelectedData(res => {
        if (!res.length) {
          this.$message.warning(this.$t('grid.others.pleaseSelectData'))
          return false
        }
        saleDetailCreate(res, this.id).then(res => {
          this.$message.success('操作成功')
          this.$emit('success')
          this.handleClose()
        })
      })
    }
  }
}
</script>
