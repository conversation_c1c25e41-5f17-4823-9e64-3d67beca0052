<template>
  <div class="forget-container">
    <div class="logo-title flexCB">
      <img src="@/assets/login/horizon.png" alt>
      <router-link to="/login">{{ $t('grid.others.backToLogin') }}></router-link>
    </div>
    <div class="layout-class">
      <el-steps :space="160" :active="activeId" align-center>
        <el-step :title="$t('login.fillInTheAccount')" />
        <el-step :title="$t('login.verifyYourIdentity')" />
        <el-step :title="$t('login.setNewPassword')" />
        <el-step :title="$t('btns.finish')" />
      </el-steps>
      <el-form
        v-if="activeId === 0"
        ref="infoForm"
        :rules="rulesForm"
        :model="infoForm"
      >
        <el-form-item prop="account">
          <el-input
            v-model="infoForm.account"
          />
        </el-form-item>
        <el-form-item
            prop="captchaCodeAcc"
            :custom-width="24"
            class="code-form-item"
          >
          <div class="flexCB">
            <el-input
              v-model="infoForm.captchaCodeAcc"
              auto-complete="on"
              :placeholder="$t('components.pleaseEnter')"
            />
            <img :src="codeImageAcc" class="code-btn" @click="getCodeImageAcc" />
          </div>
          </el-form-item>
        <el-button
          type="primary"
          style="width: 100%; margin-bottom: 20px"
          :loading="loading"
          :disabled="loading"
          @click.native.prevent="verifyAccount('infoForm')"
        >{{$t('btns.next')}}</el-button>
      </el-form>
      <el-form
        v-else-if="activeId === 1"
        ref="infoForm"
        :rules="rulesForm"
        :model="infoForm"
      >
        <p class="forget-account">{{$t('grid.others.accountNumber')+':'+infoForm.account}}</p>
        <el-form-item prop="phoneNumber">
          <el-input
            v-model="infoForm.phoneNumber"
            :placeholder="$t('login.pleaseEnterTheBoundMobilePhoneNumber')"
          />
        </el-form-item>
        <el-form-item prop="code">
          <div class="flexCB">
            <el-input v-model="infoForm.code" :placeholder="$t('grid.others.enterCellPhoneVerificationCode')" />
            <el-button
              class="code-btn"
              type="info"
              :loading="codeLoad"
              :disabled="disabled"
              @click.native.prevent="getCode('infoForm')"
            >{{ codeBtn }}</el-button>
          </div>
        </el-form-item>
        <el-form-item
            v-if="showCaptchaCode"
            prop="captchaCode"
            :rules="rulesForm"
            :custom-width="24"
            class="code-form-item"
          >
          <div class="flexCB">
            <el-input
              v-model="infoForm.captchaCode"
              auto-complete="on"
              :placeholder="$t('components.pleaseEnter')"
              @input="checkCaptchaCodeFn"
            />
            <img :src="codeImage" class="code-btn" @click="getCodeImage" />
          </div>
          </el-form-item>

        <div class="flexCC">
          <el-button
            type="primary"
            style="width: 100%; margin-bottom: 20px"
            :loading="loading"
            :disabled="loading"
            @click.native.prevent="activeId = 0"
          >{{ $t('btns.previousStep') }}</el-button>
          <el-button
            type="primary"
            style="width: 100%; margin-bottom: 20px"
            :loading="loading"
            :disabled="loading"
            @click.native.prevent="verifyPhone('infoForm')"
          >{{ $t('btns.next') }}</el-button>
        </div>
      </el-form>
      <el-form
        v-else-if="activeId === 2"
        ref="infoForm"
        :rules="rulesForm"
        :model="infoForm"
      >
        <el-form-item prop="password">
          <el-input
            v-model="infoForm.password"
            :type="pwdType"
            autocomplete="off"
            :placeholder="$t('login.pleaseEnterANewPassword')"
          />
          <div class="password-show" @click="passwordType('pwdType')">
            <cnd-icon
              :name="pwdType === 'password' ? 'cnd-eyes-close' : 'cnd-eyes'"
            />
          </div>
        </el-form-item>
        <el-form-item prop="checkPass">
          <el-input
            v-model="infoForm.checkPass"
            :type="checkPwdType"
            autocomplete="off"
            :placeholder="$t('grid.others.pleaseConfirmTheNewPassword')"
          />
          <div class="password-show" @click="passwordType('checkPwdType')">
            <cnd-icon
              :name="
                checkPwdType === 'password' ? 'cnd-eyes-close' : 'cnd-eyes'
              "
            />
          </div>
        </el-form-item>
        <el-button
          type="primary"
          style="width: 100%; margin-bottom: 20px"
          :loading="loading"
          :disabled="loading"
          @click="verifyPassword('infoForm')"
        >{{ $t('grid.others.submit') }}</el-button>
      </el-form>
      <div v-else-if="activeId === 3" class="step-success">
        <cnd-icon name="cnd-tick" />
        <p>{{$t('tips.congratulationsOnTheModification')}}</p>
        <router-link to="/login">{{$t('grid.others.loginNow')}} ></router-link>
      </div>
    </div>
  </div>
</template>
<script>
import { checkAccountV3, getPhoneCode, checkCode, updatePasswd, checkCaptchaCode } from '@/api/user'
import { Validate } from 'cnd-utils'
import { v4 as uuidv4 } from 'uuid'
export default {
  data() {
    const checkPhone = (rule, value, callback) => {
      if (!Validate.validMobile(value)) {
        callback(new Error(this.$t('grid.others.enterCorrectCellPhoneNumber')))
      } else {
        callback()
      }
    }
    const checkPassword = (rule, value, callback) => {
      let count = 0
      if (/\d/.test(value)) count++
      if (/[a-z]/.test(value)) count++
      if (/[A-Z]/.test(value)) count++
      if (/\W/.test(value)) count++
      if (value.length < 8) count = 0
      if (count < 3) {
        callback(
          new Error(this.$t('grid.others.passwordMustBeAnyTherThan8Key'))
        )
      } else {
        callback()
      }
    }
    const checkPassword_ = (rule, value, callback) => {
      if (value !== this.infoForm.password) {
        callback(new Error(this.$t('grid.others.twoTimesThePasswordTheSameKey')))
      } else {
        callback()
      }
    }
    return {
      uuidAcc: '',
      codeImageAcc: '',
      uuid: '',
      codeImage: '',
      showCaptchaCode: false,
      disabledNext: false,
      pwdType: 'password',
      checkPwdType: 'password',
      loading: false,
      disabled: false,
      codeLoad: false,
      codeBtn: this.$t('login.getSmsVerificationCode'),
      activeId: 0,
      userInfo: {},
      infoForm: {
        account: '',
        phoneNumber: '',
        code: '',
        captchaCodeAcc: '',
        password: '',
        checkPass: ''
      },
      rulesForm: {
        captchaCodeAcc: [
          {
            required: true,
            message: this.$t('components.pleaseEnter'),
            trigger: 'blur'
          }
        ],
        captchaCode: [
          {
            required: true,
            message: this.$t('components.pleaseEnter'),
            trigger: 'blur'
          }
        ],
        account: [
          {
            required: true,
            message: this.$t('login.pleaseEnterYourAccount'),
            trigger: 'blur'
          }
        ],
        phoneNumber: [
          {
            required: true,
            message: this.$t('login.pleaseEnterYourCellPhoneNumber'),
            trigger: 'blur'
          },
          {
            validator: checkPhone,
            trigger: 'blur'
          }
        ],
        code: [
          {
            required: true,
            message: this.$t('login.pleaseEnterTheVerificationCode'),
            trigger: 'blur'
          }
        ],
        password: [
          {
            required: true,
            message: this.$t('login.pleaseEnterANewPassword'),
            trigger: 'blur'
          },
          {
            validator: checkPassword,
            trigger: 'blur'
          }
        ],
        checkPass: [
          {
            required: true,
            message: this.$t('grid.others.pleaseConfirmTheNewPassword'),
            trigger: 'blur'
          },
          {
            validator: checkPassword_,
            trigger: 'blur'
          }
        ]
      }
    }
  },
  created() {
    this.getCodeImageAcc()
  },
  methods: {
    passwordType(name) {
      this[name] = this[name] === 'password' ? '' : 'password'
    },
    getCode(formName) {
      this.$refs[formName].validateField('phoneNumber', err => {
        if (err) {
          console.log('未通过')
          return
        } else {
          this.disabled = true
          const getMesCodeCount = sessionStorage.getItem('getMesCodeCount')
          if (getMesCodeCount === '2') {
            this.showCaptchaCode = true
            this.getCodeImage()
            return
          } else {
            getMesCodeCount
              ? sessionStorage.setItem('getMesCodeCount', +getMesCodeCount + 1)
              : sessionStorage.setItem('getMesCodeCount', 1)
          }
          getPhoneCode({
            account: this.infoForm.account,
            phoneNumber: this.infoForm.phoneNumber
          }).then(res => {
            this.tackBtn()
          }).catch(res => {
            this.disabled = false
          })
        }
      })
    },
    tackBtn() {
      let time = 60
      const timer = setInterval(() => {
        if (time === 0) {
          clearInterval(timer)
          this.codeBtn = this.$t('login.getSmsVerificationCode')
          this.codeLoad = false
          this.disabled = false
        } else {
          this.codeLoad = true
          this.codeBtn = time + ' ' + this.$t('grid.others.retryAfterSeconds')
          time--
        }
      }, 1000)
    },
    // 输入验证码时校验验证码
    // eslint-disable-next-line no-undef
    checkCaptchaCodeFn: _.debounce(async function() {
      await this.handleCheckCaptchaCode()
      this.disabled = false
      sessionStorage.removeItem('getMesCodeCount')
    }, 1000),
    // 调用接口校验验证码
    async handleCheckCaptchaCode() {
      await checkCaptchaCode({
        captchaCode: this.infoForm.captchaCode,
        uuid: this.uuid
      })
    },
    // 获取图形验证码
    async getCodeImage() {
      this.uuid = uuidv4()
      this.codeImage = `/api/user/register/get/captcha?uuid=${this.uuid}`
    },
    // 获取图形验证码
    async getCodeImageAcc() {
      this.uuidAcc = uuidv4()
      this.codeImageAcc = `/api/user/ext/get/captcha?uuid=${this.uuidAcc}`
    },
    verifyAccount(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.loading = true
          checkAccountV3({ account: this.infoForm.account, captchaCode: this.infoForm.captchaCodeAcc, uuid: this.uuidAcc })
            .then(res => {
              this.loading = false
              if (res.code === '0000') {
                this.activeId = 1
              }
            })
            .catch(res => {
              this.loading = false
            })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    verifyPhone(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.loading = true
          checkCode({
            account: this.infoForm.account,
            code: this.infoForm.code,
            phoneNumber: this.infoForm.phoneNumber
          })
            .then(res => {
              this.loading = false
              this.userInfo = res.data
              this.activeId = 2
            })
            .catch(res => {
              this.loading = false
            })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    verifyPassword(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.loading = true
          this.userInfo.password = this.infoForm.password
          updatePasswd(this.userInfo)
            .then(res => {
              this.loading = false
              this.activeId = 3
            })
            .catch(res => {
              this.loading = false
            })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    }
  }
}
</script>
<style lang="scss">
$bg: #fff;
$step: #3e8ddc;
$is_wait: #7e7e7e;
$is_Wait: #dddddd;
$is_finish: #d3e9ff;
.forget-container {
  .el-steps {
    display: flex;
    justify-content: center;
    margin: auto;
  }
  .el-step.is-center {
    .el-step__head {
      .el-step__line {
        height: 8px;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        margin: auto;
        background: $is_finish;
        .el-step__line-inner {
          display: block;
          width: 100% !important;
          height: 100%;
          border-radius: 4px;
          border: none;
        }
      }
      .el-step__icon {
        width: 36px;
        height: 36px;
        font-size: 16px;
      }
      &.is-finish {
        .el-step__line {
          .el-step__line-inner {
            background: $is_finish;
          }
        }
        .el-step__icon {
          background: $is_finish;
          border: $is_finish;
          color: $bg;
        }
      }
      &.is-process {
        .el-step__line {
          .el-step__line-inner {
            background: $step;
          }
        }
        .el-step__icon {
          background: $step;
          border: $step;
          color: $bg;
        }
      }
      &.is-wait {
        .el-step__line {
          background: $is_Wait;
          .el-step__line-inner {
            background: $is_Wait;
          }
        }
        .el-step__icon {
          background: $is_Wait;
          border: $is_Wait;
          color: $bg;
        }
      }
    }
    .el-step__main {
      .el-step__title {
        font-size: 12px;
        color: $step;
        font-weight: 550;
        &.is-wait {
          color: $is_wait;
        }
      }
    }
    &:last-of-type {
      .el-step__line {
        display: block;
        border-top-right-radius: 4px;
        border-bottom-right-radius: 4px;
      }
    }
    &:first-of-type {
      .el-step__line {
        display: block;
        border-top-left-radius: 4px;
        border-bottom-left-radius: 4px;
      }
    }
  }
  .el-form {
    .el-form-item__content {
      line-height: 32px;
      .el-input {
        line-height: 32px;
      }
    }
    .el-input__inner {
      font-size: 12px !important;
      border-radius: 0;
      padding: 0 10px;
      height: 32px;
      line-height: 32px;
    }
    .el-button {
      font-size: 12px;
      border-radius: 0;
      background: #3e8ddc;
      height: 32px;
      line-height: 32px;
      padding: 0;
    }
    .el-form-item__error {
      position: unset;
    }
    .el-form-item {
      margin-bottom: 10px;
    }
  }
}
</style>
<style lang="scss" scoped>
$bg: #fff;
$dark_gray: #889aa4;
$light_gray: #262626;
$code_btn: #f5f5f5;
$code_border: #ddd;
$link: #3e8ddc;
.forget-container {
  background: $bg;
  background-size: cover;
  position: relative;
  min-width: 900px;
  height: 100%;
  width: 100%;
  /* background-color: #444f73; */
  background-color: #fff;
  overflow: hidden;
  .logo-title {
    padding: 12px 20px;
    box-shadow: 0 1px 4px rgb(0 21 41 / 8%);
    transition: width 0.2s;
    img {
      max-width: 300px;
    }
    a {
      color: $link;
      font-weight: 550;
      letter-spacing: 1px;
    }
  }
  .layout-class {
    position: absolute;
    top: 0;
    bottom: 0;
    right: 0;
    left: 0;
    margin: auto;
    height: 100%;
    width: 50%;
    height: 50%;
    min-width: 640px;
    .el-form {
      width: 264px;
      margin: 65px auto;
      font-size: 12px;
      .password-show {
        position: absolute;
        right: 10px;
        top: 0;
        bottom: 0;
        margin: auto;
        cursor: pointer;
        color: $dark_gray;
      }
      .code-btn {
        width: 112px;
        background: $code_btn;
        border: 1px solid $code_border;
        color: $light_gray;
        margin-left: 6px;
        padding: 0 12px;
      }
    }
    .forget-account{
      color: #fff;
      font-size:14px
    }
    .step-success {
      text-align: center;
      width: 264px;
      margin: 53px auto;
      font-size: 14px;
      .cnd-icon {
        background: #fafafa;
        border-radius: 50%;
        font-size: 30px;
        padding: 25px;
        color: $link;
        font-weight: 550;
      }
      p {
        color: $light_gray;
        line-height: 2;
      }
      a {
        color: $link;
        font-weight: 550;
      }
    }
  }
}
</style>
