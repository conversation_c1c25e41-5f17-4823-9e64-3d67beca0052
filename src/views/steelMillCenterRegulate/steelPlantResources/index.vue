<template>
  <div class="page-container">
    <p class="page-title">钢厂资源</p>
    <div class="layout-content auto-page-title flexV">
      <steel-search-form
        ref="searchForm"
        :form-items="formItems"
        @search="onSearch"
        @searchValue="onSearchValue"
      />
      <div class="btn-group mt-10">
        <div class="text">列表</div>
        <el-button type="primary" size="mini" @click="getMaintenance('view')">
          查看
        </el-button>
      </div>
      <steelTradeAggrid
        ref="aggrid"
        :column-defs="columnDefs"
        :row-data="rowData"
        :load-data="loadData"
        :auto-load-data="false"
        table-selection="single"
        :page-sizes="pageSizes"
        :default-page-size="defaultPageSize"
        @rowDoubleClicked="onRowDoubleClicked"
        @rowSelected="onDefSelectfun"
      />
    </div>
  </div>
</template>

<script>
import { getDictet, getCnDitc } from '@/api/logistics/saleDelivery/saleorder'
import { mscsteelmillsresourcePage } from '@/api/steelInfoManage/steelPlantResources.js'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import { Moment } from 'cnd-utils'
import mixins from '../mixins'
import moment from 'moment'
export default {
  name: 'SteelPlantResources',
  components: { steelTradeAggrid },
  mixins: [mixins],
  data() {
    const statusDict = [
      {
        sCodeValue: 10,
        sCodeName: '暂存',
        sSort: 101,
        sFilter: null,
        sIsEnabled: '1',
        sLanguage: 'zh_CN'
      },
      {
        sCodeValue: '20',
        sCodeName: '已上架',
        sSort: 202,
        sFilter: null,
        sIsEnabled: '1',
        sLanguage: 'zh_CN'
      },
      {
        sCodeValue: '30',
        sCodeName: '已下架',
        sSort: 90,
        sFilter: null,
        sIsEnabled: '1',
        sLanguage: 'zh_CN'
      }
    ]
    const startDate = moment().subtract(90, 'day').startOf('day').format('YYYY-MM-DDTHH:mm:ss')
    const endDate = moment().endOf('day').format('YYYY-MM-DDTHH:mm:ss')
    return {
      pageSizes: [10, 20, 30, 100, 200, 500],
      defaultPageSize: 500,
      disRemove: true,
      options: {
        'invoice.subtype': [],
        'msc.steelmill.status': []
      },
      formItems: [
        {
          label: '状态',
          value: 'sOrderStatus',
          type: 'elSelect',
          dict: statusDict,
          itemType: 'occultation',
          placeholder: this.$t('grid.others.pleaseSelectStatus')
        },
        {
          label: this.$t('grid.title.personnel'),
          value: 'sStaffId',
          type: 'cndInputDialogItem',
          option: { valueKey: 'sPath' },
          dialogType: 'customer',
          defaultUrl: '/msc/foundation/staff/page/admin/',
          placeholder: this.$t('grid.others.pleaseSelectPersonnel')
        },
        {
          label: '联系电话',
          value: 'sPhone',
          type: 'elInput'
        },
        {
          label: '供应商',
          value: 'sSupplierName',
          type: 'elInput'
        },

        {
          label: '资源信息',
          value: 'sContent',
          type: 'elInput'
        },

        {
          label: '截止日期',
          value: ['sExpirationTime', 'vExpirationTimeTo'],
          placeholder: [
            this.$t('grid.others.pleaseSelectTheDate'),
            this.$t('grid.others.pleaseSelectTheDate')
          ],
          default: ['', ''],
          unlinkPanels: true,
          type: 'elDatePicker',
          itemType: 'occultation'
        },
        {
          label: '创建日期',
          value: ['sCreateTime', 'vCreateTimeTo'],
          placeholder: [
            this.$t('grid.others.pleaseSelectTheDate'),
            this.$t('grid.others.pleaseSelectTheDate')
          ],
          default: [startDate, endDate],
          unlinkPanels: true,
          type: 'elDatePicker',
          itemType: 'occultation'
        },
        {
          label: '修改日期',
          value: ['sModifyTime', 'vModifyTimeTo'],
          placeholder: [
            this.$t('grid.others.pleaseSelectTheDate'),
            this.$t('grid.others.pleaseSelectTheDate')
          ],
          default: ['', ''],
          unlinkPanels: true,
          type: 'elDatePicker',
          itemType: 'occultation'
        },
        {
          label: '创建人',
          value: 'sCreator',
          type: 'cndInputDialog',
          itemType: 'occultation',
          placeholder: this.$t('grid.others.pleaseSelectTheCreator'),
          dialogType: 'applicant'
        },
        {
          label: '修改人',
          value: 'sModifier',
          type: 'cndInputDialog',
          itemType: 'occultation',
          placeholder: this.$t('grid.others.pleaseSelectTheCreator'),
          dialogType: 'applicant'
        },
        {
          label: 'ID',
          value: 'sId',
          type: 'elInput'
        }
      ],
      columnDefs: [
        {
          headerName: '产品分类',
          field: 'sProductCategory',
          valueGetter: (params) => {
            return getCnDitc(
              params,
              this.options['invoice.subtype'],
              'sProductCategory'
            )
          }
        },
        {
          field: 'sContent',
          headerName: ' 资源信息'
        },
        {
          headerName: '数量',
          field: 'sCount'
        },
        {
          headerName: '销售区域',
          field: 'sArea'
        },

        {
          headerName: '供应商',
          field: 'sSupplierName'
        },
        {
          headerName: '状态',
          field: 'sOrderStatus',
          valueGetter: (params) => {
            return getCnDitc(
              params,
              this.options['msc.steelmill.status'],
              'sOrderStatus'
            )
          }
        },
        {
          headerName: '截止日期',
          field: 'sExpirationTime',
          cellStyle: { textAlign: 'left' },
          width: '140px',
          valueGetter: (params) =>
            Moment.time('YYYY-MM-DD', params.data.sExpirationTime)
        },

        {
          headerName: '人员',
          field: 'vStaffName'
        },

        {
          headerName: '联系电话',
          field: 'sPhone'
        },

        {
          headerName: '部门',
          field: 'vDepartmentName'
        },

        {
          headerName: 'ID',
          field: 'sId',
          width: '150px'
        },
        {
          headerName: '公司',
          field: 'vCompanyName'
        },

        {
          headerName: '核算组',
          field: 'vCheckGroupName'
        },
        {
          headerName: '经营单位',
          field: 'vManagementName',
          width: '100px'
        },
        {
          headerName: '创建人',
          field: 'vCreatorName'
        },
        {
          headerName: '创建时间',
          field: 'sCreateTime',
          cellStyle: { textAlign: 'right' },
          width: '140px'
        },
        {
          headerName: '修改人',
          field: 'vModifierName'
        },
        {
          headerName: '修改时间',
          field: 'sModifyTime',
          cellStyle: { textAlign: 'right' },
          width: '140px'
        }
      ],
      rowData: [],
      selectId: '',
      detailsId: null,
      getGridApi: () => {
        return this.$refs.aggrid.gridApi
      }
    }
  },
  computed: {
    // detailParams() {
    //   const sIds = []
    //   const selectIds = []
    //   this.rowData.forEach((item) => {
    //     sIds.push(item.sId)
    //     if (item && item._selected) {
    //       selectIds.push(item.sId)
    //     }
    //   })
    //   return {
    //     sIds: sIds.toString(),
    //     selectIds: selectIds.toString()
    //   }
    // }
  },
  beforeCreate() {
    getDictet(['invoice.subtype', 'msc.steelmill.status'])
      .then((result) => {
        this.options['msc.steelmill.status'] = result.data[1].dicts

        this.options['invoice.subtype'] = result.data[0].dicts.map((item) => {
          if (item.sCodeValue === '70') {
            item.sCodeName = '执行'
          }
          return item
        })
      })
      .catch(() => {})
  },
  mounted() {
    this.onSearch(false)
  },
  methods: {
    getMaintenance(detailsId) {
      var detailsIdShow
      if (detailsId === 'view') {
        detailsIdShow = this.detailsId
      } else {
        detailsIdShow = detailsId
      }
      if (detailsId === 'view') {
        console.log(this.detailsId, 'this.detailsId')
        if (!this.detailsId) {
          this.$message.error('请勾选列表需要查看的项')
          return
        }
      } else {
        if (!detailsIdShow) {
          this.$message.error('请勾选列表需要查看的项')
          return
        }
      }

      this.$router.push({
        path: `/viewDetails/${detailsIdShow}`,
        query: {
          id: detailsIdShow,
          type: 'edit',
          name: '钢厂资源查看',
          status: 70,
          activeId: localStorage.getItem('menuId')
        }
      })
    },
    onSearch(load = true) {
      this.$refs.aggrid.loadTableData()
      this.detailsId = null
    },
    onDefSelectfun(r) {
      console.log(r.data, '单选中')
      if (this.detailsId === r.data.sId) {
        this.detailsId = ''
      } else {
        this.detailsId = r.data.sId
      }
    },
    // 点击 表格
    async onRowDoubleClicked(params) {
      this.getMaintenance(params.data.sId)
      console.log(params.data, '双击')
    },
    rowClicked({ data }) {
      if (!data) {
        this.selectId = null
        // this.$refs.detailAggrid.loadTableData()
      } else if (data && this.selectId !== data.sId) {
        this.selectId = data.sId
        // this.$refs.detailAggrid.loadTableData()
      }
    },
    // 过滤时间
    filteringTime(time) {
      return time.replace('T', ' ')
    },
    // 主表
    loadData(pagination) {
      let sExpirationTimeS,
        vExpirationTimeToS,
        sCreateTimeS,
        vCreateTimeToS,
        sModifyTimeS,
        vModifyTimeToS
      if (this.$refs.searchForm.getSearchData().sExpirationTime) {
        sExpirationTimeS = this.filteringTime(
          this.$refs.searchForm.getSearchData().sExpirationTime
        )
      }
      if (this.$refs.searchForm.getSearchData().vExpirationTimeTo) {
        vExpirationTimeToS = this.filteringTime(
          this.$refs.searchForm.getSearchData().vExpirationTimeTo
        )
      }
      if (this.$refs.searchForm.getSearchData().sCreateTime) {
        sCreateTimeS = this.filteringTime(
          this.$refs.searchForm.getSearchData().sCreateTime
        )
      }
      if (this.$refs.searchForm.getSearchData().vCreateTimeTo) {
        vCreateTimeToS = this.filteringTime(
          this.$refs.searchForm.getSearchData().vCreateTimeTo
        )
      }
      if (this.$refs.searchForm.getSearchData().sModifyTime) {
        sModifyTimeS = this.filteringTime(
          this.$refs.searchForm.getSearchData().sModifyTime
        )
      }
      if (this.$refs.searchForm.getSearchData().vModifyTimeTo) {
        vModifyTimeToS = this.filteringTime(
          this.$refs.searchForm.getSearchData().vModifyTimeTo
        )
      }
      const formData = {
        ...this.$refs.searchForm.getSearchData(),
        sExpirationTime: sExpirationTimeS,
        vExpirationTimeTo: vExpirationTimeToS,
        sCreateTime: sCreateTimeS,
        vCreateTimeTo: vCreateTimeToS,
        sModifyTime: sModifyTimeS,
        vModifyTimeTo: vModifyTimeToS
      }
      return new Promise((resolve, reject) => {
        mscsteelmillsresourcePage(formData, {
          ...pagination
        })
          .then((res) => {
            this.rowData = res.data.content.map((item) => {
              item._selected = false
              item._selectedKeys = []
              return item
            })
            resolve(res.data.totalElements)
            this.rowClicked({ data: res.data.content[0] })
          })
          .catch(() => {
            reject(0)
          })
      })
    }
  }
}
</script>
