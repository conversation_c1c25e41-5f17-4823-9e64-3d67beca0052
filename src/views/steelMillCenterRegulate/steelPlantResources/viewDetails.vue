<template>
  <div class="page-container detail">
    <cnd-dialog :visible="true" @close="onClose">
      <template slot="leftBtn">
        <el-button
          type="primary"
          size="mini"
          @click="dialogVisible.approval = true"
        >
          查看附件
        </el-button>
      </template>
      <template slot="content">
        <el-form
          ref="form"
          class="el-form-w100"
          label-width="100px"
          :inline="true"
          :model="form"
          size="small"
        >
          <cnd-form-card-list :active-panel="activeCollapseName">
            <!-- 基础信息 -->
            <cnd-form-card :title="$t('grid.tabs.basicInformation')" name="1">
              <el-row>
                <cnd-form-item label="产品分类" prop="sProductCategory">
                  <el-input v-model="form.sProductCategory" disabled />
                </cnd-form-item>
                <cnd-form-item label="数量" prop="sCount">
                  <el-input v-model="form.sCount" disabled />
                </cnd-form-item>
                <cnd-form-item label="供应商" prop="sSupplierName">
                  <el-input v-model="form.sSupplierName" disabled />
                </cnd-form-item>
                <cnd-form-item label="销售区域" prop="sArea">
                  <el-input v-model="form.sArea" disabled />
                </cnd-form-item>
                <cnd-form-item label="状态" prop="sOrderStatus">
                  <el-select v-model="form.sOrderStatus" disabled>
                    <el-option
                      v-for="item in selectOps['msc.steelmill.status']"
                      :key="item.sCodeValue"
                      :label="item.sCodeName"
                      :value="item.sCodeValue"
                    />
                  </el-select>
                </cnd-form-item>

                <cnd-form-item label="截止日期" prop="sExpirationTime">
                  <el-date-picker
                    v-model="form.sExpirationTime"
                    format="yyyy-MM-dd HH:mm:ss"
                    type="date"
                    :disabled="true"
                  />
                </cnd-form-item>

                <cnd-form-item label="人员" prop="sStaffId">
                  <el-input
                    v-model="form.vStaffName"
                    clearable
                    :disabled="true"
                  />
                </cnd-form-item>

                <cnd-form-item label="部门" prop="vDepartmentName">
                  <el-input v-model="form.vDepartmentName" disabled />
                </cnd-form-item>

                <cnd-form-item label="电话" prop="sPhone">
                  <el-input v-model="form.sPhone" disabled />
                </cnd-form-item>

                <cnd-form-item label="公司" prop="vCompanyName">
                  <el-input v-model="form.vCompanyName" disabled clearable />
                </cnd-form-item>

                <cnd-form-item label="核算组" prop="vCheckGroupName">
                  <el-input v-model="form.vCheckGroupName" disabled />
                </cnd-form-item>

                <cnd-form-item label="经营单位" prop="vManagementName">
                  <el-input v-model="form.vManagementName" disabled />
                </cnd-form-item>

                <cnd-form-item
                  label="资源信息"
                  prop="sContent"
                  :custom-width="24"
                >
                  <el-input
                    v-model="form.sContent"
                    clearable
                    :disabled="true"
                    type="textarea"
                    :autosize="{ minRows: 8, maxRows: 5 }"
                  />
                </cnd-form-item>
              </el-row>
            </cnd-form-card>
            <!-- 系统信息 -->
            <cnd-form-card :title="$t('grid.tabs.systemInformation')" name="2">
              <el-row>
                <cnd-form-item label="创建人" prop="vCreatorName">
                  <el-input v-model="form.vCreatorName" disabled />
                </cnd-form-item>
                <cnd-form-item label="创建时间" prop="sCreateTime">
                  <el-date-picker
                    v-model="form.sCreateTime"
                    format="yyyy-MM-dd HH:mm:ss"
                    type="date"
                    :disabled="true"
                  />
                </cnd-form-item>
                <cnd-form-item label="修改人" prop="sModifier">
                  <el-input v-model="form.vModifierName" disabled />
                </cnd-form-item>
                <cnd-form-item label="修改时间" prop="sModifyTime">
                  <el-date-picker
                    v-model="form.sModifyTime"
                    format="yyyy-MM-dd HH:mm:ss"
                    type="date"
                    :disabled="true"
                  />
                </cnd-form-item>
              </el-row>
            </cnd-form-card>
          </cnd-form-card-list>
        </el-form>
      </template>
    </cnd-dialog>
    <steel-annex-dialog
      :visible="dialogVisible.approval"
      append-to-body
      :biz-id="selectId"
      :disabled-btn="{
        scan: isBusinessDisabled('save', form.tImages),
        del: isBusinessDisabled('save', form.tImages)
      }"
      @onSelect="dialogVisible.approval = false"
    />
  </div>
</template>

<script>
import businessMixin from '@/utils/businessMixin'
import mixins from '../mixins'
import { mscsteelmillsresourceGet } from '@/api/steelInfoManage/steelPlantResources.js'
import { getDictet } from '@/api/logistics/saleDelivery/saleorder'
export default {
  name: 'ViewDetails',
  components: {},
  mixins: [businessMixin, mixins],
  data() {
    return {
      selectId: this.$route.query.id,

      dialogVisible: {
        annex: false, // 附件管理
        processQuery: false, // 进程查询
        approval: false, // 审批情况
        approvalN8: false
      },
      selectOps: {
        'msc.steelmill.status': []
      },
      form: {
        sCreator: '',
        sCreateTime: '',
        sModifier: '',
        sModifyTime: '',
        sStaffId: '',
        sDepartmentId: '',
        sPhone: '',
        sCompanyId: '',
        sCheckGroupId: '',
        sManagementId: '',
        sContent: '',
        sProductCategory: '',
        sCount: '',
        sSupplierName: '',
        sArea: '',
        sOrderStatus: '',
        sExpirationTime: '',
        tImages: []
      },
      activeCollapseName: ['1', '2'],
      visible: false,
      disRemove: true,
      srcList: []
    }
  },
  watch: {},
  beforeMount() {
    // 获取字典状态
    getDictet(['msc.steelmill.status'])
      .then((result) => {
        this.selectOps['msc.steelmill.status'] = result.data[0].dicts
      })
      .catch(() => {})
  },
  created() {
    if (this.selectId) {
      this.getDetail()
    }
  },
  methods: {
    getDetail() {
      //  获取详情
      mscsteelmillsresourceGet(this.selectId).then((res) => {
        this.form = res.data || this.form

        this.form.tImages.forEach((sImg) => {
          if (sImg.url) {
            this.srcList.push(sImg.url)
          }
        })
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-img {
  display: flex;
  flex-wrap: wrap;
}
</style>
