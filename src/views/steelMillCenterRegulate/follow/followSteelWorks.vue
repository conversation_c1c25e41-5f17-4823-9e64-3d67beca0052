<template>
  <div class="page-container">
    <p class="page-title">用户关注钢厂</p>
    <div class="layout-content auto-page-title flexV">
      <steel-search-form
        ref="searchForm"
        :form-items="formItems"
        @search="onSearch"
        @searchValue="onSearchValue"
      />
      <div class="btn-group mt-10">
        <div class="text">列表</div>
      </div>
      <steelTradeAggrid
        ref="aggrid"
        :column-defs="columnDefs"
        :row-data="rowData"
        :load-data="loadData"
        :auto-load-data="false"
        table-selection="single"
        @rowClicked="rowClicked"
      />
    </div>
  </div>
</template>

<script>
import { Moment } from 'cnd-utils'
import { getDictet, getCnDitc } from '@/api/logistics/saleDelivery/saleorder'

import { mscsteelmillsattentioncustomerPage } from '@/api/steelInfoManage/follow.js'

import steelTradeAggrid from '@/components/steelTradeAggrid'
import mixins from '../mixins'
import moment from 'moment'
export default {
  name: 'FollowSteelWorks',
  components: { steelTradeAggrid },
  mixins: [mixins],
  data() {
    const startDate = moment()
      .subtract(90, 'day')
      .startOf('day')
      .format('YYYY-MM-DDTHH:mm:ss')
    const endDate = moment().endOf('day').format('YYYY-MM-DDTHH:mm:ss')
    return {
      disRemove: true,
      options: {
        'invoice.subtype': [],
        'dev.supply.type': [],
        'msc.steelmill.status': []
      },
      headerCount: null,
      footerCount: null,
      searchInfo: null,
      formItems: [
        {
          label: '用户',
          value: 'sUserId',
          type: 'cndInputDialogItem',
          option: { value: 'id', valueKey: 'id' },
          defaultUrl: '/user/dialog/path/page'
        },
        {
          // 供应商
          label: '关注钢厂',
          value: 'sCustomerId',
          type: 'cndInputDialogItem',
          dialogType: 'customer',
          defaultUrl: '/esc/customer/page',
          option: { valueKey: 'sPath' },
          customerType: '20'
        },
        // {
        //   label: '关注钢厂',
        //   value: 'sProductCategoryCode',
        //   type: 'elSelect',
        //   dict: 'msc.steelmill.status',
        //   itemType: 'occultation',
        //   placeholder: this.$t('grid.others.pleaseSelectStatus')
        // },
        {
          label: '关注日期',
          value: ['sModifyTime', 'vModifyTimeTo'],
          placeholder: [
            this.$t('grid.others.pleaseSelectTheDate'),
            this.$t('grid.others.pleaseSelectTheDate')
          ],
          default: [startDate, endDate],
          unlinkPanels: true,
          type: 'elDatePicker',
          itemType: 'occultation'
        }
      ],
      columnDefs: [
        {
          headerName: '用户',
          field: 'vUserName',
          width: '150px'
        },
        {
          headerName: '关注钢厂',
          field: 'vCustomerName',
          valueGetter: (params) => {
            return getCnDitc(
              params,
              this.options['msc.steelmill.status'],
              'vCustomerName'
            )
          },
          width: '260px'
        },
        {
          headerName: '关注日期',
          field: 'sModifyTime',
          cellStyle: { textAlign: 'left' },
          minWidth: 140,
          valueFormatter(params) {
            return Moment.time('YYYY-MM-DD HH:mm:ss', params.data.sModifyTime)
          },
          width: '160px'
        }
      ],
      detailsId: null,
      rowData: [],
      rowDataChild: [],
      selectId: '',
      dialogVisibleadd: false,
      selectedId: null,
      getGridApi: () => {
        return this.$refs.aggrid.gridApi
      }
    }
  },
  computed: {},
  beforeCreate() {
    getDictet(['dev.supply.type', 'msc.steelmill.status'])
      .then((result) => {
        this.options['dev.supply.type'] = result.data[0].dicts
        this.options['msc.steelmill.status'] = result.data[1].dicts
      })
      .catch(() => {})
  },
  mounted() {
    this.onSearch(false)
  },
  methods: {
    // getMaintenance(detailsId) {
    //   var detailsIdShow
    //   if (detailsId === 'view') {
    //     detailsIdShow = this.detailsId
    //   } else {
    //     detailsIdShow = detailsId
    //   }

    //   if (detailsId === 'view') {
    //     if (!this.detailsId) {
    //       this.$message.error('请勾选列表需要查看的项')
    //       return
    //     }
    //   } else {
    //     if (!detailsIdShow) {
    //       this.$message.error('请勾选列表需要查看的项')
    //       return
    //     }
    //   }

    //   this.$router.push({
    //     path: `/RequirementViewing/${detailsIdShow}`,
    //     query: {
    //       id: detailsIdShow,
    //       type: 'edit',
    //       name: '需求详情',
    //       status: 70,
    //       activeId: localStorage.getItem('menuId')
    //     }
    //   })
    // },
    onSearch(load = true) {
      const searchInfo = this.$refs.searchForm.getSearchData()
      this.searchInfo = searchInfo
      if (load) {
        this.$refs.aggrid.loadTableData()
      }
    },
    // onDefSelectfun(r) {
    //   console.log(r.data, '单选中')
    //   if (this.detailsId === r.data.sId) {
    //     this.detailsId = ''
    //   } else {
    //     this.detailsId = r.data.sId
    //   }
    // },
    // // 点击 表格
    // async onRowDoubleClicked(params) {
    //   this.getMaintenance(params.data.sId)
    //   console.log(params.data, '双击')
    // },
    rowClicked({ data }) {
      if (!data) {
        this.selectId = null
        // this.$refs.detailAggrid.loadTableData()
      } else if (data && this.selectId !== data.sId) {
        this.selectId = data.sId
        // this.$refs.detailAggrid.loadTableData()
      }
    },

    filteringTime(time) {
      return time.replace('T', ' ')
    },
    // 主表
    loadData(pagination) {
      let sModifyTimeS, vModifyTimeToS
      if (this.$refs.searchForm.getSearchData().sModifyTime) {
        sModifyTimeS = this.filteringTime(
          this.$refs.searchForm.getSearchData().sModifyTime
        )
      }
      if (this.$refs.searchForm.getSearchData().vModifyTimeTo) {
        vModifyTimeToS = this.filteringTime(
          this.$refs.searchForm.getSearchData().vModifyTimeTo
        )
      }
      const formData = {
        ...this.$refs.searchForm.getSearchData(),
        sModifyTime: sModifyTimeS,
        vModifyTimeTo: vModifyTimeToS,
        sStatus: 1
      }
      return new Promise((resolve, reject) => {
        mscsteelmillsattentioncustomerPage(formData, {
          ...pagination
        })
          .then((res) => {
            this.rowData = res.data.content.map((item) => {
              item._selected = false
              item._selectedKeys = []
              return item
            })
            resolve(res.data.totalElements)
            this.rowClicked({ data: res.data.content[0] })
          })
          .catch(() => {
            reject(0)
          })
      })
    }
  }
}
</script>
