<template>
  <div class="page-container tab">
    <div class="layout-content auto-page-title flexV">
      <div class="btn-box">
        <importBtn
          :action="`/api/msc/mscsteelmillsproduct/import/${sCustomerId}`"
          class="ml-10 mr-10"
          btn-text="导入"
          success-mark="sCustomerId"
          @success="onSuccessImporting"
          @error="onSuccessImporting"
        />

        <!-- <el-button size="mini" type="primary">
        <importBtn
          :action="`/api/msc/mscsteelmillsproduct/import/${sCustomerId}`"
          btn-text="导入"
          title="导入"
          @success="onSuccessImporting"
          is-sync
        />
      </el-button> -->
        <el-button size="mini" type="primary" @click="getAdd"> 新增 </el-button>
        <el-button size="mini" type="primary" @click="editingRegion('edit')">
          编辑
        </el-button>
        <el-button size="mini" type="danger" @click="deletingRegion">
          删除
        </el-button>
      </div>
      <steelTradeAggrid
        ref="aggrid"
        :column-defs="columnDefs"
        :row-data="rowData"
        :load-data="loadData"
        :auto-load-data="true"
        table-selection="multiple"
        @rowClicked="rowClicked"
        @selectedChange="handleFooterCount"
        @rowDoubleClicked="onRowDoubleClicked"
      />
      <cnd-dialog
        :visible.sync="dialogVisible"
        :fullscreen="false"
        :title="title"
        width="540px"
        height="220px"
        @close="handleClose"
      >
        <template slot="content">
          <!-- form表单 -->
          <el-form
            ref="form"
            size="small"
            label-width="80px"
            :model="formData"
            :rules="formRules"
            class="form-right"
          >
            <cnd-form-item
              label="分类"
              custom-width="10"
              :error-msg="formRules.sProductCategoryCode[0].message"
              prop="sProductCategoryCode"
            >
              <el-select v-model="formData.sProductCategoryCode">
                <el-option
                  v-for="item in options['msc.product.category']"
                  :key="item.sCodeValue"
                  :label="item.sCodeName"
                  :value="item.sCodeValue"
                />
              </el-select>
            </cnd-form-item>

            <cnd-form-item
              label="品名"
              prop="sProductName"
              custom-width="10"
              :error-msg="formRules.sProductName[0].message"
            >
              <el-input
                v-model="formData.sProductName"
                clearable
                placeholder="请输入品名"
              />
            </cnd-form-item>

            <cnd-form-item
              label="牌号"
              prop="sProductBrand"
              :error-msg="formRules.sProductBrand[0].message"
              custom-width="10"
            >
              <el-input
                v-model="formData.sProductBrand"
                clearable
                placeholder="请输入牌号"
              />
            </cnd-form-item>

            <cnd-form-item
              label="厚度(规格)"
              custom-width="10"
              prop="sProductSpecsShow"
              :error-msg="specsShowText"
            >
              <div class="flex-box">
                <el-input
                  v-model="formData.sProductSpecsMin"
                  placeholder="请输入最小值"
                  type="Number"
                />
                ~
                <el-input
                  v-model="formData.sProductSpecsMax"
                  placeholder="请输入最大值"
                  type="Number"
                />
              </div>
            </cnd-form-item>

            <cnd-form-item label="宽度" custom-width="10">
              <div class="flex-box">
                <el-input
                  v-model="formData.sProductWidthMin"
                  placeholder="请输入最小值"
                  type="Number"
                />
                ~
                <el-input
                  v-model="formData.sProductWidthMax"
                  placeholder="请输入最大值"
                  type="Number"
                />
              </div>
            </cnd-form-item>
          </el-form>
        </template>
        <template slot="footer">
          <el-button size="mini" @click="handleClose">{{
            $t('btns.cancel')
          }}</el-button>
          <el-button type="primary" size="mini" @click="submit(true)">{{
            $t('btns.confirm')
          }}</el-button>
        </template>
      </cnd-dialog>
    </div>
  </div>
</template>

<script>
import businessMixin from '@/utils/businessMixin'
import mixins from '../mixins'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import { getCnDitc } from '@/api/logistics/saleDelivery/saleorder'
import importBtn from '@/components/importBtn'
import {
  productsAdd,
  productsPut,
  productsPage,
  productsDeleting
} from '@/api/steelInfoManage/steelMillMessageMaintain.js'
import { getDictet } from '@/api/logistics/saleDelivery/saleorder'
export default {
  name: 'Maintenance',
  components: { steelTradeAggrid, importBtn },
  mixins: [businessMixin, mixins],
  data() {
    return {
      supportFlagArr: [
        {
          sCodeValue: '10',
          sCodeName: '建材',
          sSort: 909,
          sFilter: null,
          sIsEnabled: '11',
          sLanguage: 'zh_CN'
        },
        {
          sCodeValue: '20',
          sCodeName: '热卷',
          sSort: 908,
          sFilter: null,
          sIsEnabled: '1',
          sLanguage: 'zh_CN'
        },
        {
          sCodeValue: '30',
          sCodeName: '中厚板',
          sSort: 908,
          sFilter: null,
          sIsEnabled: '1',
          sLanguage: 'zh_CN'
        },
        {
          sCodeValue: '40',
          sCodeName: '冷轧涂镀',
          sSort: 908,
          sFilter: null,
          sIsEnabled: '1',
          sLanguage: 'zh_CN'
        }
      ],
      specsShowText: '最小数值不能为0',
      formRules: {
        sProductCategoryCode: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        sProductName: [
          { required: true, message: '请输入', trigger: 'change' }
        ],
        sProductBrand: [
          { required: true, message: '请输入', trigger: 'change' }
        ],
        sProductSpecsShow: [
          { required: true, message: '请输入', trigger: 'change' }
        ]
      },
      title: '新增',
      options: {
        'invoice.subtype': [],
        'msc.product.category': []
      },
      formData: {
        sProductCategory: '',
        sProductName: '',
        sProductBrand: '',
        sProductSpecsMin: null,
        sProductSpecsMax: null,
        sProductWidthMin: '',
        sProductWidthMax: '',
        sProductSpecsShow: '',
        sProductCategoryCode: ''
      },
      dialogVisible: false,
      sCustomerId: this.$route.query.id,
      columnDefs: [
        {
          field: 'sProductCategory',
          headerName: '分类',
          valueGetter: (params) => {
            return getCnDitc(
              params,
              this.options['msc.product.category'],
              'sProductCategory'
            )
          },
          minWidth: 150
        },
        {
          field: 'sProductName',
          headerName: '品名',
          minWidth: 150
        },
        {
          field: 'sProductBrand',
          headerName: '牌号',
          minWidth: 150
        },
        {
          // sProductSpecsMin
          // sProductSpecsMax
          // sProductWidthMin
          field: 'sInvCompany',
          headerName: '厚度(规格)',
          minWidth: 150,
          valueGetter: (params) => {
            return this.getCompany(params)
          }
        },
        {
          field: 'sProductWidthMin',
          headerName: '宽度',
          minWidth: 150,
          valueGetter: (params) => {
            return this.getSProductWidthMin(params)
          }
        }
      ],

      visible: false,
      activeName: 'shipmentCommodity',
      disRemove: true,
      footerCount: null,
      selectId: null,
      rowData: [],
      rowDataChild: [],
      getGridApi: () => {
        return this.$refs.aggrid.gridApi
      },
      sId: '',
      sVersion: '',
      vCurAmtArrsSon: '',
      vCurAmtArrEdit: []
    }
  },
  watch: {
    // 监听最大值，如果最大值
    'formData.sProductSpecsMax'(val) {
      if (val > 0) {
        if (
          Number(val) > Number(this.formData.sProductSpecsMin) &&
          this.formData.sProductSpecsMin !== 0
        ) {
          // '合格'
          this.formData.sProductSpecsShow = '1'
        } else {
          this.specsShowText = '最小值不能大于最大值'
          this.formData.sProductSpecsShow = null
        }
      } else {
        this.formData.sProductSpecsShow = '1'
      }
    },
    // 监听小的数
    'formData.sProductSpecsMin'(val) {
      if (this.formData.sProductSpecsMax > 0) {
        if (Number(this.formData.sProductSpecsMax) > Number(val) && val !== 0) {
          // 合格
          this.formData.sProductSpecsShow = '1'
        } else {
          this.formData.sProductSpecsShow = null
        }
      } else {
        this.formData.sProductSpecsShow = '1'
      }
    }
  },
  created() {},
  beforeCreate() {
    getDictet(['msc.product.category'])
      .then((result) => {
        this.options['msc.product.category'] = result.data[0].dicts.map(
          (item) => {
            return item
          }
        )
      })
      .catch(() => {})
  },
  mounted() {
    // this.options['product.category'] = this.supportFlagArr
  },
  methods: {
    successImport(res) {
      const processArrivalDetailIds = res.data.processArrivalDetailIds
      if (processArrivalDetailIds) {
        this.$refs.aggrid.loadTableData()
      }
    },
    getCompany(item) {
      if (item.data.sProductSpecsMax) {
        return item.data.sProductSpecsMin === null
          ? item.data.sProductSpecsMax
          : item.data.sProductSpecsMin + ' ~ ' + item.data.sProductSpecsMax
      } else {
        return item.data.sProductSpecsMin
      }
    },
    getSProductWidthMin(item) {
      if (item.data.sProductWidthMax && item.data.sProductWidthMin) {
        return item.data.sProductWidthMin + ' ~ ' + item.data.sProductWidthMax
      } else {
        return item.data.sProductWidthMin || item.data.sProductWidthMax
      }
    },
    handleClose() {
      this.dialogVisible = false
      this.vCurAmtArrsSon = ''
      if (this.vCurAmtArrs.length > 1) {
        this.vCurAmtArrs = []
      }
    },
    submit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          const result = this.options['msc.product.category'].find(
            (item) => item.sCodeValue === this.formData.sProductCategoryCode
          )
          this.formData.sProductCategory = result.sCodeName

          this.onSuccess(this.formData)
        }
      })
    },
    onSuccessImporting(res) {
      this.sCustomerId = this.$route.query.id
      this.onSearch()
    },
    getAdd() {
      this.title = '新增'

      this.putShow = false
      this.formData = {
        sProductCategory: '',
        sProductName: '',
        sProductBrand: '',
        sProductSpecsMin: null,
        sProductSpecsMax: null,
        sProductWidthMin: '',
        sProductWidthMax: '',
        sProductSpecsShow: '',
        sProductCategoryCode: ''
      }
      this.dialogVisible = true
      this.$refs.form.resetFields()
    },
    // 点击编辑的时候，数据回填
    editingRegion(eidtList) {
      this.title = '编辑'
      // eidtList == true 这个是编辑的
      console.log(this.vCurAmtArrEdit, '选中的数据')

      if (eidtList === 'edit') {
        this.vCurAmtArrsSon = this.vCurAmtArrEdit[0]
      }

      if (this.vCurAmtArrs.length >= 0 && !this.vCurAmtArrsSon) {
        this.$message.error('请勾选需要编辑的项')
        return
      } else if (this.vCurAmtArr.length > 1 && eidtList) {
        this.$message.error('只能勾选一个进行编辑')
        return
      }
      this.$nextTick(() => {
        this.formData = []
      })
      this.putShow = true

      var forms = this.vCurAmtArrsSon || this.vCurAmtArrs[0]
      this.sId = this.vCurAmtArrsSon || this.vCurAmtArrs[0].sId
      this.sVersion = this.vCurAmtArrsSon || this.vCurAmtArrs[0].sVersion
      this.$nextTick(() => {
        this.formData = {
          sProductSpecsShow: '1',
          ...forms
        }
      })
      this.dialogVisible = true
    },
    deletingRegion() {
      if (!this.vCurAmtArr.length) {
        this.$message.error('请勾选需要删除的项')
        return
      }
      this.$confirm('是否确认删除？', this.$t('grid.others.prompt'), {
        confirmButtonText: this.$t('btns.confirm'),
        cancelButtonText: this.$t('btns.cancel'),
        type: 'warning'
      })
        .then(() => {
          productsDeleting(this.vCurAmtArr).then((res) => {
            if (res.code === '0000') {
              this.$message.success('删除成功！')
              this.onSearch()
            } else {
              this.$message.error(res.message)
            }
          })
        })
        .catch(() => {})
    },
    onSearch(load = true) {
      this.$refs.aggrid.loadTableData()
    },
    onFormChange(v, v1, item) {
      if (item.value === 'sStaffName' && v._previewsStaffName.sName) {
        if (v._previewsStaffName.sName) {
          v.sDepartmentName = v._previewsStaffName.orgUnitName
          v.sDepartmentId = v._previewsStaffName.orgUnitId
          v.sStaffId = v._previewsStaffName.id
          v.sStaffName = v._previewsStaffName.sName
        }
      } else {
        v.sDepartmentName = ''
        v.sDepartmentId = ''
        v.sStaffId = ''
        v.sStaffName = ''
      }
    },

    onSuccess(form) {
      form.sCustomerId = this.sCustomerId
      // 编辑数据
      if (this.putShow) {
        const editForm = {
          sId: this.sId,
          sVersion: this.sVersion,
          ...form
        }
        productsPut({ ...editForm })
          .then((res) => {
            if (res.code === '0000') {
              this.$message.success('编辑成功！')
              this.putShow = false
              this.onSearch()
            } else {
              this.$message.error(res.message)
            }
          })
          .catch((error) => {
            console.log(error, '.catch(() => {}).catch(() => {})')
          })
      } else {
        // 添加数据
        productsAdd({ ...form }).then((res) => {
          if (res.code === '0000') {
            this.$message.success('添加成功！')
            this.onSearch()
          } else {
            this.$message.error(res.message)
          }
        })
      }
      this.dialogVisible = false
      this.vCurAmtArrsSon = ''
    },
    // 主表
    loadData(pagination) {
      return new Promise((resolve, reject) => {
        productsPage(
          { sCustomerId: this.sCustomerId },
          {
            ...pagination
          }
        )
          .then((res) => {
            this.rowData = res.data.content.map((item) => {
              item._selected = false
              item._selectedKeys = []
              return item
            })
            resolve(res.data.totalElements)
            this.rowClicked({ data: res.data.content[0] })
          })
          .catch(() => {
            reject(0)
          })
      })
    },
    // 点击 表格
    onRowDoubleClicked(params) {
      this.vCurAmtArrs = [params.data]
      // 双击表格编辑
      this.vCurAmtArrsSon = params.data

      this.editingRegion(false)
    },
    handleFooterCount(rowData) {
      this.vCurAmtArrs = []
      const details = rowData.filter((item) => item._selected)
      this.vCurAmtArr = details.map((item) => {
        if (item._selected) {
          return item.sId
        }
      })
      this.vCurAmtArrEdit = details.map((item) => {
        if (item._selected) {
          return item
        }
      })
      this.vCurAmtArrsSon = this.vCurAmtArrEdit[0]
    },
    rowClicked({ data }) {
      // if (!data) {
      //   this.selectId = null
      //   this.$refs.detailAggrid.loadTableData()
      // } else if (data && this.selectId !== data.sId) {
      //   this.selectId = data.sId
      //   this.$refs.detailAggrid.loadTableData()
      // }
    }
  }
}
</script>
<style lang="scss" scoped>
.form-right {
  padding-right: 40px !important;
}
.form-box {
  padding: 10px;
}
.btn-box {
  padding: 12px 0;
}
.flex-box {
  width: 100%;
  display: flex;
}
</style>
<style>
@import url('~@/styles/basisSelfCollectionSheet/detail.scss');
</style>
