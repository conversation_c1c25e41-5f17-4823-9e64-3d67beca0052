<template>
  <div class="page-container tab">
    <div class="layout-content auto-page-title flexV">
      <div class="btn-box">
        <el-button size="mini" type="primary" @click="producionAdd">
          新增
        </el-button>
        <el-button size="mini" type="primary" @click="editingRegion('edit')">
          编辑
        </el-button>
        <el-button size="mini" type="danger" @click="deletingRegion">
          删除
        </el-button>
      </div>

      <steelTradeAggrid
        ref="aggrid"
        :column-defs="columnDefs"
        :row-data="rowData"
        :load-data="loadData"
        :auto-load-data="true"
        table-selection="multiple"
        @selectedChange="handleFooterCount"
        @rowDoubleClicked="onRowDoubleClicked"
      />
      <formDialog
        :visible="dialogVisible"
        :form-items="formDialogItems"
        width="600px"
        height="340px"
        :title="title"
        @close="dialogVisible = false"
        @success="onSuccess"
      />

      <formDialog
        :visible="dialogVisible1"
        :form-items="formDialogItems1"
        width="600px"
        height="340px"
        :title="title"
        @close="dialogVisible1 = false"
        @success="onSuccess"
      />
    </div>
  </div>
</template>

<script>
import businessMixin from '@/utils/businessMixin'
import mixins from '../mixins'
import steelTradeAggrid from '@/components/steelTradeAggrid'

import {
  productionLineAdd,
  productionLinePut,
  productionLinePage,
  productionLineDeleting
} from '@/api/steelInfoManage/steelMillMessageMaintain.js'

import formDialog from '@/components/formDialog'
export default {
  name: 'Maintenance',
  components: { steelTradeAggrid, formDialog },
  mixins: [businessMixin, mixins],
  data() {
    return {
      options: {
        'invoice.subtype': []
      },

      dialogVisible: false,
      dialogVisible1: false,
      sCustomerId: this.$route.query.id,
      formDialogItems: [
        {
          label: '厂区名称',
          value: 'sMillsAreaName',
          type: 'elInput',
          customWidth: 22,
          required: true,
          errorMessage: '请输入厂区名称'
        },
        {
          label: '产线信息',
          value: 'sProductionLineContent',
          type: 'elInputTextArea',
          customWidth: 22,
          required: true,
          errorMessage: '请输入产线信息',
          rows: 16
        }
      ],
      formDialogItems1: [],
      columnDefs: [
        {
          field: 'sMillsAreaName',
          width: '140px',
          headerName: '厂区名称'
        },
        {
          field: 'sProductionLineContent',
          headerName: '产线信息',
          wrapText: true,
          cellClass: 'cell-wrap-text',
          autoHeight: true,
          minWidth: 960,
          isSizeColumnsToFit: true,
          cellRenderer: (params) => {
            const sHtml = `<pre style="text-wrap:balance">${params.data.sProductionLineContent}</pre>`
            console.log(sHtml)
            return sHtml
          }
        }
      ],

      visible: false,
      disRemove: true,
      footerCount: null,
      selectId: null,
      rowData: [],
      rowDataChild: [],
      getGridApi: () => {
        return this.$refs.aggrid.gridApi
      },
      deletingSid: [],
      vCurAmtArr: [],
      vCurAmtArrs: [],
      putShow: false,
      sId: '',
      title: '新增',
      vCurAmtArrEdit: []
    }
  },
  watch: {},
  created() {},
  mounted() {},
  methods: {
    producionAdd() {
      this.title = '新增'
      this.putShow = false
      this.dialogVisible = true
    },
    // 点击编辑的时候，数据回填
    editingRegion(eidtList) {
      this.title = '编辑'
      this.putShow = true
      this.formDialogItems1 = []
      var forms
      if (eidtList === 'edit') {
        this.vCurAmtArrs = []
        forms = this.vCurAmtArrEdit[0]
        this.vCurAmtArrs = [this.vCurAmtArrEdit[0]]
      } else {
        forms = eidtList
      }
      if (this.vCurAmtArrEdit.length === 0 && eidtList === 'edit') {
        this.$message.error('请勾选需要编辑的项')
        return
      } else if (this.vCurAmtArrEdit.length > 1 && eidtList === 'edit') {
        this.$message.error('只能勾选一个进行编辑')
        return
      }
      this.$nextTick(() => {
        this.sId = forms.sId
        this.formDialogItems1 = [
          {
            label: '厂区名称',
            value: 'sMillsAreaName',
            type: 'elInput',
            customWidth: 22,
            required: true,
            errorMessage: '请输入厂区名称',
            default: forms.sMillsAreaName
          },
          {
            label: '产线信息',
            value: 'sProductionLineContent',
            type: 'elInputTextArea',
            customWidth: 22,
            required: true,
            errorMessage: '请输入产线信息',
            default: forms.sProductionLineContent,
            rows: 16
          }
        ]
      })
      this.dialogVisible1 = true
    },
    deletingRegion() {
      console.log(this.vCurAmtArr.length, 'this.vCurAmtArr.length')
      if (!this.vCurAmtArr.length) {
        this.$message.error('请勾选需要删除的项')
        return
      }

      this.$confirm('是否确认删除？', this.$t('grid.others.prompt'), {
        confirmButtonText: this.$t('btns.confirm'),
        cancelButtonText: this.$t('btns.cancel'),
        type: 'warning'
      })
        .then(() => {
          productionLineDeleting(this.vCurAmtArr).then((res) => {
            if (res.code === '0000') {
              this.$message.success('删除成功！')
              this.onSearch()
            } else {
              this.$message.error(res.message)
            }
          })
        })
        .catch(() => {})
    },
    onSearch(load = true) {
      this.$refs.aggrid.loadTableData()
    },
    onSuccess(form) {
      form.sCustomerId = this.sCustomerId
      // 编辑数据
      if (this.putShow) {
        form.sId = this.vCurAmtArrs[0].sId
        form.sVersion = this.vCurAmtArrs[0].sVersion
        productionLinePut({ ...form }).then((res) => {
          if (res.code === '0000') {
            this.$message.success('编辑数据!')
            this.putShow = false
            this.onSearch()
          } else {
            this.$message.error(res.message)
          }
        })
      } else {
        // 添加数据
        productionLineAdd({ ...form }).then((res) => {
          if (res.code === '0000') {
            this.$message.success('添加成功！')
            this.onSearch()
          } else {
            this.$message.error(res.message)
          }
        })
      }
    },
    // 主表
    loadData(pagination) {
      return new Promise((resolve, reject) => {
        productionLinePage(
          { sCustomerId: this.sCustomerId },
          {
            ...pagination
          }
        )
          .then((res) => {
            console.log(res, 'resresres')
            this.rowData = res.data.content.map((item) => {
              item._selected = false
              item._selectedKeys = []
              return item
            })
            resolve(res.data.totalElements)
          })
          .catch(() => {
            reject(0)
          })
      })
    },
    // 点击 表格
    onRowDoubleClicked(params) {
      this.vCurAmtArrs = []
      this.vCurAmtArrs = [params.data]

      this.editingRegion(params.data)

      console.log(params.data, '双击', this.vCurAmtArrs)
    },
    handleFooterCount(rowData) {
      const details = rowData.filter((item) => item._selected)
      this.vCurAmtArr = details.map((item) => {
        if (item._selected) {
          return item.sId
        }
      })
      this.vCurAmtArrEdit = details.map((item) => {
        if (item._selected) {
          return item
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.form-box {
  padding: 10px;
}
.btn-box {
  padding: 12px 0;
  padding-left: 10px;
}
</style>
<style>
@import url('~@/styles/basisSelfCollectionSheet/detail.scss');
</style>
