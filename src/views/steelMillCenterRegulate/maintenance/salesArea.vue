<template>
  <div class="page-container tab">
    <div class="layout-content auto-page-title flexV">
      <div class="btn-box">
        <el-button size="mini" type="primary" @click="saleaAreaAdd">
          新增
        </el-button>
        <el-button size="mini" type="primary" @click="editingRegion('edit')">
          编辑
        </el-button>
        <el-button size="mini" type="danger" @click="deletingRegion">
          删除
        </el-button>
      </div>

      <steelTradeAggrid
        ref="aggrid"
        :column-defs="columnDefs"
        :row-data="rowData"
        :load-data="loadData"
        :auto-load-data="false"
        table-selection="multiple"
        @rowClicked="rowClicked"
        @selectedChange="handleFooterCount"
        @rowDoubleClicked="onRowDoubleClicked"
      />
      <formDialog
        :keys="formDialogItems"
        :visible="dialogVisible"
        :form-items="formDialogItems"
        width="350px"
        height="140px"
        :title="title"
        :clear-show="false"
        @close="cancel"
        @change="onFormChange"
        @success="onSuccess"
      />

      <formDialog
        :visible="dialogVisible1"
        :form-items="formDialogItems1"
        width="350px"
        height="140px"
        title="编辑"
        :clear-show="false"
        @close="cancelEdit"
        @change="onFormChange"
        @success="onSuccess"
      />
    </div>
  </div>
</template>

<script>
// import { getMorgUnit } from '@/api/money/cashFlow.js'
import businessMixin from '@/utils/businessMixin'
import mixins from '../mixins'
import steelTradeAggrid from '@/components/steelTradeAggrid'

import {
  salesAreaAdd,
  salesAreaPut,
  salesAreaPage,
  salesAreaDeleting
} from '@/api/steelInfoManage/steelMillMessageMaintain.js'

import formDialog from '@/components/formDialog'
export default {
  name: 'Maintenance',
  components: { steelTradeAggrid, formDialog },
  mixins: [businessMixin, mixins],
  data() {
    return {
      options: {
        'invoice.subtype': []
      },
      dialogVisible: false,
      dialogVisible1: false,
      sCustomerId: this.$route.query.id,
      formDialogItems: [
        {
          label: '人员',
          value: 'sStaffName',
          type: 'cndInputDialogItem',
          defaultUrl: '/msc/foundation/staff/page/admin',
          customWidth: 22,
          required: true,
          errorMessage: '请输入人员',
          option: {
            valueKey: 'sPath'
          },
          dialogType: 'staff'
        },
        {
          label: '部门',
          value: 'sDepartmentName',
          type: 'elInput',
          customWidth: 22,
          disabled: true
        },
        {
          label: '区域',
          value: 'sArea',
          type: 'elInput',
          customWidth: 22,
          disabled: false
        },
        {
          label: '电话',
          value: 'sPhone',
          type: 'elInput',
          customWidth: 22,
          disabled: false
        }
      ],
      formDialogItems1: [],
      columnDefs: [
        {
          field: 'vStaffName',
          headerName: '人员',
          width: '120px'
        },
        {
          field: 'vDepartmentName',
          headerName: '部门',
          width: '140px'
        },
        {
          field: 'sArea',
          headerName: '区域',
          width: '140px'
        },
        {
          field: 'sPhone',
          headerName: '电话',
          width: '140px'
        }
      ],

      visible: false,
      rowData: [],
      rowDataChild: [],
      getGridApi: () => {
        return this.$refs.aggrid.gridApi
      },
      deletingSid: [],
      vCurAmtArr: [],
      vCurAmtArrs: [],
      putShow: false,
      title: '新增',
      sId: '',
      vCurAmtArrEdit: []
    }
  },
  watch: {},
  created() {},
  mounted() {
    this.onSearch(false)
  },
  methods: {
    saleaAreaAdd() {
      this.title = '新增'
      this.putShow = false
      this.dialogVisible = true
      // this.formDialogItems = []
    },
    // 点击编辑的时候，数据回填
    editingRegion(eidtList) {
      this.title = '编辑'
      this.putShow = true
      this.formDialogItems1 = []
      var forms
      if (eidtList === 'edit') {
        this.vCurAmtArrs = []
        forms = this.vCurAmtArrEdit[0]
        this.vCurAmtArrs = [this.vCurAmtArrEdit[0]]
      } else {
        forms = eidtList
      }
      if (this.vCurAmtArrEdit.length === 0 && eidtList === 'edit') {
        this.$message.error('请勾选需要编辑的项')
        return
      } else if (this.vCurAmtArrEdit.length > 1 && eidtList === 'edit') {
        this.$message.error('只能勾选一个进行编辑')
        return
      }
      const asId = {
        sDepartmentId: forms.sDepartmentId,
        vDepartmentName: forms.sDepartmentName,
        vStaffName: forms.vStaffName,
        sStaffId: forms.sStaffId
      }

      this.$nextTick(() => {
        this.formDialogItems1 = [
          {
            label: '人员',
            value: 'sStaffName',
            type: 'cndInputDialogItem',
            defaultUrl: '/msc/foundation/staff/page/admin',
            customWidth: 22,
            required: true,
            default: { ...asId },
            errorMessage: '请输入人员',
            option: {
              valueKey: 'sPath'
            },
            dialogType: 'staff',
            labelDefault: forms.vStaffName
          },
          {
            label: '部门',
            value: 'sDepartmentName',
            type: 'elInput',
            customWidth: 22,
            disabled: true,
            default: forms.vDepartmentName
          },
          {
            label: '区域',
            value: 'sArea',
            type: 'elInput',
            customWidth: 22,
            default: forms.sArea
          },
          {
            label: '电话',
            value: 'sPhone',
            type: 'elInput',
            customWidth: 22,
            default: forms.sPhone
          }
        ]
      })

      this.dialogVisible1 = true
    },
    deletingRegion() {
      if (!this.vCurAmtArr.length) {
        this.$message.error('请勾选需要删除的项')
        return
      }
      this.$confirm('是否确认删除？', this.$t('grid.others.prompt'), {
        confirmButtonText: this.$t('btns.confirm'),
        cancelButtonText: this.$t('btns.cancel'),
        type: 'warning'
      })
        .then(() => {
          salesAreaDeleting(this.vCurAmtArr).then((res) => {
            if (res.code === '0000') {
              this.$message.success('删除成功！')
              this.onSearch()
            } else {
              this.$message.error(res.message)
            }
          })
        })
        .catch(() => {})
    },
    onSearch(load = true) {
      this.$refs.aggrid.loadTableData()
    },
    onFormChange(v, v1, item) {
      // 新增
      if (item.value === 'sStaffName' && v._previewsStaffName.sName) {
        if (v._previewsStaffName.sName) {
          v.sDepartmentName = v._previewsStaffName.orgUnitName
          v.sDepartmentId = v._previewsStaffName.orgUnitId
          v.sStaffId = v._previewsStaffName.sId
          v.sStaffName = v._previewsStaffName.sName
          v.sPhone = v._previewsStaffName.sMobilePhone1
        }
      } else {
        v.sDepartmentName = ''
        v.sDepartmentId = ''
        v.sStaffId = ''
        v.sStaffName = ''
      }
    },

    cancel(type = false) {
      this.dialogVisible = false
    },
    cancelEdit() {
      this.dialogVisible1 = false
      this.$set(this, 'dialogVisible1', false)
    },
    onSuccess(form) {
      if (form.sArea) {
        var formData
        if (this.dialogVisible1) {
          formData = {
            sArea: form.sArea,
            sCustomerId: this.sCustomerId,
            sDepartmentId: form.sStaffName.sDepartmentId || form.sDepartmentId,
            vDepartmentName: form.sDepartmentName,
            sPhone: form.sPhone,
            sStaffId: form.sStaffName.sStaffId || form.sStaffId,
            vStaffName: form.sStaffName.vStaffName || form.sStaffName
          }
          console.log('编辑数据')
        } else {
          formData = {
            sArea: form.sArea,
            sCustomerId: this.sCustomerId,
            sDepartmentId: form.sDepartmentId,
            vDepartmentName: form.sDepartmentName,
            sPhone: form.sPhone,
            sStaffId: form.sStaffId,
            vStaffName: form.sStaffName
          }
        }
        console.log(formData)
        if (this.dialogVisible1) {
          formData.sId = this.vCurAmtArrs[0].sId
          formData.sVersion = this.vCurAmtArrs[0].sVersion
        }
        // 编辑数据
        if (this.putShow) {
          salesAreaPut({ ...formData }).then((res) => {
            if (res.code === '0000') {
              this.$message.success('编辑数据!')
              this.putShow = false
              this.onSearch()
            } else {
              this.$message.error(res.message)
            }
          })
        } else {
          // 添加数据
          salesAreaAdd({ ...formData }).then((res) => {
            if (res.code === '0000') {
              this.$message.success('添加成功！')
              this.onSearch()
            } else {
              this.$message.error(res.message)
            }
          })
        }
      }
    },
    // 主表
    loadData(pagination) {
      return new Promise((resolve, reject) => {
        salesAreaPage(
          { sCustomerId: this.sCustomerId },
          {
            ...pagination
          }
        )
          .then((res) => {
            console.log(res, 'resresres')
            this.rowData = res.data.content.map((item) => {
              item._selected = false
              item._selectedKeys = []
              return item
            })
            resolve(res.data.totalElements)
            this.rowClicked({ data: res.data.content[0] })
          })
          .catch(() => {
            reject(0)
          })
      })
    },
    // 点击 表格
    onRowDoubleClicked(params) {
      this.vCurAmtArrs = []
      this.vCurAmtArrs = [params.data]
      this.editingRegion(params.data)
      console.log(params.data, '双击', this.vCurAmtArrs)
    },
    handleFooterCount(rowData) {
      const details = rowData.filter((item) => item._selected)
      this.vCurAmtArr = details.map((item) => {
        if (item._selected) {
          return item.sId
        }
      })
      this.vCurAmtArrEdit = details.map((item) => {
        if (item._selected) {
          return item
        }
      })
    },
    rowClicked({ data }) {
      // if (!data) {
      //   this.selectId = null
      //   this.$refs.detailAggrid.loadTableData()
      // } else if (data && this.selectId !== data.sId) {
      //   this.selectId = data.sId
      //   this.$refs.detailAggrid.loadTableData()
      // }
    }
  }
}
</script>
<style lang="scss" scoped>
.form-box {
  padding: 10px;
}
.btn-box {
  padding: 12px 0;
  padding-left: 10px;
}
</style>
<style>
@import url('~@/styles/basisSelfCollectionSheet/detail.scss');
</style>
