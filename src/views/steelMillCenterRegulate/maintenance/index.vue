<template>
  <div class="detail">
    <cnd-dialog :visible="true" @close="onClose">
      <template slot="content">
        <el-tabs v-model="activeName">
          <el-tab-pane label="基础信息" name="shipmentCommodity">
            <div>
              <el-collapse v-model="activeNames">
                <cnd-form-card
                  :inline="true"
                  :title="$t('grid.tabs.basicInformation')"
                  name="1"
                >
                  <el-form
                    ref="ruleForm"
                    :model="form"
                    :rules="rules"
                    label-width="120px"
                    size="small"
                  >
                    <cnd-form-item label="编码" :custom-width="6">
                      <cnd-input-number
                        v-model="form.sCode"
                        type="text"
                        disabled
                      />
                    </cnd-form-item>

                    <cnd-form-item
                      label="常用名称"
                      :custom-width="6"
                      prop="sName"
                    >
                      <cnd-input-number
                        v-model="form.sName"
                        type="text"
                        disabled
                      />
                    </cnd-form-item>

                    <cnd-form-item
                      label="统一社会信用代码"
                      :custom-width="6"
                      prop="sBusinessLicense"
                    >
                      <cnd-input-number
                        v-model="form.sBusinessLicense"
                        type="text"
                        disabled
                      />
                    </cnd-form-item>

                    <cnd-form-item label="简称" :custom-width="6">
                      <cnd-input-number
                        v-model="form.sShortName"
                        type="text"
                        disabled
                      />
                    </cnd-form-item>

                    <cnd-form-item label="地址" :custom-width="6">
                      <cnd-input-number
                        v-model="form.sAddress"
                        type="text"
                        disabled
                      />
                    </cnd-form-item>
                    <cnd-form-item label="备注" :custom-width="12">
                      <cnd-input-number
                        v-model="form.sRemark"
                        type="text"
                        disabled
                      />
                    </cnd-form-item>
                  </el-form>
                </cnd-form-card>
                <cnd-form-card title="系统信息" name="2">
                  <el-form
                    ref="form1"
                    label-width="100px"
                    size="small"
                    :model="form"
                  >
                    <cnd-form-item label="创建人" :custom-width="6">
                      <cnd-input-number
                        v-model="form.vCreatorName"
                        type="text"
                        disabled
                      />
                    </cnd-form-item>

                    <cnd-form-item label="创建时间" :custom-width="6">
                      <cnd-input-number
                        v-model="form.sCreateTimes"
                        type="text"
                        disabled
                      />
                    </cnd-form-item>

                    <cnd-form-item label="修改人" :custom-width="6">
                      <cnd-input-number
                        v-model="form.vModifierName"
                        type="text"
                        disabled
                      />
                    </cnd-form-item>
                    <cnd-form-item label="修改时间" :custom-width="6">
                      <cnd-input-number
                        v-model="form.sModifyTimes"
                        type="text"
                        disabled
                      />
                    </cnd-form-item>
                  </el-form>
                </cnd-form-card>
              </el-collapse>
            </div>
          </el-tab-pane>
          <el-tab-pane label="销售区域" name="shippingLogistics">
            <salesArea v-if="activeName == 'shippingLogistics'" />
          </el-tab-pane>

          <el-tab-pane label="产线信息" name="shippingLogistics1">
            <production v-if="activeName == 'shippingLogistics1'" />
          </el-tab-pane>

          <el-tab-pane label="产品信息" name="shippingLogistics2">
            <productInformation v-if="activeName == 'shippingLogistics2'" />
          </el-tab-pane>
        </el-tabs>
      </template>
    </cnd-dialog>
  </div>
</template>

<script>
var Decimal = window.Decimal
import businessMixin from '@/utils/businessMixin'
import mixins from '../mixins'
import salesArea from './salesArea'
import production from './production'
import productInformation from './productInformation'

import { factInvoicePage } from '@/api/steelInfoManage/purchaseInvoice.js'
import exportBtn from '@/components/exportBtnV2'
export default {
  name: 'Maintenance',
  components: { salesArea, exportBtn, production, productInformation },
  mixins: [businessMixin, mixins],
  data() {
    return {
      rules: {
        sBusinessLicense: [{ required: true, message: '', trigger: 'change' }],
        sName: [{ required: true, message: '', trigger: 'change' }]
      },
      options: {
        'invoice.subtype': []
      },
      columnDefs: [
        {
          field: 'sInvCompany',
          headerName: '人员'
        },
        {
          field: 'sInvCompany',
          headerName: '部门'
        },
        {
          field: 'sInvCompany',
          headerName: '区域'
        },
        {
          field: 'sInvCompany',
          headerName: '电话'
        }
      ],
      form: {
        outQty: ''
      },
      form1: {
        outQty: ''
      },
      activeNames: ['1', '2'],
      visible: false,
      activeName: 'shipmentCommodity',
      disRemove: true,
      // options: {
      //   'dev.common.sheet.status': []
      // },
      headerCount: null,
      footerCount: null,
      searchInfo: null,
      selectId: null,
      rowData: [],
      rowDataChild: [],
      getGridApi: () => {
        return this.$refs.aggrid.gridApi
      },
      sCustomerId: this.$route.query.id
    }
  },
  watch: {
    activeName(val) {
      this.$nextTick(() => {
        // this.getChildHeader()
      })
    }
  },
  created() {},
  mounted() {
    if (this.sCustomerId) {
      this.form = JSON.parse(sessionStorage.getItem('BASICS'))
    }
  },
  methods: {
    // 主表
    loadData(pagination) {
      return new Promise((resolve, reject) => {
        factInvoicePage({
          ...pagination
        })
          .then((res) => {
            this.rowData = res.data.page.content.map((item, index) => {
              if (this.selectId) {
                item._selected = this.selectId === item?.sId
              } else {
                item._selected = index === 0
              }
              item._selectedKeys = []
              return item
            })
            resolve(res.data.page.totalElements)
            this.rowClicked({ data: res.data.page.content[0] })
          })
          .catch(() => {
            reject(0)
          })
      })
    },
    // 点击 表格
    onRowDoubleClicked(params) {},
    rowClicked({ data }) {
      if (!data) {
        this.selectId = null
        this.$refs.detailAggrid.loadTableData()
      } else if (data && this.selectId !== data.sId) {
        this.selectId = data.sId
        this.$refs.detailAggrid.loadTableData()
      }
    },
    handleChildFooterCount(rowData) {
      this.$refs.detailAggrid.getSelectedData((res) => {
        const vCount = res.length
        let sContractQty = 0
        let sQty = 0
        res.forEach((el) => {
          sContractQty = +new Decimal(sContractQty).add(+el.sContractQty)
          sQty = +new Decimal(sQty).add(+el.sQty)
        })
        this.setCount(vCount, sContractQty, sQty, 'footerCount')
      })
    }
  }
}
</script>

<style scoped>
@import url('~@/styles/basisSelfCollectionSheet/detail.scss');
</style>
<style lang="scss" scoped>
.detail {
  ::v-deep .dialog-body-position {
    top: 0 !important;
  }
}
</style>
