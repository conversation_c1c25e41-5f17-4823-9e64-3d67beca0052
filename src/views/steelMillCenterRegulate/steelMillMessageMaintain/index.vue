<template>
  <div class="page-container">
    <p class="page-title">钢厂维护信息</p>
    <div class="layout-content auto-page-title flexV">
      <steel-search-form
        ref="searchForm"
        :form-items="formItems"
        @search="onSearch"
        @searchValue="onSearchValue"
      />
      <div class="btn-group mt-10">
        <div class="text">列表</div>
        <el-button size="mini" type="primary" @click="getMaintenance('view')">维护</el-button>
      </div>
      <steelTradeAggrid
        ref="aggrid"
        :column-defs="columnDefs"
        :row-data="rowData"
        :load-data="loadData"
        :auto-load-data="false"
        table-selection="single"
        @rowClicked="rowClicked"
        @selectedChange="handleFooterCount"
        @rowDoubleClicked="onRowDoubleClicked"
        @rowSelected="onDefSelectfun"
      />
    </div>
  </div>
</template>

<script>
import moment from 'moment'
import { Moment } from 'cnd-utils'
import { SteelFormat } from 'cnd-horizon-utils'
import { getDictet } from '@/api/logistics/saleDelivery/saleorder'
import { customerPage } from '@/api/steelInfoManage/steelMillMessageMaintain.js'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import exportBtn from '@/components/exportBtnV2'
import mixins from '../mixins'
export default {
  name: 'SteelMillMessageMaintain',
  components: { steelTradeAggrid, exportBtn },
  mixins: [mixins],
  data() {
    const startDate = moment().subtract(90, 'day').startOf('day').format('YYYY-MM-DDTHH:mm:ss')
    const endDate = moment().endOf('day').format('YYYY-MM-DDTHH:mm:ss')
    return {
      disRemove: true,
      options: {
        'invoice.subtype': []
      },
      headerCount: null,
      footerCount: null,
      searchInfo: null,
      formItems: [
        {
          label: '编码',
          value: 'sCode',
          type: 'elInput'
        },
        {
          label: '常用名称',
          value: 'sName',
          type: 'elInput'
        },
        {
          label: '简称',
          value: 'sShortName',
          type: 'elInput'
        },
        {
          label: '社会信用代码',
          value: 'sBusinessLicense',
          type: 'elInput'
        },
        {
          label: '创建日期',
          value: ['sCreateTime', 'vCreateTimeTo'],
          placeholder: [
            this.$t('grid.others.pleaseSelectTheDate'),
            this.$t('grid.others.pleaseSelectTheDate')
          ],
          default: [startDate, endDate],
          unlinkPanels: true,
          type: 'elDatePicker',
          itemType: 'occultation'
        },

        {
          label: '修改日期',
          value: ['sModifyTime', 'vModifyTimeTo'],
          placeholder: [
            this.$t('grid.others.pleaseSelectTheDate'),
            this.$t('grid.others.pleaseSelectTheDate')
          ],
          default: ['', ''],
          unlinkPanels: true,
          type: 'elDatePicker',
          itemType: 'occultation'
        },
        {
          label: '维护日期',
          value: ['sCustomerModifyTime', 'vCustomerModifyTimeTo'],
          placeholder: [
            this.$t('grid.others.pleaseSelectTheDate'),
            this.$t('grid.others.pleaseSelectTheDate')
          ],
          default: ['', ''],
          unlinkPanels: true,
          type: 'elDatePicker',
          itemType: 'occultation'
        }
      ],
      columnDefs: [
        {
          field: 'sCode',
          headerName: '编码'
        },

        {
          headerName: '常用名称',
          field: 'sName',
          width: '160px'
        },
        {
          headerName: '简称',
          field: 'sShortName',
          width: '160px'
        },
        {
          headerName: '统一社会信用代码',
          field: 'sBusinessLicense',
          width: '160px'
        },
        {
          headerName: '备注',
          field: 'sRemark'
        },
        {
          headerName: '销售区域',
          field: 'steelMillsSalesAreaType'
        },
        {
          headerName: '产线信息',
          field: 'steelMillsProductLineType'
        },
        {
          headerName: '产品信息',
          field: 'steelMillsProductType'
        },
        {
          headerName: '维护人',
          field: 'vCustomerModifierName'
        },
        {
          headerName: '维护时间',
          field: 'sCustomerModifyTime',
          valueFormatter(params) {
            return Moment.time(
              'YYYY-MM-DD hh:mm:ss',
              params.data.sCustomerModifyTime
            )
          },
          width: '160px'
        },
        {
          headerName: '创建人',
          field: 'vCreatorName'
        },
        {
          headerName: '创建时间',
          field: 'sCreateTime',
          cellStyle: { textAlign: 'right' },
          valueFormatter(params) {
            return Moment.time('YYYY-MM-DD hh:mm:ss', params.data.sCreateTime)
          },
          width: '160px'
        },
        {
          headerName: '修改人',
          field: 'vModifierName'
        },
        {
          headerName: '修改时间',
          field: 'sModifyTime',
          cellStyle: { textAlign: 'right' },
          valueFormatter(params) {
            return Moment.time('YYYY-MM-DD HH:mm:ss', params.data.sModifyTime)
          },
          width: '160px'
        }
        // {
        //   field: 'vManagementName',
        //   headerName: '经营单位'
        // },
      ],
      rowData: [],
      rowDataChild: [],
      selectId: '',
      dialogVisibleadd: false,
      selectedId: null,
      getGridApi: () => {
        return this.$refs.aggrid.gridApi
      },
      detailsId: null,
      unitary: null
    }
  },
  computed: {
    detailParams() {
      const sIds = []
      const selectIds = []
      this.rowData.forEach((item) => {
        sIds.push(item.sId)
        if (item && item._selected) {
          selectIds.push(item.sId)
        }
      })
      return {
        sIds: sIds.toString(),
        selectIds: selectIds.toString()
      }
    }
  },
  beforeCreate() {
    getDictet(['invoice.subtype'])
      .then((result) => {
        this.options['invoice.subtype'] = result.data[0].dicts.map((item) => {
          if (item.sCodeValue === '70') {
            item.sCodeName = '执行'
          }
          return item
        })
      })
      .catch(() => {})
  },
  mounted() {
    this.onSearch(false)
  },
  methods: {
    getMaintenance(detailsId) {
      var detailsIdShow
      if (detailsId === 'view') {
        detailsIdShow = this.detailsId
        sessionStorage.setItem('BASICS', JSON.stringify(this.unitary))
      } else {
        detailsIdShow = detailsId
      }

      // var detailsIdShow = this.detailsId || detailsId

      if (detailsId === 'view') {
        console.log(this.detailsId, 'this.detailsId')
        if (!this.detailsId) {
          this.$message.error('请勾选列表需要维护的项')
          return
        }
      } else {
        if (!detailsIdShow) {
          this.$message.error('请勾选列表需要维护的项')
          return
        }
      }
      this.$router.push({
        path: `/maintenance/${detailsIdShow}`,
        query: {
          id: detailsIdShow,
          type: 'edit',
          name: '钢厂信息维护',
          status: 70,
          activeId: localStorage.getItem('menuId')
        }
      })
    },
    onSearch(load = true) {
      this.$refs.aggrid.loadTableData()
      this.detailsId = null
      // const searchInfo = this.$refs.searchForm.getSearchData()
      // this.searchInfo = searchInfo
      // const sUpSourceType = '05'
      // if (load && sUpSourceType) {
      //   this.selectId = null
      //   const headerConfig = {
      //     sUpSourceType: '05',
      //     sMenuCode: '03',
      //     aggridRefs: this.$refs.detailAggrid,
      //     defaultColumns: this.childrenColumns
      //   }
      //   this.getHeader(headerConfig)
      // } else {
      //   this.selectId = null
      //   this.rowData = []
      //   this.rowDataChild = []
      //   this.headerCount = null
      //   this.footerCount = null
      // }
    },
    setCount(vCount = 0, vSumQty = 0, vSumTaxAmt = 0, flag) {
      this[flag] = [
        { count: vCount, key: 'count' },
        {
          title: '开票数量',
          count: SteelFormat.formatThousandthSign(vSumQty, 4),
          unit: this.$t('grid.others.ton')
        },
        {
          title: '含税金额',
          count: SteelFormat.formatPrice(vSumTaxAmt),
          unit: this.$t('grid.others.yuan')
        }
      ]
    },
    onDefSelectfun(r) {
      console.log(r.data, '单选中')
      if (this.detailsId === r.data.sId) {
        this.detailsId = ''
      } else {
        this.unitary = r.data
        this.detailsId = r.data.sId
      }
    },
    // 点击 表格
    onRowDoubleClicked(params) {
      // this.detailsId = params.data.sId
      params.data.sCreateTimes = Moment.time(
        'YYYY-MM-DD hh:mm:ss',
        params.data.sCreateTime
      )
      params.data.sModifyTimes = Moment.time(
        'YYYY-MM-DD hh:mm:ss',
        params.data.sModifyTime
      )
      sessionStorage.setItem('BASICS', JSON.stringify(params.data))
      this.getMaintenance(params.data.sId)
      console.log(params.data, '双击')
    },
    rowClicked({ data }) {
      if (!data) {
        this.selectId = null
        // this.$refs.detailAggrid.loadTableData()
      } else if (data && this.selectId !== data.sId) {
        this.selectId = data.sId
        // this.$refs.detailAggrid.loadTableData()
      }
    },
    handleFooterCount() {
      // this.$refs.aggrid.getSelectedData(res => {
      // })
    },
    // 主表
    loadData(pagination) {
      return new Promise((resolve, reject) => {
        customerPage(this.$refs.searchForm.getSearchData(), {
          ...pagination
        })
          .then((res) => {
            this.rowData = res.data.content.map((item) => {
              item._selected = false
              item._selectedKeys = []
              return item
            })
            resolve(res.data.totalElements)
            this.rowClicked({ data: res.data.content[0] })
          })
          .catch(() => {
            reject(0)
          })
      })
    },
    loadDetail(pagination) {
      return new Promise((resolve, reject) => {
        customerPage(this.selectId, pagination)
          .then((res) => {
            this.rowDataChild = res.data.page.content.map((item) => {
              item._selected = false
              item._selectedKeys = []
              return item
            })
            const { vCount, vSumQty, vSumTaxAmt } = res.data
            this.setCount(vCount, vSumQty, vSumTaxAmt, 'headerCount')
            resolve(res.data.page)
          })
          .catch(() => {
            reject(0)
          })
      })
    }
  }
}
</script>
