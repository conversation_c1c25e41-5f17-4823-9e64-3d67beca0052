<template>
  <div class="page-container">
    <p class="page-title">云钢自营资源</p>
    <div class="layout-content auto-page-title flexV">
      <!-- 搜索条件 -->
      <steel-search-form
        ref="searchForm"
        :form-items="formItems"
        @search="onSearch"
      />
      <div class="btn-group">
        <div class="text">列表</div>
      </div>
      <!-- 统计报表 -->
      <steelTradeAggrid
        ref="aggrid"
        :column-defs="columnDefs"
        :row-data="rowData"
        :load-data="loadData"
        :auto-load-data="false"
        table-selection=""
        :footer-total="footerCount"
        :private-column-defs="true"
        :suppress-copy-rows-to-clipboard="true"
        row-group-panel-show="always"
      />
    </div>
  </div>
</template>

<script>
var Decimal = window.Decimal
import { getDictet } from '@/api/logistics/saleDelivery/saleorder'
// import { Moment } from 'cnd-horizon-utils'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import basisSelfCollectionSheet from '@/api/basisSelfCollectionSheet'
// import { getCnDitc } from '@/api/logistics/saleDelivery/saleorder'
import exportBtn from '@/components/exportBtnV2'
import moment from 'moment'
export default {
  name: 'SelfSupportResources',
  components: {
    steelTradeAggrid,
    exportBtn
  },
  data() {
    const startDate = moment().subtract(90, 'day').startOf('day').format('YYYY-MM-DDTHH:mm:ss')
    const endDate = moment().endOf('day').format('YYYY-MM-DDTHH:mm:ss')
    return {
      options: {
        'msc.steelmill.types': [],
        'msc.steelmill.status': []
      },
      formItems: [
        {
          label: '供应商',
          value: 'vSupplierName',
          type: 'elInput'
          // value: 'sSupplierId',
          // type: 'cndInputDialog',
          // dialogType: 'customer',
          // defaultUrl: '/esc/customer/page',
          // option: { valueKey: 'sPath' },
          // placeholder: this.$t('grid.others.pleaseSelectSupplier')
        },
        {
          label: this.$t('grid.title.personnel'),
          value: 'sStaffId',
          type: 'cndInputDialogItem',
          option: { valueKey: 'sPath' },
          dialogType: 'customer',
          defaultUrl: '/msc/foundation/staff/page/admin/',
          placeholder: this.$t('grid.others.pleaseSelectPersonnel')
        },
        {
          label: '联系电话',
          value: 'sPhone',
          type: 'elInput'
        },
        {
          label: '商品名称',
          value: 'vGoodsDesc',
          type: 'elInput'
        },
        {
          label: '采购合同号',
          value: 'sPurContractCode',
          type: 'elInput'
        },
        {
          label: '创建日期',
          value: ['vCreateTime', 'vCreateTimeTo'],
          placeholder: [
            this.$t('grid.others.pleaseSelectTheDate'),
            this.$t('grid.others.pleaseSelectTheDate')
          ],
          default: [startDate, endDate],
          unlinkPanels: true,
          type: 'elDatePicker',
          itemType: 'occultation'
        },
        {
          label: '修改日期',
          value: ['vModifyTime', 'vModifyTimeTo'],
          placeholder: [
            this.$t('grid.others.pleaseSelectTheDate'),
            this.$t('grid.others.pleaseSelectTheDate')
          ],
          default: ['', ''],
          unlinkPanels: true,
          type: 'elDatePicker',
          itemType: 'occultation'
        },
        {
          label: '创建人',
          value: 'sCreator',
          type: 'cndInputDialog',
          itemType: 'occultation',
          placeholder: this.$t('grid.others.pleaseSelectTheCreator'),
          dialogType: 'applicant'
        },
        {
          label: '修改人',
          value: 'sModifier',
          type: 'cndInputDialog',
          itemType: 'occultation',
          placeholder: this.$t('grid.others.pleaseSelectTheCreator'),
          dialogType: 'applicant'
        },
        {
          label: 'ID',
          value: 'sId',
          type: 'elInput'
        },
        {
          label: '经营单位',
          value: 'sManagementId',
          type: 'cndInputDialog',
          dialogType: 'escOrg',
          itemType: 'occultation'
        }
      ],
      columnDefs: [
        {
          headerName: '商品分类',
          field: 'sName',
          enableRowGroup: true
        },
        {
          field: 'vGoodsDesc',
          headerName: ' 商品名称'
        },
        {
          headerName: '数量',
          field: 'vCurContractQty',
          valueGetter: (params) => {
            return params.data && this.keepFloatFormat(params.data.vCurContractQty)
          }
        },
        {
          headerName: '件数',
          field: 'vCurQty',
          valueGetter: (params) => {
            return params.data && this.keepFloatFormat(params.data.vCurQty)
          }
        },

        {
          headerName: '价格',
          field: 'sPrice',
          width: '100px'
        },
        {
          headerName: '车船号',
          field: 'sVesselNo'
        },

        {
          headerName: '仓库',
          field: 'vWarehouseName',
          enableRowGroup: true
        },

        {
          headerName: '仓库所属市',
          field: 'vWarehouseCityName',
          enableRowGroup: true,
          width: '100px'
        },

        {
          headerName: '供应商',
          field: 'vSupplierName',
          enableRowGroup: true
        },

        {
          headerName: '产地',
          field: 'vOrigin',
          enableRowGroup: true
        },

        {
          headerName: '采购合同号',
          field: 'sPurContractCode',
          enableRowGroup: true
        },
        {
          headerName: '人员',
          field: 'vStaffName'
        },
        {
          headerName: '联系电话',
          field: 'sPhone'
        },
        {
          headerName: '合同备注',
          field: 'sRemark'
        },
        {
          headerName: '部门',
          field: 'vDepartmentName'
        },
        {
          headerName: 'ID',
          field: 'sId',
          width: '150px'
        },
        {
          headerName: '公司',
          field: 'vCompanyName',
          enableRowGroup: true
        },
        {
          headerName: '核算组',
          field: 'vCheckGroupName'
        },

        {
          headerName: '经营单位',
          field: 'vManagementName'
        },
        {
          headerName: '创建人',
          field: 'vCreatorName'
        },
        {
          headerName: '创建时间',
          field: 'sCreateTime',
          width: '140px'
        },
        {
          headerName: '修改人',
          field: 'vModifierName'
        },
        {
          headerName: '修改时间',
          field: 'sModifyTime',
          cellStyle: { textAlign: 'right' },
          width: '140px'
        }
      ],
      rowData: [],
      curSelRowData: [],
      searchInfo: {},

      getGridApi: () => {
        return this.$refs.aggrid.gridApi
      },
      footerCount: null,
      selectId: this.$route.query.Id
    }
  },
  created() {},

  beforeCreate() {
    getDictet(['msc.steelmill.types', 'msc.steelmill.status'])
      .then((result) => {
        this.options['msc.steelmill.types'] = result.data[0].dicts
        this.options['msc.steelmill.status'] = result.data[1].dicts
      })
      .catch(() => {})
  },

  methods: {
    formatAmount(number, digtCount = 2) {
      if (!number) return number

      return number.toFixed(digtCount).replace(/\d(?=(\d{3})+\.)/g, '$&,')
    },

    // 保留小数位格式化
    keepFloatFormat(value, replenishText = '') {
      if (!value) {
        return value
      }
      const [int, float] = `${+value}`.split('.')
      const linkagePoint = float || replenishText ? '.' : ''

      return `${this.formatAmount(+int, 0)}${linkagePoint}${
        float || replenishText
      }`
    },
    onSearch(load = true) {
      const searchInfo = this.$refs.searchForm.getSearchData()
      this.searchInfo = searchInfo
      if (load) {
        this.$refs.aggrid.loadTableData()
      }
    },

    // 过滤时间
    filteringTime(time) {
      return time.replace('T', ' ')
    },

    loadData(pagination = { page: 0, limit: 20 }) {
      let sExpirationTimeS,
        vExpirationTimeToS,
        sCreateTimeS,
        vCreateTimeToS,
        sModifyTimeS,
        vModifyTimeToS
      if (this.$refs.searchForm.getSearchData().sExpirationTime) {
        sExpirationTimeS = this.filteringTime(
          this.$refs.searchForm.getSearchData().sExpirationTime
        )
      }
      if (this.$refs.searchForm.getSearchData().vExpirationTimeTo) {
        vExpirationTimeToS = this.filteringTime(
          this.$refs.searchForm.getSearchData().vExpirationTimeTo
        )
      }
      if (this.$refs.searchForm.getSearchData().vCreateTime) {
        sCreateTimeS = this.filteringTime(
          this.$refs.searchForm.getSearchData().vCreateTime
        )
      }
      if (this.$refs.searchForm.getSearchData().vCreateTimeTo) {
        vCreateTimeToS = this.filteringTime(
          this.$refs.searchForm.getSearchData().vCreateTimeTo
        )
      }
      if (this.$refs.searchForm.getSearchData().vModifyTime) {
        sModifyTimeS = this.filteringTime(
          this.$refs.searchForm.getSearchData().vModifyTime
        )
      }
      if (this.$refs.searchForm.getSearchData().vModifyTimeTo) {
        vModifyTimeToS = this.filteringTime(
          this.$refs.searchForm.getSearchData().vModifyTimeTo
        )
      }
      const formData = {
        ...this.$refs.searchForm.getSearchData(),
        vExpirationTime: sExpirationTimeS,
        vExpirationTimeTo: vExpirationTimeToS,
        vCreateTime: sCreateTimeS,
        vCreateTimeTo: vCreateTimeToS,
        vModifyTime: sModifyTimeS,
        vModifyTimeTo: vModifyTimeToS
      }
      return new Promise((resolve, reject) => {
        basisSelfCollectionSheet
          .fattrvalueGoodsPage(formData, pagination)
          .then((res) => {
            this.rowData = res.data.content.map((item) => {
              item._selected = false
              item._selectedKeys = []
              return item
            })
            setTimeout(() => {
              this.$refs.aggrid?.gridApi?.setPinnedBottomRowData([
                this.setCount(this.rowData)
              ])
            }, 0)
            resolve(res.data)
            // this.rowClicked({
            //   data: res.data.content[0]
            // })
          })
          .catch(() => {
            reject(0)
          })
      })
    },
    setCount(rowData) {
      let vCurContractQty = 0
      let vCurQty = 0
      this.rowData.map((item) => {
        // 0.3
        //  sVatAmt = +new Decimal(sTaxAmt).sub(+sNetAmt || 0)

        vCurContractQty = new Decimal(vCurContractQty).add(
          +item.vCurContractQty
        )

        vCurQty = new Decimal(vCurQty).add(+item.vCurQty)
        // vCurContractQty += Number(item.vCurContractQty || 0)
        // vCurQty += Number(item.vCurQty || 0)
      })
      return {
        sName: '合计',
        vCurContractQty,
        vCurQty,
        _selected: false,
        _hiddenCheckbox: true
      }
    }
  }
}
</script>
<style lang="scss" scoped>
@import url('~@/styles/basisSelfCollectionSheet/detail.scss');
</style>
