<template>
  <div class="page-container">
    <p class="page-title">ERP资源</p>
    <div class="layout-content auto-page-title flexV">
      <!-- 搜索条件 -->
      <steel-search-form
        ref="searchForm"
        :form-items="formItems"
        @search="onSearch"
      />
      <div class="btn-group">
        <div class="text">列表</div>
      </div>
      <!-- 统计报表 -->
      <steelTradeAggrid
        ref="aggrid"
        :column-defs="columnDefs"
        :row-data="rowData"
        :load-data="loadData"
        :auto-load-data="true"
        table-selection=""
        :footer-total="footerCount"
        :private-column-defs="true"
        :suppress-copy-rows-to-clipboard="true"
        row-group-panel-show="always"
        :page-sizes="pageSizes"
        :default-page-size="defaultPageSize"
      />
    </div>
  </div>
</template>

<script>
var Decimal = window.Decimal
import { getDictet } from '@/api/logistics/saleDelivery/saleorder'

// import { Moment } from 'cnd-horizon-utils'
import { Moment } from 'cnd-utils'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import basisSelfCollectionSheet from '@/api/basisSelfCollectionSheet'
import { getCnDitc } from '@/api/logistics/saleDelivery/saleorder'
import exportBtn from '@/components/exportBtnV2'
import moment from 'moment'
export default {
  name: 'ErpResources',
  components: {
    steelTradeAggrid,
    exportBtn
  },
  data() {
    const startDate = moment().subtract(90, 'day').startOf('day').format('YYYY-MM-DDTHH:mm:ss')
    const endDate = moment().endOf('day').format('YYYY-MM-DDTHH:mm:ss')
    return {
      pageSizes: [10, 20, 30, 100, 200, 500],
      defaultPageSize: 500,
      options: {
        'msc.steelmill.types': [],
        'msc.steelmill.status': []
      },
      formItems: [
        {
          label: '状态',
          value: 'sOrderStatus',
          type: 'elSelect',
          dict: 'msc.steelmill.status',
          itemType: 'occultation',
          placeholder: this.$t('grid.others.pleaseSelectStatus')
        },
        {
          label: '类型',
          value: 'sType',
          type: 'elSelect',
          dict: 'msc.steelmill.types',
          itemType: 'occultation',
          placeholder: this.$t('grid.others.pleaseSelectStatus')
        },
        {
          label: this.$t('grid.title.personnel'),
          value: 'sStaffId',
          type: 'cndInputDialogItem',
          option: { valueKey: 'sPath' },
          dialogType: 'customer',
          defaultUrl: '/msc/foundation/staff/page/admin/',
          placeholder: this.$t('grid.others.pleaseSelectPersonnel')
        },
        {
          label: '联系电话',
          value: 'sPhone',
          type: 'elInput'
        },
        {
          label: '商品名称',
          value: 'goodsName',
          type: 'elInput'
        },

        {
          label: '采购合同号',
          value: 'sPurContractCode',
          type: 'elInput'
        },
        {
          label: '供应商',
          value: 'sSupplierName',
          type: 'elInput'
        },
        {
          label: '截止日期',
          value: ['sExpirationTime', 'vExpirationTimeTo'],
          placeholder: [
            this.$t('grid.others.pleaseSelectTheDate'),
            this.$t('grid.others.pleaseSelectTheDate')
          ],
          default: ['', ''],
          unlinkPanels: true,
          type: 'elDatePicker',
          itemType: 'occultation'
        },
        {
          label: '创建人',
          value: 'sCreator',
          type: 'cndInputDialog',
          itemType: 'occultation',
          placeholder: this.$t('grid.others.pleaseSelectTheCreator'),
          dialogType: 'applicant'
        },
        {
          label: '修改人',
          value: 'sModifier',
          type: 'cndInputDialog',
          itemType: 'occultation',
          placeholder: this.$t('grid.others.pleaseSelectTheCreator'),
          dialogType: 'applicant'
        },
        {
          label: '创建日期',
          value: ['sCreateTime', 'vCreateTimeTo'],
          placeholder: [
            this.$t('grid.others.pleaseSelectTheDate'),
            this.$t('grid.others.pleaseSelectTheDate')
          ],
          default: [startDate, endDate],
          unlinkPanels: true,
          type: 'elDatePicker',
          itemType: 'occultation'
        },
        {
          label: '修改日期',
          value: ['sModifyTime', 'vModifyTimeTo'],
          placeholder: [
            this.$t('grid.others.pleaseSelectTheDate'),
            this.$t('grid.others.pleaseSelectTheDate')
          ],
          default: ['', ''],
          unlinkPanels: true,
          type: 'elDatePicker',
          itemType: 'occultation'
        },
        {
          label: 'ID',
          value: 'goodsId',
          type: 'elInput'
        }
      ],
      columnDefs: [
        // {
        //   headerName: '产品分类',
        //   field: 'sProductCategory',
        //   enableRowGroup: true
        // },
        {
          field: 'sName',
          headerName: ' 商品名称'
        },
        {
          headerName: '数量',
          field: 'sCount'
        },
        {
          headerName: '件数',
          field: 'sPartCount'
        },

        {
          headerName: '价格',
          field: 'sPrice'
        },
        {
          headerName: '车船号',
          field: 'sVehicleVesselNo'
        },

        {
          headerName: '仓库',
          field: 'sWarehouseName',
          enableRowGroup: true
        },

        {
          headerName: '仓库所属市',
          field: 'vWarehouseCityName',
          enableRowGroup: true,
          width: '100px'
        },

        {
          headerName: '供应商',
          field: 'sSupplierName',
          enableRowGroup: true
        },

        {
          headerName: '产地',
          field: 'origin',
          enableRowGroup: true
        },

        {
          headerName: '采购合同号',
          field: 'sPurContractCode'
        },
        {
          headerName: '类型',
          field: 'sType',
          valueGetter: (params) => {
            return (
              params.data &&
              getCnDitc(params, this.options['msc.steelmill.types'], 'sType')
            )
          }
        },
        {
          headerName: '状态',
          field: 'sOrderStatus',
          valueGetter: (params) => {
            return (
              params.data &&
              getCnDitc(
                params,
                this.options['msc.steelmill.status'],
                'sOrderStatus'
              )
            )
          }
        },
        {
          headerName: '截止日期',
          field: 'sExpirationTime',
          cellStyle: { textAlign: 'left' },
          width: '140px',
          enableRowGroup: false,
          valueGetter: (params) =>
            Moment.time('YYYY-MM-DD', params.data.sExpirationTime)
          // valueFormatter(params) {
          //   return Moment.time('YYYY-MM-DD', params.data.sExpirationTime)
          // }
        },
        {
          headerName: '人员',
          field: 'vStaffName'
        },
        {
          headerName: '联系电话',
          field: 'sPhone'
        },
        {
          headerName: '合同备注',
          field: 'sRemark'
        },
        {
          headerName: '部门',
          field: 'vDepartmentName',
          enableRowGroup: true
        },
        {
          headerName: 'ID',
          field: 'goodsId',
          width: '150px'
        },
        {
          headerName: '公司',
          field: 'vCompanyName',
          enableRowGroup: true
        },
        {
          headerName: '核算组',
          field: 'vCheckGroupName'
        },
        {
          headerName: '经营单位',
          field: 'vManagementName'
        },
        {
          headerName: '创建人',
          field: 'vCreatorName'
        },
        {
          headerName: '创建时间',
          field: 'sCreateTime',
          cellStyle: { textAlign: 'right' },
          width: '140px'
          // valueFormatter(params) {
          //   return Moment.time('YYYY-MM-DD hh:mm:ss', params.data.sCreateTime)
          // }
        },
        {
          headerName: '修改人',
          field: 'vModifierName'
        },
        {
          headerName: '修改时间',
          field: 'sModifyTime',
          cellStyle: { textAlign: 'right' },
          width: '140px'
          // valueFormatter(params) {
          //   return Moment.time('YYYY-MM-DD hh:mm:ss', params.data.sModifyTime)
          // }
        }
      ],
      rowData: [],
      curSelRowData: [],
      searchInfo: {},

      getGridApi: () => {
        return this.$refs.aggrid.gridApi
      },
      footerCount: null,
      selectId: this.$route.query.Id
    }
  },
  created() {},

  beforeCreate() {
    getDictet(['msc.steelmill.types', 'msc.steelmill.status'])
      .then((result) => {
        this.options['msc.steelmill.types'] = result.data[0].dicts
        this.options['msc.steelmill.status'] = result.data[1].dicts
      })
      .catch(() => {})
  },

  methods: {
    onSearch(load = true) {
      const searchInfo = this.$refs.searchForm.getSearchData()
      this.searchInfo = searchInfo
      if (load) {
        this.$refs.aggrid.loadTableData()
      }
    },

    // 过滤时间
    filteringTime(time) {
      return time.replace('T', ' ')
    },

    loadData(pagination = { page: 0, limit: 20 }) {
      let sExpirationTimeS,
        vExpirationTimeToS,
        sCreateTimeS,
        vCreateTimeToS,
        sModifyTimeS,
        vModifyTimeToS
      if (this.$refs.searchForm.getSearchData().sExpirationTime) {
        sExpirationTimeS = this.filteringTime(
          this.$refs.searchForm.getSearchData().sExpirationTime
        )
      }
      if (this.$refs.searchForm.getSearchData().vExpirationTimeTo) {
        vExpirationTimeToS = this.filteringTime(
          this.$refs.searchForm.getSearchData().vExpirationTimeTo
        )
      }
      if (this.$refs.searchForm.getSearchData().sCreateTime) {
        sCreateTimeS = this.filteringTime(
          this.$refs.searchForm.getSearchData().sCreateTime
        )
      }
      if (this.$refs.searchForm.getSearchData().vCreateTimeTo) {
        vCreateTimeToS = this.filteringTime(
          this.$refs.searchForm.getSearchData().vCreateTimeTo
        )
      }
      if (this.$refs.searchForm.getSearchData().sModifyTime) {
        sModifyTimeS = this.filteringTime(
          this.$refs.searchForm.getSearchData().sModifyTime
        )
      }
      if (this.$refs.searchForm.getSearchData().vModifyTimeTo) {
        vModifyTimeToS = this.filteringTime(
          this.$refs.searchForm.getSearchData().vModifyTimeTo
        )
      }
      const formData = {
        ...this.$refs.searchForm.getSearchData(),
        sExpirationTime: sExpirationTimeS,
        vExpirationTimeTo: vExpirationTimeToS,
        sCreateTime: sCreateTimeS,
        vCreateTimeTo: vCreateTimeToS,
        sModifyTime: sModifyTimeS,
        vModifyTimeTo: vModifyTimeToS
      }
      return new Promise((resolve, reject) => {
        basisSelfCollectionSheet
          .mscsteelmillsresourceselfErp(formData, pagination)
          .then((res) => {
            this.rowData = res.data.content.map((item) => {
              item._selected = false
              item._selectedKeys = []
              return item
            })
            setTimeout(() => {
              this.$refs.aggrid?.gridApi?.setPinnedBottomRowData([
                this.setCount(this.rowData)
              ])
            }, 0)
            resolve(res.data)
            // this.rowClicked({
            //   data: res.data.content[0]
            // })
          })
          .catch(() => {
            reject(0)
          })
      })
    },
    setCount(rowData) {
      let sCount = 0
      let sPartCount = 0
      const sExpirationTime = ''
      this.rowData.map((item) => {
        sCount = new Decimal(sCount).add(+item.sCount)
        sPartCount = new Decimal(sPartCount).add(+item.sPartCount)
      })
      return {
        sName: '合计',
        sCount,
        sPartCount,
        sExpirationTime,
        _selected: false,
        _hiddenCheckbox: true
      }
    },
    onSaveRemark() {
      basisSelfCollectionSheet.saveStatisticsRemark(this.rowData).then(() => {
        this.$message.success('保存成功')
      })
    }
  }
}
</script>
<style lang="scss" scoped>
@import url('~@/styles/basisSelfCollectionSheet/detail.scss');
</style>
