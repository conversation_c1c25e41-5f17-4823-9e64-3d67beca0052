<template>
  <div class="page-container detail">
    <p class="page-title">需求明细</p>
    <div class="layout-content auto-page-title flexV">
      <steel-search-form
        ref="searchForm"
        :form-items="formItems"
        @search="onSearch"
        @searchValue="onSearchValue"
      />
      <div class="btn-group mt-10">
        <div class="text">列表</div>
        <!-- <div @click="getAddd">测试添加数据</div> -->
        <el-button type="primary" size="mini" @click="getMaintenance('view')">
          查看
        </el-button>
      </div>
      <steelTradeAggrid
        ref="aggrid"
        :column-defs="columnDefs"
        :row-data="rowData"
        :load-data="loadData"
        :auto-load-data="false"
        table-selection="single"
        @rowClicked="rowClicked"
        @selectedChange="handleFooterCount"
        @rowDoubleClicked="onRowDoubleClicked"
        @rowSelected="onDefSelectfun"
      />
    </div>
  </div>
</template>

<script>
import { getDictet } from '@/api/logistics/saleDelivery/saleorder'
import { Moment } from 'cnd-utils'
import { mscsteelmillsdemandPage } from '@/api/steelInfoManage/requirements.js'

import steelTradeAggrid from '@/components/steelTradeAggrid'
import mixins from '../mixins'

export default {
  name: 'PointsDetails',
  components: { steelTradeAggrid },
  mixins: [mixins],
  data() {
    return {
      selectId: this.$route.query.id,
      disRemove: true,
      options: {
        'invoice.subtype': [],
        'dev.supply.type': [],
        'msc.steelmill.status': [],
        'msc.steelmill.technology-center': []
      },
      headerCount: null,
      footerCount: null,
      searchInfo: null,
      formItems: [
        {
          label: '截止日期',
          value: ['sExpirationTime', 'vExpirationTimeTo'],
          placeholder: [
            this.$t('grid.others.pleaseSelectTheDate'),
            this.$t('grid.others.pleaseSelectTheDate')
          ],
          default: ['', ''],
          unlinkPanels: true,
          type: 'elDatePicker',
          itemType: 'occultation'
        },
        {
          label: '用户',
          value: 'sCreator',
          type: 'elInput'
        },
        {
          label: '事项',
          value: 'sCreator',
          type: 'elInput'
        },
        {
          label: '资源ID',
          value: 'sCreator',
          type: 'elInput'
        }
      ],
      columnDefs: [
        {
          field: 'sProductCategory',
          headerName: '用户',
          width: '160px'
        },
        {
          field: 'sProductCategory',
          headerName: '事项',
          width: '160px'
        },
        {
          field: 'sProductCategory',
          headerName: '积分',
          width: '160px',
          cellStyle: { textAlign: 'right' }
        },
        {
          headerName: '时间',
          field: 'sCreateTime',
          cellStyle: { textAlign: 'right' },
          valueFormatter(params) {
            return Moment.time('YYYY-MM-DD hh:mm:ss', params.data.sCreateTime)
          },
          width: '160px'
        },
        {
          field: 'sProductCategory',
          headerName: '资源ID',
          width: '160px'
        }
      ],
      detailsId: null,
      rowData: [],
      rowDataChild: [],
      dialogVisibleadd: false,
      selectedId: null,
      getGridApi: () => {
        return this.$refs.aggrid.gridApi
      }
    }
  },
  watch: {},
  mounted() {
    this.onSearch(false)
  },
  beforeMount() {
    // 获取字典状态
    getDictet(['msc.steelmill.status'])
      .then((result) => {
        this.selectOps['msc.steelmill.status'] = result.data[0].dicts
      })
      .catch(() => {})
  },
  created() {
    if (this.selectId) {
      this.getDetail()
    }
  },
  methods: {
    getDetail() {
      //  获取详情
      //   mscsteelmillsresourceGet(this.selectId).then((res) => {
      //     this.form = res.data || this.form
      //   })
    },
    getMaintenance(detailsId) {
      var detailsIdShow = this.detailsId || detailsId

      if (detailsId === 'view') {
        console.log(this.detailsId, 'this.detailsId')
        if (!this.detailsId) {
          this.$message.error('请勾选列表需要查看的项')
          return
        }
      } else {
        if (!detailsIdShow) {
          this.$message.error('请勾选列表需要查看的项')
          return
        }
      }

      detailsIdShow = '3345739359946817536'
      this.$router.push({
        path: `/PointsDetails/${detailsIdShow}`,
        query: {
          id: detailsIdShow,
          type: 'edit',
          name: '积分明细',
          status: 70,
          activeId: localStorage.getItem('menuId')
        }
      })
    },
    getAddd() {
      console.log(this.options, '333333333333333')
      // const ab = {
      //   sId: '',
      //   sOrderStatus: '10',
      //   sProductCategory: '产品分类名称2',
      //   sExpirationTime: '',
      //   sTechnicalCenterSupportFlag: '0',
      //   sPhone: '18668554240',
      //   sContent: '续期名称2',
      //   sArea: 'Duis ea proident',
      //   sSupplierId: '18',
      //   sSupplierName: '供应商2',
      //   sStaffId: '小小2',
      //   sStaffName: '参程员整',
      //   sDepartmentId: '部门Id',
      //   sDepartmentName: '实备持表所',
      //   sCompanyId: '公司ID',
      //   sCompanyName: '军气小',
      //   sCheckGroupId: '核算组ID',
      //   sCheckGroupName: '确程起采',
      //   sManagementId: '21',
      //   sManagementName: '经营单位2',
      //   sCreator: '创建人1',
      //   sCreateTime: '',
      //   sModifier: 'dolor cupidatat',
      //   sModifyTime: '',
      //   sRootUnitId: '94',
      //   sVersion: 21
      // }
      // getAdds(ab).then((res) => {
      //   console.log(res, '添加成功')
      // })
    },
    onSearch(load = true) {
      this.$refs.aggrid.loadTableData()
    },
    onDefSelectfun(r) {
      console.log(r.data, '单选中')
      if (this.detailsId === r.data.sId) {
        this.detailsId = ''
      } else {
        this.detailsId = r.data.sId
      }
    },
    // 点击 表格
    async onRowDoubleClicked(params) {
      this.detailsId = params.data.sId
      this.getMaintenance(this.detailsId)
      console.log(params.data, '双击')
    },
    rowClicked({ data }) {
      if (!data) {
        this.selectId = null
      } else if (data && this.selectId !== data.sId) {
        this.selectId = data.sId
      }
    },
    handleFooterCount() {
      // this.$refs.aggrid.getSelectedData(res => {
      // })
    },
    // 主表
    loadData(pagination) {
      return new Promise((resolve, reject) => {
        mscsteelmillsdemandPage(this.$refs.searchForm.getSearchData(), {
          ...pagination
        })
          .then((res) => {
            this.rowData = res.data.content.map((item) => {
              item._selected = false
              item._selectedKeys = []
              return item
            })
            resolve(res.data.totalElements)
            this.rowClicked({ data: res.data.content[0] })
          })
          .catch(() => {
            reject(0)
          })
      })
    }
  }
}
</script>
