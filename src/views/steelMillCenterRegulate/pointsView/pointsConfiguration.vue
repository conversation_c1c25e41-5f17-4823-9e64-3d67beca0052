<template>
  <div class="page-container detail">
    <cnd-dialog :visible="true" @close="onClose">
      <template slot="leftBtn">
        <el-button
          type="primary"
          size="mini"
          @click="dialogVisible.approval = true"
        >
          保存
        </el-button>
      </template>
      <template slot="content">
        <el-form
          ref="form"
          class="el-form-w100"
          label-width="100px"
          :inline="true"
          :model="form"
          size="small"
        >
          <cnd-form-card-list :active-panel="activeCollapseName">
            <!-- 基础信息 -->
            <cnd-form-card title="积分配置" name="1">
              <el-row>
                <cnd-form-item label="上架积分获取" prop="sProductCategory">
                  <cnd-checkbox-group v-model="checkList" class="checkboxs">
                    <el-checkbox label="钢厂资源"> 钢厂资源 </el-checkbox>
                    <el-checkbox label="需求"> 需求</el-checkbox>
                  </cnd-checkbox-group>
                </cnd-form-item>

                <cnd-form-item label="单次积分获取量" prop="sProductCategory">
                  <el-input v-model="form.sProductCategory" />
                </cnd-form-item>
              </el-row>
            </cnd-form-card>
            <cnd-form-card title="积分扣减配置" name="3">
              <el-row>
                <cnd-form-item label="下架积分扣减" prop="sProductCategory">
                  <cnd-checkbox-group v-model="checkList" class="checkboxs">
                    <el-checkbox label="钢厂资源"> 钢厂资源 </el-checkbox>
                    <el-checkbox label="需求"> 需求</el-checkbox>
                  </cnd-checkbox-group>
                </cnd-form-item>
                <cnd-form-item label="上架未满设置" prop="sProductCategory">
                  <el-input v-model="form.sProductCategory" />
                </cnd-form-item>
                <cnd-form-item label="单次积分扣减量" prop="sProductCategory">
                  <el-input v-model="form.sProductCategory" />
                </cnd-form-item>
              </el-row>
            </cnd-form-card>
            <!-- 系统信息 -->
            <cnd-form-card :title="$t('grid.tabs.systemInformation')" name="2">
              <el-row>
                <cnd-form-item label="创建人" prop="sCreator">
                  <el-input v-model="form.sCreator" disabled />
                </cnd-form-item>
                <cnd-form-item label="创建时间" prop="sCreateTime">
                  <el-date-picker
                    v-model="form.sCreateTime"
                    format="yyyy-MM-dd HH:mm:ss"
                    type="date"
                    :disabled="true"
                  />
                </cnd-form-item>
                <cnd-form-item label="修改人" prop="sModifier">
                  <el-input v-model="form.sModifier" disabled />
                </cnd-form-item>
                <cnd-form-item label="修改时间" prop="sModifyTime">
                  <el-date-picker
                    v-model="form.sModifyTime"
                    format="yyyy-MM-dd HH:mm:ss"
                    type="date"
                    :disabled="true"
                  />
                </cnd-form-item>
              </el-row>
            </cnd-form-card>
          </cnd-form-card-list>
        </el-form>
      </template>
    </cnd-dialog>
  </div>
</template>

<script>
import businessMixin from '@/utils/businessMixin'
import mixins from '../mixins'
import { mscsteelmillsresourceGet } from '@/api/steelInfoManage/steelPlantResources.js'

export default {
  name: 'CashFlowWorkbench',
  components: {},
  mixins: [businessMixin, mixins],
  data() {
    return {
      checkList: [],
      selectId: '3345739359946817536',

      dialogVisible: {
        annex: false, // 附件管理
        processQuery: false, // 进程查询
        approval: false, // 审批情况
        approvalN8: false
      },
      selectOps: {
        'msc.steelmill.status': []
      },
      form: {
        sCreator: '',
        sCreateTime: '',
        sModifier: '',
        sModifyTime: '',
        sStaffId: '',
        sDepartmentId: '',
        sPhone: '',
        sCompanyId: '',
        sCheckGroupId: '',
        sManagementId: '',
        sContent: '',
        sProductCategory: '',
        sCount: '',
        sSupplierName: '',
        sArea: '',
        sOrderStatus: '',
        sExpirationTime: ''
      },
      activeCollapseName: ['1', '2', '3'],
      visible: false,
      disRemove: true
    }
  },
  watch: {},
  beforeMount() {},
  created() {
    if (this.selectId) {
      this.getDetail()
    }
  },
  methods: {
    getDetail() {
      //  获取详情
      mscsteelmillsresourceGet(this.selectId).then((res) => {
        this.form = res.data || this.form
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.checkboxs {
  display: flex;
  align-items: center;
  padding: 0 14px;
  border: 1px solid #e4e7ed;
  min-width: 250px;
  border-radius: 4px;
}
</style>
