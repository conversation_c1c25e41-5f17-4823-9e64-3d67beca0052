<template>
  <div class="detail">
    <cnd-dialog :visible="true" @close="onClose">
      <template slot="content">
        <el-form
          ref="formvalue"
          class="el-form-w100"
          label-width="100px"
          :inline="true"
          :rules="formDataRules"
          :model="formData"
          size="small"
        >
          <cnd-form-card-list
            ref="cndFormCardList"
            :active-panel="activeCollapseName"
            :error-position="true"
          >
            <cnd-form-card :title="$t('grid.tabs.basicInformation')" name="1">
              <el-row>
                <!-- <cnd-form-item :label="$t('grid.title.status')" prop="sStatus">
                  <el-select
                    v-model="formData.sStatus"
                    :placeholder="$t('components.pleaseSelect')"
                    :disabled="true"
                  >
                    <el-option
                      v-for="item in abnormalState"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </cnd-form-item> -->
                <cnd-form-item label="产品分类" prop="sProductCategory">
                  <el-input
                    v-model="formData.sProductCategory"
                    clearable
                    :disabled="true"
                  />
                </cnd-form-item>

                <cnd-form-item label="供应商" prop="sSupplierName">
                  <el-input
                    v-model="formData.sSupplierName"
                    clearable
                    :disabled="true"
                  />
                </cnd-form-item>

                <cnd-form-item label="源需求区域" prop="sArea">
                  <el-input
                    v-model="formData.sArea"
                    clearable
                    :disabled="true"
                  />
                </cnd-form-item>

                <cnd-form-item
                  label="技术中心支持"
                  prop="sTechnicalCenterSupportFlag"
                >
                  <el-select
                    v-model="formData.sTechnicalCenterSupportFlag"
                    disabled
                  >
                    <el-option
                      v-for="item in selectOps[
                        'msc.steelmill.technology-center'
                      ]"
                      :key="item.sCodeValue"
                      :label="item.sCodeName"
                      :value="item.sCodeValue"
                    />
                  </el-select>
                </cnd-form-item>

                <cnd-form-item label="状态" prop="sOrderStatus">
                  <el-select v-model="formData.sOrderStatus" disabled>
                    <el-option
                      v-for="item in selectOps['msc.steelmill.status']"
                      :key="item.sCodeValue"
                      :label="item.sCodeName"
                      :value="item.sCodeValue"
                    />
                  </el-select>
                </cnd-form-item>

                <cnd-form-item label="截止日期" prop="sExpirationTime">
                  <el-date-picker
                    v-model="formData.sExpirationTime"
                    format="yyyy-MM-dd HH:mm:ss"
                    type="date"
                    :disabled="true"
                  />
                </cnd-form-item>

                <cnd-form-item label="人员" prop="vStaffName">
                  <el-input
                    v-model="formData.vStaffName"
                    clearable
                    :disabled="true"
                  />
                </cnd-form-item>

                <cnd-form-item label="部门" prop="vDepartmentName">
                  <el-input
                    v-model="formData.vDepartmentName"
                    clearable
                    :disabled="true"
                  />
                </cnd-form-item>

                <cnd-form-item label="电话" prop="sPhone">
                  <el-input
                    v-model="formData.sPhone"
                    clearable
                    :disabled="true"
                  />
                </cnd-form-item>

                <cnd-form-item label="公司" prop="vCompanyName">
                  <el-input
                    v-model="formData.vCompanyName"
                    clearable
                    :disabled="true"
                  />
                </cnd-form-item>

                <cnd-form-item label="核算组" prop="vCheckGroupName">
                  <el-input
                    v-model="formData.vCheckGroupName"
                    clearable
                    :disabled="true"
                  />
                </cnd-form-item>

                <cnd-form-item label="经营单位" prop="vManagementName">
                  <el-input
                    v-model="formData.vManagementName"
                    clearable
                    :disabled="true"
                  />
                </cnd-form-item>

                <cnd-form-item label="需求" prop="sContent" :custom-width="24">
                  <el-input
                    v-model="formData.sContent"
                    clearable
                    :disabled="true"
                    type="textarea"
                    :autosize="{ minRows: 8, maxRows: 5 }"
                  />
                </cnd-form-item>
              </el-row>
            </cnd-form-card>
          </cnd-form-card-list>

          <cnd-form-card-list
            ref="cndFormCardList"
            :active-panel="activeCollapseName"
            :error-position="true"
          >
            <cnd-form-card title="评论列表" name="1">
              <div class="ht-35">
                <div class="layout-content auto-page-title flexV">
                  <!-- 合同明细 -->
                  <steelTradeAggrid
                    ref="aggrid"
                    :column-defs="columnDefs"
                    :row-data="rowData"
                    :load-data="loadData"
                    :auto-load-data="true"
                    table-selection=""
                    row-key="sId"
                    @selectedChange="handleFooterCount"
                  />
                </div>
              </div>
            </cnd-form-card>
          </cnd-form-card-list>

          <cnd-form-card-list
            ref="cndFormCardList"
            :active-panel="activeCollapseName"
            :error-position="true"
          >
            <cnd-form-card title="系统信息" name="1">
              <el-row>
                <cnd-form-item label="创建人" prop="vCreatorName">
                  <el-input
                    v-model="formData.vCreatorName"
                    clearable
                    :disabled="true"
                  />
                </cnd-form-item>

                <cnd-form-item label="创建时间" prop="sCreateTime">
                  <el-date-picker
                    v-model="formData.sCreateTime"
                    format="yyyy-MM-dd HH:mm:ss"
                    type="date"
                    :disabled="true"
                  />
                </cnd-form-item>

                <cnd-form-item label="修改人" prop="vModifierName">
                  <el-input
                    v-model="formData.vModifierName"
                    clearable
                    :disabled="true"
                  />
                </cnd-form-item>

                <cnd-form-item label="修改时间" prop="sCreateTime">
                  <el-date-picker
                    v-model="formData.sCreateTime"
                    format="yyyy-MM-dd HH:mm:ss"
                    type="date"
                    :disabled="true"
                  />
                </cnd-form-item>
              </el-row>
            </cnd-form-card>
          </cnd-form-card-list>
        </el-form>
      </template>
    </cnd-dialog>
  </div>
</template>

<script>
import {
  mscsteelmillsdemandGet,
  mscsteelmillsmessagePage
} from '@/api/steelInfoManage/requirements.js'
import steelTradeAggrid from '@/components/steelTradeAggrid'

import { getDictet } from '@/api/logistics/saleDelivery/saleorder'

export default {
  name: 'RequirementViewing',
  components: { steelTradeAggrid },
  props: {
    width: {
      type: String,
      default: '700px'
    }
  },
  data() {
    return {
      selectOps: {
        'msc.steelmill.status': [],
        'msc.steelmill.technology-center': []
      },
      columnDefs: [
        {
          field: 'vFromStaffName',
          headerName: '人员'
        },
        {
          field: 'vDepartmentName',
          headerName: '部门',
          width: '160px'
        },
        {
          field: 'sContent',
          headerName: '内容',
          width: '460px'
        },
        {
          field: 'sCreateTime',
          headerName: '时间',
          width: '160px'
        }
        // {
        //   field: 'sCreator',
        //   headerName: '创建人'
        // },
        // {
        //   field: 'sCreateTime',
        //   headerName: '创建时间'
        // }
      ],
      rowData: [],
      selectId: this.$route.query.id,
      activeCollapseName: ['1'],
      formData: {
        sProductCategory: '',
        sSupplierName: '',
        sArea: '',
        sHandlerKey: '',
        sOrderStatus: '',
        sExpirationTime: '',
        sStaffName: '',
        sDepartmentName: '',
        sPhone: '',
        vCompanyName: '',
        sCheckGroupId: '',
        sManagementName: '',
        sContent: '',
        vStaffName: '',
        vDepartmentName: '',
        vManagementName: ''
      },
      abnormalState: [],
      status: [
        {
          sCodeValue: '10',
          sCodeName: '暂存',
          sSort: 911,
          sFilter: null,
          sIsEnabled: '11',
          sLanguage: 'zh_CN'
        },
        {
          sCodeValue: '20',
          sCodeName: '已上架',
          sSort: 912,
          sFilter: null,
          sIsEnabled: '11',
          sLanguage: 'zh_CN'
        },
        {
          sCodeValue: '30',
          sCodeName: '已下架',
          sSort: 913,
          sFilter: null,
          sIsEnabled: '11',
          sLanguage: 'zh_CN'
        }
      ],
      supportFlagArr: [
        {
          sCodeValue: '1',
          sCodeName: '是',
          sSort: 909,
          sFilter: null,
          sIsEnabled: '11',
          sLanguage: 'zh_CN'
        },
        {
          sCodeValue: '0',
          sCodeName: '否',
          sSort: 908,
          sFilter: null,
          sIsEnabled: '1',
          sLanguage: 'zh_CN'
        }
      ],
      getGridApi: () => {
        return this.$refs.aggrid.gridApi
      }
    }
  },
  beforeMount() {
    // 获取字典状态
    getDictet(['msc.steelmill.status'])
      .then((result) => {
        // this.options['msc.steelmill.status'] = result.data[0].dicts
        this.selectOps['msc.steelmill.status'] = this.status
        this.selectOps['msc.steelmill.technology-center'] = this.supportFlagArr
      })
      .catch(() => {})
  },
  created() {
    if (this.selectId) {
      this.getDetail()
    }
  },
  mounted() {
    this.onSearch(false)
  },
  methods: {
    handleFooterCount() {
      // this.$refs.aggrid.getSelectedData(res => {
      // })
    },
    onSearch(load = true) {
      this.$refs.aggrid.loadTableData()
    },
    // 主表
    loadData(pagination) {
      const sTopicId = {
        sTopicId: this.selectId,
        sSort: 'DESC'
      }
      console.log(pagination, 'pagination')
      return new Promise((resolve, reject) => {
        mscsteelmillsmessagePage(sTopicId, {
          ...pagination
        })
          .then((res) => {
            console.log(res, '/////')
            this.rowData = res.data.content.map((item) => {
              item._selected = false
              item._selectedKeys = []
              return item
            })
            resolve(res.data.totalElements)
            this.rowClicked({ data: res.data.content[0] })
          })
          .catch(() => {
            reject(0)
          })
      })
    },
    getDetail() {
      //  获取详情
      mscsteelmillsdemandGet(this.selectId).then((res) => {
        this.formData = res.data || this.form
      })
    }
  }
}
</script>
<style scoped>
@import url('~@/styles/basisSelfCollectionSheet/detail.scss');
</style>
<style lang="scss" scoped>
.ht-35 {
  height: 30vh !important;
}
.auto-page-title {
  height: calc(100%) !important;
}
.detail {
  ::v-deep .dialog-body-position {
    top: 0 !important;
  }
}
</style>
