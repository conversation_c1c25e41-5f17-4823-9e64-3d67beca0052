<template>
  <div class="page-container">
    <p class="page-title">需求列表</p>
    <div class="layout-content auto-page-title flexV">
      <steel-search-form
        ref="searchForm"
        :form-items="formItems"
        @search="onSearch"
        @searchValue="onSearchValue"
      />
      <div class="btn-group mt-10">
        <div class="text">列表</div>
        <!-- <div @click="getAddd">测试添加数据</div> -->
        <el-button type="primary" size="mini" @click="getMaintenance('view')">查看</el-button>
      </div>
      <steelTradeAggrid
        ref="aggrid"
        :column-defs="columnDefs"
        :row-data="rowData"
        :load-data="loadData"
        :auto-load-data="false"
        table-selection="single"
        :page-sizes="pageSizes"
        :default-page-size="defaultPageSize"
        @rowClicked="rowClicked"
        @selectedChange="handleFooterCount"
        @rowDoubleClicked="onRowDoubleClicked"
        @rowSelected="onDefSelectfun"
      />
    </div>
  </div>
</template>

<script>
import { Moment } from 'cnd-utils'
import { getDictet, getCnDitc } from '@/api/logistics/saleDelivery/saleorder'

import { mscsteelmillsdemandPagePC } from '@/api/steelInfoManage/requirements.js'

import steelTradeAggrid from '@/components/steelTradeAggrid'
import mixins from '../mixins'
import moment from 'moment'
export default {
  name: 'Requirements',
  components: { steelTradeAggrid },
  mixins: [mixins],
  data() {
    const statusDict = [
      {
        sCodeValue: 10,
        sCodeName: '暂存',
        sSort: 101,
        sFilter: null,
        sIsEnabled: '1',
        sLanguage: 'zh_CN'
      },
      {
        sCodeValue: 20,
        sCodeName: '已上架',
        sSort: 202,
        sFilter: null,
        sIsEnabled: '1',
        sLanguage: 'zh_CN'
      },
      {
        sCodeValue: 30,
        sCodeName: '已下架',
        sSort: 90,
        sFilter: null,
        sIsEnabled: '1',
        sLanguage: 'zh_CN'
      }
    ]
    const startDate = moment().subtract(90, 'day').startOf('day').format('YYYY-MM-DDTHH:mm:ss')
    const endDate = moment().endOf('day').format('YYYY-MM-DDTHH:mm:ss')
    return {
      pageSizes: [10, 20, 30, 100, 200, 500],
      defaultPageSize: 500,
      supportFlagArr: [
        {
          sCodeValue: '1',
          sCodeName: '是',
          sSort: 909,
          sFilter: null,
          sIsEnabled: '11',
          sLanguage: 'zh_CN'
        },
        {
          sCodeValue: '0',
          sCodeName: '否',
          sSort: 908,
          sFilter: null,
          sIsEnabled: '1',
          sLanguage: 'zh_CN'
        }
      ],
      disRemove: true,
      options: {
        'invoice.subtype': [],
        'dev.supply.type': [],
        'msc.steelmill.status': [],
        'msc.steelmill.technology-center': []
      },
      headerCount: null,
      footerCount: null,
      searchInfo: null,
      formItems: [
        {
          label: '状态',
          value: 'sOrderStatus',
          type: 'elSelect',
          dict: statusDict,
          itemType: 'occultation',
          placeholder: this.$t('grid.others.pleaseSelectStatus')
        },
        {
          label: this.$t('grid.title.personnel'),
          value: 'sStaffId',
          type: 'cndInputDialogItem',
          option: { valueKey: 'sPath' },
          dialogType: 'customer',
          defaultUrl: '/msc/foundation/staff/page/admin/',
          placeholder: this.$t('grid.others.pleaseSelectPersonnel')
        },
        {
          label: '联系电话',
          value: 'sPhone',
          type: 'elInput'
        },
        {
          label: '供应商',
          value: 'sSupplierName',
          type: 'elInput'
        },
        {
          label: '需求',
          value: 'sContent',
          // sArea
          type: 'elInput'
        },
        {
          label: '截止日期',
          value: ['sExpirationTime', 'vExpirationTimeTo'],
          placeholder: [
            this.$t('grid.others.pleaseSelectTheDate'),
            this.$t('grid.others.pleaseSelectTheDate')
          ],
          default: ['', ''],
          unlinkPanels: true,
          type: 'elDatePicker',
          itemType: 'occultation'
        },
        {
          label: '创建日期',
          value: ['sCreateTime', 'vCreateTimeTo'],
          placeholder: [
            this.$t('grid.others.pleaseSelectTheDate'),
            this.$t('grid.others.pleaseSelectTheDate')
          ],
          default: [startDate, endDate],
          unlinkPanels: true,
          type: 'elDatePicker',
          itemType: 'occultation'
        },

        {
          label: '修改日期',
          value: ['sModifyTime', 'vModifyTimeTo'],
          placeholder: [
            this.$t('grid.others.pleaseSelectTheDate'),
            this.$t('grid.others.pleaseSelectTheDate')
          ],
          default: ['', ''],
          unlinkPanels: true,
          type: 'elDatePicker',
          itemType: 'occultation'
        },

        {
          label: '创建人',
          value: 'sCreator',
          type: 'cndInputDialog',
          itemType: 'occultation',
          placeholder: this.$t('grid.others.pleaseSelectTheCreator'),
          dialogType: 'applicant'
        },
        {
          label: '修改人',
          value: 'sModifier',
          type: 'cndInputDialog',
          itemType: 'occultation',
          placeholder: this.$t('grid.others.pleaseSelectTheCreator'),
          dialogType: 'applicant'
        },
        {
          label: 'ID',
          value: 'sId',
          type: 'elInput'
        }
      ],
      columnDefs: [
        {
          field: 'sProductCategory',
          headerName: '产品分类'
        },

        {
          // sContent  sArea
          field: 'sContent',
          headerName: '需求'
        },
        {
          headerName: '供应商',
          field: 'sSupplierName'
        },
        {
          headerName: '资源需求区域',
          field: 'sArea'
        },
        {
          headerName: '技术中心支持',
          field: 'sTechnicalCenterSupportFlag',
          valueGetter: (params) => {
            return getCnDitc(
              params,
              this.options['msc.steelmill.technology-center'],
              'sTechnicalCenterSupportFlag'
            )
          },
          width: '130px'
        },
        {
          headerName: '状态',
          field: 'sOrderStatus',
          valueGetter: (params) => {
            return getCnDitc(
              params,
              this.options['msc.steelmill.status'],
              'sOrderStatus'
            )
          }
        },
        {
          headerName: '截止日期',
          field: 'sExpirationTime',
          cellStyle: { textAlign: 'left' },
          minWidth: 140,
          valueFormatter(params) {
            return Moment.time(
              'YYYY-MM-DD',
              params.data.sExpirationTime
            )
          },
          width: '160px'
        },

        {
          headerName: '人员',
          field: 'vStaffName'
        },

        {
          headerName: '联系电话',
          field: 'sPhone'
        },

        {
          headerName: '部门',
          field: 'vDepartmentName'
        },

        {
          headerName: 'ID',
          field: 'sId',
          width: '150px'
        },

        {
          headerName: '公司',
          field: 'vCompanyName'
        },

        {
          headerName: '核算组',
          field: 'vCheckGroupName'
        },
        {
          headerName: '经营单位',
          field: 'vManagementName'
        },
        {
          headerName: '创建人',
          field: 'vCreatorName'
        },
        {
          headerName: '创建时间',
          field: 'sCreateTime',
          cellStyle: { textAlign: 'right' },
          minWidth: 150,
          valueFormatter(params) {
            return Moment.time('YYYY-MM-DD HH:mm:ss', params.data.sCreateTime)
          },
          width: '160px'
        },
        {
          headerName: '修改人',
          field: 'vModifierName'
        },
        {
          headerName: '修改时间',
          field: 'sModifyTime',
          cellStyle: { textAlign: 'right' },
          minWidth: 150,
          valueFormatter(params) {
            return Moment.time('YYYY-MM-DD HH:mm:ss', params.data.sModifyTime)
          },
          width: '160px'
        }
      ],
      detailsId: null,
      rowData: [],
      rowDataChild: [],
      selectId: '',
      dialogVisibleadd: false,
      selectedId: null,
      getGridApi: () => {
        return this.$refs.aggrid.gridApi
      }
    }
  },
  computed: {},
  beforeCreate() {
    getDictet(['dev.supply.type', 'msc.steelmill.status'])
      .then((result) => {
        this.options['dev.supply.type'] = result.data[0].dicts
        this.options['msc.steelmill.status'] = result.data[1].dicts
        this.options['msc.steelmill.technology-center'] = this.supportFlagArr
      })
      .catch(() => {})
  },
  mounted() {
    this.onSearch(false)
  },
  methods: {
    getMaintenance(detailsId) {
      var detailsIdShow
      if (detailsId === 'view') {
        detailsIdShow = this.detailsId
      } else {
        detailsIdShow = detailsId
      }

      if (detailsId === 'view') {
        if (!this.detailsId) {
          this.$message.error('请勾选列表需要查看的项')
          return
        }
      } else {
        if (!detailsIdShow) {
          this.$message.error('请勾选列表需要查看的项')
          return
        }
      }

      this.$router.push({
        path: `/RequirementViewing/${detailsIdShow}`,
        query: {
          id: detailsIdShow,
          type: 'edit',
          name: '需求详情',
          status: 70,
          activeId: localStorage.getItem('menuId')
        }
      })
    },
    getAddd() {
      console.log(this.options, '333333333333333')
    },
    onSearch(load = true) {
      this.$refs.aggrid.loadTableData()
    },
    onDefSelectfun(r) {
      console.log(r.data, '单选中')
      if (this.detailsId === r.data.sId) {
        this.detailsId = ''
      } else {
        this.detailsId = r.data.sId
      }
    },
    // 点击 表格
    async onRowDoubleClicked(params) {
      this.getMaintenance(params.data.sId)
      console.log(params.data, '双击')
    },
    rowClicked({ data }) {
      if (!data) {
        this.selectId = null
        // this.$refs.detailAggrid.loadTableData()
      } else if (data && this.selectId !== data.sId) {
        this.selectId = data.sId
        // this.$refs.detailAggrid.loadTableData()
      }
    },
    handleFooterCount() {
      // this.$refs.aggrid.getSelectedData(res => {
      // })
    },
    filteringTime(time) {
      return time.replace('T', ' ')
    },
    // 主表
    loadData(pagination) {
      let sExpirationTimeS,
        vExpirationTimeToS,
        sCreateTimeS,
        vCreateTimeToS,
        sModifyTimeS,
        vModifyTimeToS
      if (this.$refs.searchForm.getSearchData().sExpirationTime) {
        sExpirationTimeS = this.filteringTime(
          this.$refs.searchForm.getSearchData().sExpirationTime
        )
      }
      if (this.$refs.searchForm.getSearchData().vExpirationTimeTo) {
        vExpirationTimeToS = this.filteringTime(
          this.$refs.searchForm.getSearchData().vExpirationTimeTo
        )
      }
      if (this.$refs.searchForm.getSearchData().sCreateTime) {
        sCreateTimeS = this.filteringTime(
          this.$refs.searchForm.getSearchData().sCreateTime
        )
      }
      if (this.$refs.searchForm.getSearchData().vCreateTimeTo) {
        vCreateTimeToS = this.filteringTime(
          this.$refs.searchForm.getSearchData().vCreateTimeTo
        )
      }
      if (this.$refs.searchForm.getSearchData().sModifyTime) {
        sModifyTimeS = this.filteringTime(
          this.$refs.searchForm.getSearchData().sModifyTime
        )
      }
      if (this.$refs.searchForm.getSearchData().vModifyTimeTo) {
        vModifyTimeToS = this.filteringTime(
          this.$refs.searchForm.getSearchData().vModifyTimeTo
        )
      }
      const formData = {
        ...this.$refs.searchForm.getSearchData(),
        sExpirationTime: sExpirationTimeS,
        vExpirationTimeTo: vExpirationTimeToS,
        sCreateTime: sCreateTimeS,
        vCreateTimeTo: vCreateTimeToS,
        sModifyTime: sModifyTimeS,
        vModifyTimeTo: vModifyTimeToS
      }
      return new Promise((resolve, reject) => {
        mscsteelmillsdemandPagePC(formData, {
          ...pagination
        })
          .then((res) => {
            this.rowData = res.data.content.map((item) => {
              item._selected = false
              item._selectedKeys = []
              return item
            })
            resolve(res.data.totalElements)
            this.rowClicked({ data: res.data.content[0] })
          })
          .catch(() => {
            reject(0)
          })
      })
    }
  }
}
</script>
