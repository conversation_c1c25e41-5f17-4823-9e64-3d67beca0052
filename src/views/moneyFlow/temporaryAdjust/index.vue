<template>
  <div class="page-container">
    <p class="page-title">暂应冲抵调整单管理</p>
    <div class="layout-content auto-page-title flexV">
      <steel-search-form
        ref="searchForm"
        :form-items="formItems"
        @search="onSearch"
      />
      <div class="btn-group mt-10">
        <div class="text">
          暂应冲抵调整单列表
        </div>
        <div>
          <el-button
            v-has:esc_temporary_adjust_list_add
            type="primary"
            size="mini"
            @click="add"
          >
            {{ $t('btns.add') }}
          </el-button>
          <el-button
            v-has:esc_temporary_adjust_list_delete
            type="danger"
            size="mini"
            class="mr-10"
            :disabled="delDisable"
            @click="removes"
          >
            {{ $t('btns.delete') }}
          </el-button>
          <export-btn
            v-has:esc_temporary_adjust_list_export
            file-name="暂应冲抵调整单"
            api-url="/esc/pay/offset/master/export"
            :post-params="searchInfo"
            :export-config="exportConfig"
            :get-grid-api="getGridApi"
          />
        </div>
      </div>
      <steelTradeAggrid
        ref="aggrid"
        :column-defs="columnDefs"
        :row-data="rowData"
        :load-data="loadData"
        :auto-load-data="false"
        table-selection="multiple"
        row-key="sId"
        :header-total="headerTotal"
        :footer-total="footerTotal"
        @rowDoubleClicked="onRowDoubleClicked"
        @selectedChange="handleFooterCount"
      />
    </div>
    <workbenchDialog
      :dialog-visible="workbenchVisible"
      @close="closeWorkbenchDialog"
    />
  </div>
</template>

<script>
import { SteelFormat } from 'cnd-horizon-utils'
import { Moment } from 'cnd-utils'
import {
  payOffsetMasterPage,
  payOffsetRemoves
} from '@/api/money/temporaryAdjust.js'
import {
  getDictet, getCnDitc
} from '@/api/logistics/saleDelivery/saleorder'
import { handleDict } from '@/utils/common'
import { statusDict } from '@/utils/dict'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import exportBtn from '@/components/exportBtnV2'
import workbenchDialog from './dialog/workbenchDialog.vue'
export default {
  name: 'TemporaryAdjustList',
  components: { steelTradeAggrid, exportBtn, workbenchDialog },

  data() {
    return {
      workbenchVisible: false,
      formItems: [
        {
          label: '冲抵调整单号',
          value: 'sCode',
          type: 'elInput'
        },
        {
          label: this.$t('grid.others.salesOrderNumber'),
          value: 'sNoticeGoodsCode',
          type: 'elInput',
          placeholder: this.$t('grid.others.pleaseEnterTheSalesOrderNumber')
        },
        {
          label: this.$t('grid.others.itemNumberTag'),
          value: 'sProjectCode',
          type: 'elInput',
          placeholder: this.$t('grid.others.pleaseEnterTheItemNumberTag')
        },
        {
          label: this.$t('grid.others.customer'),
          value: 'sCustomerId',
          type: 'cndInputDialogItem',
          dialogType: 'customer',
          defaultUrl: '/esc/customer/page',
          option: { valueKey: 'sPath' }
        },
        {
          label: this.$t('grid.title.status'),
          value: 'sSheetStatus',
          type: 'elSelect',
          dict: statusDict,
          itemType: 'occultation',
          placeholder: this.$t('grid.others.pleaseSelectStatus')
        },
        {
          label: this.$t('grid.title.salesContractNumber'),
          value: 'sSaleContractCode',
          type: 'elInput',
          placeholder: this.$t('grid.others.pleaseEnterTheSalesContractNumber')
        },
        {
          label: this.$t('grid.title.company'),
          value: 'sCompanyId',
          type: 'cndInputDialog',
          dialogType: 'company',
          placeholder: this.$t('grid.others.pleaseSelectCompany')
        },
        {
          label: this.$t('grid.others.department'),
          value: 'sDepartmentId',
          type: 'cndInputDialog',
          dialogType: 'depart',
          placeholder: this.$t('grid.others.pleaseSelectTheDepartment')
        },
        {
          label: this.$t('grid.title.accountingGroup'),
          value: 'sCheckGroupId',
          type: 'cndInputDialog',
          dialogType: 'cost',
          placeholder: this.$t('grid.others.pleaseSelectAccountingGroup')
        },
        {
          label: this.$t('grid.title.personnel'),
          value: 'sStaffId',
          type: 'cndInputDialogItem',
          option: { valueKey: 'sPath' },
          dialogType: 'staff',
          placeholder: this.$t('grid.others.pleaseSelectPersonnel')
        },
        {
          label: '自动生成',
          value: 'sIsAuto',
          type: 'elSelect',
          dict: 'base.yes-no',
          default: '0',
          hidden: true
        },
        // {
        //   label: '收入类型',
        //   value: 'vIncomeType',
        //   type: 'elSelect',
        //   dict: 'income.type',
        //   itemType: 'occultation'
        // },
        // {
        //   label: '收入日期',
        //   value: ['sIncomeDate', 'vIncomeDateTo'],
        //   placeholder: [this.$t('grid.others.startDate'), this.$t('grid.others.endDate')],
        //   type: 'elDatePicker',
        //   unlinkPanels: true,
        //   itemType: 'occultation'
        // }
        {
          label: this.$t('grid.title.createdBy'),
          value: 'sCreator',
          type: 'cndInputDialog',
          placeholder: this.$t('grid.others.pleaseSelectTheCreator'),
          dialogType: 'applicant'
        },
        {
          label: this.$t('grid.title.createdAt'),
          value: ['sCreateTime', 'vCreateTimeTo'],
          placeholder: [this.$t('grid.others.pleaseSelectTheDate'), this.$t('grid.others.pleaseSelectTheDate')],
          unlinkPanels: true,
          type: 'elDatePicker'
        }
      ],
      searchInfo: null,
      columnDefs: [
        {
          headerName: '冲抵调整单号',
          field: 'sCode'
        },
        {
          headerName: this.$t('grid.title.status'),
          field: 'sSheetStatus',
          valueGetter: (params) => {
            return getCnDitc(params, this.options['dev.common.sheet.status'], 'sSheetStatus')
          }
        },
        {
          headerName: this.$t('grid.others.customer'),
          field: 'vCustomerName'
        },
        {
          headerName: '调整差额',
          field: 'sTaxAmt',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatPrice(params.value)
          }
        },
        {
          headerName: this.$t('grid.others.salesOrderNumber'),
          field: 'sNoticeGoodsCode'
        },
        {
          headerName: this.$t('grid.title.salesContractNumber'),
          field: 'sSaleContractCode'
        },
        {
          headerName: this.$t('grid.others.itemNumberTag'),
          field: 'sProjectCode'
        },
        {
          headerName: this.$t('grid.title.company'),
          field: 'vCompanyName'
        },
        {
          headerName: this.$t('grid.others.department'),
          field: 'vDepartmentName'
        },
        {
          headerName: this.$t('grid.title.accountingGroup'),
          field: 'vCheckGroupName'
        },
        {
          headerName: this.$t('grid.title.personnel'),
          field: 'vStaffName'
        },
        {
          field: 'vCreatorName',
          headerName: this.$t('grid.title.createdBy')
        },
        {
          field: 'sCreateTime',
          headerName: this.$t('grid.title.createdAt'),
          minWidth: 150,
          valueFormatter(params) {
            return Moment.time('YYYY-MM-DD HH:mm:ss', params.data.sCreateTime)
          }
        }
      ],
      rowData: [],
      headerTotal: null,
      footerTotal: null,
      delDisable: true,
      options: {
        'dev.common.sheet.status': ''
      },
      getGridApi: () => {
        return this.$refs.aggrid.gridApi
      },
      exportConfig: [
        { label: '收入调整单号', value: 'sCode' },
        { label: this.$t('grid.title.status'), value: 'sSheetStatus',
          setValue: value => {
            return handleDict(value, this.options['dev.common.sheet.status'])
          }
        },
        { label: this.$t('grid.others.salesOrderNumber'), value: 'sNoticeGoodsCode' },
        { label: this.$t('grid.title.salesContractNumber'), value: 'sSaleContractCode' },
        { label: this.$t('grid.others.customer'), value: 'vCustomerName'
        },
        { label: '调整时间', value: 'sIncomeAdjustDate',
          setValue(value) {
            return Moment.time('YYYY-MM-DD', value)
          }
        },
        { label: '调整原因', value: 'sAdjustReason' },
        { label: this.$t('grid.title.company'), value: 'vCompanyName' },
        { label: this.$t('grid.others.department'), value: 'vDepartmentName' },
        { label: this.$t('grid.title.personnel'), value: 'vStaffName' },
        { label: this.$t('grid.title.accountingGroup'), value: 'vCheckGroupName' },
        { value: 'vCreatorName', label: this.$t('grid.title.createdBy') },
        { value: 'sCreateTime', label: this.$t('grid.title.createdAt'),
          setValue(value) {
            return Moment.time('YYYY-MM-DD HH:mm:ss', value)
          }
        }
      ]
    }
  },
  created() {
    getDictet([
      'dev.common.sheet.status'
    ]).then(result => {
      this.options['dev.common.sheet.status'] = result.data[0].dicts
    }).catch(() => {
    })
  },
  mounted() {
    this.onSearch(false)
  },
  methods: {
    onSearch(load = true) {
      const searchInfo = this.$refs.searchForm.getSearchData()
      this.searchInfo = searchInfo
      if (load) {
        this.$refs.aggrid.loadTableData()
      }
    },
    setTotal(vCount = 0, vSumAmt = 0, flag) {
      this[flag] = [
        { count: vCount, key: 'count' },
        { title: '调整金额', count: SteelFormat.formatPrice(vSumAmt), unit: this.$t('grid.others.yuan') }
      ]
    },
    loadData(pagination) {
      return new Promise((resolve, reject) => {
        payOffsetMasterPage(
          this.$refs.searchForm.getSearchData(),
          pagination
        ).then(res => {
          this.rowData = res.data.offsetPage.content.map(item => {
            item._selected = false
            return item
          })
          const { vCount, vSumAmt } = res.data
          this.setTotal(vCount, vSumAmt, 'headerTotal')
          resolve(res.data.offsetPage)
        }).catch(() => {
          reject([])
        })
      })
    },
    onRowDoubleClicked(params) {
      this.$router.push({
        path: `/temporary_adjust_detail/${params.data.sId}`,
        query: {
          Id: params.data.sId,
          status: params.data.sSheetStatus,
          type: 'edit',
          name: `暂应冲抵调整单【${params.data.sCode}】`,
          activeId: localStorage.getItem('menuId')
        }
      })
    },
    handleFooterCount(list) {
      const details = list.filter(item => item._selected)
      this.delDisable = true
      if (details.length) {
        this.delDisable = !details.every(val => val.sSheetStatus === '10')
      }
      const vCount = details.length
      let vSumAmt = 0
      details.forEach(el => {
        vSumAmt += (Number(el.sTaxAmt) || 0)
      })
      this.setTotal(vCount, vSumAmt, 'footerTotal')
    },
    removes() {
      this.$refs.aggrid.getSelectedData(res => {
        this.$confirm(this.$t('tips.isItOkToDelete'), this.$t('grid.others.prompt'), {
          confirmButtonText: this.$t('btns.confirm'),
          cancelButtonText: this.$t('btns.cancel'),
          type: 'info'
        }).then(() => {
          payOffsetRemoves(res.map(item => item.sId))
            .then(() => {
              this.$message.success(this.$t('grid.tips.deletionSuccess'))
              this.$refs.aggrid.reloadTableData()
            })
        })
      })
    },
    add() {
      this.workbenchVisible = true
    },
    closeWorkbenchDialog() {
      this.workbenchVisible = false
      this.$refs.aggrid.loadTableData()
    }
  }
}
</script>

<style scoped>

</style>
