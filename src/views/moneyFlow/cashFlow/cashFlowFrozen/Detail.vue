<template>
  <div class="page-container">
    <cnd-dialog
      :title="$t('grid.tabs.basicInformation')"
      :visible="true"
      @close="onClose"
    >
      <template slot="leftBtn">
        <el-button
          v-has:esc_pay_act_interaction_frozon_save
          type="primary"
          size="mini"
          :disabled="isBusinessDisabled('save', sSheetStatus)||sIsAuto === '1'|| $route.query.disabled === '1'"
          @click="send('save')"
        >{{ $t('btns.save') }}</el-button>
        <el-button
          v-has:esc_pay_act_interaction_frozon_submit
          type="primary"
          size="mini"
          :disabled="isBusinessDisabled('submit', sSheetStatus)||sIsAuto === '1'|| $route.query.disabled === '1'"
          @click="send('submit')"
        >{{ $t('grid.others.submit') }}</el-button>
        <el-button
          v-has:esc_pay_act_interaction_frozon_delete
          type="danger"
          size="mini"
          :disabled="isBusinessDisabled('remove', sSheetStatus)||sIsAuto === '1'"
          @click="del"
        >{{ $t('btns.delete') }}</el-button>
        <el-button
          v-has:esc_pay_act_interaction_frozon_withdraw
          type="warning"
          size="mini"
          :disabled="isBusinessDisabled('withdraw', sSheetStatus)||sIsAuto === '1'"
          @click="revoke"
        >{{ $t('grid.others.withdrawRequest') }}</el-button>
        <template v-if="showExtend1">
          <el-button
            v-has:esc_pay_act_interaction_frozon_modifysExtend1
            type="primary"
            size="mini"
            :disabled="sSheetStatus !== '70' || $route.query.disabled === '1'"
            @click="controlThaw()"
          >控制解冻</el-button>
        </template>
        <el-button
          v-has:esc_pay_act_interaction_frozon_file
          type="primary"
          size="mini"
          :disabled="isBusinessDisabled('annex', sSheetStatus)"
          @click="dialogVisible.annex = true"
        >{{ $t('btns.attachmentManagement') }}</el-button>
        <el-button
          v-has:esc_pay_act_interaction_frozon_approval
          type="primary"
          size="mini"
          :disabled="isBusinessDisabled('approval', sSheetStatus)"
          @click="dialogVisible.approvaldia = true"
        >{{ $t('grid.others.approvalStatus') }}</el-button>
        <!-- <el-button
          type="primary"
          size="mini"
          :disabled="isBusinessDisabled('approval', sSheetStatus)"
          @click="dialogVisible.approvaldiaN8 = true"
        >ERP{{ $t('grid.others.approvalInquiry') }}</el-button> -->
      </template>
      <template slot="content">
        <el-form
          ref="form"
          class="el-form-w100"
          label-width="100px"
          :inline="true"
          :model="form"
          :rules="rules"
          :disabled="isBusinessDisabled('save', sSheetStatus)"
          size="small"
        >
          <cnd-form-card-list :active-panel="activeCollapseName">
            <!-- 基础信息 -->
            <cnd-form-card
              class="mb-10"
              :title="$t('grid.tabs.basicInformation')"
              name="1"
            >
              <el-row>
                <cnd-form-item
                  label="款项冻结解冻单号"
                  prop="sCode"
                >
                  <el-input
                    v-model="form.sCode"
                    disabled
                    clearable
                  />
                </cnd-form-item>
                <cnd-form-item
                  :label="$t('grid.others.customer')"
                  prop="sCustomerId"
                >
                  <horizon-search-select
                    v-model="form.vCustomerName"
                    type="customer"
                    disabled
                    @change="handleChange($event, 'sCustomerId')"
                  />
                </cnd-form-item>
                <cnd-form-item
                  :label="$t('grid.title.company')"
                  prop="sCompanyId"
                >
                  <horizon-search-select
                    v-model="form.vCompanyName"
                    disabled
                    type="company"
                    @change="handleChange($event, 'sCompanyId')"
                  />
                </cnd-form-item>
                <cnd-form-item
                  :label="$t('grid.title.accountingGroup')"
                >
                  <horizon-search-select
                    v-model="form.vCheckGroupName"
                    type="cost"
                    disabled
                    @change="handleChange($event, 'sCheckGroupId')"
                  />
                </cnd-form-item>
                <cnd-form-item
                  label="操作类型"
                >
                  <el-select
                    v-model="form.sFrozenType"
                    disabled
                    :placeholder="$t('components.pleaseSelect')"
                    clearable
                  >
                    <el-option
                      v-for="item in frozenTypeList"
                      :key="item.sCodeValue"
                      :label="item.sCodeName"
                      :value="item.sCodeValue"
                    />
                  </el-select>
                </cnd-form-item>
                <cnd-form-item
                  v-if="showExtend1"
                  label="是否控制解冻"
                >
                  <el-select
                    v-model="form.sExtend1"
                    :placeholder="$t('components.pleaseSelect')"
                  >
                    <el-option
                      v-for="item in yesorno"
                      :key="item.sCodeValue"
                      :label="item.sCodeName"
                      :value="item.sCodeValue"
                    />
                  </el-select>
                </cnd-form-item>
              </el-row>
              <el-row>
                <cnd-form-item
                  :custom-width="12"
                  :label="$t('grid.others.reasonForAdjustment')"
                  prop="sRemark"
                >
                  <el-input
                    v-model="form.sRemark"
                    type="textarea"
                    :rows="2"
                    clearable
                  />
                </cnd-form-item>
              </el-row>
            </cnd-form-card>
            <!-- 调整明细 -->
            <div class="auto-page-title flexV">
              <div class="btn-group">
                <div class="text">
                  <!-- 调整明细 -->
                  {{ $t('grid.others.adjustmentDetails') }}
                </div>
                <!-- <div>
                  <el-button
                    v-has:esc_inc_income_adjust_detail__add
                    type="primary"
                    size="mini"
                    :disabled="isBusinessDisabled('save', sSheetStatus)|| $route.query.disabled === '1'"
                    @click="dialogVisible.list = true"
                  >{{ $t('btns.add') }}</el-button>
                  <el-button
                    v-has:esc_inc_income_adjust_detail__delete
                    class="mr-10"
                    type="danger"
                    size="mini"
                    :disabled="isBusinessDisabled('save', sSheetStatus)|| $route.query.disabled === '1'"
                    @click="delDetail"
                  >{{ $t('btns.delete') }}</el-button>
                </div> -->
              </div>
            </div>
            <!-- 表格 -->
            <steelTradeAggrid
              ref="aggrid"
              style="margin-bottom: 10px"
              heightinif="400"
              :column-defs="IndexColumnDefs"
              :row-data="rowData"
              :load-data="loadData"
              table-selection="multiple"
            />
            <!-- <cnd-form-card
              class="mb-10"
              :title="$t('grid.others.adjustmentDetails')"
              name="2"
            >
              <el-row>
                <steelTradeAggrid
                  ref="aggrid"
                  heightinif="400"
                  :column-defs="IndexColumnDefs"
                  :row-data="rowData"
                  :load-data="loadData"
                  table-selection="multiple"
                />
              </el-row>
            </cnd-form-card> -->
            <!-- 系统信息 -->
            <cnd-form-card
              class="mb-10"
              :title="$t('grid.tabs.systemInformation')"
              name="3"
            >
              <el-row>
                <cnd-form-item
                  :label="$t('grid.title.createdBy')"
                  prop="vCreatorName"
                >
                  <el-input
                    v-model="form.vCreatorName"
                    disabled
                    clearable
                  />
                </cnd-form-item>
                <cnd-form-item
                  :label="$t('grid.title.createdAt')"
                  prop="sCreateTime"
                >
                  <el-date-picker
                    v-model="form.sCreateTime"
                    type="date"
                    format="yyyy-MM-dd HH:mm:ss"
                    clearable
                    disabled
                  />
                </cnd-form-item>
                <cnd-form-item :label="$t('grid.title.status')">
                  <el-select
                    v-model="form.sSheetStatus"
                    :placeholder="$t('components.pleaseSelect')"
                    clearable
                    disabled
                  >
                    <el-option
                      v-for="(item, index) in status"
                      :key="index"
                      :label="item.sCodeName"
                      :value="item.sCodeValue"
                    />
                  </el-select>
                </cnd-form-item>
                <cnd-form-item
                  :label="$t('grid.others.effectiveTime')"
                  prop="sRatifyDate"
                >
                  <el-date-picker
                    v-model="form.sRatifyDate"
                    type="date"
                    disabled
                    format="yyyy-MM-dd HH:mm:ss"
                    clearable
                  />
                </cnd-form-item>
              </el-row>
            </cnd-form-card>
          </cnd-form-card-list>
        </el-form>
      </template>
    </cnd-dialog>
    <!-- 弹出层 -->
    <horizon-approval-dialog
      :id="sid"
      :solt-btn="false"
      :visible.sync="dialogVisible.approvaldia"
      @handleClose="dialogVisible.approvaldia = false"
    />
    <horizon-approval-dialog
      :id="sid"
      :type="'n8'"
      :solt-btn="false"
      :visible.sync="dialogVisible.approvaldiaN8"
      @handleClose="dialogVisible.approvaldiaN8 = false"
    />
    <steel-annex-dialog
      :visible="dialogVisible.annex"
      append-to-body
      :biz-id="sid"
      :disabled-btn="{ scan: isBusinessDisabled('save', sSheetStatus)|| $route.query.disabled === '1', del: isBusinessDisabled('save', sSheetStatus)|| $route.query.disabled === '1' }"
      @onSelect="dialogVisible.annex = false"
    />
    <!-- <list-dialog
      v-if="dialogVisible.list"
      :visible="dialogVisible.list"
      type="detail"
      :s-customer-ids="form.sCustomerId"
      :s-company-ids="form.sCompanyId"
      :typetable="typetable"
      :s-interaction-subclass="sInteractionSubclass"
      :s-id="id"
      @cancel="cancel"
      @add="listAdd"
      @thaw="listThaw"
      @forzen="listForzen"
    /> -->
    <submitDialog
      :id="invoiceId"
      :visible="dialogVisible.submitDialog"
      :api-url="`/esc/pay/act/interaction/frozon/desc/${sid}`"
      @handleClose="dialogVisible.submitDialog = false"
      @onSure="onSubmit"
    />
  </div>
</template>

<script>
import businessMixin from '@/utils/businessMixin'
import {
  SteelFormat,
  DictUtil
} from 'cnd-horizon-utils'
import { Moment } from 'cnd-utils'
// 接口
import {
  getFrozonDetailList,
  getFrozonDetail,
  frozonEdit,
  frozonSubmit,
  frozonRemove,
  frozonWithdraw,
  deleteadjusts,
  doComfirm,
  modifysExtend1
} from '@/api/money/cashFlow.js'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import submitDialog from '@/components/submitDialog'
// import ListDialog from '../ListDialog/ListDialog.vue'

let sInteractionType = []
let sPaymentType = []
// let sFundType = ''

export default {
  name: 'CashFlowFrozenDetail',
  components: {
    steelTradeAggrid,
    submitDialog
    // ListDialog
  },
  mixins: [businessMixin],
  data() {
    return {
      frozenTypeList: [{
        sCodeValue: '10',
        sCodeName: '冻结'
      }, {
        sCodeValue: '20',
        sCodeName: '解冻'
      }
      ],
      IndexColumnDefs: [
        {
          headerName: this.$t('grid.others.transactionType'),
          field: 'sInteractionSubclass',
          valueGetter: params => {
            let txt = params.data.sInteractionSubclass
            sInteractionType.map(item => {
              if (item.sCodeValue === params.data.sInteractionSubclass) {
                txt = item.sCodeName
              }
            })
            return txt
          }
        },
        {
          headerName: this.$t('grid.others.paragraphType'),
          field: 'sFundType',
          valueGetter: params => {
            let txt = params.data.sFundType
            sPaymentType.map(item => {
              if (item.sCodeValue === txt) {
                txt = item.sCodeName
              }
            })
            return txt
          }
        },
        {
          headerName: '收款余额',
          field: 'sLeftAmt',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatPrice(params.value)
          }
        },
        {
          headerName: '调整余额',
          field: 'vTaxAmt',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatPrice(params.value)
          }
        },
        {
          headerName: this.$t('grid.others.itemNumberTag'),
          field: 'sProjectCode'
        },
        {
          headerName: this.$t('grid.title.salesContractNumber'),
          field: 'sSaleContractCode'
        },
        // {
        //   headerName: this.$t('grid.title.purchaseContractNumber'),
        //   field: 'sPurContractCode'
        // },
        // {
        //   headerName: this.$t('grid.others.salesOrderNumber'),
        //   field: 'sNoticeGoodsCode'
        // },
        {
          headerName: this.$t('grid.others.originalDocumentNumber'),
          field: 'sOriUpCode'
        },
        {
          headerName: this.$t('grid.others.receiptDate'),
          field: 'sPayDate',
          cellStyle: { textAlign: 'right' },
          valueFormatter(params) {
            return Moment.time('YYYY-MM-DD', params.data.sPayDate)
          }
        },
        {
          headerName: this.$t('grid.others.noteNumber'),
          field: 'sBillCode'
        },
        {
          headerName: this.$t('grid.others.maturityDateOfAcceptance'),
          field: 'sMaturityDate',
          cellStyle: { textAlign: 'right' },
          valueFormatter(params) {
            return Moment.time('YYYY-MM-DD', params.data.sMaturityDate)
          }
        },
        {
          headerName: this.$t('grid.title.accountingGroup'),
          field: 'vCheckGroupName'
        },
        {
          headerName: this.$t('grid.title.personnel'),
          field: 'vStaffName'
        }
      ],
      activeCollapseName: ['1', '2', '3'],
      form: {
        sBillCode: '',
        sCreator: '',
        sCreateTime: '',
        sOriratifyDate: '',
        sCompanyId: '',
        vCompanyName: '',
        sCustomerId: '',
        vCustomerName: '',
        sSheetStatus: '',
        sCode: '',
        sRemark: ''
      },
      dialogVisible: {
        approvaldia: false,
        approvaldiaN8: false,
        annex: false,
        list: false,
        submitDialog: false
      },
      detail: null,
      rowData: [],
      rules: {},
      status: [],
      typetable: null,
      sid: '',
      sInteractionSubclass: '',
      sSaleContractCode: '',
      sSaleContractId: '',
      yesorno: []
    }
  },
  computed: {
    sSheetStatus() {
      return this.form.sSheetStatus
    },
    sIsAuto() {
      return this.form.sIsAuto
    },
    isDesc() {
      return this.form.sFrozenType === '20' && this.form.vManagementCode === '00014960'
    },
    showExtend1() {
      return this.form.sFrozenType === '10' && ['00014867', '00011061'].includes(this.form.vManagementCode)
    }
  },
  created() {
    this.param = this.$route.query
    this.getDict()
    if (this.param.id) {
      this.sid = this.param.id
      this.getDetail()
    }
  },
  methods: {
    getDict() {
      DictUtil.getDict(['pay.payment.type', 'pay.subtype', 'dev.common.sheet.status', 'base.yes-no'], (res) => {
        sPaymentType = res[0].dicts
        sInteractionType = res[1].dicts
        this.status = res[2].dicts
        this.yesorno = res[3].dicts
      })
    },
    initForm() {
      this.form = this.detail
    },
    getDetail() {
      getFrozonDetail(this.sid).then(res => {
        this.detail = res.data
        this.param.type = 'edit'
        this.param.id = res.data.sId
        if (this.detail) {
          this.initForm()
        }
      })
    },
    loadData(pagination = { page: 0, limit: 30 }) {
      return new Promise((resolve, reject) => {
        getFrozonDetailList(
          {
            id: this.sid
          },
          pagination
        )
          .then((res) => {
            // const { totalElements } = res.data
            // let sTaxAmt = 0
            this.rowData = res.data.content.map((item) => {
              item._selected = false
              item._selectedKeys = []
              //   sTaxAmt += item.sTaxAmt
              if ((item.sReceivableDc + '') === '1') {
                // this.sInteractionSubclass = item.sInteractionSubclass
                this.sSaleContractCode = item.sSaleContractCode
                this.sSaleContractId = item.sSaleContractId
              } else {
                if (!this.typetable) {
                  if (!item.sSaleContractCode) {
                    this.typetable = '1'
                  } else {
                    this.typetable = '0'
                  }
                }
              }
              return item
            })
            this.sInteractionSubclass = res.data.content[0].sInteractionSubclass
            resolve(res.data.content)
          })
          .catch(() => {
            reject(0)
          })
      })
    },
    onClose() {
      this.$emit('onClose')
    },
    send(t) {
      if (t === 'save') {
        let params = {}
        if (this.sid) {
          params['sId'] = this.sid
        }
        params = {
          ...params,
          ...this.form
        }
        frozonEdit(params).then((res) => {
          if (res.code === '0000') {
            this.$message.success(this.$t('tips.saveSuccess'))
            this.getDetail()
          }
        })
      } else {
        console.log({
          id: this.sid,
          ...this.form
        })
        this.$confirm(this.$t('tips.isSubmissionConfirmed'), this.$t('grid.others.prompt'), {
          confirmButtonText: this.$t('btns.confirm'),
          cancelButtonText: this.$t('btns.cancel'),
          type: 'warning'
        })
          .then(() => {
            frozonEdit(this.form).then((res) => {
              if (res.code === '0000') {
                if (this.isDesc) {
                  this.dialogVisible.submitDialog = true
                } else {
                  this.onSubmit('')
                }
              }
            })
          })
          .catch(() => { })
      }
    },
    onSubmit(value) {
      frozonSubmit({ id: this.sid }, { param: value }).then((res) => {
        if (res.code === '0000') {
          this.$message.success(this.$t('tips.submitSuccess'))
          this.dialogVisible.submitDialog = false
          this.getDetail()
        }
      })
    },
    handleChange(val, key) {
      this.form[key] = val.sId || val.sCode
    },
    del() {
      this.$confirm(this.$t('grid.tips.isTheDeletionConfirmed'), this.$t('grid.others.prompt'), {
        confirmButtonText: this.$t('btns.confirm'),
        cancelButtonText: this.$t('btns.cancel'),
        type: 'warning'
      })
        .then(() => {
          frozonRemove(this.sid).then((res) => {
            if (res.code === '0000') {
              this.$message.success(this.$t('tips.deletedSuccessfully'))
              this.$tabDelete()
            }
          })
        })
        .catch(() => { })
    },
    revoke() {
      this.$confirm('是否撤销？', this.$t('grid.others.prompt'), {
        confirmButtonText: this.$t('btns.confirm'),
        cancelButtonText: this.$t('btns.cancel'),
        type: 'warning'
      })
        .then(() => {
          frozonWithdraw({
            id: this.sid
          }).then((res) => {
            if (res.code === '0000') {
              this.$message.success(this.$t('tips.withdrawalSuccessTag'))
              this.getDetail()
            }
          })
        })
        .catch(() => { })
    },
    delDetail() {
      this.$refs.aggrid.getSelectedData(selRows => {
        if (selRows.length === 0) {
          this.$message.warning(this.$t('grid.others.pleaseSelectData'))
          return
        }
        const arr = []
        selRows.map(item => {
          arr.push(item.sId)
        })
        this.$confirm('是否删除？', this.$t('grid.others.prompt'), {
          confirmButtonText: this.$t('btns.confirm'),
          cancelButtonText: this.$t('btns.cancel'),
          type: 'warning'
        })
          .then(() => {
            deleteadjusts(arr).then(res => {
              if (res.code === '0000') {
                this.$message.success(this.$t('tips.deletedSuccessfully'))
                this.$refs.aggrid.reloadTableData()
              } else {
                this.$message.error(res.message)
              }
            })
          })
          .catch(() => { })
      })
    },
    cancel() {
      this.dialogVisible.list = false
    },
    listAdd(e) {
      if ((!e.param.sSaleContractId || !e.param.sSaleContractCode) && e.param.sInteractionSubclass !== '190') {
        e.param.sSaleContractId = this.sSaleContractId
        e.param.sSaleContractCode = this.sSaleContractCode
      }
      doComfirm(e, this.sid).then(res => {
        if (res.code === '0000') {
          this.$message.success(this.$t('tips.addedSuccessfully'))
          this.$refs.aggrid.reloadTableData()
          this.cancel()
        } else {
          this.$message.error(res.message)
        }
      })
    },
    listThaw(e) {
      console.log('listThaw:', e)
      doComfirm(e, this.sid).then(res => {
        if (res.code === '0000') {
          this.$message.success(this.$t('grid.others.successfulOperation'))
          this.$refs.aggrid.reloadTableData()
          this.cancel()
        } else {
          this.$message.error(res.message)
        }
      })
    },
    listForzen(e) {
      console.log('listForzen:', e)
      doComfirm(e, this.sid).then(res => {
        if (res.code === '0000') {
          this.$message.success(this.$t('grid.others.successfulOperation'))
          this.$refs.aggrid.reloadTableData()
          this.cancel()
        } else {
          this.$message.error(res.message)
        }
      })
    },
    controlThaw() {
      console.log(this.form.sExtend1)
      this.$confirm(`是否控制解冻修改为${this.form.sExtend1 === '1' ? '否' : '是'}`, this.$t('grid.others.prompt'), {
        confirmButtonText: this.$t('btns.confirm'),
        cancelButtonText: this.$t('btns.cancel'),
        type: 'warning'
      }).then(() => {
        modifysExtend1({
          sId: this.form.sId,
          sExtend1: this.form.sExtend1 === '1' ? '0' : '1'
        }).then(() => {
          this.$message.success(this.$t('grid.others.successfulOperation'))
          this.getDetail()
          this.$refs.aggrid.reloadTableData()
        })
      })
        .catch(() => { })
    }
  }
}

</script>

