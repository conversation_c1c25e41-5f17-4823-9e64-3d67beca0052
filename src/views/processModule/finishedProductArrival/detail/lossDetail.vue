<template>
  <div>
    <div class="btn-group">
      <div class="text">
        损耗明细
      </div>
    </div>
    <steelTradeAggrid
      ref="aggrid"
      :column-defs="columnDefs"
      :row-data="rowData"
      :auto-load-data="false"
      :heightinif="200"
      row-key="sId"
      table-selection="multiple"
      :load-data="loadData"
      @selectedChange="selectedChange"
    />
  </div>
</template>

<script>
// var Decimal = window.Decimal
import steelTradeAggrid from '@/components/steelTradeAggrid'
import businessMixin from '@/utils/businessMixin'
import { SteelFormat } from 'cnd-horizon-utils'
import {
  processArivalPickPage
} from '@/api/processModule/finishedProductArrival'
import { computeCellTotal } from '@/utils/common'

export default {
  components: { steelTradeAggrid },
  mixins: [businessMixin],
  props: {
    id: {
      type: String,
      default: null
    },
    info: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      dialogVisible: false,
      columnDefs: [
        {
          headerName: this.$t('grid.others.item'),
          field: 'vArtName'
        },
        {
          headerName: this.$t('grid.title.quantity'),
          field: 'sQtx',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatThousandthSign(params.value, 4)
          }
        },
        {
          headerName: this.$t('grid.others.numberOfPiecesTag'),
          field: 'sQty',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatThousandthSign(params.value)
          }
        },
        {
          headerName: this.$t('grid.others.steelCoilNumber'),
          field: 'sSteelNo'
        },
        {
          headerName: '加工任务单号',
          field: 'sProcessTaskCode'
        }
      ],
      rowData: [],
      headerCount: null,
      footerCount: null,
      noticeExcuteIds: []
    }
  },
  computed: {
    sSheetStatus() {
      return this.info?.sSheetStatus
    }
  },
  methods: {
    setExeData(v) {
      if (v.length) {
        this.noticeExcuteIds = v.map(e => e.sId)
        this.$refs.aggrid.loadTableData()
      } else {
        this.rowData = []
        this.noticeExcuteIds = []
      }
    },
    refresh() {
      this.$refs.aggrid.loadTableData()
    },
    loadData(pagination) {
      return new Promise((resolve, reject) => {
        processArivalPickPage(
          {
            queryType: '2',
            noticeExcuteIds: this.noticeExcuteIds
          },
          pagination
        ).then(res => {
          this.rowData = res.data.page.content.map((item, index) => {
            item._selected = false
            return item
          })
          resolve(res.data.page.totalElements)
          this.handleCellTotal()
        }).catch(() => {
          reject([])
        })
      })
    },
    handleCellTotal(type = 'all') {
      if (!this.rowData.length) {
        this.$refs.aggrid.gridApi.setPinnedBottomRowData([])
        return false
      }
      setTimeout(() => {
        const totalList = computeCellTotal(
          this.rowData,
          {
            sQtx: 0,
            sQty: 0
          },
          {
            vArtName: type === 'all' ? this.$t('grid.others.amountTo') : `${this.$t('components.selected')} ${this.rowData.filter(item => item._selected).length} ${this.$t('pagination.items')}`,
            sExtend3: null,
            _selected: false,
            _hiddenCheckbox: true
          },
          type
        )
        this.$refs.aggrid.gridApi.setPinnedBottomRowData([totalList])
      }, 0)
    },
    selectedChange(list) {
      this.$refs.aggrid.getSelectedData(res => {
        this.handleCellTotal(res.length ? 'selected' : 'all')
      })
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
