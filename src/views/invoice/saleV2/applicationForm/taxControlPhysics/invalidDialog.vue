<template>
  <cnd-dialog
    :title="$t('grid.others.selectInvoiceDetailsToVoid')"
    append-to-body
    height="500"
    width="80%"
    :fullscreen="false"
    :visible.sync="visible"
    @close="handleClose"
  >
    <template slot="content">
      <steel-search-form
        ref="searchForm"
        :form-items="formItems"
        :params-button="false"
        @search="$refs.aggrid.loadTableData()"
      />
      <auto-wrap class="mt-10">
        <steelTradeAggrid
          ref="aggrid"
          :column-defs="columnDefs"
          :row-data="rowData"
          :load-data="loadData"
          :page-sizes="pageSizes"
          :footer-total="footerTotal"
          table-selection="multiple"
          row-key="sId"
          full-row-type="parent"
          @selectedChange="selectedChange"
          @rowValueChanged="rowValueChanged"
          @cellValueChanged="cellValueChanged"
        />
      </auto-wrap>
    </template>
    <template slot="footer" class="dialog-footer">
      <el-button size="mini" @click="handleClose">{{ $t('btns.cancel') }}</el-button>
      <el-button type="primary" size="mini" @click="handleSubmit">{{ $t('btns.confirm') }}</el-button>
    </template>
  </cnd-dialog>
</template>

<script>
import Vue from 'vue'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import {
  saleInvoiceInvalidDetail,
  saleInvoiceInvalidComfirm
} from '@/api/invoice/saleV2'
import {
  getSystemRate
} from '@/api/logistics/saleDelivery/saleorder'
import { SteelFormat } from 'cnd-horizon-utils'
import { Middleware } from 'cndinfo-ui'
var Decimal = window.Decimal
export default {
  components: {
    steelTradeAggrid
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    id: {
      type: String,
      default: null
    },
    physicsDetailId: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      footerTotal: [],
      pageSizes: [10, 20, 30, 50, 100, 200, 1000],
      systemRate: null,
      formItems: [{
        label: this.$t('grid.title.salesContractNumber'),
        value: 'sSaleContractCode',
        type: 'elInput',
        placeholder: this.$t('grid.others.enterSalesSalesContractNumber'),
        customWidth: '12'
      }, {
        label: this.$t('grid.others.salesOrderNumber'),
        value: 'sNoticeGoodsCode',
        type: 'elInput',
        placeholder: this.$t('grid.others.pleaseEnterTheSalesOrderNumber'),
        customWidth: '12'
      }
      // {
      //   label: this.$t('grid.others.item'),
      //   value: 'sGoodsDetailId',
      //   type: 'elInput',
      //   placeholder: '请输入品名',
      //   customWidth: '8'
      // }
      ],
      columnDefs: [{
        field: 'sSaleContractCode',
        headerName: this.$t('grid.title.salesContractNumber'),
        cellStyle: { textAlign: 'left' }
      }, {
        field: 'sNoticeGoodsCode',
        headerName: this.$t('grid.others.salesOrderNumber'),
        cellStyle: { textAlign: 'left' }
      }, {
        field: 'vArtName',
        headerName: this.$t('grid.others.item'),
        cellStyle: { textAlign: 'left' },
        valueFormatter: params => {
          return params.value ? params.value : params.data.vGoodsDesc
        }
      }, {
        field: 'vToPlanQty',
        headerName: this.$t('grid.title.quantity'),
        cellStyle: { textAlign: 'right' },
        editable: true,
        cellEditorFramework: Vue.extend(
          Middleware.createComponent(
            'CndInputNumber',
            {
              mark: 'vToPlanQty',
              type: 'number',
              decimalDigit: 4
            },
            {
              blur: ({ event, rowData, middleware }) => {
                const vToPlanQty = event.target.value
                const { sTaxPrice, vUnFinishQty, sVatRate } = rowData.data
                const vatRate = sVatRate || this.systemRate
                if (vToPlanQty > vUnFinishQty) {
                  event.target.value = vUnFinishQty
                  middleware.rendered.vToPlanQty.setValue(vUnFinishQty)
                  rowData.data.vToPlanQty = vUnFinishQty
                  const sTaxAmt = this.$toFixed(vUnFinishQty * sTaxPrice)
                  const sNetAmt = this.$toFixed(sTaxAmt / (1 + vatRate))
                  const sVatAmt = this.$toFixed(sTaxAmt - sNetAmt)
                  middleware.rendered.sTaxAmt.setValue(sTaxAmt)
                  rowData.data.sNetAmt = sNetAmt
                  rowData.data.sVatAmt = sVatAmt
                } else {
                  const sTaxAmt = this.$toFixed(vToPlanQty * sTaxPrice)
                  const sNetAmt = this.$toFixed(sTaxAmt / (1 + vatRate))
                  const sVatAmt = this.$toFixed(sTaxAmt - sNetAmt)
                  middleware.rendered.sTaxAmt.setValue(sTaxAmt)
                  rowData.data.sNetAmt = sNetAmt
                  rowData.data.sVatAmt = sVatAmt
                }
                this.$refs.aggrid.gridApi.refreshCells(rowData)
              }
            }
          )
        ),
        valueFormatter: params => {
          return SteelFormat.formatThousandthSign(params.value, 4)
        }
      }, {
        field: 'sTaxPrice',
        headerName: this.$t('grid.title.unitPriceWithTax'),
        cellStyle: { textAlign: 'right' },
        valueFormatter: params => {
          return SteelFormat.formatPrice(params.value)
        }
      }, {
        field: 'sTaxAmt',
        headerName: this.$t('grid.title.amountWithTax'),
        editable: true,
        cellStyle: { textAlign: 'right' },
        cellEditorFramework: Vue.extend(
          Middleware.createComponent(
            'CndInputNumber',
            {
              mark: 'sTaxAmt',
              type: 'number',
              decimalDigit: 2
            },
            {
              blur: ({ event, rowData, middleware }) => {
                const sTaxAmt = event.target.value
                const { sVatRate, vToPlanQty, vLeftTaxAmt } = rowData.data
                const vatRate = sVatRate || this.systemRate
                if (sTaxAmt > vLeftTaxAmt) {
                  const sTaxAmt1 = vLeftTaxAmt
                  const sNetAmt = this.$toFixed(sTaxAmt1 / (1 + vatRate))
                  const sVatAmt = this.$toFixed(sTaxAmt1 - sNetAmt)
                  const sTaxPrice = this.$toFixed(sTaxAmt1 / vToPlanQty)
                  middleware.rendered.sTaxAmt.setValue(sTaxAmt1)
                  rowData.data.sTaxPrice = sTaxPrice
                  rowData.data.sTaxAmt = sTaxAmt1
                  rowData.data.sNetAmt = sNetAmt
                  rowData.data.sVatAmt = sVatAmt
                } else {
                  const sTaxPrice = this.$toFixed(sTaxAmt / vToPlanQty)
                  const sNetAmt = this.$toFixed(sTaxAmt / (1 + vatRate))
                  const sVatAmt = this.$toFixed(sTaxAmt - sNetAmt)
                  rowData.data.sTaxPrice = sTaxPrice
                  rowData.data.sNetAmt = sNetAmt
                  rowData.data.sVatAmt = sVatAmt
                }
                this.$refs.aggrid.gridApi.refreshCells(rowData)
              }
            }
          )
        ),
        valueFormatter: params => {
          return SteelFormat.formatPrice(params.value)
        }
      }, {
        field: 'sNetAmt',
        headerName: this.$t('grid.title.amountWithoutTax'),
        cellStyle: { textAlign: 'right' },
        valueFormatter: params => {
          return SteelFormat.formatPrice(params.value)
        }
      }, {
        field: 'sVatRate',
        headerName: this.$t('grid.title.vatRate'),
        cellStyle: { textAlign: 'right' },
        valueFormatter: params => {
          return SteelFormat.toPercent(params.value)
        }
      }, {
        field: 'sVatAmt',
        headerName: this.$t('grid.title.vatAmount'),
        cellStyle: { textAlign: 'right' },
        valueFormatter: params => {
          return SteelFormat.formatPrice(params.value)
        }
      }],
      rowData: [],
      searchIsOver: null
    }
  },
  beforeCreate() {
    getSystemRate().then(res => {
      this.systemRate = res.data || 0.13
    })
  },
  methods: {
    loadData(pagination) {
      console.log(this.$refs.searchForm.getSearchData())
      const params = {
        ...pagination,
        sInvoiceSaleId: this.id,
        sPhysicsDetailId: this.physicsDetailId
      }
      return new Promise((resolve, reject) => {
        saleInvoiceInvalidDetail(
          this.$refs.searchForm.getSearchData(),
          params
        ).then(res => {
          this.rowData = res.data.content.map(item => {
            item._selected = false
            return item
          })
          resolve(res.data)
        }).catch(() => {
          reject(0)
        })
      })
    },
    selectedChange(newval) {
      this.footerTotal = newval.reduce((prev, next, index) => {
        if (next._selected) {
          prev[1].count = new Decimal(prev[1].count).add(next.vToPlanQty)
          prev[2].count = new Decimal(prev[2].count).add(next.sTaxAmt)
          prev[3].count = new Decimal(prev[3].count).add(next.sNetAmt)
          prev[4].count = new Decimal(prev[4].count).add(next.sVatAmt)
        }
        if (index + 1 === newval.length) {
          prev[1].count = SteelFormat.formatThousandthSign(prev[1].count, 4)
          prev[2].count = SteelFormat.formatPrice(prev[2].count)
          prev[3].count = SteelFormat.formatPrice(prev[3].count)
          prev[4].count = SteelFormat.formatPrice(prev[4].count)
        }
        return prev
      }, [{
        key: 'count',
        count: newval.filter(item => item._selected).length,
        unit: this.$t('pagination.items')
      }, {
        title: this.$t('grid.title.quantity'),
        count: 0,
        unit: this.$t('grid.others.ton')
      }, {
        title: this.$t('grid.title.amountWithTax'),
        count: 0,
        unit: this.$t('grid.others.yuan')
      }, {
        title: this.$t('grid.title.amountWithoutTax'),
        count: 0,
        unit: this.$t('grid.others.yuan')
      }, {
        title: this.$t('grid.title.vatAmount'),
        count: 0,
        unit: this.$t('grid.others.yuan')
      }])
    },
    handleSubmit() {
      this.$refs.aggrid.getSelectedData(res => {
        if (!res.length) {
          this.$message.error(this.$t('grid.others.pleaseCheckTheData'))
          return
        }
        const params = {
          sInvoiceSaleId: this.id,
          sPhysicsDetailId: this.physicsDetailId
        }
        saleInvoiceInvalidComfirm(
          res,
          params
        ).then(res => {
          this.$emit('success')
          this.handleClose()
        })
      })
    },
    handleClose() {
      this.$emit('close')
    },
    // cellValueChanged(params) {
    //   const { data, colDef, newValue, oldValue } = params
    //   if (oldValue !== newValue) {
    //     data[colDef.field] = newValue
    //   }
    //   console.log(params)
    // },
    rowValueChanged(params) {
      const { data, colDef, newValue, oldValue } = params
      if (data.vToPlanQty > data.vUnFinishQty) {
        data.vToPlanQty = data.vUnFinishQty
      }
      if (data.sTaxAmt > data.vLeftTaxAmt) {
        data.sTaxAmt = data.vLeftTaxAmt
      }
      console.log('data:', data)
      this.$refs.aggrid.gridApi.refreshCells(data)
      if (oldValue !== newValue) {
        data[colDef.field] = newValue
      }
    }
  }
}
</script>
