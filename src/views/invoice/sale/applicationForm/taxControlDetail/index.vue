<template>
  <!-- 销售发票 税控开票明细 -->
  <div>
    <cnd-btn-position top="6" right="10">
      <!-- <el-button
        v-has:esc_inv_sale_detail_autoUpdatesVatToStdAmt
        type="primary"
        size="mini"
        :disabled="isBusinessDisabled('submit', form.sSheetStatus)|| $route.query.disabled === '1'"
        @click="autoAdjust"
      >
        {{ $t('grid.others.adjustTailDifference') }}
      </el-button> -->
      <el-dropdown v-has:esc_sale_invoice_tax_import split-button size="mini" type="primary" style="margin-right: 10px;">
        {{ $t('excel.import') }}
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item>
            <importBtn
              type="one"
              btn-text="直接导入"
              :action="`/api/esc/sale/invoice/tax/import/${id}`"
              :action-success-url="`/esc/sale/invoice/tax/importSuccessData/${id}`"
              @success="successImport"
            />
          </el-dropdown-item>
          <el-dropdown-item>
            <importBtn
              type="two"
              is-sync
              :action="`/api/esc/sale/invoice/tax/syn/import/${id}`"
              btn-text="后台导入"
            />
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      <el-button
        v-has:esc_sale_invoice_tax_detail_removes
        type="danger"
        size="mini"
        :disabled="isBusinessDisabled('submit', form.sSheetStatus)|| $route.query.disabled === '1'"
        @click="removes"
      >{{ $t('btns.delete') }}</el-button>
      <el-button
        v-has:esc_sale_invoice_tax_autoUpdatesNetPrice
        type="primary"
        size="mini"
        :disabled="isBusinessDisabled('save', form.sSheetStatus)|| $route.query.disabled === '1'"
        @click="openModifyDialog('netAmt')"
      >
        调整不含税单价
      </el-button>
      <el-button
        v-has:esc_inv_sale_detail_tax_modifiesTaxType
        type="primary"
        size="mini"
        :disabled="isBusinessDisabled('submit', form.sSheetStatus)|| $route.query.disabled === '1'"
        @click="openModifyDialog('taxType')"
      >
        修改税收分类
      </el-button>
      <el-button
        v-has:esc_inv_sale_detail_tax-modifies
        type="primary"
        size="mini"
        :disabled="isBusinessDisabled('submit', form.sSheetStatus)|| $route.query.disabled === '1'"
        @click="openModifyDialog('batchModify')"
      >
        {{ $t('grid.others.batchModify') }}
      </el-button>
      <el-button
        key="comm_purcontact_list_add"
        v-has:esc_inv_sale_detail_submit
        type="primary"
        size="mini"
        :disabled="isBusinessDisabled('submit', form.sSheetStatus)|| $route.query.disabled === '1'"
        @click="mergeData"
      >
        {{ $t('grid.others.mergeData') }}
      </el-button>
      <el-button
        key="comm_purcontact_list_add"
        v-has:esc_inv_sale_detail_delete
        type="primary"
        size="mini"
        :disabled="isBusinessDisabled('submit', form.sSheetStatus)|| $route.query.disabled === '1'"
        @click="splitData"
      >
        {{ $t('grid.others.splitData') }}
      </el-button>
      <!-- <el-button
        key="comm_purcontact_list_add"
        v-has:esc_inv_sale_detail_cancel
        type="primary"
        size="mini"
        :disabled="isBusinessDisabled('submit', form.sSheetStatus)|| $route.query.disabled === '1'"
        @click="splitDataLimit"
      >
        {{ $t('grid.others.invoiceLimitSplitting') }}
      </el-button> -->
      <el-dropdown v-has:esc_inv_sale_detail_cancel style="margin: 0 10px" split-button type="primary" size="mini" :disabled="isBusinessDisabled('submit', form.sSheetStatus)|| $route.query.disabled === '1'">
        <div @click="splitDataLimit()">{{ $t('grid.others.invoiceLimitSplitting') }}</div>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item>
            <div @click="splitDataLimit()">开具清单</div>
          </el-dropdown-item>
          <el-dropdown-item>
            <div @click="splitDataLimit(false)">不开具清单</div>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      <el-button
        v-has:esc_inv_sale_detail_merge-tax
        type="primary"
        size="mini"
        :disabled="isBusinessDisabled('submit', form.sSheetStatus)|| $route.query.disabled === '1'"
        @click="mergeTaxAuto"
      >
        {{ $t('grid.others.specificationMerge') }}
      </el-button>
      <el-button
        v-has:esc_inv_sale_detail_modifiesSpec
        type="primary"
        size="mini"
        :disabled="isBusinessDisabled('submit', form.sSheetStatus)|| $route.query.disabled === '1'"
        @click="rejectMaterial"
      >
        {{ $t('grid.others.cullMaterial') }}
      </el-button>
    </cnd-btn-position>
    <steelTradeAggrid
      ref="aggrid"
      :column-defs="columnDefs"
      :row-data="rowData"
      :load-data="loadData"
      :heightinif="tableHeight"
      table-selection="multiple"
      row-key="id"
      open-after-filter
      :header-total="headerTotal"
      :footer-total="footerTotal"
      :page-sizes="[10, 20, 30, 50, 100, 200, 1000]"
      @selectedChange="handleFooterTotal"
      @cellValueChanged="cellValueChanged"
      @cellEditingStopped="cellEditingStopped"
      @onPasteEnd="onPasteEnd"
    />

    <splitDataDialog
      v-if="splitDataVisible"
      :visible="splitDataVisible"
      :max-amt="sTaxAmt"
      :max-qty="sContractQty"
      :form-data="selectSplit"
      :s-id="sId"
      @success="$refs.aggrid.reloadTableData()"
      @onClose="closeSplitDialog"
    />

    <cnd-dialog
      v-if="modifyDialog"
      :visible="modifyDialog"
      :fullscreen="false"
      append-to-body
      :title="modifyConfig[modifyDialogType].title"
      width="350px"
      :height="modifyConfig[modifyDialogType].height"
      @close="modifyDialog = false"
    >
      <template slot="content">
        <div>
          <el-form
            :model="modifyForm"
            label-width="100px"
            size="small"
            @keyup.enter.native="modifySubmit"
          >
            <template v-if="modifyDialogType === 'taxType'">
              <cnd-form-item
                label="税收分类"
                :custom-width="20"
              >
                <el-select v-model="modifyForm.sTaxGoodsId" clearable :placeholder="$t('components.pleaseSelect')">
                  <el-option
                    v-for="item in taxTypeList"
                    :key="item.sId"
                    :label="item.sTaxTypeName"
                    :value="item.sId"
                  />
                </el-select>
              </cnd-form-item>
            </template>
            <template v-if="modifyDialogType === 'batchModify'">
              <cnd-form-item
                :label="$t('grid.others.commodityName')"
                :custom-width="20"
              >
                <el-input v-model="modifyForm.goodsDesc" :maxlength="44" :placeholder="$t('components.pleaseEnter')" /></cnd-form-item>
              <!-- <cnd-form-item
              label="规格"
              :custom-width="20"
            >
              <el-input v-model="modifyForm.spec" :placeholder="$t('components.pleaseEnter')" /></cnd-form-item> -->
              <cnd-form-item
                :label="$t('grid.title.unitPriceWithTax')"
                :custom-width="20"
              >
                <cnd-input-number v-model="modifyForm.modifyPrice" type="amount" negative :decimal-digit="2" clearable :placeholder="$t('components.pleaseEnter')" />
              </cnd-form-item>
              <cnd-form-item
                :label="$t('grid.title.remarks')"
                :custom-width="20"
              >
                <el-input v-model="modifyForm.sRemark" :maxlength="69" :placeholder="$t('components.pleaseEnter')" /></cnd-form-item>
            </template>
          </el-form>
        </div>
      </template>
      <template slot="footer">
        <el-button
          size="mini"
          @click="modifyDialog = false"
        >{{ $t('btns.cancel') }}</el-button>
        <el-button
          type="primary"
          size="mini"
          @click="modifySubmit"
        >{{ $t('btns.confirm') }}</el-button>
      </template>
    </cnd-dialog>
  </div>
</template>

<script>
import Vue from 'vue'
import {
  SteelFormat,
  MessageUtil
} from 'cnd-horizon-utils'
import { Middleware } from 'cndinfo-ui'
import splitDataDialog from './splitDataDialog'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import importBtn from '@/components/importBtn'
import {
  getSaleInvoiceTaxPage,
  invoiceSaleTaxDetailMargeTax,
  invoiceSaleTaxDetailSplitLimit,
  invoiceMergeTaxAuto,
  invoiceSaleTaxDetailModifies,
  invoiceSaleTaxModifiesSpec,
  invoiceAutoUpdatesVatToStdAmt,
  invoiceTaxDetailRemoves,
  listTaxGoodsCode,
  modifiesTaxType,
  autoUpdatesNetPrice
} from '@/api/invoice/sale'
import {
  getParameterLimit,
  getMeasurementUnit
} from '@/api/logistics/saleDelivery/common'
import businessMixin from '@/utils/businessMixin'
import { DictUtil } from 'cnd-horizon-utils'
import { getCnDitc } from '@/utils/common'
// import customMiddleware from '@/components/agComponent/middleware'
var Decimal = window.Decimal
// import { debounce } from '@/utils/common'
function handleSpaces(str) {
  return str && str.trim().replace(/\n|\r|\t/g, '')
}
export default {
  components: { splitDataDialog, steelTradeAggrid, importBtn },
  mixins: [businessMixin],
  props: {
    id: {
      type: String,
      default: ''
    },
    form: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      columnDefs: [{
        field: 'sSeq',
        headerName: this.$t('grid.title.serialNumber'),
        editable: () => {
          return (this.form.sSheetStatus === '10' || this.form.sSheetStatus === '11' || this.form.sSheetStatus === '15') && this.$route.query.disabled !== '1'
        },
        cellStyle: { textAlign: 'left' }
      }, {
        field: 'sInvNum',
        headerName: this.$t('grid.others.numberOfInvoices'),
        editable: () => {
          return (this.form.sSheetStatus === '10' || this.form.sSheetStatus === '11' || this.form.sSheetStatus === '15') && this.$route.query.disabled !== '1'
        },
        cellStyle: { textAlign: 'right' }
      }, {
        field: 'sTaxShort',
        headerName: this.$t('grid.others.taxAbbreviation'),
        cellStyle: { textAlign: 'left' }
      }, {
        field: 'sGoodsDesc',
        headerName: this.$t('grid.others.descriptionOfCommodityName'),
        cellStyle: { textAlign: 'left' },
        editable: () => {
          return (this.form.sSheetStatus === '10' || this.form.sSheetStatus === '11' || this.form.sSheetStatus === '15') && this.$route.query.disabled !== '1'
        }
      },
      {
        field: 'sTaxTypeName',
        headerName: this.$t('grid.others.taxClassification'),
        cellStyle: { textAlign: 'left' }
      },
      {
        field: 'sCoalType',
        headerName: '煤炭种类',
        cellStyle: { textAlign: 'left' },
        editable: () => {
          return (this.form.sSheetStatus === '10' || this.form.sSheetStatus === '11' || this.form.sSheetStatus === '15') && this.$route.query.disabled !== '1'
        },
        valueGetter: params => {
          return getCnDitc(params, this.coalTypeList, 'sCoalType')
        },
        cellEditorFramework: Vue.extend(Middleware.createComponent(
          'AgGridSelect',
          {
            mark: 'sCoalType',
            config: {
              label: 'sCodeName',
              value: 'sCodeValue'
            },
            filterable: false,
            remote: false,
            searchLimit: 0,
            placeholder: this.$t('grid.others.pleaseEnterTheSearch'),
            queryMethod: () => {
              return new Promise((resolve, reject) => {
                resolve(this.coalTypeList)
              })
            }
          },
          {
            getOption: (option, params, middleware) => {
            }
          }
        ))
      },
      {
        field: 'sAgreementTermType',
        headerName: '协议期限',
        cellStyle: { textAlign: 'left' },
        editable: () => {
          return (this.form.sSheetStatus === '10' || this.form.sSheetStatus === '11' || this.form.sSheetStatus === '15') && this.$route.query.disabled !== '1'
        },
        valueGetter: params => {
          return getCnDitc(params, this.agreementTermTypeList, 'sAgreementTermType')
        },
        cellEditorFramework: Vue.extend(Middleware.createComponent(
          'AgGridSelect',
          {
            mark: 'sAgreementTermType',
            config: {
              label: 'sCodeName',
              value: 'sCodeValue'
            },
            filterable: false,
            remote: false,
            searchLimit: 0,
            placeholder: this.$t('grid.others.pleaseEnterTheSearch'),
            queryMethod: () => {
              return new Promise((resolve, reject) => {
                resolve(this.agreementTermTypeList)
              })
            }
          }
        ))
      },
      {
        field: 'sDafValue',
        headerName: '干燥无灰基挥发分（%）',
        cellStyle: { textAlign: 'right' },
        editable: () => {
          return (this.form.sSheetStatus === '10' || this.form.sSheetStatus === '11' || this.form.sSheetStatus === '15') && this.$route.query.disabled !== '1'
        },
        cellEditorFramework: Vue.extend(Middleware.createComponent(
          'CndInputNumber',
          {
            mark: 'sDafValue',
            type: 'percent',
            decimalDigit: 2,
            autoFocus: true,
            focusSelect: true
          }
        )),
        valueFormatter: params => {
          return SteelFormat.toPercent(params.value)
        }
      },
      {
        field: 'sTotalSulfur',
        headerName: '干基全硫（%）',
        cellStyle: { textAlign: 'right' },
        editable: () => {
          return (this.form.sSheetStatus === '10' || this.form.sSheetStatus === '11' || this.form.sSheetStatus === '15') && this.$route.query.disabled !== '1'
        },
        cellEditorFramework: Vue.extend(Middleware.createComponent(
          'CndInputNumber',
          {
            mark: 'sTotalSulfur',
            type: 'percent',
            decimalDigit: 2,
            autoFocus: true,
            focusSelect: true
          }
        )),
        valueFormatter: params => {
          return SteelFormat.toPercent(params.value)
        }
      },
      {
        field: 'sCalorificValue',
        headerName: '每千克煤炭发热量（千卡）',
        cellStyle: { textAlign: 'right' },
        editable: () => {
          return (this.form.sSheetStatus === '10' || this.form.sSheetStatus === '11' || this.form.sSheetStatus === '15') && this.$route.query.disabled !== '1'
        },
        cellEditorFramework: Vue.extend(Middleware.createComponent(
          'CndInputNumber',
          {
            mark: 'sCalorificValue',
            type: 'number',
            decimalDigit: 2,
            autoFocus: true,
            focusSelect: true
          }
        )),
        valueFormatter: params => {
          return SteelFormat.keepDecimalPlaces(params.value, 2)
        }
      },
      {
        field: 'sSpec',
        headerName: this.$t('grid.others.specification'),
        cellStyle: { textAlign: 'left' },
        editable: () => {
          return (this.form.sSheetStatus === '10' || this.form.sSheetStatus === '11' || this.form.sSheetStatus === '15') && this.$route.query.disabled !== '1'
        },
        valueFormatter: params => {
          const sHtml = '<span style=white-space:pre>' + params.value ? params.value : '' + '</span>'
          return sHtml
        }
      }, {
        field: 'sContractQty',
        headerName: this.$t('grid.title.quantity'),
        cellStyle: { textAlign: 'right' },
        valueFormatter: params => {
          return SteelFormat.formatThousandthSign(params.value, 4)
        },
        editable: () => {
          return (this.form.sSheetStatus === '10' || this.form.sSheetStatus === '11' || this.form.sSheetStatus === '15') && this.$route.query.disabled !== '1'
        },
        cellEditorFramework: Vue.extend(Middleware.createComponent(
          'CndInputNumber',
          {
            mark: 'sContractQty',
            type: 'number',
            decimalDigit: 4,
            autoFocus: true,
            focusSelect: true,
            // negative: this.form.sInnerType === '30'
            negative: true
          }
        ))
      }, {
        field: 'vContractQtyUnitName',
        headerName: this.$t('grid.title.unit'),
        cellStyle: { textAlign: 'left' },
        editable: () => {
          return (this.form.sSheetStatus === '10' || this.form.sSheetStatus === '11' || this.form.sSheetStatus === '15') && this.$route.query.disabled !== '1'
        },
        cellEditorFramework: Vue.extend(Middleware.createComponent(
          'AgSearchSelect',
          {
            mark: 'vContractQtyUnitName',
            type: 'measurementunit'
          },
          {
            getOption: (option, rowNode) => {
              // console.log('option--', option)
              this.rowData[rowNode.rowIndex].sContractQtyUnit = option.sId
            }
          }
        ))
      }, {
        field: 'sTaxPrice',
        headerName: this.$t('grid.title.unitPriceWithTax'),
        cellStyle: { textAlign: 'right' },
        editable: () => {
          return (this.form.sSheetStatus === '10' || this.form.sSheetStatus === '11' || this.form.sSheetStatus === '15') && this.$route.query.disabled !== '1'
        },
        cellEditorFramework: Vue.extend(Middleware.createComponent(
          'CndInputNumber',
          {
            mark: 'sTaxPrice',
            type: 'number',
            decimalDigit: 6,
            autoFocus: true,
            focusSelect: true,
            negative: true
          }
        )),
        valueFormatter: params => {
          return SteelFormat.formatThousandthSign(params.value, 6)
        }
      }, {
        field: 'sTaxAmt',
        headerName: this.$t('grid.title.amountWithTax'),
        editable: () => {
          return (this.form.sSheetStatus === '10' || this.form.sSheetStatus === '11' || this.form.sSheetStatus === '15') && this.$route.query.disabled !== '1'
        },
        cellStyle: { textAlign: 'right' },
        cellEditorFramework: Vue.extend(Middleware.createComponent(
          'CndInputNumber',
          {
            mark: 'sTaxAmt',
            type: 'number',
            decimalDigit: 2,
            autoFocus: true,
            focusSelect: true,
            // negative: this.form.sInnerType === '30'
            negative: true
          }
        )),
        valueFormatter: params => {
          return SteelFormat.formatPrice(params.value)
        }
      }, {
        field: 'sNetPrice',
        headerName: this.$t('grid.title.excludingTaxUnitPrice'),
        cellStyle: { textAlign: 'right' },
        valueFormatter: params => {
          return SteelFormat.formatThousandthSign(params.value, 6)
        }
      }, {
        field: 'sNetAmt',
        headerName: this.$t('grid.title.amountWithoutTax'),
        cellStyle: { textAlign: 'right' },
        valueFormatter: params => {
          return SteelFormat.formatPrice(params.value)
        }
      }, {
        field: 'sVatRate',
        headerName: this.$t('grid.title.taxRate'),
        editable: () => {
          return (this.form.sSheetStatus === '10' || this.form.sSheetStatus === '11' || this.form.sSheetStatus === '15') && this.$route.query.disabled !== '1'
        },
        cellStyle: { textAlign: 'right' },
        cellEditorFramework: Vue.extend(Middleware.createComponent(
          'CndInputNumber',
          {
            mark: 'sVatRate',
            type: 'percent',
            decimalDigit: 6,
            autoFocus: true,
            focusSelect: true
          }
        )),
        valueFormatter: params => {
          return SteelFormat.toPercent(params.value)
        }
      }, {
        field: 'sVatToStdAmt',
        headerName: this.$t('grid.title.vatAmount'),
        cellStyle: { textAlign: 'right' },
        valueFormatter: params => {
          return SteelFormat.formatPrice(params.value)
        }
      }, {
        field: 'sRemark',
        headerName: this.$t('grid.title.remarks'),
        cellStyle: { textAlign: 'left' },
        editable: () => {
          return (this.form.sSheetStatus === '10' || this.form.sSheetStatus === '11' || this.form.sSheetStatus === '15') && this.$route.query.disabled !== '1'
        }
      }],
      rowData: [],
      splitDataVisible: false,
      selectSplit: {},
      sTaxAmt: null,
      sContractQty: null,
      sId: null,
      headerTotal: [],
      footerTotal: [],
      seqList: [],
      needSubmit: false,
      modifyForm: {
        modifyPrice: null,
        goodsDesc: null,
        spec: null,
        sRemark: null,
        sTaxGoodsId: null
      },
      modifyData: [],
      modifyDialog: false,
      modifyDialogType: false, // true: 修改税收分类， false: 批量修改
      taxTypeList: [], // 税收分类下拉选择框数据
      modifyConfig: {
        taxType: {
          title: '修改税收分类',
          height: '40px'
        },
        batchModify: {
          title: this.$t('grid.others.batchModify'),
          height: '120px'
        }
      },
      coalTypeList: [],
      agreementTermTypeList: [],
      coalTypeCode: '',
      unitToMap: {}
    }
  },
  computed: {
    tableHeight() {
      return window.innerHeight - 160
    }
  },
  mounted() {
    this.loadDict()
    this.getParameterQuery()
    // 获取单位
    this.getContractQtyUnit()
  },
  methods: {
    loadDict() {
      // 数据字典获取 公告状态 公告通知形式
      DictUtil.getDict([
        'invoice.taxdetail.coal.type',
        'invoice.taxdetail.agreement.type'
      ], res => {
        this.coalTypeList = res[0].dicts
        this.agreementTermTypeList = res[1].dicts
      })
    },
    getParameterQuery() {
      getParameterLimit({
        code: 'SALE_INVOICE_TAXDETAIL_COAL_CODE'
      }).then(res => {
        this.coalTypeCode = res.data.value
      })
    },
    getContractQtyUnit() {
      getMeasurementUnit({
        page: 0,
        limit: 20
      }, {}).then(res => {
        res.data.content.forEach((item) => {
          this.unitToMap[item.sName] = item.sId
        })
      })
    },
    getInitHide() {
      const columnApi = this.$refs.aggrid.gridApi.columnController.columnApi
      const data = this.rowData.some(el => (el.sTaxTypeCode.slice(0, 5)) === this.coalTypeCode)
      const data2 = this.rowData.some(el => (el.sCoalType === '02'))
      columnApi.setColumnVisible('sCoalType', data)
      columnApi.setColumnVisible('sAgreementTermType', data2)
      if (data && this.form.sIsNaturalSystem !== '1' && ['140', '150'].includes(this.form.sInvClassSub)) {
        columnApi.setColumnVisible('sDafValue', true)
        columnApi.setColumnVisible('sTotalSulfur', true)
        columnApi.setColumnVisible('sCalorificValue', true)
      } else {
        columnApi.setColumnVisible('sDafValue', false)
        columnApi.setColumnVisible('sTotalSulfur', false)
        columnApi.setColumnVisible('sCalorificValue', false)
      }
    },
    stopAgGridEditCheck(falg = false) {
      const showCoalType = this.rowData.some(el => (el.sTaxTypeCode.slice(0, 5)) === this.coalTypeCode)
      this.rowData.some((item) => {
        if (((item.sTaxTypeCode.slice(0, 5)) === this.coalTypeCode) && (item.sCoalType === undefined || item.sCoalType === '')) {
          throw this.$message.error(`序号为${item.sSeq}的煤炭种类为必填`)
        }
        if (item.sCoalType === '02' && (item.sAgreementTermType === undefined || item.sAgreementTermType === '')) {
          throw this.$message.error(`序号为${item.sSeq}请填写长协煤协议期限`)
        }
        const res = this.rowData.filter((i) => i.sInvNum === item.sInvNum)
        const resAmt = res.reduce((sum, j) => Number(j.sNetAmt) + sum, 0)
        if (resAmt > 10000000 && (showCoalType && this.form.sIsNaturalSystem !== '1' && ['140', '150'].includes(this.form.sInvClassSub))) {
          if (item.sDafValue === 0 || item.sTotalSulfur === 0 || item.sCalorificValue === 0) {
            throw this.$message.error(`发票张数为${item.sInvNum}的干燥无灰基挥发分,干基全硫, 每千克煤炭发热量为必填`)
          }
        }
      })
      if (falg) {
        this.stopAgGridEdit(true)
      }
    },
    stopAgGridEdit(flag = false) {
      if (this.$refs.aggrid.gridApi.getEditingCells().length) {
        this.$refs.aggrid.gridApi.clearFocusedCell()
        this.needSubmit = flag
      } else {
        this.$emit('submit')
      }
    },
    loadData(pagination = { page: 0, limit: 20 }) {
      return new Promise((resolve, reject) => {
        getSaleInvoiceTaxPage({
          sInvoiceId: this.id,
          ...pagination
        }).then(res => {
          this.rowData = res.data.detail.content.map(item => {
            const isTrue = this.seqList.length ? !this.seqList.some(res => res === item.sSeq) : true
            item._selected = isTrue
            // item.sNetPrice = item.sContractQty ? (item.sNetAmt / item.sContractQty) : item.sNetPrice
            item._isEdit = false
            return item
          })
          this.headerTotal = [
            { key: 'count', count: res.data.vCount, unit: this.$t('pagination.items') },
            { title: this.$t('grid.title.quantity'), count: SteelFormat.formatThousandthSign((res.data.vSumQty), 4), unit: this.$t('grid.others.ton') },
            { title: this.$t('grid.title.amountWithTax'), count: SteelFormat.formatPrice((res.data.vSumAmt)), unit: this.$t('grid.others.yuan') },
            { title: this.$t('grid.title.amountWithoutTax'), count: SteelFormat.formatPrice((res.data.vSumNetAmt)), unit: this.$t('grid.others.yuan') },
            { title: this.$t('grid.others.taxAmount'), count: SteelFormat.formatPrice((res.data.vSumVatAmt)), unit: this.$t('grid.others.yuan') }
          ]
          resolve(res.data.detail)
          // 使用 columnApi 设置列的可见性
          this.getInitHide()
        }).catch(() => {
          reject(0)
        })
      })
    },

    modifySubmit() {
      if (this.modifyDialogType === 'taxType') { // 修改税收分类
        if (!this.modifyForm.sTaxGoodsId) return this.$message.error('请选择税收分类')
        const ids = this.modifyData.map(item => item.sId)
        modifiesTaxType(this.modifyForm.sTaxGoodsId, ids).then((res) => {
          if (res.code === '0000') {
            this.$message.success(this.$t('grid.others.modifySuccessfully'))
            this.modifyDialog = false
            this.$refs.aggrid.loadTableData()
          }
        })
      } else if (this.modifyDialogType === 'batchModify') { // 批量修改
        const list = JSON.parse(JSON.stringify(this.modifyData))
        const { modifyPrice, goodsDesc, spec, sRemark } = this.modifyForm
        list.map(data => {
          if (modifyPrice !== null && modifyPrice !== '') {
            const sTaxAmt = this.$toFixed(modifyPrice * data.sContractQty)
            const sNetAmt = this.$toFixed(sTaxAmt / (1 + data.sVatRate))
            const sNetPrice = data.sContractQty ? (sNetAmt / data.sContractQty) : data.sNetPrice
            const sVatToStdAmt = +new Decimal(sTaxAmt).sub(+sNetAmt || 0)
            const sStdAmt = this.$toFixed(sTaxAmt * data.sToStdRate)
            const sStdNetAmt = this.$toFixed(sNetAmt * data.sToStdRate)
            data.sTaxPrice = modifyPrice
            data.sTaxAmt = sTaxAmt
            data.sNetAmt = sNetAmt
            data.sNetPrice = sNetPrice
            data.sVatToStdAmt = sVatToStdAmt
            data.sStdAmt = sStdAmt
            data.sStdNetAmt = sStdNetAmt
          }
          data.sGoodsDesc = handleSpaces(goodsDesc) || data.sGoodsDesc
          data.sSpec = handleSpaces(spec) || data.sSpec
          data.sRemark = sRemark || data.sRemark
        })
        console.log('批量修改后的数据：', JSON.parse(JSON.stringify(list)))
        invoiceSaleTaxDetailModifies(list).then(res => {
          this.$message.success(this.$t('grid.others.modifySuccessfully'))
          this.modifyDialog = false
          this.$refs.aggrid.loadTableData()
          if (this.needSubmit) {
            this.$emit('submit')
          }
        }).catch(() => {
          if (this.needSubmit) {
            this.$emit('submit')
          }
        })
      }
    },
    openModifyDialog(type) {
      this.modifyDialogType = type
      this.$refs.aggrid.getSelectedData(res => {
        if (res.length) {
          if (type === 'taxType') {
            let isTrue = false
            let isNull = false
            for (const item of res) {
              if (!item.sGoodsDesc) {
                isNull = true
                break
              }
              if (item.sGoodsDesc !== res[0].sGoodsDesc) {
                isTrue = true
                break
              }
            }
            if (isNull) return this.$message.error('所选商品名称描述不能为空')
            if (isTrue) return this.$message.error('商品名称描述不一致，请重新选择')
            listTaxGoodsCode(res[0].sId).then(val => {
              this.taxTypeList = val.data
              this.setModifyDialog(res)
            })
          } else if (type === 'batchModify') {
            this.setModifyDialog(res)
          } else if (type === 'netAmt') {
            autoUpdatesNetPrice(res.map(item => item.sId)).then(() => {
              this.$message.success(this.$t('tips.operationSuccessful'))
              this.$refs.aggrid.loadTableData()
            })
          }
        } else {
          this.$message.error(this.$t('grid.others.pleaseSelectData'))
        }
      })
    },
    setModifyDialog(res) {
      this.modifyForm.modifyPrice = null
      this.modifyForm.goodsDesc = null
      this.modifyForm.spec = null
      this.modifyForm.sRemark = null
      this.modifyForm.sTaxGoodsId = null
      this.modifyDialog = true
      this.modifyData = res
    },
    mergeData() {
      const list = []
      this.$refs.aggrid.gridApi.forEachNodeAfterFilterAndSort((node, index) => {
        if (node.data) {
          list.push(node.data)
          this.seqList.push(node.data.sSeq)
        }
      })
      this.$refs.aggrid.getSelectedData(res => {
        if (!res.length) {
          MessageUtil.error(this.$t('grid.others.pleaseSelectTheDataToBeMerged'))
          return
        }
        if (res.length < 2) {
          MessageUtil.error(this.$t('grid.others.moreThan2ItemsNeedTrgeDataKey'))
          return
        }
        let repeat = false
        for (let i = 0; i < res.length - 1; i++) {
          for (let j = i + 1; j < res.length; j++) {
            if (res[i].sGoodsDesc !== res[j].sGoodsDesc || res[i].sSpec !== res[j].sSpec) {
              repeat = true
            }
          }
        }
        const newList = list.filter(item => {
          return res.some(i => item.sSeq === i.sSeq)
        })
        if (repeat) {
          this.$confirm(this.$t('grid.others.nameSpecificationsAToMergeKey'), this.$t('grid.others.prompt'), {
            confirmButtonText: this.$t('btns.confirm'),
            cancelButtonText: this.$t('btns.cancel'),
            type: 'warning'
          }).then(() => {
            invoiceSaleTaxDetailMargeTax(newList)
              .then(() => {
                MessageUtil.success(this.$t('grid.others.mergeSuccess'))
                this.$refs.aggrid.clearSelection()
                this.$refs.aggrid.reloadTableData()
              })
          })
        } else {
          invoiceSaleTaxDetailMargeTax(newList)
            .then(() => {
              MessageUtil.success(this.$t('grid.others.mergeSuccess'))
              this.$refs.aggrid.clearSelection()
              this.$refs.aggrid.reloadTableData()
            })
        }
      })
    },
    splitData() {
      this.$refs.aggrid.gridApi.forEachNodeAfterFilterAndSort((node, index) => {
        if (node.data) {
          this.seqList.push(node.data.sSeq)
        }
      })
      this.$refs.aggrid.getSelectedData(res => {
        if (!res.length) {
          MessageUtil.error(this.$t('grid.others.pleaseSelectTheDataToBeSplit'))
          return
        }
        if (res.length > 1) {
          MessageUtil.error(this.$t('grid.others.onlySingleDataCanBelittingKey'))
          return
        }
        this.selectSplit = res[0]
        this.sTaxAmt = SteelFormat.formatPrice(res[0].sTaxAmt)
        this.sContractQty = SteelFormat.formatThousandthSign(res[0].sContractQty, 4)
        this.sId = res[0].sId
        this.splitDataVisible = true
      })
    },
    closeSplitDialog() {
      this.splitDataVisible = false
      this.selectSplit = null
    },
    splitDataLimit(falg = true) {
      if (this.isBusinessDisabled('submit', this.form.sSheetStatus) || this.$route.query.disabled === '1') return false
      invoiceSaleTaxDetailSplitLimit({
        sInvoiceId: this.id,
        flag: falg
      }).then(() => {
        MessageUtil.success(this.$t('grid.others.successfulSplitting'))
        this.$refs.aggrid.clearSelection()
        this.$refs.aggrid.reloadTableData()
      })
    },
    mergeTaxAuto() {
      this.$confirm('是否确认按规格合并数据？', this.$t('grid.others.prompt'), {
        confirmButtonText: this.$t('btns.confirm'),
        cancelButtonText: this.$t('btns.cancel'),
        type: 'warning'
      })
        .then(() => {
          invoiceMergeTaxAuto({
            sInvoiceId: this.id
          }).then(() => {
            MessageUtil.success(this.$t('grid.others.mergeSuccessfully'))
            this.$refs.aggrid.clearSelection()
            this.$refs.aggrid.reloadTableData()
          })
        })
        .catch(() => { })
    },
    getStringLength(str) {
      let len = 0
      if (str) {
        for (let i = 0; i < str.length; i++) {
          if (str.charAt(i).match(/[\u4e00-\u9fa5]/g) != null) len += 2
          else len += 1
        }
        return len
      }
    },

    cellEditingStopped(params) {
      const { data, oldValue, newValue } = params
      if (oldValue !== newValue) {
        data.sSpec = handleSpaces(data.sSpec)
        data.sGoodsDesc = handleSpaces(data.sGoodsDesc)
        if (this.getStringLength(data.sSpec) > 40) {
          this.$message.warning('规格输入不能超过40个字符')
          this.$refs.aggrid.reloadTableData()
          return false
        }
        if (this.getStringLength(data.sGoodsDesc) > 88) {
          this.$message.warning('商品名称输入不能超过88个字符')
          this.$refs.aggrid.reloadTableData()
          return false
        }
        if (this.getStringLength(data.sRemark) > 138) {
          this.$message.warning('备注输入不能超过138个字符')
          this.$refs.aggrid.reloadTableData()
          return false
        }
        const showCoalType = this.rowData.some(el => (el.sTaxTypeCode.slice(0, 5)) === this.coalTypeCode)
        const curCoalType = data.sTaxTypeCode.slice(0, 5) === this.coalTypeCode
        if (showCoalType && curCoalType && (data.sCoalType === undefined || data.sCoalType === '')) {
          this.$message.error(`序号为${data.sSeq}煤炭种类为必填`)
          this.$refs.aggrid.reloadTableData()
          return false
        }
        if (params.colDef.field === 'sCoalType') {
          data.sAgreementTermType = ''
        }
        invoiceSaleTaxDetailModifies([data]).then(res => {
          this.$message.success(this.$t('grid.others.modifySuccessfully'))
          this.$refs.aggrid.reloadTableData()
          if (this.needSubmit) {
            this.$emit('submit')
          }
        }).catch(() => {
          if (this.needSubmit) {
            this.$emit('submit')
          }
        })
      }
    },
    rejectMaterial() {
      invoiceSaleTaxModifiesSpec(this.rowData).then(() => {
        MessageUtil.success(this.$t('grid.others.rejectMaterialSuccess'))
        this.$refs.aggrid.clearSelection()
        this.$refs.aggrid.reloadTableData()
      })
    },
    autoAdjust() {
      invoiceAutoUpdatesVatToStdAmt({ sInvoiceId: this.id }).then((res) => {
        if (res.code === '0000') {
          MessageUtil.success(this.$t('grid.tips.adjustedTailDifferenceSuccessfully'))
          this.$refs.aggrid.clearSelection()
          this.$refs.aggrid.reloadTableData()
        }
      })
    },
    removes() {
      this.$refs.aggrid.getSelectedData(res => {
        console.log('res: ', res)
        if (!res.length) {
          this.$message.error(this.$t('grid.others.pleaseSelectTheDataToBeDeleted'))
          return
        }
        const ids = res.map(item => item.sId)
        invoiceTaxDetailRemoves(ids).then(res => {
          this.$message.success(this.$t('tips.deletedSuccessfully'))
          this.$refs.aggrid.clearSelection()
          this.$refs.aggrid.reloadTableData()
        })
      })
    },
    cellValueChanged(params) {
      // 含税金额sTaxAmt=含税单价sTaxPrice*数量sContractQty
      // 不含税金额sNetAmt=含税金额sTaxAmt/(1+增值税率sVatRate)
      // 增值税额sVatToStdAmt=含税金额sTaxAmt-不含税金额sNetAmt
      // sStdAmt（本位币金额）= sTaxAmt（含税原币金额）*  sToStdRate（原币折本币汇率）
      // sStdNetAmt = sNetAmt * sToStdRate
      const { data, colDef, newValue } = params
      let sNetAmt; let sTaxAmt; let sNetPrice; let sTaxPrice; let sStdAmt; let sStdNetAmt; let sVatToStdAmt; let sContractQty; let sVatRate

      // 修改单价,数量不变,金额变更
      if (colDef.field === 'sTaxPrice') {
        sTaxPrice = +newValue || 0
        sTaxAmt = this.$toFixed(newValue * data.sContractQty)
        sNetAmt = this.$toFixed(sTaxAmt / (1 + data.sVatRate))
        sNetPrice = sNetAmt / data.sContractQty
        sVatToStdAmt = +new Decimal(sTaxAmt).sub(+sNetAmt || 0)
        sStdAmt = this.$toFixed(sTaxAmt * data.sToStdRate)
        sStdNetAmt = this.$toFixed(sNetAmt * data.sToStdRate)
        data.sTaxPrice = sTaxPrice
        data.sTaxAmt = sTaxAmt
        data.sNetAmt = sNetAmt
        data.sNetPrice = sNetPrice
        data.sVatToStdAmt = sVatToStdAmt
        data.sStdAmt = sStdAmt
        data.sStdNetAmt = sStdNetAmt
      }
      // 修改数量,单价不变,金额变更
      if (colDef.field === 'sContractQty') {
        sContractQty = +newValue || 0
        sTaxAmt = this.$toFixed(newValue * data.sTaxPrice)
        sNetAmt = this.$toFixed(sTaxAmt / (1 + data.sVatRate))
        sVatToStdAmt = +new Decimal(sTaxAmt).sub(+sNetAmt || 0)
        sStdAmt = this.$toFixed(sTaxAmt * data.sToStdRate)
        sStdNetAmt = this.$toFixed(sNetAmt * data.sToStdRate)
        data.sContractQty = sContractQty
        data.sTaxAmt = sTaxAmt
        data.sNetAmt = sNetAmt
        data.sVatToStdAmt = sVatToStdAmt
        data.sStdAmt = sStdAmt
        data.sStdNetAmt = sStdNetAmt
      }
      // 修改金额,数量不变,单价变
      if (colDef.field === 'sTaxAmt') {
        sTaxAmt = +newValue || 0
        sTaxPrice = data.sContractQty ? (newValue / data.sContractQty) : data.sTaxPrice
        sNetAmt = this.$toFixed(newValue / (1 + data.sVatRate))
        sNetPrice = data.sContractQty ? (sNetAmt / data.sContractQty) : data.sNetPrice
        sVatToStdAmt = +new Decimal(newValue).sub(+sNetAmt || 0)
        sStdAmt = this.$toFixed(newValue * data.sToStdRate)
        sStdNetAmt = this.$toFixed(sNetAmt * data.sToStdRate)
        data.sTaxAmt = sTaxAmt
        data.sTaxPrice = sTaxPrice
        data.sNetAmt = sNetAmt
        data.sNetPrice = sNetPrice
        data.sVatToStdAmt = sVatToStdAmt
        data.sStdAmt = sStdAmt
        data.sStdNetAmt = sStdNetAmt
      }
      if (colDef.field === 'sVatRate') {
        // 修改税率sVatRate，含税金额不变sTaxAmt，不含税金额变sNetAmt，增值税额sVatAmt变
        sVatRate = +newValue || 0
        sNetAmt = this.$toFixed(data.sTaxAmt / (1 + +newValue))
        sVatToStdAmt = +new Decimal(data.sTaxAmt).sub(+sNetAmt || 0)
        sNetPrice = data.sContractQty ? (sNetAmt / data.sContractQty) : data.sNetPrice
        sStdNetAmt = this.$toFixed(sNetAmt * data.sToStdRate)
        data.sVatRate = sVatRate
        data.sNetAmt = sNetAmt
        data.sVatToStdAmt = sVatToStdAmt
        data.sNetPrice = sNetPrice
        data.sStdNetAmt = sStdNetAmt
      }
      // 给煤炭种类赋值
      if (colDef.field === 'sCoalType') {
        const columnApi = this.$refs.aggrid.gridApi.columnController.columnApi
        const data = this.rowData.some(el => (el.sCoalType === '02'))
        columnApi.setColumnVisible('sAgreementTermType', data)

        const matchedItem = this.coalTypeList.find(item => item.sCodeName === newValue)
        const matchedCur = this.coalTypeList.find(item => item.sCodeName === params.oldValue)
        if (matchedItem) {
          params.data.sCoalType = matchedItem.sCodeValue
        } else {
          if (matchedCur) {
            params.data.sCoalType = matchedCur.sCodeValue
          } else {
            params.data.sCoalType = ''
          }
          this.$refs.aggrid.reloadTableData()
          return this.$message.error('煤炭种类不存在!')
        }
      }
      // 给煤炭种类赋值
      if (colDef.field === 'sAgreementTermType') {
        const matchedItem = this.agreementTermTypeList.find(item => item.sCodeName === newValue)
        const matchedCur = this.agreementTermTypeList.find(item => item.sCodeName === params.oldValue)
        if (matchedItem) {
          params.data.sAgreementTermType = matchedItem.sCodeValue
        } else {
          if (matchedCur) {
            params.data.sAgreementTermType = matchedCur.sCodeValue
          } else {
            params.data.sAgreementTermType = ''
          }
          this.$refs.aggrid.reloadTableData()
          return this.$message.error('协议期限不存在!')
        }
      }
      data._isEdit = true
    },
    onPasteEnd() {
      const list = this.rowData.filter(item => item._isEdit === true)
      const newList = list.map(el => {
        return {
          ...el,
          sSpec: handleSpaces(el.sSpec),
          sGoodsDesc: handleSpaces(el.sGoodsDesc),
          sContractQtyUnit: this.unitToMap[el.vContractQtyUnitName] || ''
        }
      })
      console.log('黏贴修改：', JSON.parse(JSON.stringify(newList)))
      if (newList.length) {
        invoiceSaleTaxDetailModifies(newList).then(res => {
          this.$message.success(this.$t('grid.others.modifySuccessfully'))
          this.$refs.aggrid.reloadTableData()
          if (this.needSubmit) {
            this.$emit('submit')
          }
        }).catch(() => {
          if (this.needSubmit) {
            this.$emit('submit')
          }
        })
      }
      // invoiceSaleTaxDetailModifies(newList).then(res => {
      //   this.$message.success(this.$t('grid.others.modifySuccessfully'))
      //   this.$refs.aggrid.reloadTableData()
      //   if (this.needSubmit) {
      //     this.$emit('submit')
      //   }
      // }).catch(() => {
      //   if (this.needSubmit) {
      //     this.$emit('submit')
      //   }
      // })
    },
    handleFooterTotal(_, list) {
      this.footerTotal = list.reduce((prev, next, index) => {
        if (next._selected) {
          if (next.vContractQtyUnitName === '千克') {
            prev[1].count = +new Decimal(prev[1].count).add(next.sContractQty / 1000 || 0)
          } else {
            prev[1].count = +new Decimal(prev[1].count).add(next.sContractQty || 0)
          }
          prev[2].count = +new Decimal(prev[2].count).add(next.sTaxAmt || 0)
          prev[3].count = +new Decimal(prev[3].count).add(next.sNetAmt || 0)
          prev[4].count = +new Decimal(prev[4].count).add(next.sVatToStdAmt || 0)
        }
        if (index + 1 === list.length) {
          prev[1].count = SteelFormat.formatThousandthSign(prev[1].count, 4)
          prev[2].count = SteelFormat.formatPrice(prev[2].count)
          prev[3].count = SteelFormat.formatPrice(prev[3].count)
          prev[4].count = SteelFormat.formatPrice(prev[4].count)
        }
        return prev
      }, [{
        key: 'count',
        count: list.length,
        unit: this.$t('pagination.items')
      }, {
        title: this.$t('grid.title.quantity'),
        count: 0,
        unit: this.$t('grid.others.ton')
      },
      {
        title: this.$t('grid.title.amountWithTax'),
        count: 0,
        unit: this.$t('grid.others.yuan')
      },
      {
        title: this.$t('grid.title.amountWithoutTax'),
        count: 0,
        unit: this.$t('grid.others.yuan')
      },
      {
        title: this.$t('grid.others.taxAmount'),
        count: 0,
        unit: this.$t('grid.others.yuan')
      }])
    },
    successImport() {
      this.$refs.aggrid.reloadTableData()
    }
  }
}
</script>
