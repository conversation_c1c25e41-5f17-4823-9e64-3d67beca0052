<template>
  <el-dialog
    title="短信认证"
    :visible.sync="visible"
    width="400px"
    :close-on-click-modal="false"
    :show-close="true"
    custom-class="verification-dialog"
    @close="handleClose"
  >
    <div class="verification-content">
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        @submit.native.prevent="handleSubmit"
      >
        <el-form-item prop="code">
          <div class="flexCB">
            <el-input
              v-model="form.code"
              placeholder="验证码"
              maxlength="6"
              class="verification-input"
            />
            <el-button
              class="code-btn"
              type="text"
              :disabled="countdown > 0"
              @click.native.prevent="handleGetCode"
            >
              {{ countdown > 0 ? `${countdown}s后重新获取` : '获取验证码' }}
            </el-button>
          </div>
        </el-form-item>
        <!-- <div v-if="errorMsg" class="verification-tip">
          <span class="error-msg">{{ errorMsg }}</span>
        </div> -->
      </el-form>

      <el-checkbox v-model="remember" class="remember-device">
        同设备登录免二次验证
      </el-checkbox>

      <el-button
        type="primary"
        class="verification-submit"
        :loading="loading"
        @click="handleSubmit"
      >
        登录
      </el-button>
    </div>
  </el-dialog>
</template>

<script>

import { getAccountCode } from '@/api/user'
export default {
  name: 'VerificationDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    account: {
      type: String,
      default: ''
    },
    device: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      form: {
        code: ''
      },
      rules: {
        code: [
          { required: true, message: '请输入验证码', trigger: 'blur' },
          { len: 6, message: '请输入6位验证码', trigger: 'blur' }
        ]
      },
      countdown: 0,
      timer: null,
      loading: false,
      errorMsg: '',
      isGettingCode: false,
      remember: false // 添加记住设备选项
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.handleGetCode()
      }
      if (!val) {
        this.form.code = ''
        this.errorMsg = ''
        this.countdown = 0
      }
    }
  },
  beforeDestroy() {
    clearInterval(this.timer)
  },
  methods: {

    // 获取验证码
    async handleGetCode() {
      if (this.countdown > 0 || this.isGettingCode) return
      if (!this.account) {
        this.$message.error('账号不能为空')
        return
      }

      this.isGettingCode = true
      getAccountCode({
        account: this.account,
        device: this.device
      }).then(res => {
        if (res.code === '0000') {
          this.countdown = 120
          this.startCountdown()
          this.$message.success('验证码发送成功')
        } else {
          throw new Error(res.message || '获取验证码失败')
        }
      }).finally(() => {
        this.isGettingCode = false
      })
    },

    // 开始倒计时
    startCountdown() {
      this.timer = setInterval(() => {
        if (this.countdown > 0) {
          this.countdown--
        } else {
          clearInterval(this.timer)
        }
      }, 1000)
    },

    // 提交验证
    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (!valid) return

        this.loading = true
        this.errorMsg = ''

        // 触发验证事件，添加设备信息和记住选项
        this.$emit('verify', {
          code: this.form.code,
          remember: this.remember ? '1' : '0',
          callback: (error) => {
            this.loading = false
            if (error) {
              this.errorMsg = error
            }
          }
        })
      })
    },

    // 关闭弹窗
    handleClose() {
      this.form.code = ''
      this.errorMsg = ''
      this.countdown = 0
      this.$emit('update:visible', false)
      clearInterval(this.timer)
    }
  }
}
</script>

<style lang="scss" scoped>
.verification-dialog {
  .verification-content {
    padding-top: 20px;
  }

  .flexCB {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .verification-input {
    width: calc(100% - 120px);
  }

  .code-btn {
    width: 96px;
    margin-left: 10px;
    padding: 0;
    height: 32px;
    font-size: 12px;
    color: #409EFF;
    border: none;
    background: none;

    &[disabled] {
      color: #999;
      background: none;
      border: none;
      cursor: not-allowed;
      box-shadow: none;
      outline: none;
    }

    &:hover {
      color: #66b1ff;
      border: none;
      background: none;
    }

    &:focus {
      color: #409EFF;
      border: none;
      background: none;
      outline: none;
    }
  }

  .verification-tip {
    margin-top: -10px;
    margin-bottom: 15px;

    .error-msg {
      color: #F56C6C;
      font-size: 12px;
    }
  }

  .remember-device {
    margin-bottom: 15px;
    font-size: 14px;
  }

  .verification-submit {
    margin-top: 10px;
    width: 100%;
  }
}
</style>
