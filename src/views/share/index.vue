<template>
  <div class="height: 100%;">
    <annualReview ref="review" />
  </div>
</template>

<script>
const wx = window.wx
import { jsapiTicket } from '@/api/user'
import AnnualReview from '@/components/AnnualReview'

export default {
  name: 'Share',
  components: {
    AnnualReview
  },
  data() {
    return {
      logo: require('@/assets/login/logo.png')
    }
  },
  mounted() {
    this.$nextTick(() => {
      document.title = '建发云钢 | 2023年度用户报告'
    })
    const originalString = location.href.split('#')[0]
    // const encodedString = encodeURIComponent(originalString)
    jsapiTicket(
      { param: originalString }
    ).then(res => {
      const { appId, timestamp, nonceStr, signature } = res.data
      wx.config({
        debug: false,
        appId: appId, // 必填，公众号的唯一标识,需要注册公众号提供appid
        timestamp: timestamp, // 必填，生成签名的时间戳
        nonceStr: nonceStr, // 必填，生成签名的随机串
        signature: signature, // 必填，签名，见附录1
        jsApiList: [
          'onMenuShareTimeline', // 分享到朋友圈
          'onMenuShareAppMessage' // 分享给朋友
          // 'updateAppMessageShareData',
          // 'updateTimelineShareData'
        ] // 必填，需要使用的JS接口列表，所有JS接口列表见附录2 onMenuShareAppMessage
      })
    })
    wx.ready(function() { // 需在用户可能点击分享按钮前就先调用
      const config = {
        title: '建发云钢 | 2023年度用户报告', // 分享标题
        desc: '快新年伊始，请查收来自云钢的第一封信来查看我的年终报告吧!', // 分享描述
        link: location.href // 分享链接，该链接域名或路径必须与当前页面对应的公众号 JS 安全域名一致
        // imgUrl: this.logo // 分享图标
      }
      // wx.updateAppMessageShareData(config)
      // wx.updateTimelineShareData(config)
      wx.onMenuShareTimeline(config)
      wx.onMenuShareAppMessage(config)
    })
    wx.error(function(res) {
    })
  },
  methods: {
  }
}
</script>

<style lang="scss" scoped>

</style>
