<template>
  <!-- 合同条款信息 -->
  <div>
    <el-form
      ref="form"
      class="el-form-w100"
      label-width="100px"
      :inline="true"
      :model="form"
      size="small"
      :rules="formRules"
      :disabled="$route.query.disabledShow != 'add'"
    >
      <!-- <el-button
        size="mini"
        type="primary"
        @click="saveRegion('edit')"
      >保存</el-button>-->
      <cnd-form-card-list :active-panel="activeCollapseName">
        <cnd-form-card title="合同条款信息" name="2">
          <el-row class="ml-20">
            <div class="weight-size">
              <span class="color-red">*</span>
              一、合同标的是否显示列“暂定含税运费单价(元/吨)”
            </div>
            <el-radio-group v-model="form.sFreight.level1" class="ml-20">
              <div
                v-for="(item, index) in selectList.contractList"
                :key="index"
                class="disabled-color"
              >
                <el-radio :label="item.labelName" style="color: #000">{{ item.name }}</el-radio>
              </div>
            </el-radio-group>
          </el-row>

          <el-row class="ml-20">
            <div class="weight-size">
              <span class="color-red">*</span> 二、交货地点：
            </div>
            <el-radio-group v-model="form.sDeliveryLocation.level1" class="ml-20">
              <div v-for="(item, index) in selectList.deliver" :key="index" class="disabled-color">
                <el-radio :label="item.labelName" style="color: #000">{{ item.name }}</el-radio>
              </div>
            </el-radio-group>
            <cnd-form-item
              class="ml-20"
              :label="
                form.sDeliveryLocation.level1 == 1
                  ? '交货仓库/码头'
                  : '交货地点'
              "
              prop="sDeliveryLocation"
              required
            >
              <el-input v-model="form.sDeliveryLocation.value" />
            </cnd-form-item>
            <!-- <cnd-form-item class="ml-20" label="运输模式" prop="sCashRebatePercentage">
              <el-input v-model="form.sDeliveryLocation.transportMode" />
            </cnd-form-item>
            <cnd-form-item class="ml-20" label="物流费⽤承担" prop="sCashRebatePercentage">
              <el-input v-model="logisticsFeesType" />
            </cnd-form-item>
            -->
            <cnd-form-item
              v-show="sAutoGenerateProject == '1'"
              label="运输模式"
              prop="sDeliveryLocation"
              required
            >
              <!-- <span class="color-red">*</span> -->
              <el-select
                v-model="form.sDeliveryLocation.transportMode"
                placeholder="请选择运输模式"
                size="mini"
              >
                <el-option
                  v-for="item in options['msb.transport.mode']"
                  :key="item.sCodeValue"
                  :label="item.sCodeName"
                  :value="item.sCodeValue"
                />
              </el-select>
            </cnd-form-item>

            <cnd-form-item
              v-show="sAutoGenerateProject == '1'"
              label="物流费⽤承担"
              prop="sDeliveryLocation"
              required
            >
              <el-select
                v-model="form.sDeliveryLocation.logisticsFeesType"
                placeholder="请选择物流费⽤承担"
                size="mini"
                :multiple-limit="2"
                multiple
              >
                <el-option
                  v-for="item in options['msb.cost.bearing']"
                  :key="item.sCodeValue"
                  :label="item.sCodeName"
                  :value="item.sCodeValue"
                />
              </el-select>
            </cnd-form-item>
          </el-row>

          <el-row class="ml-20">
            <div class="weight-size">
              <span class="color-red">*</span>
              三、合理损耗：
            </div>
            <cnd-form-item class="ml-20" label="溢短装" prop="sMoreOrLess" required>
              <el-input v-model="form.sMoreOrLess" type="Number">
                <span slot="suffix">%</span>
              </el-input>
            </cnd-form-item>
            <cnd-form-item label="磅差" prop="sPoundDifference" required>
              <el-input v-model="form.sPoundDifference" type="Number">
                <span slot="suffix">%</span>
              </el-input>
            </cnd-form-item>
          </el-row>

          <el-row class="ml-20">
            <div class="weight-size">
              <span class="color-red">*</span> 四、保险：
            </div>
            <el-radio-group v-model="form.sInsurance.level1" class="ml-20">
              <div
                v-for="(item, index) in selectList.insurance"
                :key="index"
                class="disabled-color"
              >
                <el-radio :label="item.labelName" style="color: #000">{{ item.name }}</el-radio>
              </div>
            </el-radio-group>
            <cnd-form-item
              v-if="form.sInsurance.level1 == 2"
              class="ml-20"
              label="保费"
              prop="sInsurance"
              required
            >
              <el-input v-model="form.sInsurance.value" type="Number" />
            </cnd-form-item>
          </el-row>

          <el-row class="ml-20">
            <div class="weight-size">
              <span class="color-red">*</span>
              五、保证金：
            </div>

            <el-radio-group v-model="marginDeposit1" class="ml-20" @change="getMarginDeposit1">
              <div
                v-for="(item, index) in selectList.insuranceBenefit"
                :key="index"
                class="disabled-color radio-box"
              >
                <el-radio
                  :label="item.labelName"
                  @click.native.prevent="clickitem1(item.labelName)"
                >
                  <div class="disabled-color-bg" />
                  <div class="radio-span">{{ item.name }}</div>
                </el-radio>
              </div>
            </el-radio-group>

            <div class="ml-20">
              <el-checkbox-group v-model="checkList" @change="handleChange">
                <el-checkbox
                  label="3"
                >2.保证金追加：合同签约后，若上海期货交易所{合约}合约期货价格上涨超{上涨比例}（以供方认定为准），需方应追加未点价部分相应涨价金额的保证金,追加保证金比例为涨价金额的{追加保证金比例}；</el-checkbox>
                <br>
                <el-checkbox label="4">3. 需方应在点价后来款提货，保证金只能作为最后一笔货款冲抵。</el-checkbox>
              </el-checkbox-group>
            </div>

            <el-radio-group v-model="marginDeposit2" class="ml-20" @change="getMarginDeposit2">
              <div
                v-for="(item, index) in selectList.insuranceBenefit1"
                :key="index"
                class="disabled-color radio-box"
              >
                <el-radio
                  :label="item.labelName"
                  @click.native.prevent="clickitem2(item.labelName)"
                >
                  <div class="disabled-color-bg" />
                  <div class="radio-span">{{ item.name }}</div>
                </el-radio>
              </div>
            </el-radio-group>
            <div class="ml-20">
              <cnd-form-item v-if="getValue('1')" label="保证金比例" prop="sBond" required>
                <div class="dis">
                  <div
                    class="checkListson"
                    :class="
                      [
                        {
                          prohibited1:$route.query.disabledShow != 'add',
                          prohibited:form.sBond.value.depositRatio == 10 && $route.query.disabledShow != 'add'
                        },
                        { colro: form.sBond.value.depositRatio == 10 }
                      ]"
                    @click="getSecurityDepositBtn(10)"
                  >10</div>
                  <div
                    class="checkListson"
                    :class="[
                      {
                        prohibited1:$route.query.disabledShow != 'add' ,
                        prohibited:form.sBond.value.depositRatio == 15 && $route.query.disabledShow != 'add'
                      },
                      { colro: form.sBond.value.depositRatio == 15 }
                    ]"
                    @click="getSecurityDepositBtn(15)"
                  >15</div>
                  <el-input
                    v-model="form.sBond.value.depositRatio"
                    type="number"
                    @input="integer($event)"
                  >
                    <span slot="suffix">%</span>
                  </el-input>
                </div>
              </cnd-form-item>

              <div v-if="getValue('2')">
                <cnd-form-item label="保证金金额" prop="sBond" required>
                  <el-input
                    v-model="form.sBond.value.bondMoney"
                    input-align="right"
                    type="Number"
                    placeholder="请输入"
                  />
                </cnd-form-item>
                <cnd-form-item label="支付时间" prop="sBond" required>
                  <el-input
                    v-model="form.sBond.value.paymentTime"
                    input-align="right"
                    placeholder="请输入"
                  />
                </cnd-form-item>
              </div>

              <div v-if="getValue('3')">
                <cnd-form-item label="合约" prop="sBond" required>
                  <el-input
                    v-model="form.sBond.value.contract"
                    input-align="right"
                    placeholder="请输入"
                  />
                </cnd-form-item>
                <cnd-form-item label="上涨比例" prop="sBond" required>
                  <el-input
                    v-model="form.sBond.value.increaseRatio"
                    input-align="right"
                    placeholder="请输入"
                    type="Number"
                    @input="inputBXJBL('increaseRatio', $event)"
                  >
                    <span slot="suffix">%</span>
                  </el-input>
                </cnd-form-item>

                <cnd-form-item label="追加保证金比例" prop="sBond" required>
                  <el-input
                    v-model="form.sBond.value.additionalMarginRatio"
                    input-align="right"
                    placeholder="请输入"
                    type="Number"
                    @input="inputBXJBL('additionalMarginRatio', $event)"
                  >
                    <span slot="suffix">%</span>
                  </el-input>
                </cnd-form-item>
              </div>
              <!-- 4.1 -->
              <div v-if="getValue('5')">
                <cnd-form-item label="暂定单价比例" prop="sBond" required>
                  <el-input
                    v-model="form.sBond.value.provisionalUnitPriceRatio"
                    input-align="right"
                    placeholder="请输入"
                    type="Number"
                  />
                </cnd-form-item>
                <cnd-form-item label="提货天数" prop="sBond" required>
                  <el-input
                    v-model="form.sBond.value.deliveryDays"
                    input-align="right"
                    placeholder="请输入"
                    type="Number"
                    @input="inputBXJBL('deliveryDays', $event)"
                  >
                    <!-- <span slot="suffix">%</span> -->
                  </el-input>
                </cnd-form-item>
              </div>
              <!-- 4.2 -->
              <div v-if="getValue('6')">
                <cnd-form-item label="暂定单价加价金额" prop="sBond" required>
                  <el-input
                    v-model="form.sBond.value.provisionalUnitPriceMarkup"
                    input-align="right"
                    placeholder="请输入"
                    type="Number"
                  />
                </cnd-form-item>
                <cnd-form-item label="提货天数" prop="sBond" required>
                  <el-input
                    v-model="form.sBond.value.deliveryDays"
                    input-align="right"
                    placeholder="请输入"
                    type="Number"
                  />
                </cnd-form-item>
              </div>
            </div>
          </el-row>

          <el-row class="ml-20">
            <div class="weight-size">
              <span class="color-red">*</span> 六、结算期及结算方式：
            </div>

            <div class="ml-20">
              <div>
                <span class="color-red">*</span>1、价格条款
              </div>
              <el-radio-group v-model="form.sPriceTerms.level1" class="ml-20">
                <div
                  v-for="(item, index) in selectList.priceClause"
                  :key="index"
                  class="disabled-color radio-box"
                >
                  <!--  -->
                  <el-radio :label="item.labelName">
                    <div class="radio-span">{{ item.name }}</div>
                  </el-radio>
                </div>
              </el-radio-group>

              <div class="ml-20">
                <cnd-form-item
                  v-if="form.sPriceTerms.level1 == 3"
                  label="付款方式"
                  prop="sPriceTerms"
                  required
                >
                  <div class="dis">
                    <div
                      class="checkListson"
                      :class="[
                        {
                          prohibited1:$route.query.disabledShow != 'add' ,
                          prohibited: form.sPriceTerms.value.paymentMethod.includes('现款') && $route.query.disabledShow != 'add'
                        },
                        { colro: cjeckShow1 === 1 && form.sPriceTerms.value.paymentMethod.includes('现款')}
                      ]"
                      @click="getCheckListson(1)"
                    >现款</div>
                    <div
                      class="checkListson"
                      :class="
                        [
                          {
                            prohibited1:$route.query.disabledShow != 'add' ,
                            prohibited: form.sPriceTerms.value.paymentMethod.includes('承兑') && $route.query.disabledShow != 'add'
                          },
                          { colro: cjeckShow2 === 2 && form.sPriceTerms.value.paymentMethod.includes('承兑')}
                        ]"
                      @click="getCheckListson(2)"
                    >承兑</div>
                    <el-input
                      v-model="form.sPriceTerms.value.paymentMethod"
                      input-align="right"
                      placeholder="请输入"
                    />
                  </div>
                </cnd-form-item>

                <div v-if="form.sPriceTerms.level1 == 5">
                  <cnd-form-item label="参考网价" required prop="sPriceTerms">
                    <el-input
                      v-model="form.sPriceTerms.value.referenceOnlinePrice"
                      input-align="right"
                      placeholder="请输入"
                    />
                  </cnd-form-item>

                  <cnd-form-item label="销售下浮金额" required prop="sPriceTerms">
                    <el-input
                      v-model="form.sPriceTerms.value.salesDecreaseAmount"
                      input-align="right"
                      placeholder="请输入"
                    />
                  </cnd-form-item>
                </div>
              </div>
            </div>
          </el-row>

          <el-row class="ml-20">
            <div class="ml-20">
              <div>
                <span class="color-red">*</span> 2、结算期：
              </div>
              <el-radio-group v-model="form.sSettlementPeriod.level1" class="ml-20">
                <div
                  v-for="(item, index) in selectList.result"
                  :key="index"
                  class="disabled-color radio-box"
                >
                  <!--  -->
                  <el-radio :label="item.labelName">
                    <div class="radio-span">{{ item.name }}</div>
                  </el-radio>
                </div>
              </el-radio-group>

              <div
                v-if="
                  form.sSettlementPeriod.level1 == 1 ||
                    form.sSettlementPeriod.level1 == 3 ||
                    form.sSettlementPeriod.level1 == 4
                "
              >
                <cnd-form-item label="付款日期" required prop="sSettlementPeriod">
                  <!-- <el-input
                      v-model="form.sSettlementPeriod.value.paymentDate"
                      input-align="right"
                      placeholder="请选择付款日期"
                  />-->
                  <el-date-picker
                    v-model="form.sSettlementPeriod.value.paymentDate"
                    type="date"
                    placeholder="请选择付款日期"
                    style="width: 100%"
                  />
                </cnd-form-item>
              </div>
              <div
                v-if="
                  form.sSettlementPeriod.level1 != 3 &&
                    form.sSettlementPeriod.level1 != ''
                "
              >
                <cnd-form-item label="结算天数" prop="sSettlementPeriod" required>
                  <el-input
                    v-model="form.sSettlementPeriod.value.withinDays"
                    input-align="right"
                    placeholder="请输入"
                  />
                </cnd-form-item>
              </div>
              <div
                v-if="
                  form.sSettlementPeriod.level1 == 4 ||
                    form.sSettlementPeriod.level1 == 5
                "
              >
                <cnd-form-item label="付款方式" prop="sSettlementPeriod" required>
                  <div class="dis">
                    <div
                      class="checkListson"
                      :class="
                        [
                          {
                            prohibited1:$route.query.disabledShow != 'add' ,
                            prohibited: form.sSettlementPeriod.value.paymentMethod.includes('现款') && $route.query.disabledShow != 'add'
                          },
                          { colro: cjeckShow3 === 1 && form.sSettlementPeriod.value.paymentMethod.includes('现款')}]"
                      @click="getCheckListson1(1)"
                    >现款</div>
                    <div
                      class="checkListson"
                      :class="
                        [
                          {
                            prohibited1:$route.query.disabledShow != 'add' ,
                            prohibited: form.sSettlementPeriod.value.paymentMethod.includes('承兑') && $route.query.disabledShow != 'add'
                          },
                          { colro: cjeckShow4 === 2 && form.sSettlementPeriod.value.paymentMethod.includes('承兑')}]"
                      @click="getCheckListson1(2)"
                    >承兑</div>
                    <el-input
                      v-model="form.sSettlementPeriod.value.paymentMethod"
                      input-align="right"
                      placeholder="请输入"
                    />
                  </div>
                </cnd-form-item>

                <div v-if="form.sSettlementPeriod.level1 == 4">
                  <cnd-form-item label="付款比例" prop="sSettlementPeriod" required>
                    <el-input
                      v-model="form.sSettlementPeriod.value.paymentRatio"
                      input-align="right"
                      placeholder="请输入"
                      @input="
                        inputFKBL('sSettlementPeriod', 'paymentRatio', $event)
                      "
                    >
                      <span slot="suffix">%</span>
                    </el-input>
                  </cnd-form-item>
                </div>
              </div>
              <br>
              <template v-if="form.sSettlementPeriod.level1 == 4">
                <br>
                <cnd-form-item label="付款日期2" prop="sSettlementPeriod" required>
                  <el-date-picker
                    v-model="form.sSettlementPeriod.value.paymentDateTwo"
                    type="date"
                    placeholder="请选择付款日期2"
                    style="width: 100%"
                  />
                </cnd-form-item>
                <cnd-form-item label="结算天数2" prop="sSettlementPeriod" required>
                  <el-input
                    v-model="form.sSettlementPeriod.value.withinDaysTwo"
                    input-align="right"
                    placeholder="请输入"
                  />
                </cnd-form-item>
                <!-- <cnd-form-item
                    label="付款方式2"
                    prop="sSettlementPeriod"
                    required
                  >
                    <el-input
                      v-model="form.sSettlementPeriod.value.paymentMethodTwo"
                      input-align="right"
                      placeholder="请输入"
                    />
                </cnd-form-item>-->
                <cnd-form-item label="付款方式2" prop="sSettlementPeriod" required>
                  <div class="dis">
                    <div
                      class="checkListson"
                      :class="
                        [
                          {
                            prohibited1:$route.query.disabledShow != 'add' ,
                            prohibited: form.sSettlementPeriod.value.paymentMethodTwo.includes('现款') && $route.query.disabledShow != 'add'
                          },
                          { colro: cjeckShow5 === 1 && form.sSettlementPeriod.value.paymentMethodTwo.includes('现款')}
                        ]"
                      @click="getCheckListson2(1)"
                    >现款</div>
                    <div
                      class="checkListson"
                      :class="[
                        {
                          prohibited1:$route.query.disabledShow != 'add' ,
                          prohibited: form.sSettlementPeriod.value.paymentMethodTwo.includes('承兑') && $route.query.disabledShow != 'add'
                        },
                        { colro: (cjeckShow6 === 2 && form.sSettlementPeriod.value.paymentMethodTwo.includes('承兑'))}
                      ]"
                      @click="getCheckListson2(2)"
                    >承兑</div>
                    <el-input
                      v-model="form.sSettlementPeriod.value.paymentMethodTwo"
                      input-align="right"
                      placeholder="请输入"
                    />
                  </div>
                </cnd-form-item>
              </template>
            </div>
          </el-row>
          <el-row>
            <div class="ml-20">
              <div class="ml-20">
                <div class="dis" @click="getHandwritten2(99)">
                  <div
                    class="handwritten-checkbox"
                    :class="
                      [{ handwritten: handwrittenFour == 99 || form.sCashDiscount.level1 },
                       form.sCashDiscount.level1 !== '' && $route.query.disabledShow != 'add' ? 'handwritten1' : 'handwritten2'
                      ]
                    "
                  />4、现金折扣
                </div>
                <div class="ml-20">
                  <div class="pl-18">
                    <div>
                      <div class="d-flex cash" @click="cashDiscount(1)">
                        <div
                          class="cash-checkbox"
                          :class="
                            [
                              {
                                checkedCash: form.sCashDiscount.level1 == 1
                              },
                              form.sCashDiscount.level1 == 1 && $route.query.disabledShow != 'add' ? 'disabledColor1' : 'disabledColor2'
                            ]
                          "
                        />
                        4.1、若需方合同支付方式从{支付方式}变更为银行承兑汇票；
                      </div>
                      <div class="cash-son">
                        <div class="d-flex cash-pt" @click="cashDiscount(1, 1)">
                          <div
                            class="cash-checkbox"
                            :class="
                              [
                                {
                                  checkedCash:
                                    form.sCashDiscount.level2 == '1' &&
                                    form.sCashDiscount.level1 == '1'
                                },
                                (form.sCashDiscount.level2 == '1' &&
                                  form.sCashDiscount.level1 == '1' && $route.query.disabledShow != 'add' )? 'disabledColor1' : 'disabledColor2'
                              ]
                            "
                          />
                          4.1.1、需方应按照{费率}/年支付从汇票转让日至汇票到期日之间的费用。
                        </div>
                        <div class="d-flex cash-pt" @click="cashDiscount(1, 2)">
                          <div
                            class="cash-checkbox"
                            :class="
                              [
                                {
                                  checkedCash:
                                    form.sCashDiscount.level2 == '2' &&
                                    form.sCashDiscount.level1 == '1'
                                },
                                ( form.sCashDiscount.level2 == '2' &&
                                  form.sCashDiscount.level1 == '1' && $route.query.disabledShow != 'add' )? 'disabledColor1' : 'disabledColor2'
                              ]
                            "
                          />
                          4.1.2、本合同签订后{几日起}天起，需方应按照 {费率}
                          /月（含税）支付费用，期限为{承兑期现天数}天。
                        </div>
                      </div>
                    </div>
                    <div>
                      <div class="d-flex cash" @click="cashDiscount(2)">
                        <div
                          class="cash-checkbox"
                          :class="
                            [
                              {
                                checkedCash:
                                  form.sCashDiscount.level1 == 2
                              },
                              ( form.sCashDiscount.level1 == 2 && $route.query.disabledShow != 'add' )? 'disabledColor1' : 'disabledColor2'
                            ]
                          "
                        />
                        4.2、若需方合同支付方式从{支付方式}变更为现款，则供方从收到现款之日起按下述方式（择一）给予现金折让，具体现金折让金额在结算单中一并处理；
                      </div>
                      <div class="cash-son">
                        <div class="d-flex cash-pt" @click="cashDiscount(2, 1)">
                          <div
                            class="cash-checkbox"
                            :class="
                              [
                                {
                                  checkedCash:
                                    form.sCashDiscount.level2 == '1' &&
                                    form.sCashDiscount.level1 == '2'
                                },
                                ( form.sCashDiscount.level2 == '1' &&
                                  form.sCashDiscount.level1 == '2' && $route.query.disabledShow != 'add' )? 'disabledColor1' : 'disabledColor2'
                              ]
                            "
                          />
                          4.2.1、本合同签订后 {几日起} 天起，以
                          {费率}/月（含税）计收现金折让金额，折让期限为{折让期现天数}天。
                        </div>
                        <div class="d-flex cash-pt" @click="cashDiscount(2, 2)">
                          <div
                            class="cash-checkbox"
                            :class="
                              [
                                {
                                  checkedCash:
                                    form.sCashDiscount.level2 == '2' &&
                                    form.sCashDiscount.level1 == '2'
                                },
                                ( form.sCashDiscount.level2 == '2' &&
                                  form.sCashDiscount.level1 == '2' && $route.query.disabledShow != 'add' )? 'disabledColor1' : 'disabledColor2'
                              ]
                            "
                          />
                          4.2.2、以{折让金额}元/吨计收现金折让金额。
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!--4、现金折扣 支付方式 -->
                <div v-if="form.sCashDiscount.level1">
                  <div>
                    <!-- v-if="fourArr[0] == '4.1'" -->
                    <cnd-form-item label="支付方式" prop="sCashDiscount" required>
                      <el-input
                        v-model="form.sCashDiscount.value.sPayType"
                        input-align="right"
                        placeholder="请输入"
                      />
                    </cnd-form-item>

                    <cnd-form-item
                      v-if="
                        form.sCashDiscount.level1 == '1' &&
                          form.sCashDiscount.level2 == '2'
                      "
                      label="几天起"
                      prop="sCashDiscount"
                      required
                    >
                      <el-input
                        v-model="form.sCashDiscount.value.startingDays"
                        input-align="right"
                        placeholder="请输入"
                      />
                    </cnd-form-item>

                    <cnd-form-item
                      v-if="
                        form.sCashDiscount.level1 == '1' &&
                          (form.sCashDiscount.level2 == '1' ||
                            form.sCashDiscount.level2 == '2')
                      "
                      label="费率"
                      prop="sCashDiscount"
                      required
                    >
                      <el-input
                        v-model="form.sCashDiscount.value.rate"
                        input-align="right"
                        placeholder="请输入"
                        type="Number"
                      >
                        <span slot="suffix">%</span>
                      </el-input>
                    </cnd-form-item>

                    <cnd-form-item
                      v-if="
                        form.sCashDiscount.level1 == '1' &&
                          form.sCashDiscount.level2 == '2'
                      "
                      label="承兑期限天数"
                      prop="sCashDiscount"
                      required
                    >
                      <el-input
                        v-model="form.sCashDiscount.value.acceptancePeriodDays"
                        input-align="right"
                        placeholder="请输入"
                      />
                    </cnd-form-item>
                  </div>
                  <div v-if="form.sCashDiscount.level1 == '2'">
                    <!-- <cnd-form-item label="支付方式" prop="sCashDiscount" required>
                    <el-input
                      v-model="form.sCashDiscount.value.sPayType"
                      input-align="right"
                      placeholder="请输入"
                    />
                    </cnd-form-item>-->

                    <cnd-form-item
                      v-if="
                        form.sCashDiscount.level1 == '2' &&
                          form.sCashDiscount.level2 == '1'
                      "
                      label="几天起"
                      prop="sCashDiscount"
                      required
                    >
                      <el-input
                        v-model="form.sCashDiscount.value.startingDays"
                        input-align="right"
                        placeholder="请输入"
                      />
                    </cnd-form-item>

                    <cnd-form-item
                      v-if="
                        form.sCashDiscount.level1 == '2' &&
                          form.sCashDiscount.level2 == '1'
                      "
                      label="费率"
                      prop="sCashDiscount"
                      required
                    >
                      <el-input
                        v-model="form.sCashDiscount.value.rate"
                        input-align="right"
                        placeholder="请输入"
                        type="Number"
                      >
                        <span slot="suffix">%</span>
                      </el-input>
                    </cnd-form-item>
                    <cnd-form-item
                      v-if="
                        form.sCashDiscount.level1 == '2' &&
                          form.sCashDiscount.level2 == '1'
                      "
                      label="折让期限天数"
                      prop="sCashDiscount"
                      required
                    >
                      <el-input
                        v-model="form.sCashDiscount.value.discountPeriodDays"
                        input-align="right"
                        placeholder="请输入"
                      />
                    </cnd-form-item>
                    <cnd-form-item
                      v-if="
                        form.sCashDiscount.level1 == '2' &&
                          form.sCashDiscount.level2 == '2'
                      "
                      label="折让金额"
                      prop="sCashDiscount"
                      required
                    >
                      <el-input
                        v-model="form.sCashDiscount.value.discountAmount"
                        input-align="right"
                        placeholder="请输入"
                        type="Number"
                      />
                    </cnd-form-item>
                  </div>
                </div>
              </div>
            </div>
          </el-row>
          <el-row class="ml-20">
            <div class="ml-20">
              <div class="dis" @click="getHandwritten(1)">
                <div
                  class="handwritten-checkbox"
                  :class="[{ handwritten: handwritten == 1 && $route.query.type == 'add' || form.sCommercialDiscount.level1 },
                           form.sCommercialDiscount.level1 != '' && $route.query.disabledShow != 'add' ? 'handwritten1' : 'handwritten2'
                  ]"
                />5、商业折让
              </div>
              <el-radio-group
                v-model="form.sCommercialDiscount.level1"
                class="ml-20"
                @change="getHandwritten1(1)"
              >
                <div
                  v-for="(item, index) in selectList.shopDiscount"
                  :key="index"
                  class="disabled-color radio-box"
                >
                  <!--  -->
                  <el-radio :label="item.labelName">
                    <div class="radio-span">{{ item.name }}</div>
                  </el-radio>
                </div>

                <div class="ml-20">
                  <el-radio-group
                    v-model="form.sCommercialDiscount.level2"
                    direction="vertical"
                    @change="getHandwritten1(2)"
                  >
                    <div
                      v-for="(item, index) in selectList.shopDiscountSon"
                      :key="index"
                      class="disabled-color radio-box"
                    >
                      <el-radio :label="item.labelName">
                        <div class="radio-span">{{ item.name }}</div>
                      </el-radio>
                    </div>
                  </el-radio-group>
                </div>
              </el-radio-group>
            </div>
          </el-row>

          <el-row class="ml-20">
            <!-- 结算 -->
            <el-row>
              <!-- -->
              <div class="ml-20">
                <div class="dis" @click="getHandwritten(2)">
                  <div
                    class="handwritten-checkbox"
                    :class="
                      [{ handwritten: handwritten == 2 || form.sSettlement.level1},
                       form.sSettlement.level1 != '' && $route.query.disabledShow != 'add' ? 'handwritten1' : 'handwritten2'
                      ]"
                  />5、结算
                </div>
                <el-radio-group
                  v-model="form.sSettlement.level1"
                  class="ml-20"
                  @change="getHandwritten1(3)"
                >
                  <div
                    v-for="(item, index) in selectList.settleAccount"
                    :key="index"
                    class="disabled-color radio-box"
                  >
                    <!--  -->
                    <el-radio :label="item.labelName">
                      <div class="radio-span">{{ item.name }}</div>
                    </el-radio>
                  </div>
                </el-radio-group>

                <div v-if="form.sSettlement.level1 == 1">
                  <cnd-form-item label="合同签订后几天内" prop="sSettlement" required>
                    <el-input
                      v-model="
                        form.sSettlement.value.withinDaysAfterContractSigning
                      "
                      placeholder="请输入"
                      input-align="right"
                    />
                  </cnd-form-item>

                  <cnd-form-item label="结算数量加价1" prop="sSettlement" required>
                    <el-switch
                      v-model="
                        form.sSettlement.value.settlementQuantityMarkupOne
                      "
                      :active-value="1"
                      :inactive-value="0"
                    />
                  </cnd-form-item>

                  <cnd-form-item
                    v-if="form.sSettlement.value.settlementQuantityMarkupOne"
                    label="加价1"
                    prop="sSettlement"
                    required
                  >
                    <el-input
                      v-model="form.sSettlement.value.markupOne"
                      placeholder="请输入"
                      type="Number"
                    />
                  </cnd-form-item>

                  <cnd-form-item
                    label="合同签订后几天及以上"
                    class="label-whites"
                    prop="sSettlement"
                    required
                  >
                    <el-input
                      v-model="
                        form.sSettlement.value.daysAfterContractSigningOrMore
                      "
                      placeholder="请输入"
                    />
                  </cnd-form-item>
                  <cnd-form-item label="结算数量加价2" prop="sSettlement" required>
                    <el-switch
                      v-model="
                        form.sSettlement.value.settlementQuantityMarkupTwo
                      "
                      :active-value="1"
                      :inactive-value="0"
                    />
                  </cnd-form-item>

                  <cnd-form-item
                    v-if="form.sSettlement.value.settlementQuantityMarkupTwo"
                    label="加价2"
                    prop="sSettlement"
                    required
                  >
                    <el-input
                      v-model="form.sSettlement.value.markupTwo"
                      placeholder="请输入"
                      type="Number"
                    />
                  </cnd-form-item>
                </div>

                <div v-if="form.sSettlement.level1 == 2">
                  <cnd-form-item label="固定日期1" prop="sSettlement" required>
                    <!-- <el-input
                      v-model="form.sSettlement.value.fixedDateOne"
                      placeholder="请输入"
                    />-->

                    <el-date-picker
                      v-model="form.sSettlement.value.fixedDateOne"
                      type="date"
                      placeholder="请选择付款日期1"
                      style="width: 100%"
                    />
                  </cnd-form-item>

                  <cnd-form-item label="合同签订后几天内" class="label-whites" prop="sSettlement" required>
                    <el-input
                      v-model="
                        form.sSettlement.value.withinDaysAfterContractSigning
                      "
                      placeholder="请输入"
                    />
                  </cnd-form-item>
                  <cnd-form-item label="加价1" prop="sSettlement" required>
                    <el-input
                      v-model="form.sSettlement.value.markupOne"
                      placeholder="请输入"
                      type="Number"
                    />
                  </cnd-form-item>

                  <cnd-form-item label="固定日期2" prop="sSettlement" required>
                    <!-- <el-input
                      v-model="form.sSettlement.value.fixedDateTwo"
                      placeholder="请输入"
                    />-->
                    <el-date-picker
                      v-model="form.sSettlement.value.fixedDateTwo"
                      type="date"
                      placeholder="请选择付款日期2"
                      style="width: 100%"
                    />
                  </cnd-form-item>
                  <cnd-form-item
                    label="合同签订后几天及以上"
                    class="label-whites"
                    prop="sSettlement"
                    required
                  >
                    <el-input
                      v-model="
                        form.sSettlement.value.daysAfterContractSigningOrMore
                      "
                      placeholder="请输入"
                    />
                  </cnd-form-item>
                  <cnd-form-item label="加价2" prop="sSettlement" required>
                    <el-input
                      v-model="form.sSettlement.value.markupTwo"
                      placeholder="请输入"
                      type="Number"
                    />
                  </cnd-form-item>
                </div>
                <div v-if="form.sSettlement.level1 == 3">
                  <cnd-form-item label="货物到达地点" prop="sSettlement" required>
                    <el-input v-model="form.sSettlement.value.deliveryLocation" placeholder="请输入" />
                  </cnd-form-item>
                  <cnd-form-item label="合同签订后几天内" class="label-whites" prop="sSettlement" required>
                    <el-input
                      v-model="
                        form.sSettlement.value.withinDaysAfterContractSigning
                      "
                      placeholder="请输入"
                    />
                  </cnd-form-item>
                  <cnd-form-item label="结算数量加价1" class="label-whites" prop="sSettlement" required>
                    <el-switch
                      v-model="
                        form.sSettlement.value.settlementQuantityMarkupOne
                      "
                      :active-value="1"
                      :inactive-value="0"
                    />
                  </cnd-form-item>

                  <cnd-form-item
                    v-if="form.sSettlement.value.settlementQuantityMarkupOne"
                    label="加价1"
                    prop="sSettlement"
                    required
                  >
                    <el-input
                      v-model="form.sSettlement.value.markupOne"
                      placeholder="请输入"
                      type="Number"
                    />
                  </cnd-form-item>

                  <cnd-form-item
                    label="合同签订后几天及以上"
                    class="label-whites"
                    prop="sSettlement"
                    required
                  >
                    <el-input
                      v-model="
                        form.sSettlement.value.daysAfterContractSigningOrMore
                      "
                      placeholder="请输入"
                    />
                  </cnd-form-item>

                  <cnd-form-item label="结算数量加价2" class="label-whites" prop="sSettlement" required>
                    <el-switch
                      v-model="
                        form.sSettlement.value.settlementQuantityMarkupTwo
                      "
                      :active-value="1"
                      :inactive-value="0"
                    />
                  </cnd-form-item>

                  <cnd-form-item
                    v-if="form.sSettlement.value.settlementQuantityMarkupTwo"
                    label="加价2"
                    prop="sSettlement"
                    required
                  >
                    <el-input
                      v-model="form.sSettlement.value.markupTwo"
                      placeholder="请输入"
                      type="Number"
                    />
                  </cnd-form-item>
                </div>

                <div v-if="form.sSettlement.level1 == 4">
                  <cnd-form-item label="加价" prop="markup" required>
                    <el-input
                      v-model="form.sSettlement.value.markup"
                      placeholder="请输入"
                      type="Number"
                    />
                  </cnd-form-item>
                </div>

                <div v-if="form.sSettlement.level1 == 5">
                  <cnd-form-item label="合约" prop="sSettlement" required>
                    <el-input v-model="form.sSettlement.value.contract" placeholder="请输入" />
                  </cnd-form-item>

                  <cnd-form-item label="价格" prop="sSettlement" required>
                    <el-input
                      v-model="form.sSettlement.value.price"
                      placeholder="请输入"
                      type="Number"
                    />
                  </cnd-form-item>

                  <cnd-form-item label="点价日期截止" prop="sSettlement" required>
                    <!-- <el-input
                      v-model="form.sSettlement.value.pricingDateEnd"
                      placeholder="请输入"
                    />-->
                    <el-date-picker
                      v-model="form.sSettlement.value.pricingDateEnd"
                      type="date"
                      placeholder="请选择付款日期2"
                      style="width: 100%"
                    />
                  </cnd-form-item>

                  <!-- <cnd-form-item
                required
                label="点价日期截止"
                right-icon="calendar"
                prop="sSettlementPeriod"
                :clickable="true"
                placeholder="请选择点价日期截止"
                @onClick="signDateShow = true"
              >
                <div slot="right">
                  <text
                    :class="{
                      'picker-text': !form.sSettlement.value.pricingDateEnd
                    }"
                  >
                    {{
                      formatDate(form.sSettlement.value.pricingDateEnd) ||
                      "请选择日期"
                    }}
                  </text>
                </div>
                <cube-date-picker
                  v-model="form.sSettlement.value.pricingDateEnd"
                  :visible="signDateShow"
                  mode="date"
                  is-closable
                  @onCancel="signDateShow = false"
                  @onConfirm="signDateConfirm('点价', $event)"
                />
                  </cnd-form-item>-->

                  <cnd-form-item label="点价天数" prop="sSettlement" required>
                    <el-input
                      v-model="form.sSettlement.value.pricingDays"
                      placeholder="请输入"
                      type="Number"
                    />
                  </cnd-form-item>

                  <cnd-form-item label="基差" prop="sSettlement" required>
                    <el-input
                      v-model="form.sSettlement.value.basis"
                      placeholder="请输入"
                      type="Number"
                    />
                  </cnd-form-item>
                  <cnd-form-item label="提货天数" prop="sSettlement" required>
                    <el-input
                      v-model="form.sSettlement.value.pickupDays"
                      placeholder="请输入"
                      type="Number"
                    />
                  </cnd-form-item>

                  <cnd-form-item label="最迟点价日" prop="sSettlement" required>
                    <!-- <el-input
                      v-model="form.sSettlement.value.latestPricingDate"
                      placeholder="请输入"
                    />-->
                    <el-date-picker
                      v-model="form.sSettlement.value.latestPricingDate"
                      type="date"
                      placeholder="请选择付款日期2"
                      style="width: 100%"
                    />
                  </cnd-form-item>
                </div>
              </div>
            </el-row>
          </el-row>

          <el-row class="ml-20">
            <div class="weight-size">八、需方指定送达地址及联系方式：</div>
            <div class="ml-20">
              <cnd-form-item label="收件人" prop="sAddressee">
                <el-input v-model="form.sAddressee" input-align="right" placeholder="请输入" />
              </cnd-form-item>

              <cnd-form-item
                class="label-whites"
                label="与需方的关系"
                prop="sNeedSideRelationshipPosition"
              >
                <el-input
                  v-model="form.sNeedSideRelationshipPosition"
                  input-align="right"
                  placeholder="请输入"
                />
              </cnd-form-item>

              <cnd-form-item label="邮递地址" prop="sPostalAddress">
                <el-input v-model="form.sPostalAddress" input-align="right" placeholder="请输入" />
              </cnd-form-item>

              <cnd-form-item label="电话/手机号码" prop="sPhoneNumber">
                <el-input v-model="form.sPhoneNumber" input-align="right" placeholder="请输入" />
              </cnd-form-item>
              <cnd-form-item
                label="电子邮箱"
                prop="sEmail"
                required
              >
                <el-input
                  v-model="form.sEmail"
                  input-align="right"

                  placeholder="请输入"
                />
              </cnd-form-item>

              <cnd-form-item label="传真" prop="sFax">
                <el-input v-model="form.sFax" input-align="right" placeholder="请输入" />
              </cnd-form-item>
            </div>
          </el-row>

          <el-row class="ml-20">
            <div class="weight-size">九、供方指定邮箱：</div>
            <div class="ml-20">
              <cnd-form-item label="指定邮箱" prop="sCommonlyClause" required>
                <el-input v-model="form.sAppointEmail" input-align="right" placeholder="请输入" />
              </cnd-form-item>
            </div>
          </el-row>
          <el-row class="ml-20">
            <div class="weight-size">十、其他</div>
            <div class="ml-20">
              <cnd-form-item label="关联公司" prop="sRelatedCompanies">
                <el-input v-model="form.sRelatedCompanies" input-align="right" placeholder="请输入" />
              </cnd-form-item>
            </div>
          </el-row>

          <el-row class="ml-20">
            <div class="weight-size">一般条款</div>
            <div class="ml-20">
              <div>5.4、货物和账务核对：供方于每月{供方于每月几日前}日前通过合同约定的送达方式或其他方式将库存货物数量及结算单等账目明细清单发送给需方，需方应在每月{需方应在每月几日前}日前审核完毕，若需方未于每月{需方未于每月几日前}日前提出书面异议的，则视为库存货物数量及结算单等账目明细清单无误。</div>

              <cnd-form-item label="供方于每月几日前" class="label-whites" prop="sCommonlyClause" required>
                <el-input
                  v-model="
                    form.sCommonlyClause.value.supplierDeadlineBeforeEndOfMonth
                  "
                  input-align="right"
                  placeholder="请输入"
                />
              </cnd-form-item>

              <cnd-form-item label="需方应在每月几日前" class="label-whites" prop="sCommonlyClause" required>
                <el-input
                  v-model="
                    form.sCommonlyClause.value.buyerDeadlineBeforeEndOfMonth
                  "
                  input-align="right"
                  placeholder="请输入"
                />
              </cnd-form-item>
              <cnd-form-item label="需方未于每月几日前" class="label-whites" prop="sCommonlyClause" required>
                <el-input
                  v-model="
                    form.sCommonlyClause.value
                      .buyerMissedDeadlineBeforeEndOfMonth
                  "
                  input-align="right"
                  placeholder="请输入"
                />
              </cnd-form-item>

              <div>
                <el-radio-group v-model="form.sCommonlyClause.level1">
                  <!-- {{ form.sCommonlyClause.level1 }} -->
                  <div class="disabled-color radio-box">
                    <el-radio :label="1" style="color: #000" @click.native.prevent="clickitem(1)">
                      <div class="radio-span">
                        <div class="disabled-color-bg" />5.5、对于供方及供方关联公司与需方签订的全部合同项下需方所付的任意款项，需方同意供方及供方关联公司有权按照合同履约情况自行分配回款金额并自行决定每笔款项的用途和冲抵顺序，包括但不限于用于冲抵保证金、追加保证金、
                        货款、超期结算费
                      </div>
                    </el-radio>
                  </div>
                </el-radio-group>
              </div>
            </div>
          </el-row>

          <!-- <el-row   class="ml-20">
            <div class="weight-size">需x
              <cnd-form-item label="联系电话" prop="sNeedSidePhoneNumber">
                <el-input
                  v-model="form.sNeedSidePhoneNumber"
                  input-align="right"
                  placeholder="请输入"
                />
              </cnd-form-item>
              <cnd-form-item label="开户银行" prop="sNeedSideBankOfDeposit">
                <el-input
                  v-model="form.sNeedSideBankOfDeposit"
                  input-align="right"
                  placeholder="请输入"
                />
              </cnd-form-item>
              <cnd-form-item label="帐号" prop="sNeedSideAccounts">
                <el-input v-model="form.sNeedSideAccounts" input-align="right" placeholder="请输入" />
              </cnd-form-item>
            </div>
          </el-row  >
          <el-row   class="ml-20">
            <div class="weight-size">供方信息</div>
            <div class="ml-20">
              <cnd-form-item label="法定/授权代表人" class="label-whites" prop="premium">
                <el-input
                  v-model="form.sSupplySideRepresentative"
                  input-align="right"
                  placeholder="请输入"
                />
              </cnd-form-item>
              <cnd-form-item label="联系电话" prop="premium">
                <el-input
                  v-model="form.sSupplySidePhoneNumber"
                  input-align="right"
                  placeholder="请输入"
                />
              </cnd-form-item>
              <cnd-form-item label="开户银行" prop="premium">
                <el-input
                  v-model="form.sSupplySideBankOfDeposit"
                  input-align="right"
                  placeholder="请输入"
                />
              </cnd-form-item>
              <cnd-form-item label="帐号" prop="premium">
                <el-input v-model="form.sSupplySideAccounts" input-align="right" placeholder="请输入" />
              </cnd-form-item>
            </div>
          </el-row  >-->
        </cnd-form-card>
      </cnd-form-card-list>
    </el-form>
  </div>
</template>

<script>
import { getSettlementRateDetail } from '@/api/preContract/preContract'

// import { getPreContractDetail } from '@/api/preContract/aggregateMethodContract'

import { getDictet } from '@/api/logistics/saleDelivery/saleorder'
import steelTradeAggrid from '@/components/steelTradeAggrid'
// import { getCnDitc } from '@/utils/common'
// import { SteelFormat } from 'cnd-horizon-utils'

export default {
  name: 'ClauseInformation',
  components: {
    steelTradeAggrid
  },
  props: {
    sAutoGenerateProject: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      isEmpty: false,
      handwritten: 1,
      handwrittenFour: 0,
      checkList: [],
      marginDeposit1: [],
      marginDeposit2: [],
      selectList: {
        contractList: [
          {
            name: '显示',
            labelName: '1'
          },
          {
            name: '不显示',
            labelName: '0'
          }
        ],
        // 交货地点
        deliver: [
          {
            name: '交货仓库/码头为：{交货仓库/码头}，需方自提/供方代办运输。',
            labelName: '1'
          },
          {
            name: '交货地点：{交货地点}，供方代办运输。',
            labelName: '2'
          }
        ],
        // 保险
        insurance: [
          {
            name: '不购买保险。',
            labelName: '1'
          },
          {
            name: '保险由供方负责投保，相应的保费预计{保费}元/吨，由需方承担。',
            labelName: '2'
          }
        ],
        // 保险金
        insuranceBenefit: [
          {
            name: '暂定价的{保证金比例}；',
            labelName: '1'
          },
          {
            name: '具体金额{保证金金额}，支付时间：{支付时间}。',
            labelName: '2'
          }
          // {
          //   name: '2.保证金追加：合同签约后，若上海期货交易所{合约}合约期货价格上涨超{上涨比例}（以供方认定为准），需方应追加未点价部分相应涨价金额的保证金,追加保证金比例为涨价金额的{追加保证金比例}；',
          //   labelName: '3'
          // },
          // {
          //   name: '3. 需方应在点价后来款提货，保证金只能作为最后一笔货款冲抵。',
          //   labelName: '4'
          // },
        ],
        insuranceBenefit1: [
          {
            name: '4.1 若客户先提货后点价，需方应按照供方确认的暂定单价{暂定单价比例}付清提货部分货款后提货。且不可使用前述保证金，并且需要在提货后{提货天数}天内完成点价。',
            labelName: '5'
          },
          {
            name: '4.2 若客户先提货后点价，需方应按照供方确认的暂定单价＋{暂定单价加价金额}元/吨，付清提货部分货款后提货。且不可使用前述保证金，并且需要在提货后{提货天数}天内完成点价。',
            labelName: '6'
          }
        ],
        // 价格条款
        priceClause: [
          {
            name: '本合同价格为现汇锁定一票制价格。',
            labelName: '1'
          },
          {
            name: '本合同价格为银行承兑汇票锁定一票制价格。',
            labelName: '2'
          },
          {
            name: '本合同价格为{付款方式}锁定一票制价格。',
            labelName: '3'
          },
          {
            name: '本合同价格为暂定价，若有运杂费按实结算，最终以供方结算单为准。',
            labelName: '4'
          },
          {
            name: '本合同价格为暂定价，结算时参照网价/{参考网价} 钢厂售价下浮 {销售下浮金额}元进行，最终以供方结算单为准。',
            labelName: '5'
          }
        ],
        // 结算期
        result: [
          {
            name: '需方应于{付款日期}之日起{结算天数}天内结算。',
            labelName: '1'
          },
          {
            name: '需方应于货物到交货仓库/码头起{结算天数}天内结算。',
            labelName: '2'
          },
          {
            name: '需方应于{付款日期}前付清全部款项。',
            labelName: '3'
          },

          {
            name: '需方应于{付款日期}之日起{结算天数}天内以{付款方式}等方式付清{付款比例}%款项， {付款日期2}之日起{结算天数2}天内以{付款方式2}等方式付清剩余款项后提清货物（允许分批付款提货），否则视为需方违约，相关违约责任参照商务条款第七条及相关一般条款执行。如果付款时间及金额有调整，以供方确认为准。',
            labelName: '4'
          },
          {
            name: '需方应于{结算天数}天内以{付款方式}等方式付清全部货款及费用后提清货物（允许分批付款提货），否则视为需方违约，相关违约责任参照商务条款第七条及相关一般条款执行。',
            labelName: '5'
          }
        ],
        shopDiscount: [
          {
            name: '合同签订后{提前天数}日',
            labelName: '1'
          },
          {
            name: '货物到港后{提前天数}天',
            labelName: '2'
          },
          {
            // 选中这一个，下面还可以选中
            name: '货物进仓后{提前天数}天',
            labelName: '3'
          }
        ],
        shopDiscountSon: [
          {
            name: '标准一：提前提货天数×优惠费率{标准一费率}/日×（总价-保证金）{标准二提前天数止})×{标准二费率}×（总价-保证金），提前提货天数：合同签约后第{标准一提前天数止}-{标准二提前天数止}日至实际提货日的天数间隔；标准二：提前提货天数×优惠费率{标准二费率}/日×（总价-保证金），提前提货天数：实际提货日至合同签约后第{标准一提前天数止}日的天数间隔；',
            labelName: '1'
          },
          {
            name: '标准一： {标准二提前天数止}×优惠费率{标准二费率}/日×（总价-保证金）；标准二：提前付款天数×优惠费率{标准二费率}/日×提前付款金额（不含保证金），提前付款天数：合同签约后第{标准一提前天数止}日至实际付款日的天数间隔；',
            labelName: '2'
          },
          {
            name: '标准一：提前付款天数×优惠费率{标准一费率}/日×提前付款金额+{标准二提前天数止}×{标准二费率}×提前付款金额，提前付款天数：实际付款日至合同签约后第{标准一提前天数止}-{标准二提前天数止}日的天数间隔；标准二：提前付款天数×优惠费率{标准二费率}/日×提前付款金额，提前付款天数：实际付款日至合同签约后第{标准一提前天数止}日的天数间隔；',
            labelName: '3'
          },
          {
            name: '标准一： {标准二提前天数止}×优惠费率{标准二费率}/日×提前付款金额（不含保证金）； 标准二：提前付款天数×优惠费率{标准二费率}/日×提前付款金额（不含保证金），提前付款天数：实际付款日至合同签约后第{标准一提前天数止}日的天数间隔；',
            labelName: '4'
          }
        ],
        // 结算
        settleAccount: [
          {
            name: '按吨位加价（固定日期后结算）',
            labelName: '1'
          },
          {
            name: '分批收保证金多个固定日期分批加价',
            labelName: '2'
          },
          {
            name: '按吨位加价（货后结算）',
            labelName: '3'
          },
          {
            name: '后结算固定加价销售',
            labelName: '4'
          },
          {
            name: '点价结算',
            labelName: '5'
          }
        ]
      },
      headerCount: null,
      footerCount: null,
      searchInfo: null,
      getGridApi: () => {
        return this.$refs.aggrid.gridApi
      },
      activeCollapseName: ['2'],
      options: {
        'msb.deposit.pay.type': '',
        'esc.settle.interest.start': '',
        'base.yes-no': '',
        'msb.return.interest.type': '',
        'msb.transport.mode': '',
        'msb.cost.bearing': ''
      },
      form: {
        sRelatedCompanies: '关联公司',
        sFreight: {
          level1: '1',
          value: ''
        },
        sDeliveryLocation: {
          level1: '1',
          transportMode: '',
          value: '',
          logisticsFeesType: ''
        },
        sMoreOrLess: '10',
        sPoundDifference: '0.3',
        sInsurance: {
          level1: '1'
        },
        sAppointEmail: '',
        sBond: {
          level1: [], // 因为是多选这个是一个数组
          value: {
            depositRatio: '',
            paymentTime: '本合同签订之日起2个工作日内',
            bondMoney: '',
            contract: '',
            increaseRatio: '',
            additionalMarginRatio: '',
            deliveryDays: '',
            provisionalUnitPriceRatio: '',
            provisionalUnitPriceMarkup: ''
          }
        },
        sPriceTerms: {
          level1: '4',
          value: {
            paymentMethod: '现款或承兑',
            referenceOnlinePrice: '',
            salesDecreaseAmount: ''
          }
        },
        transportMode: '',
        sSettlementPeriod: {
          level1: '5',
          value: {
            paymentDate: '',
            withinDays: '',
            referenceOnlinePrice: '',
            salesDecreaseAmount: '',
            paymentMethod: '现款或承兑'
          }
        },
        sSettlement: {
          level1: '',
          value: {}
        },
        sCashDiscount: {
          level1: '',
          value: {
            paymentDate: '',
            referenceOnlinePrice: '',
            salesDecreaseAmount: ''
          }
        },
        sCommercialDiscount: {
          level1: '1',
          level2: '4',
          value: {}
        },
        sCommonlyClause: {
          level1: '',
          value: {
            supplierDeadlineBeforeEndOfMonth: '28',
            buyerDeadlineBeforeEndOfMonth: '28',
            buyerMissedDeadlineBeforeEndOfMonth: '28'

          }
        },
        sNeedSideRepresentative: '',
        sEarnestMoneyPayWay: undefined,
        sEarnestMoneyPercentage: undefined,
        sEarnestMoneyAmount: undefined,
        sInterestStartWay: undefined,
        sDaysOfSettlement: undefined,
        sSettlementInterval: undefined,
        sCashRebate: undefined,
        sCashRebateWay: undefined,
        sCashRebatePercentage: undefined,
        sCashTonRebate: undefined,
        sAcceptanceDiscount: undefined,
        sAcceptanceDiscountWay: undefined,
        sAcceptanceDiscountPercentage: undefined,
        sAcceptanceTonDiscount: undefined
      },
      rowData: [],
      lastNum: 0,
      preNum: 0,
      fourArr: [],
      formRules: {
        sDeliveryLocation: [
          { required: true, message: '请输入', trigger: 'change' }
        ],
        sMoreOrLess: [{ required: true, message: '请输入', trigger: 'change' }],
        sPoundDifference: [
          { required: true, message: '请输入', trigger: 'change' }
        ],
        sInsurance: [{ required: true, message: '请输入', trigger: 'change' }],
        sBond: [{ required: true, message: '请输入', trigger: 'change' }],
        sPriceTerms: [{ required: true, message: '请输入', trigger: 'change' }],
        sSettlementPeriod: [
          { required: true, message: '请输入', trigger: 'change' }
        ],
        sCashDiscount: [
          { required: true, message: '请输入', trigger: 'change' }
        ],
        sSettlement: [{ required: true, message: '请输入', trigger: 'change' }],
        sAppointEmail: [
          { required: true, message: '请输入', trigger: 'change' }
        ],
        sEmail: [
          { required: true, message: '需方电子邮箱必填', trigger: 'change' }
        ],
        sCommonlyClause: [
          { required: true, message: '请输入', trigger: 'change' }
        ]
      },
      cjeckShow2: 2,
      cjeckShow1: 1,
      cjeckShow3: 1,
      cjeckShow4: 2,
      cjeckShow5: 1,
      cjeckShow6: 2,
      dataEcho: 0
    }
  },
  coputed: {
    transportMode() {
      return this.options['msb.transport.mode'] &&
        this.options['msb.transport.mode'].length &&
        this.options['msb.transport.mode'].find(
          (item) =>
            item.sCodeValue === this.form.sDeliveryLocation.transportMode
        )
        ? this.options['msb.transport.mode'].find(
          (item) =>
            item.sCodeValue === this.form.sDeliveryLocation.transportMode
        ).sCodeName
        : ''
    },
    logisticsFeesType() {
      let str = ''
      if (
        this.form.sDeliveryLocation &&
        this.form.sDeliveryLocation.logisticsFeesType &&
        this.form.sDeliveryLocation.logisticsFeesType.length
      ) {
        for (
          let index = 0;
          index < this.form.sDeliveryLocation.logisticsFeesType.length;
          index++
        ) {
          const element = this.form.sDeliveryLocation.logisticsFeesType[index]
          str += this.options['msb.cost.bearing'].find(
            (item) => item.sCodeValue === element
          )
            ? this.options['msb.cost.bearing'].find(
              (item) => item.sCodeValue === element
            ).sCodeName + '、'
            : ''
        }
      }
      return str.slice(0, -1)
    }
  },
  watch: {
    sAutoGenerateProject(val) {
      if (val === '0') {
        this.form.sDeliveryLocation.transportMode = ''
        this.form.sDeliveryLocation.logisticsFeesType = ''
      }
    },
    'form.sBond.level1'(val) {
      if (this.dataEcho !== 2) {
        this.$nextTick(() => {
          console.log('this.form?.sBond.level1````````', val)
          this.form.sBond.value = JSON.parse(
            JSON.stringify({
              depositRatio: val.includes('1') ? 10 : '',
              paymentTime: '本合同签订之日起2个工作日内',
              bondMoney: '',
              contract: '',
              increaseRatio: '',
              additionalMarginRatio: '',
              deliveryDays: '',
              provisionalUnitPriceRatio: '',
              provisionalUnitPriceMarkup: ''
            })
          )
        })
        console.log('this.form?.sBond.value.depositRatio````````', this.form.sBond.value.depositRatio)
        this.$forceUpdate()
      } else {
        setTimeout(() => {
          this.dataEcho = 0
          this.$forceUpdate()
        }, 2000)
      }
    },
    'form.sPriceTerms.level1'(n) {
      if (this.dataEcho !== 2) {
        this.$nextTick(() => {
          this.cjeckShow1 = 1
          this.cjeckShow2 = 2
          this.form.sPriceTerms.value = JSON.parse(
            JSON.stringify({
              referenceOnlinePrice: '',
              paymentMethod: n + '' === '3' ? '现款或承兑' : '',
              salesDecreaseAmount: ''
            })
          )
        })
      }
    },
    'form.sSettlementPeriod.level1'(n) {
      if (this.dataEcho !== 2) {
        this.$nextTick(() => {
          this.cjeckShow5 = 1
          this.cjeckShow6 = 2
          this.cjeckShow3 = 1
          this.cjeckShow4 = 2
          this.form.sSettlementPeriod.value = JSON.parse(
            JSON.stringify({
              paymentDate: '',
              withinDays: '',
              paymentMethod:
                n + '' === '4' || n + '' === '5' ? '现款或承兑' : '',
              paymentRatio: '',
              paymentDateTwo: '',
              withinDaysTwo: '',
              paymentMethodTwo: n + '' === '4' ? '现款或承兑' : ''
            })
          )
        })
        this.$forceUpdate()
      }
    },
    'form.sSettlement.level1'(n) {
      if (this.dataEcho !== 2) {
        this.$nextTick(() => {
          this.form.sSettlement.value = JSON.parse(
            JSON.stringify({
              withinDaysAfterContractSigning: '',
              settlementQuantityMarkupOne: '',
              markupOne: '',
              daysAfterContractSigningOrMore: '',
              settlementQuantityMarkupTwo: '',
              markupTwo: '',
              fixedDateOne: '',
              contract: '',
              markup: '',
              fixedDateTwo: '',
              deliveryLocation: '',
              price: '',
              pricingDateEnd: '',
              basis: '',
              pricingDays: '',
              latestPricingDate: ''
            })
          )
        })
      } else {
        this.dataEcho = 0
        this.$forceUpdate()
      }
    },
    deep: true
  },
  created() {
    // this.getDetail()
    getDictet([
      'msb.deposit.pay.type',
      'esc.settle.interest.start',
      'base.yes-no',
      'msb.return.interest.type',
      'msb.transport.mode',
      'msb.cost.bearing'
    ])
      .then((result) => {
        this.options['msb.deposit.pay.type'] = result.data[0].dicts
        this.options['esc.settle.interest.start'] = result.data[1].dicts
        this.options['base.yes-no'] = result.data[2].dicts
        this.options['msb.return.interest.type'] = result.data[3].dicts
        this.options['msb.transport.mode'] = result.data[4].dicts
        this.options['msb.cost.bearing'] = result.data[5].dicts
      })
      .catch(() => {})
  },
  methods: {
    childNoDiscounts() {
      this.handwritten = ''
      this.form.sCommercialDiscount.level1 = ''
      this.form.sCommercialDiscount.level2 = ''
    },
    clickitem(e) {
      if (this.$route.query.disabledShow !== 'add') return
      e === this.form.sCommonlyClause.level1 ? this.form.sCommonlyClause.level1 = 0 : this.form.sCommonlyClause.level1 = e
    },
    clickitem1(e) {
      if (this.$route.query.disabledShow !== 'add') return
      e === this.marginDeposit1 ? this.marginDeposit1 = '' : this.marginDeposit1 = e
      this.getMarginDeposit1(this.marginDeposit1)
      this.$forceUpdate()
    },
    clickitem2(e) {
      if (this.$route.query.disabledShow !== 'add') return
      e === this.marginDeposit2 ? this.marginDeposit2 = '' : this.marginDeposit2 = e
      this.getMarginDeposit2(this.marginDeposit2)
      this.$forceUpdate()
    },
    getCheckListson(e) {
      if (this.$route.query.disabledShow !== 'add') return
      if (e === 1) {
        this.cjeckShow1 = this.cjeckShow1 === e ? 0 : e
        this.form.sPriceTerms.value.paymentMethod = !this.cjeckShow2
          ? '现款'
          : '承兑'
      } else {
        this.cjeckShow2 = this.cjeckShow2 === e ? 0 : e
        this.form.sPriceTerms.value.paymentMethod = !this.cjeckShow1
          ? '承兑'
          : '现款'
      }

      if (this.cjeckShow1 === 1 && this.cjeckShow2 === 2) {
        this.form.sPriceTerms.value.paymentMethod = '现款或承兑'
      }
      if (this.cjeckShow1 === 0 && this.cjeckShow2 === 0) {
        this.form.sPriceTerms.value.paymentMethod = ''
      }
    },
    getCheckListson1(e) {
      if (this.$route.query.disabledShow !== 'add') return
      if (e === 1) {
        this.cjeckShow3 = this.cjeckShow3 === e ? 0 : e
        this.form.sSettlementPeriod.value.paymentMethod = !this.cjeckShow4
          ? '现款'
          : '承兑'
      } else {
        this.cjeckShow4 = this.cjeckShow4 === e ? 0 : e
        this.form.sSettlementPeriod.value.paymentMethod = !this.cjeckShow3
          ? '承兑'
          : '现款'
      }
      if (this.cjeckShow3 === 1 && this.cjeckShow4 === 2) {
        this.form.sSettlementPeriod.value.paymentMethod = '现款或承兑'
      }
      if (this.cjeckShow3 === 0 && this.cjeckShow4 === 0) {
        this.form.sSettlementPeriod.value.paymentMethod = ''
      }
    },
    getCheckListson2(e) {
      if (this.$route.query.disabledShow !== 'add') return
      if (e === 1) {
        this.cjeckShow5 = this.cjeckShow5 === e ? 0 : e
        this.form.sSettlementPeriod.value.paymentMethodTwo = !this.cjeckShow6
          ? '现款'
          : '承兑'
      } else {
        this.cjeckShow6 = this.cjeckShow6 === e ? 0 : e
        this.form.sSettlementPeriod.value.paymentMethodTwo = !this.cjeckShow5
          ? '承兑'
          : '现款'
      }
      if (this.cjeckShow5 === 1 && this.cjeckShow6 === 2) {
        this.form.sSettlementPeriod.value.paymentMethodTwo = '现款或承兑'
      }
      if (this.cjeckShow5 === 0 && this.cjeckShow6 === 0) {
        this.form.sSettlementPeriod.value.paymentMethodTwo = ''
      }
    },

    // 接受兄弟之间传值
    clickDelivery(el) {
      this.$nextTick(() => {
        this.form.sDeliveryLocation.value = el
      })
      this.$message.success('设置成功')
    },
    clickSemailSon(el) {
      this.$nextTick(() => {
        this.form.sAppointEmail = el
      })
    },
    // 获取详情数据sDeliveryLocations
    async getInfoData(em) {
      this.dataEcho = 2
      // const jsonStringify = [
      //   'sFreight',
      //   'sDeliveryLocation',
      //   'sInsurance',
      //   'sBond',
      //   'sPriceTerms',
      //   'sSettlementPeriod',
      //   'sCashDiscount',
      //   'sCommercialDiscount',
      //   'sSettlement',
      //   'sCommonlyClause'
      // ]

      em.sFreight = JSON.parse(em.sFreight)
      em.sDeliveryLocation = JSON.parse(em.sDeliveryLocation)
      em.sInsurance = JSON.parse(em.sInsurance)
      em.sBond = JSON.parse(em.sBond)
      em.sPriceTerms = JSON.parse(em.sPriceTerms)
      em.sSettlementPeriod = JSON.parse(em.sSettlementPeriod)
      em.sSettlement = JSON.parse(em.sSettlement)
      em.sCashDiscount = JSON.parse(em.sCashDiscount)
      em.sCommercialDiscount = JSON.parse(em.sCommercialDiscount)
      em.sCommonlyClause = JSON.parse(em.sCommonlyClause)

      // jsonStringify.forEach((el) => {
      //   if (em[el]) {
      //     em[el] = JSON.parse(em[el])
      //   }
      // })
      this.$nextTick(() => {
        const infoData = { ...this.form, ...em }
        this.form = JSON.parse(JSON.stringify(infoData))

        // console.log('this.form?.sBond.level1', this.form?.sBond.level1)

        // 给保证金赋值 marginDeposit1 1，2 -- checkList:[3,4] --  marginDeposit2: 5,6
        if (this.form?.sBond.level1.length > 0) {
          this.form?.sBond.level1.forEach((bond) => {
            if (Number(bond) === 1 || Number(bond) === 2) {
              this.marginDeposit1 = bond
            }
            if (Number(bond) === 3 || Number(bond) === 4) {
              this.checkList.push(bond)
            }
            if (Number(bond) === 5 || Number(bond) === 6) {
              this.marginDeposit2 = bond
            }
          })
        }
      })
    },
    checkValues(obj) {
      this.isEmpty = true
      console.log(obj.sBond.level1, '105555555555')
      const sBondLevel1 = obj.sBond.level1.filter(item => item !== 0 && item !== '')
      if (
        (!obj.sDeliveryLocation.transportMode &&
          this.sAutoGenerateProject + '' === '1') ||
        !obj.sDeliveryLocation.value ||
        (obj.sDeliveryLocation.logisticsFeesType.length < 1 &&
          this.sAutoGenerateProject + '' === '1')
      ) {
        this.$message.error('请检查交货地点是否有未填写值')
        return
      } else if (!obj.sMoreOrLess || !obj.sPoundDifference) {
        this.$message.error('请检查三、合理损耗下是否有未填写值')
        return
      } else if (
        String(obj.sInsurance.level1) === '2' &&
        !obj.sInsurance.value
      ) {
        this.$message.error('请检查四、保险下是否有未填写值')
        return
      } else if (sBondLevel1.length <= 0) {
        this.$message.error('请检查 五、保证金：下是否有未填写值')
        return
      }
      if (obj.sBond.level1.length > 0) {
        if (obj.sBond.level1.includes('1') && !obj.sBond.value.depositRatio) {
          this.$message.error('请检查 五、保证金：下是否有未填写值')
          return
        }
        if (obj.sBond.level1.includes('2')) {
          if (!obj.sBond.value.bondMoney || !obj.sBond.value.paymentTime) {
            this.$message.error('请检查 五、保证金：下是否有未填写值')
            return
          }
        }
        if (obj.sBond.level1.includes('3')) {
          if (
            !obj.sBond.value.contract ||
            !obj.sBond.value.increaseRatio ||
            !obj.sBond.value.additionalMarginRatio
          ) {
            this.$message.error('请检查 五、保证金：下是否有未填写值')
            return
          }
        }
        if (obj.sBond.level1.includes('5')) {
          if (
            !obj.sBond.value.provisionalUnitPriceRatio ||
            !obj.sBond.value.deliveryDays
          ) {
            this.$message.error('请检查 五、保证金：是否有未填写值')
            return
          }
        }
        if (obj.sBond.level1.includes('6')) {
          if (
            !obj.sBond.value.provisionalUnitPriceMarkup ||
            !obj.sBond.value.deliveryDays
          ) {
            this.$message.error('请检查 五、保证金：是否有未填写值')
            return
          }
        }
        this.isEmpty = true
        if (
          obj.sPriceTerms.level1 + '' === '3' &&
          !obj.sPriceTerms.value.paymentMethod
        ) {
          this.$message.error(
            '请检查六、结算期及结算方式-1、价格条款 下是否有未填写值'
          )
          return
        } else if (
          obj.sPriceTerms.level1 + '' === '5' &&
          (!obj.sPriceTerms.value.referenceOnlinePrice ||
            !obj.sPriceTerms.value.salesDecreaseAmount)
        ) {
          this.$message.error(
            '请检查六、结算期及结算方式-1、价格条款 下是否有未填写值'
          )
          return
        } else if (
          (obj.sSettlementPeriod.level1 + '' === '1' ||
            obj.sSettlementPeriod.level1 + '' === '3' ||
            obj.sSettlementPeriod.level1 + '' === '4') &&
          !obj.sSettlementPeriod.value.paymentDate
        ) {
          this.$message.error(
            '请检查六、结算期及结算方式-2、结算期： 下是否有未填写值'
          )
          return
        } else if (
          obj.sSettlementPeriod.level1 + '' !== '3' &&
          !obj.sSettlementPeriod?.value.withinDays
        ) {
          this.$message.error(
            '请检查六、结算期及结算方式-2、结算期： 下是否有未填写值'
          )
          return
        } else if (
          obj.sSettlementPeriod.level1 + '' === '4' &&
          (!obj.sSettlementPeriod.value.paymentMethod ||
            !obj.sSettlementPeriod.value.paymentRatio ||
            !obj.sSettlementPeriod.value.paymentDateTwo ||
            !obj.sSettlementPeriod.value.withinDaysTwo ||
            !obj.sSettlementPeriod.value.paymentMethodTwo)
        ) {
          this.$message.error(
            '请检查六、结算期及结算方式-2、结算期： 下是否有未填写值'
          )
          return
        } else if (
          obj.sSettlementPeriod.level1 + '' === '5' &&
          !obj.sSettlementPeriod.value.paymentMethod
        ) {
          this.$message.error(
            '请检查六、结算期及结算方式-2、结算期： 下是否有未填写值'
          )
          return
        } else if (this.handwrittenFour === 99) {
          if (
            (obj.sCashDiscount.level1 === 1 ||
              obj.sCashDiscount.level1 === 2) &&
            !obj.sCashDiscount.value.sPayType
          ) {
            this.$message.error(
              '请检查六、结算期及结算方式-4、现金折扣： 下是否有未填写值'
            )
            return
          } else if (obj.sCashDiscount.level1 === 1) {
            if (
              obj.sCashDiscount.level2 === 1 &&
              !obj.sCashDiscount.value.rate
            ) {
              this.$message.error(
                '请检查六、结算期及结算方式-4、现金折扣： 下是否有未填写值'
              )
              return
            } else if (obj.sCashDiscount.level2 === 2) {
              if (
                !obj.sCashDiscount.value.startingDays ||
                !obj.sCashDiscount.value.rate ||
                !obj.sCashDiscount.value.acceptancePeriodDays
              ) {
                this.$message.error(
                  '请检查六、结算期及结算方式-4、现金折扣： 下是否有未填写值'
                )
                return
              }
            }
          } else if (obj.sCashDiscount.level1 === 2) {
            if (
              obj.sCashDiscount.level2 === 1 &&
              (!obj.sCashDiscount.value.startingDays ||
                !obj.sCashDiscount.value.rate ||
                !obj.sCashDiscount.value.discountPeriodDays)
            ) {
              this.$message.error(
                '请检查六、结算期及结算方式-4、现金折扣： 下是否有未填写值'
              )
              return
            } else if (obj.sCashDiscount.level2 === 2) {
              if (!obj.sCashDiscount.value.discountAmount) {
                this.$message.error(
                  '请检查六、结算期及结算方式-4、现金折扣： 下是否有未填写值'
                )
                return
              }
            }
          }
        } else if (this.handwritten === 2) {
          if (obj.sSettlement.level1 + '' === '1') {
            if (
              !obj.sSettlement.value.withinDaysAfterContractSigning ||
              !obj.sSettlement.value.daysAfterContractSigningOrMore ||
              (obj.sSettlement.value.settlementQuantityMarkupOne + '' === '1' &&
                !obj.sSettlement.value.markupOne) ||
              (obj.sSettlement.value.settlementQuantityMarkupTwo + '' === '1' &&
                !obj.sSettlement.value.markupTwo)
            ) {
              this.$message.error('请检查5、结算: 下是否有未填写值')
              return
            }
          } else if (obj.sSettlement.level1 + '' === '2') {
            if (
              !obj.sSettlement.value.fixedDateOne ||
              !obj.sSettlement.value.withinDaysAfterContractSigning ||
              !obj.sSettlement.value.markupOne ||
              !obj.sSettlement.value.fixedDateTwo ||
              !obj.sSettlement.value.daysAfterContractSigningOrMore ||
              !obj.sSettlement.value.markupTwo
            ) {
              this.$message.error('请检查5、结算: 下是否有未填写值')
              return
            }
          } else if (obj.sSettlement.level1 + '' === '3') {
            if (
              !obj.sSettlement.value.deliveryLocation ||
              !obj.sSettlement.value.withinDaysAfterContractSigning ||
              !obj.sSettlement.value.daysAfterContractSigningOrMore ||
              (obj.sSettlement.value.settlementQuantityMarkupOne + '' === '1' &&
                !obj.sSettlement.value.markupOne) ||
              (obj.sSettlement.value.settlementQuantityMarkupTwo + '' === '1' &&
                !obj.sSettlement.value.markupTwo)
            ) {
              this.$message.error('请检查5、结算: 下是否有未填写值')
              return
            }
          } else if (obj.sSettlement.level1 + '' === '4') {
            if (!obj.sSettlement.value.markup) {
              this.$message.error('请检查5、结算: 下是否有未填写值')
              return
            }
          } else if (obj.sSettlement.level1 + '' === '5') {
            if (
              !obj.sSettlement.value.contract ||
              !obj.sSettlement.value.price ||
              !obj.sSettlement.value.pricingDateEnd ||
              !obj.sSettlement.value.pricingDays ||
              !obj.sSettlement.value.basis ||
              !obj.sSettlement.value.pickupDays ||
              !obj.sSettlement.value.latestPricingDate
            ) {
              this.$message.error('请检查5、结算: 下是否有未填写值')
              return
            }
          }
        } else if (!obj.sAppointEmail) {
          this.$message.error('请检查九、供方指定邮箱：指定邮箱是否未填写值')
          return
        } else if (
          !obj.sCommonlyClause.value.supplierDeadlineBeforeEndOfMonth ||
          !obj.sCommonlyClause.value.buyerDeadlineBeforeEndOfMonth ||
          !obj.sCommonlyClause.value.buyerMissedDeadlineBeforeEndOfMonth
        ) {
          this.$message.error('请检查一般条款下是否未填写值')
          return
        } else if (!obj.sEmail) {
          this.$message.error('请检查需方电子邮箱否未填写值')
          return
        }
        this.isEmpty = false
      }
      this.isEmpty = false
    },
    // 点击保存数据 传给后端
    async saveRegion(showSave = false) {
      if (!showSave) {
        await this.checkValues(this.form)
        if (this.isEmpty) return false
      }
      return this.form
    },
    cashDiscount(e, k) {
      if (this.$route.query.disabledShow + '' !== 'add') return
      this.form.sCashDiscount.level1 = ''
      this.form.sCashDiscount.level2 = ''
      this.form.sCashDiscount.level1 = e
      this.form.sCashDiscount.level2 = k
      this.handwrittenFour = 99
      this.form.sCashDiscount.value = {
        sPayType: '',
        startingDays: '',
        rate: '',
        acceptancePeriodDays: '',
        discountPeriodDays: '',
        discountAmount: ''
      }
      if (Number(e) === 1) {
        this.form.sCashDiscount.value.sPayType = '现金'
      } else {
        this.form.sCashDiscount.value.sPayType = '银行承诺汇票'
      }
    },

    getHandwritten(e) {
      if (this.$route.query.disabledShow + '' !== 'add') return
      if (e === this.handwritten) {
        this.form.sCommercialDiscount.level1 = ''
        this.form.sCommercialDiscount.level2 = ''
        this.form.sSettlement.level1 = ''
        this.handwritten = ''
        return
      }
      if (Number(e) === 1) {
        this.form.sSettlement.level1 = ''
      } else {
        this.form.sCommercialDiscount.level1 = ''
        this.form.sCommercialDiscount.level2 = ''
      }
      this.handwritten = e
    },
    getHandwritten1(k) {
      if (this.$route.query.disabledShow + '' !== 'add') return
      if (k === 1) {
        this.handwritten = 1
        this.form.sSettlement.level1 = ''
      } else if (k === 2) {
        if (!this.form.sCommercialDiscount.level1) {
          this.form.sCommercialDiscount.level1 = '1'
        }
        this.form.sSettlement.level1 = ''
        this.handwritten = 1
      } else {
        this.form.sCommercialDiscount.level1 = ''
        this.form.sCommercialDiscount.level2 = ''
        this.handwritten = 2
      }
    },
    getHandwritten2(e) {
      if (this.$route.query.disabledShow + '' !== 'add') return
      if (this.handwrittenFour === 99) {
        this.handwrittenFour = 1
        this.form.sCashDiscount.level1 = ''
        this.form.sCashDiscount.level2 = ''
        this.form.sCashDiscount.value = {
          sPayType: '',
          startingDays: '',
          rate: '',
          acceptancePeriodDays: '',
          discountPeriodDays: '',
          discountAmount: ''
        }
        return
      } else {
        this.handwrittenFour = e
        this.cashDiscount(1)
        this.$forceUpdate()
      }
    },
    getValue(value) {
      if (value && this.form.sBond?.level1) {
        return this.form.sBond?.level1.includes(value)
      }
    },
    handleChange(e) {
      const a = this.form.sBond?.level1.filter(
        (item) => Number(item) !== 3 && Number(item) !== 4
      )
      this.form.sBond.level1 = [...new Set([...a, ...e])]
    },
    getMarginDeposit1(e) {
      this.form.sBond.level1 = this.form.sBond?.level1.filter(
        (item) => Number(item) !== 1 && Number(item) !== 2
      )
      if (!e) return
      this.form.sBond.level1.push(e)
    },
    getMarginDeposit2(e) {
      this.form.sBond.level1 = this.form.sBond?.level1.filter(
        (item) => Number(item) !== 5 && Number(item) !== 6
      )
      if (!e) return
      this.form.sBond.level1.push(e)
    },
    fourCheckbox(str) {
      this.fourArr = []
      this.fourArr = str.split(',').map((item) => item.trim())

      this.form.sCashDiscount.value = {}
      // 给第二段赋值
      this.form.sCashDiscount.level1 = this.fourArr[0]
      this.form.sCashDiscount.level2 = this.fourArr[1]
    },
    // 五。保证金
    getSecurityDepositBtn(s) {
      if (this.$route.query.disabledShow !== 'add') return
      this.form.sBond.value.depositRatio = s
    },
    loadSettlementData(pagination) {
      return new Promise((resolve, reject) => {
        this.searchInfo = { sContractId: this.$route.query.Id }
        getSettlementRateDetail(
          { sContractId: this.$route.query.Id },
          pagination
        )
          .then((res) => {
            this.rowData = res.data.content.map((item) => {
              item._selected = false
              item._selectedKeys = []
              item.details = []
              return item
            })
            resolve(res.data)
          })
          .catch(() => {
            reject([])
          })
      })
    },
    mySixDigit(val) {
      // 最多6位小数。
      var regPos = /^\d+(?:\.\d{1,4})?$/
      // var regPos = /^\d*\.?\d{0,6}$/
      if (regPos.test(val)) {
        return true
      } else {
        return false
      }
    },
    integer(val) {
      this.form.sBond.value.depositRatio = val.replace(/\./g, '')
    },
    inputBXJBL(nameString, val) {
      if (val) {
        // 6位小数
        if (this.mySixDigit(val)) {
          this.form.sBond.value[nameString] = val
        } else {
          const v = val.replace(/^(\-)*(\d+)\.(\d\d\d\d\d\d).*$/, '$1$2.$3')
          this.$nextTick(() => {
            this.form.sBond.value[nameString] = v
          })
        }
      }
    },
    inputFKBL(oneName, nameString, val) {
      if (val) {
        // 6位小数
        if (this.mySixDigit(val)) {
          this.form[oneName].value[nameString] = val
        } else {
          const v = val.replace(/^(\-)*(\d+)\.(\d\d\d\d\d\d).*$/, '$1$2.$3')
          this.$nextTick(() => {
            this.form[oneName].value[nameString] = v
          })
        }
      }
    }
  }
}
</script>

<style scoped lang="scss">
::v-deep .el-checkbox__label {
  font-size: 14px !important;
  color: #000 !important;
}
.ml-20 {
  margin-left: 20px;
}
.weight-size {
  font-weight: 800;
  padding-bottom: 5px;
}
.radio-box {
  ::v-deep .el-radio:last-child {
    color: #000;
    display: flex;
    // padding-top: 10px;
    // align-items: center;
  }
  ::v-deep .el-radio__input {
    margin-top: 4px;
  }
  .radio-span {
    line-height: 24px;
    overflow-wrap: break-word;
    overflow: hidden;
    white-space: break-spaces;
  }
}
.disabled-color {
  ::v-deep .el-radio__input.is-disabled + span.el-radio__label {
    color: #000 !important;
  }
  ::v-deep .el-radio__input.is-disabled.is-checked .el-radio__inner::after {
    width: 6px;
    height: 6px;
    background-color: #777777 !important;
  }
}
.d-flex {
  display: flex;
}
.cash-checkbox {
  margin-top: 6px;
  width: 14px;
  min-width: 14px;
  height: 14px;
  margin-right: 6px;
  border-radius: 50%;
  border: 1px solid #e4e7ed;
  position: relative;
}

.cash-checkbox::after {
  content: '';
  position: absolute;
  width: 4px;
  height: 4px;
  top: 4px;
  left: 4px;
  border-radius: 20px;
  background: #fff;
}

.disabledColor2 {
  background: #ffffff;
}
.checkedCash {
  border: 1px solid #409dff;
  background: #409dff !important;
}
.cash-pt {
  padding-left: 18px;
}
.disabled-color-bg {
  width: 6px;
  height: 6px;
  position: absolute;
  border-radius: 20px;
  // background: #fff;
  left: 4px;
  top: 8px;
}
.dis {
  display: flex;
  align-items: center;
}
.handwritten-checkbox {
  width: 16px;
  height: 16px;
  margin-right: 6px;
  background: #fff;
  border: 1px solid #e4e7ed;
}
.handwritten {
  position: relative;
  width: 16px;
  height: 16px;
  color: #fff;
  background: #409eff !important;
}
.handwritten::after {
  content: '✓';
  position: absolute;
  left: 3px;
  top: -2px;
  font-size: 12px;
}

.disabledColor {
  background: #f5f7fa !important;
}

::v-deep .el-radio {
  line-height: 28px !important;
}
.color-red {
  color: red;
  font-size: 16px;
}
.checkListson {
  min-width: 35px;
  text-align: center;
  line-height: 26px;
  margin-right: 4px;
  font-size: 12px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  cursor: pointer;
}
.colro {
  color: #409eff !important;
  border: 1px solid #409eff;
  border-radius: 4px;
}

.prohibited1{
  color: #e4e7ed !important;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}
.prohibited{
  color: #000 !important;
  border: 1px solid #777777;
  border-radius: 4px;
}
.disabledColor1 {
  position: relative;
  border: 1px solid #e4e7ed;
  background: #fff !important;
}
.disabledColor1::after {
  content: '';
  position: absolute;
  width: 6px;
  height: 6px;
  top: 3.5px;
  left: 3.5px;
  border-radius: 20px;
  background: #777777 !important;
}
.handwritten1 {
  position: relative;
  width: 16px;
  height: 16px;
  color: #777777;
  background: #f5f7fa !important;
}
.handwritten1::after {
  content: '✓';
  position: absolute;
  left: 3px;
  top: -2px;
  font-size: 12px;
}

::v-deep
  .el-radio:focus:not(.is-focus):not(:active):not(.is-disabled)
  .el-radio__inner {
  -webkit-box-shadow: 0 0 2px 2px #fff !important;
  box-shadow: 0 0 2px 2px #fff;
}

@media only screen and (min-width: 1200px){
::v-deep .el-col-lg-6{
  width: 33% !important;
}
}

</style>
