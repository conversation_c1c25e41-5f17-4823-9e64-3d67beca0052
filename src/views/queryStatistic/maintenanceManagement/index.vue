<template>
  <div class="page-container">
    <p class="page-title">运维单据管理</p>
    <div class="layout-content auto-page-title flexV">
      <el-form ref="form" class="el-form-w100" label-width="130px" :inline="true" :model="form" size="small">
        <cnd-form-card-list
          :active-panel="activeCollapseName"
          class-name="filter-collapse-container"
        >
          <cnd-form-card title="系统处理" name="1">
            <el-row>
              <cnd-form-item label="单据号">
                <el-input v-model="form.sBizCode" placeholder="请输入单据号" />
              </cnd-form-item>
              <cnd-form-item label="操作类型">
                <el-select v-model="form.code" :placeholder="$t('components.pleaseSelect')">
                  <el-option
                    v-for="item in codeSelectOps"
                    :key="item.code"
                    :label="item.operationName"
                    :value="item.code"
                  />
                </el-select>
              </cnd-form-item>
              <cnd-form-item label="处理原因">
                <el-input v-model="form.sReason" placeholder="请输入处理原因" />
              </cnd-form-item>
              <cnd-form-item>
                <el-button
                  :loading="btnLoading"
                  type="primary"
                  size="mini"
                  @click="chickOperation"
                >
                  执行
                </el-button>
              </cnd-form-item>
            </el-row>
          </cnd-form-card>
          <cnd-form-card title="物流附件补推送" name="2">
            <el-row>
              <cnd-form-item label="发货单号">
                <el-input v-model="form.sCode" placeholder="请输入发货单号" @input="onChange" />
              </cnd-form-item>
              <cnd-form-item label="发货单附件">
                <el-select v-model="form.sFilesId" :placeholder="$t('components.pleaseSelect')">
                  <el-option
                    v-for="item in fileOps"
                    :key="item.sId"
                    :label="item.sName"
                    :value="item.sId"
                  />
                </el-select>
              </cnd-form-item>
              <cnd-form-item label="N8文件位置">
                <el-select v-model="form.sLocationCode" :placeholder="$t('components.pleaseSelect')">
                  <el-option
                    v-for="item in N8Location"
                    :key="item.sCode"
                    :label="item.sName"
                    :value="item.sCode"
                  />
                </el-select>
              </cnd-form-item>

              <cnd-form-item>
                <el-button
                  :loading="upbtnLoading"
                  type="primary"
                  size="mini"
                  @click="checkUpload"
                >
                  上传
                </el-button>
              </cnd-form-item>
            </el-row>
          </cnd-form-card>
          <cnd-form-card title="单据生效处理" name="3">
            <el-row>
              <cnd-form-item label="单据号">
                <el-input v-model="form.sBizCodeV2" placeholder="请输入单据号" />
              </cnd-form-item>
              <cnd-form-item label="操作类型">
                <el-select v-model="form.codeV2" :placeholder="$t('components.pleaseSelect')">
                  <el-option
                    v-for="item in codeV2SelectOps"
                    :key="item.code"
                    :label="item.operationName"
                    :value="item.code"
                  />
                </el-select>
              </cnd-form-item>
              <cnd-form-item label="处理原因">
                <el-input v-model="form.sReasonV2" placeholder="请输入处理原因" />
              </cnd-form-item>
              <cnd-form-item>
                <el-button
                  v-has:esc_maintenance_doDocumentsTakeEffect
                  :loading="btnLoadingV2"
                  type="primary"
                  size="mini"
                  @click="chickOperationV2"
                >
                  执行
                </el-button>
              </cnd-form-item>
            </el-row>
          </cnd-form-card>
          <cnd-form-card title="组织导出" name="4">
            <el-row>
              <cnd-form-item label="组织类型">
                <el-select v-model="form.orgType" :placeholder="$t('components.pleaseSelect')">
                  <el-option
                    v-for="item in orgTypeList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </cnd-form-item>
              <cnd-form-item>
                <el-button
                  style="margin-left: -80px;"
                  :loading="expBtnLoading"
                  type="primary"
                  size="mini"
                  @click="orgbusiExport"
                >
                  导出
                </el-button>
              </cnd-form-item>
            </el-row>
          </cnd-form-card>
          <cnd-form-card title="保证金是否可用处理" name="5">
            <el-row>
              <cnd-form-item label="经营单位" prop="managementId">
                <el-select v-model="form.managementId">
                  <el-option
                    v-for="item in orgList"
                    :key="item.sId"
                    :label="item.sName"
                    :value="item.sId"
                  />
                </el-select>
              </cnd-form-item>
              <cnd-form-item label="保证金是否可用" prop="isAble">
                <el-select v-model="form.isAble" clearable>
                  <el-option
                    v-for="item in isAbleList"
                    :key="item.sCodeValue"
                    :label="item.sCodeName"
                    :value="item.sCodeValue"
                  />
                </el-select>
              </cnd-form-item>
              <cnd-form-item>
                <importBtn
                  class="mr-10"
                  :action="`/api/sale/margin/config/isAble/maintain/batch/V1`"
                  :params="importParams"
                  success-mark="sign"
                  btn-text="导入附件"
                  @success="importSuccess"
                  @handleUpload="handleUpload"
                />
                <el-button
                  v-has:sale_margin_config_isAble_maintain_batch
                  :loading="btnLoadingV5"
                  type="primary"
                  size="mini"
                  @click="chickOperationV5"
                >
                  执行
                </el-button>
              </cnd-form-item>
            </el-row>
          </cnd-form-card>
          <cnd-form-card title="商品是否需填写钢卷号校验" name="6">
            <el-row>
              <cnd-form-item label="商品编码">
                <el-input v-model="form.sCodeV6" placeholder="请输入商品编码" />
              </cnd-form-item>
              <cnd-form-item label="是否填写钢卷号校验">
                <el-select v-model="form.sValueV6">
                  <el-option
                    v-for="item in isAbleList"
                    :key="item.sCodeValue"
                    :label="item.sCodeName"
                    :value="item.sCodeValue"
                  />
                </el-select>
              </cnd-form-item>
              <cnd-form-item>
                <el-button
                  :loading="btnLoadingV6"
                  type="primary"
                  size="mini"
                  @click="chickOperationV6"
                >
                  执行
                </el-button>
              </cnd-form-item>
            </el-row>
          </cnd-form-card>
          <cnd-form-card title="商品是否自动发起收据" name="7">
            <el-row>
              <cnd-form-item label="商品编码">
                <el-input v-model="form.sCodeV7" placeholder="请输入商品编码" />
              </cnd-form-item>
              <cnd-form-item label="是否自动发起收据">
                <el-select v-model="form.sValueV7">
                  <el-option
                    v-for="item in isAbleList"
                    :key="item.sCodeValue"
                    :label="item.sCodeName"
                    :value="item.sCodeValue"
                  />
                </el-select>
              </cnd-form-item>
              <cnd-form-item>
                <el-button
                  :loading="btnLoadingV7"
                  type="primary"
                  size="mini"
                  @click="chickOperationV7"
                >
                  执行
                </el-button>
              </cnd-form-item>
            </el-row>
          </cnd-form-card>
          <cnd-form-card title="补充购销关系" name="8">
            <el-row>
              <cnd-form-item label="项目号">
                <el-input v-model="form.sCodeV8" placeholder="请输入项目号" />
              </cnd-form-item>
              <cnd-form-item label="采购合同号">
                <el-input v-model="form.purCodeV8" placeholder="请输入采购合同号" />
              </cnd-form-item>
              <cnd-form-item label="销售合同号">
                <el-input v-model="form.saleCodeV8" placeholder="请输入销售合同号" />
              </cnd-form-item>
              <cnd-form-item label="品名">
                <horizon-search-select-item
                  v-model="form.goodsIdV8"
                  clearable
                  :option="{
                    valueKey: 'sName'
                  }"
                  default-url="/esc/goods/ext/goodsDesc"
                  placeholder="请选择品名"
                />
              </cnd-form-item>
              <cnd-form-item label="处理原因">
                <el-input v-model="form.sReasonV8" placeholder="请输入处理原因" />
              </cnd-form-item>
              <cnd-form-item>
                <el-button
                  :loading="btnLoadingV8"
                  type="primary"
                  size="mini"
                  @click="chickOperationV8"
                >
                  执行
                </el-button>
              </cnd-form-item>
            </el-row>
          </cnd-form-card>
          <cnd-form-card title="商品分类是否为建材类" name="9">
            <el-row>
              <cnd-form-item label="商品编码">
                <el-input v-model="form.sCodeV9" placeholder="请输入商品编码" />
              </cnd-form-item>
              <cnd-form-item label="是否为建材类">
                <el-select v-model="form.sValueV9">
                  <el-option
                    v-for="item in isAbleList"
                    :key="item.sCodeValue"
                    :label="item.sCodeName"
                    :value="item.sCodeValue"
                  />
                </el-select>
              </cnd-form-item>
              <cnd-form-item>
                <el-button
                  :loading="btnLoadingV9"
                  type="primary"
                  size="mini"
                  @click="chickOperationV9"
                >
                  执行
                </el-button>
              </cnd-form-item>
            </el-row>
          </cnd-form-card>
          <cnd-form-card title="终结算后又钢厂提货" name="10">
            <el-row>
              <cnd-form-item label="销售合同号">
                <el-input v-model="form.sCodeV10" placeholder="请输入销售合同号" @input="onChangeV10" />
              </cnd-form-item>
              <cnd-form-item label="结算剩余货值">
                <el-input v-model="form.sLeftAmt" disabled />
              </cnd-form-item>
              <cnd-form-item label="本次需调整应收">
                <el-input v-model="form.adjustAmt" placeholder="请输入本次需调整应收" />
              </cnd-form-item>
              <cnd-form-item>
                <el-button
                  :loading="btnLoadingV10"
                  type="primary"
                  size="mini"
                  @click="chickOperationV10"
                >
                  执行
                </el-button>
              </cnd-form-item>
            </el-row>
          </cnd-form-card>

          <cnd-form-card title="限定保证金比例处理" name="11">
            <el-row>
              <cnd-form-item label="销售合同号">
                <el-input v-model="form.sCodeV11" placeholder="请输入销售合同号" />
              </cnd-form-item>
              <cnd-form-item label="限定保证金比例">
                <cnd-input-number v-model="form.sMarginRate" type="percent" placeholder="请输入限定保证金比例" />
              </cnd-form-item>
              <cnd-form-item label="处理原因">
                <el-input v-model="form.sRemark" placeholder="请输入处理原因" />
              </cnd-form-item>
              <cnd-form-item>
                <el-button
                  :loading="btnLoadingV11"
                  type="primary"
                  size="mini"
                  @click="chickOperationV11"
                >
                  执行
                </el-button>
              </cnd-form-item>
            </el-row>
          </cnd-form-card>

          <cnd-form-card title="维护内外部客户信息" name="12">
            <el-row>
              <cnd-form-item label="客商名称" :custom-width="4">
                <horizon-search-select-item
                  v-model="form.sCodeV12"
                  clearable
                  :option="{
                    valueKey: 'sName',
                    label: 'sPath',
                    value: 'sName'
                  }"
                  default-url="/esc/customer/page"
                  placeholder="请选择客商名称"
                  @change="onChangeCustomer"
                />
              </cnd-form-item>
              <cnd-form-item label="客商code" :custom-width="4">
                <el-input v-model="form.sCodeCustomer" disabled />
              </cnd-form-item>
              <cnd-form-item label="是否内部客户" :custom-width="4">
                <el-select v-model="form.isCustomer">
                  <el-option
                    v-for="item in isAbleList"
                    :key="item.sCodeValue"
                    :label="item.sCodeName"
                    :value="item.sCodeValue"
                  />
                </el-select>
              </cnd-form-item>
              <cnd-form-item label="公司">
                <horizon-search-select-item
                  v-model="form.company12"
                  clearable
                  :option="{
                    valueKey: 'sId',
                    label: 'sName',
                    value: 'sId'
                  }"
                  default-url="/org/dialog/corp/v2/page"
                  placeholder="请选择公司"
                />
              </cnd-form-item>
              <cnd-form-item>
                <el-button
                  :loading="btnLoadingV12"
                  type="primary"
                  size="mini"
                  @click="chickOperationV12"
                >
                  执行
                </el-button>
              </cnd-form-item>
            </el-row>
          </cnd-form-card>
          <cnd-form-card title="清理发货单缓存" name="13">
            <el-row>
              <cnd-form-item label="发货单号">
                <el-input v-model="form.sCodeV13" placeholder="请输入发货单号" />
              </cnd-form-item>
              <cnd-form-item>
                <el-button
                  :loading="btnLoadingV13"
                  type="primary"
                  size="mini"
                  @click="chickOperationV13"
                >
                  执行
                </el-button>
              </cnd-form-item>
            </el-row>
          </cnd-form-card>
          <cnd-form-card title="修改核算方式" name="14">
            <el-row>
              <cnd-form-item label="销售合同号">
                <el-input v-model="form.codeV14" placeholder="请输入销售合同号" />
              </cnd-form-item>
              <cnd-form-item label="结算单号">
                <el-input v-model="form.sBizCodeV14" placeholder="请输入结算单号" />
              </cnd-form-item>
              <cnd-form-item label="核算方式">
                <el-select v-model="form.sCheckTypeV14" :placeholder="$t('components.pleaseSelect')">
                  <el-option
                    v-for="item in checkTypeList"
                    :key="item.sCodeValue"
                    :label="item.sCodeName"
                    :value="item.sCodeValue"
                  />
                </el-select>
              </cnd-form-item>
              <cnd-form-item label="处理原因">
                <el-input v-model="form.sReasonV14" placeholder="请输入处理原因" />
              </cnd-form-item>
              <cnd-form-item>
                <el-button
                  :loading="btnLoadingV14"
                  type="primary"
                  size="mini"
                  @click="chickOperationV14"
                >
                  执行
                </el-button>
              </cnd-form-item>
            </el-row>
          </cnd-form-card>

          <cnd-form-card title="底吨配置单处理" name="15">
            <el-row>
              <cnd-form-item label="底吨配置单号" prop="sBizCodeV15">
                <el-input v-model="form.sBizCodeV15" placeholder="请输入底吨配置单号" />
              </cnd-form-item>
              <cnd-form-item label="锁货标识" prop="sCheckTypeV15">
                <el-select v-model="form.sCheckTypeV15" clearable>
                  <el-option
                    v-for="item in sysEndisList"
                    :key="item.sCodeValue"
                    :label="item.sCodeName"
                    :value="item.sCodeValue"
                  />
                </el-select>
              </cnd-form-item>
              <cnd-form-item>
                <el-button
                  :loading="btnLoadingV15"
                  type="primary"
                  size="mini"
                  @click="chickOperationV15"
                >
                  执行
                </el-button>
              </cnd-form-item>
            </el-row>
          </cnd-form-card>

        </cnd-form-card-list>
      </el-form>
    </div>
  </div>
</template>

<script>
import {
  getAllOperations,
  getTakeEffectOperations,
  doOperation,
  doDocumentsTakeEffect,
  maintenanceGetFiles,
  maintenanceGetN8Location,
  maintenanceDoUpload,
  marginConfigIsAbleMaintain,
  doExecuteOperation,
  doAuto,
  goodsIsWire,
  overviewUpdateRec,
  querySettleAmt,
  doUpdateMarginRate,
  getCustomerUpdate,
  deleteNoticeOutCache,
  updateCheckType,
  contractExtRelCreateV2,
  ftrStockBaseConfig
} from '@/api/queryStatistic/purchase'
import {
  getOrgDialog
} from '@/api/customerOutConfig'
import { DictUtil } from 'cnd-horizon-utils'
import { exportAll } from '@/api/export'
import { debounce } from '@/utils/common'
import importBtn from '@/components/importBtn'
export default {
  name: 'MaintenanceManagement',
  components: { importBtn },
  data() {
    return {
      activeCollapseName: Array.from({ length: 15 }, (_, i) => (i + 1).toString()),
      form: {
        code: '',
        sBizCode: '',
        sReason: '',

        sCode: '',
        sFilesId: '',
        sLocationCode: '',

        sBizCodeV2: '',
        codeV2: '',
        sReasonV2: '',

        orgType: '',

        managementId: '',
        isAble: '',

        sCodeV6: '',
        sValueV6: '',
        sTypeV6: '10',

        sCodeV7: '',
        sValueV7: '',

        sCodeV8: '',
        saleCodeV8: '',
        purCodeV8: '',
        sReasonV8: '',
        goodsIdV8: '',

        sCodeV9: '',
        sValueV9: '',

        sCodeV10: '',
        sSettlementId: '',
        sLeftAmt: '',
        adjustAmt: '',

        sCodeV11: '',
        sMarginRate: '',
        sRemark: '',
        sCodeV12: '',
        sCodeCustomer: '',
        isCustomer: '',
        company12: '',

        sCodeV13: '',

        codeV14: '',
        sBizCodeV14: '',
        sCheckTypeV14: '',
        sReasonV14: '',

        sBizCodeV15: '',
        sCheckTypeV15: ''
      },
      btnLoading: false,
      codeSelectOps: [],
      codeV2SelectOps: [],
      N8Location: [],
      fileOps: [],
      upbtnLoading: false,
      expBtnLoading: false,
      orgTypeList: [
        {
          label: '业务组织',
          value: '100'
        },
        {
          label: '法人组织',
          value: '200'
        },
        {
          label: '成本结算中心组织',
          value: '300'
        }
      ],
      isAbleList: [],
      orgList: [],
      customerList: [],
      checkTypeList: [],
      sysEndisList: [],
      btnLoadingV2: false,
      btnLoadingV5: false,
      btnLoadingV6: false,
      btnLoadingV7: false,
      btnLoadingV8: false,
      btnLoadingV9: false,
      btnLoadingV10: false,
      btnLoadingV11: false,
      btnLoadingV12: false,
      btnLoadingV13: false,
      btnLoadingV14: false,
      btnLoadingV15: false
    }
  },
  computed: {
    importParams() {
      const { managementId, isAble } = this.form
      return {
        managementId,
        isAble
      }
    }
  },
  created() {
    getAllOperations().then(res => {
      this.codeSelectOps = res.data
    })
    maintenanceGetN8Location().then(res => {
      this.N8Location = res.data
    })
    getTakeEffectOperations().then(res => {
      this.codeV2SelectOps = res.data
    })
    DictUtil.getDict([
      'base.yes-no',
      'contract.sale.check.type',
      'sys.en-dis'
    ], res => {
      this.isAbleList = res[0].dicts
      this.checkTypeList = res[1].dicts
      this.sysEndisList = res[2].dicts
    })
    getOrgDialog({}).then(res => {
      this.orgList = res.data.content
    })
  },
  methods: {
    chickOperationV2() {
      this.btnLoadingV2 = true
      const { sBizCodeV2, sReasonV2, codeV2 } = this.form
      doDocumentsTakeEffect({
        code: codeV2,
        sBizCode: sBizCodeV2,
        sReason: sReasonV2
      }).then(res => {
        if (res.code === '0000') {
          this.$message.success('执行成功')
        }
      }).finally(() => {
        this.btnLoadingV2 = false
      })
    },
    chickOperation() {
      this.btnLoading = true
      const { code, sBizCode, sReason } = this.form
      doOperation({ code, sBizCode, sReason }).then(res => {
        if (res.code === '0000') {
          this.$message.success('执行成功')
        }
      }).finally(() => {
        this.btnLoading = false
      })
    },
    onChange(e) {
      debounce(() => {
        this.fileOps = []
        this.form.sFilesId = ''
        if (e) {
          maintenanceGetFiles(e).then(res => {
            console.log('res: ', res)
            this.fileOps = res.data
          })
        }
      }, 300)()
    },
    onChangeCustomer(e) {
      this.form.sCodeCustomer = ''
      if (e) {
        this.form.sCodeCustomer = e.sPath.split('/')[0]
      }
    },
    checkUpload() {
      console.log(this.form)
      const { sCode, sFilesId, sLocationCode } = this.form
      if (!sCode) return this.$message.error('请输入发货单号')
      if (!sFilesId) return this.$message.error('请选择发货单附件')
      if (!sLocationCode) return this.$message.error('请选择N8文件位置')
      this.upbtnLoading = true
      maintenanceDoUpload({ sCode, sFilesId, sLocationCode }).then(res => {
        if (res.code === '0000') {
          this.$message.success(this.$t('grid.others.uploadedSuccessfully'))
          this.form.sFilesId = ''
          this.form.sCode = ''
          this.form.sLocationCode = ''
        }
      }).finally(() => {
        this.upbtnLoading = false
      })
    },
    orgbusiExport() {
      if (!this.form.orgType) {
        return this.$message.error('请选择导出的组织类型')
      }
      this.expBtnLoading = true
      exportAll(`/esc/common/orgbusi/export/${this.form.orgType}`, {}).then(res => {
        const blob = new Blob([res], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
        const link = document.createElement('a')
        const fileName = this.orgTypeList.find(item => item.value === this.form.orgType)?.label
        link.style.display = 'none'
        link.href = URL.createObjectURL(blob)
        link.download = fileName
        document.body.appendChild(link)
        link.click()
      }).finally(() => {
        this.expBtnLoading = false
      })
    },
    handleUpload(e) {
      const { managementId } = this.form
      if (!managementId) {
        this.$message.error('经营单位，为必填项!')
        e.stopPropagation()
      }
    },
    importSuccess(e) {
      console.log(e)
    },
    chickOperationV5() {
      const { managementId, isAble } = this.form
      if (!managementId) {
        this.$message.error('经营单位，为必填项!')
        return false
      }
      this.btnLoadingV5 = true
      marginConfigIsAbleMaintain({
        managementId,
        isAble
      }).then(res => {
        if (res.code === '0000') {
          this.$message.success('执行成功')
        }
      }).finally(() => {
        this.btnLoadingV5 = false
      })
    },
    chickOperationV6() {
      const {
        sCodeV6: sCode,
        sValueV6: sValue,
        sTypeV6: sType
      } = this.form
      if (!sCode || !sValue) {
        this.$message.error('商品编码和是否填写钢卷号校验，为必填项!')
        return false
      }
      this.btnLoadingV6 = true
      doExecuteOperation({
        sCode,
        sValue,
        sType
      }).then(res => {
        if (res.code === '0000') {
          this.$message.success('执行成功')
        }
      }).finally(() => {
        this.btnLoadingV6 = false
      })
    },
    chickOperationV7() {
      const {
        sCodeV7: sCode,
        sValueV7: sValue
      } = this.form
      if (!sCode || !sValue) {
        this.$message.error('商品编码和是否自动发起收据，为必填项!')
        return false
      }
      this.btnLoadingV7 = true
      doAuto({
        sCode,
        sValue
      }).then(res => {
        if (res.code === '0000') {
          this.$message.success('执行成功')
        }
      }).finally(() => {
        this.btnLoadingV7 = false
      })
    },
    chickOperationV8() {
      const { sCodeV8, purCodeV8, saleCodeV8, goodsIdV8, sReasonV8 } = this.form
      if (!sCodeV8 || !purCodeV8 || !saleCodeV8 || !goodsIdV8 || !sReasonV8) {
        this.$message.error('项目号、采购合同号、销售合同号、品名、处理原因不能为空')
        return false
      }
      this.btnLoadingV8 = true
      contractExtRelCreateV2({
        sProjectCode: sCodeV8,
        sSaleContractCode: saleCodeV8,
        sPurContractCode: purCodeV8,
        sGoodsDetailId: goodsIdV8.sId,
        sReason: sReasonV8
      }).then(res => {
        if (res.code === '0000') {
          this.$message.success('执行成功')
        }
      }).finally(() => {
        this.btnLoadingV8 = false
      })
    },
    chickOperationV9() {
      const {
        sCodeV9: sCode,
        sValueV9: sValue
      } = this.form
      if (!sCode || !sValue) {
        this.$message.error('商品编码和是否为建材类，为必填项!')
        return false
      }
      this.btnLoadingV9 = true
      goodsIsWire({
        sCode,
        sValue
      }).then(res => {
        if (res.code === '0000') {
          this.$message.success('执行成功')
        }
      }).finally(() => {
        this.btnLoadingV9 = false
      })
    },
    onChangeV10(e) {
      console.log('e: ', e)
      debounce(() => {
        if (e) {
          querySettleAmt(e).then(res => {
            this.form.sLeftAmt = res.data.sLeftAmt
            this.form.sSettlementId = res.data.sSettlementId
          })
        } else {
          this.form.sLeftAmt = ''
          this.form.sSettlementId = ''
        }
      }, 300)()
    },
    chickOperationV10() {
      const { sCodeV10, adjustAmt, sSettlementId } = this.form
      if (!sCodeV10 || !adjustAmt) {
        this.$message.error('销售合同号和本次需调整应收，为必填项!')
        return false
      }
      this.btnLoadingV10 = true
      overviewUpdateRec({
        sSettlementId,
        sCalcType: '150',
        sCfmField2: adjustAmt
      }).then(res => {
        if (res.code === '0000') {
          this.$message.success('执行成功')
          this.form.sLeftAmt = ''
          this.form.sSettlementId = ''
          this.form.adjustAmt = ''
          this.form.sCodeV10 = ''
        }
      }).finally(() => {
        this.btnLoadingV10 = false
      })
    },

    chickOperationV11() {
      const { sCodeV11, sMarginRate, sRemark } = this.form
      if (!sCodeV11 || !sMarginRate || !sRemark) {
        this.$message.error('销售合同号、限定保证金比例、处理原因，为必填项!')
        return false
      }
      this.btnLoadingV11 = true
      doUpdateMarginRate({
        sSaleContractCode: sCodeV11,
        sMarginRate,
        sRemark
      }).then(res => {
        if (res.code === '0000') {
          this.$message.success('执行成功')
          this.form.sCodeV11 = ''
          this.form.sMarginRate = ''
          this.form.sRemark = ''
        }
      }).finally(() => {
        this.btnLoadingV11 = false
      })
    },
    chickOperationV12() {
      const { sCodeV12, sCodeCustomer, isCustomer, company12 } = this.form
      if (!sCodeV12 || !sCodeCustomer || !isCustomer) {
        this.$message.error('客商名称、客商code、是否内部客户，为必填项!')
        return false
      }
      this.btnLoadingV112 = true
      getCustomerUpdate({
        sId: sCodeV12.sId,
        sIsInternal: isCustomer,
        sExtend4: company12.sId
      }).then(res => {
        if (res.code === '0000') {
          this.$message.success('执行成功')
          this.form.sCodeV12 = ''
          this.form.sCodeCustomer = ''
          this.form.isCustomer = ''
          this.form.company12 = ''
        }
      }).finally(() => {
        this.btnLoadingV12 = false
      })
    },
    chickOperationV13() {
      const { sCodeV13 } = this.form
      if (!sCodeV13) {
        this.$message.error('请输入发货单号')
        return false
      }
      this.btnLoadingV13 = true
      deleteNoticeOutCache(sCodeV13).then(res => {
        if (res.code === '0000') {
          this.$message.success('执行成功')
        }
      }).finally(() => {
        this.btnLoadingV13 = false
      })
    },
    chickOperationV14() {
      const { codeV14, sBizCodeV14, sCheckTypeV14, sReasonV14 } = this.form
      if (!codeV14 || !sBizCodeV14 || !sCheckTypeV14 || !sReasonV14) {
        this.$message.error('销售合同号、结算单号、核算方式、处理原因，为必填项!')
        return false
      }
      this.btnLoadingV14 = true
      updateCheckType({
        code: codeV14,
        sBizCode: sBizCodeV14,
        sCheckType: sCheckTypeV14,
        sReason: sReasonV14
      }).then(res => {
        if (res.code === '0000') {
          this.$message.success('执行成功')
        }
      }).finally(() => {
        this.btnLoadingV14 = false
      })
    },
    chickOperationV15() {
      const { sBizCodeV15, sCheckTypeV15 } = this.form
      if (!sBizCodeV15 || !sCheckTypeV15) {
        this.$message.error('底吨配置单号、锁货标识，为必填项!!')
        return false
      }
      this.btnLoadingV15 = true
      ftrStockBaseConfig({
        sBizCode: sBizCodeV15,
        sCheckType: sCheckTypeV15
      }).then(res => {
        if (res.code === '0000') {
          this.$message.success('执行成功')
        }
      }).finally(() => {
        this.btnLoadingV15 = false
      })
    }

  }
}
</script>

<style lang="scss" scoped>

</style>
