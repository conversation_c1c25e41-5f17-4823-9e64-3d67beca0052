<template>
  <steelTradeAggrid
    ref="aggrid"
    class="stellTradeAggridBor"
    :auto-height="true"
    :column-defs="columnDefs"
    :child-column-defs="childColumnDefs"
    :row-data="rowData"
    :load-data="loadData"
    table-selection="multiple"
    row-key="sId"
    child-row-key="sId"
    children-list-key="purContractStockAdjustDetailVos"
    :header-total="headerTotal"
    :footer-total="footerTotal"
    is-subtable
    @selectedChange="gridSelectedChange"
  />
</template>

<script>
import {
  SteelFormat,
  DictUtil
} from 'cnd-horizon-utils'
import { Moment } from 'cnd-utils'
var Decimal = window.Decimal
import steelTradeAggrid from '@/components/steelTradeAggrid'
import {
  getStockAdjustList,
  getStockAdjustListDetail
} from '@/api/queryStatistic/purchase'

export default {
  components: {
    steelTradeAggrid
  },
  props: {
    sId: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      rowData: [],
      columnDefs: [{
        headerName: this.$t('grid.others.logisticsOrderNumber'),
        field: 'sCode'
      }, {
        headerName: this.$t('grid.title.status'),
        field: 'sSheetStatus',
        valueGetter: (params) => {
          const status = this.sheetStatus.filter(item => item.sCodeValue === params.data.sSheetStatus)
          return status.length ? status[0].sCodeName : params.value
        }
      }, {
        headerName: this.$t('grid.title.quantity'),
        field: 'vQtx',
        cellStyle: { textAlign: 'right' },
        valueFormatter: params => {
          return SteelFormat.formatThousandthSign(params.value, 4)
        }
      }, {
        headerName: this.$t('grid.others.numberOfPiecesTag'),
        field: 'vQty',
        cellStyle: { textAlign: 'right' },
        valueFormatter: params => {
          return SteelFormat.formatThousandthSign(params.value)
        }
      },
      // {
      //   headerName: this.$t('grid.title.amount'),
      //   field: 'vSumAmt',
      //   cellStyle: { textAlign: 'right' },
      //   valueFormatter: params => {
      //     return SteelFormat.formatPrice(params.value)
      //   }
      // },
      {
        headerName: this.$t('grid.others.warehouse'),
        field: 'vWarehouseName'
      }, {
        headerName: this.$t('grid.title.createdBy'),
        field: 'vCreatorName'
      }, {
        headerName: this.$t('grid.title.createdAt'),
        field: 'sCreateTime',
        minWidth: 150,
        valueGetter(params) {
          return Moment.time('YYYY-MM-DD HH:mm:ss', params.data.sCreateTime)
        }
      }, {
        headerName: this.$t('grid.others.originalLogisticsBillNumber'),
        field: 'sUpCode'
      }],
      childColumnDefs: [{
        headerName: this.$t('grid.title.salesContractNumber'),
        field: 'sSaleContractCode'
      }, {
        headerName: this.$t('grid.others.item'),
        field: 'vArtName'
      }, {
        headerName: this.$t('grid.others.steelCoilNumber'),
        field: 'sExtend4'
      }, {
        headerName: this.$t('grid.others.carriageNumber'),
        field: 'sExtend5'
      }, {
        headerName: this.$t('grid.title.quantity'),
        field: 'vQtx',
        cellStyle: { textAlign: 'right' },
        valueFormatter: params => {
          return SteelFormat.formatThousandthSign(params.value, 4)
        }
      }, {
        headerName: this.$t('grid.others.numberOfPiecesTag'),
        field: 'vQty',
        cellStyle: { textAlign: 'right' },
        valueFormatter: params => {
          return SteelFormat.formatThousandthSign(params.value)
        }
      }
      // {
      //   headerName: this.$t('grid.title.unitPrice'),
      //   field: 'sPrice',
      //   cellStyle: { textAlign: 'right' },
      //   valueFormatter: params => {
      //     return SteelFormat.formatPrice(params.value)
      //   }
      // }, {
      //   headerName: this.$t('grid.title.amount'),
      //   field: 'vSumAmt',
      //   cellStyle: { textAlign: 'right' },
      //   valueFormatter: params => {
      //     return SteelFormat.formatPrice(params.value)
      //   }
      // }
      ],

      headerTotal: [],
      footerTotal: [],
      sheetStatus: []
    }
  },
  watch: {
    sId() {
      this.$refs.aggrid.loadTableData()
    }
  },
  mounted() {
    this.loadDict()
  },
  methods: {
    loadDict() {
      DictUtil.getDict(['dev.common.sheet.status'], res => {
        this.sheetStatus = res[0].dicts
      })
    },
    loadData() {
      return new Promise((resolve, reject) => {
        getStockAdjustList({
          sPurContractId: this.sId
        }).then(res => {
          this.rowData = res.data.map(item => {
            item._selected = false
            item._selectedKeys = []
            item._details = item._details || []
            return item
          })
          resolve(res.data)
          this.headerTotal = res.data.reduce((prev, next, index) => {
            prev[1].count = +new Decimal(prev[1].count).add(next.vQtx || 0)
            prev[2].count = +new Decimal(prev[2].count).add(next.vQty || 0)
            // prev[3].count = +new Decimal(prev[3].count).add(next.vSumAmt || 0)
            if (index + 1 === res.data.length) {
              prev[1].count = SteelFormat.formatThousandthSign(prev[1].count, 4)
              prev[2].count = SteelFormat.formatThousandthSign(prev[2].count)
              // prev[3].count = SteelFormat.formatPrice(prev[3].count)
            }
            return prev
          }, [{
            key: 'count',
            count: res.data.length,
            unit: this.$t('pagination.items')
          }, {
            title: this.$t('grid.title.quantity'),
            count: 0,
            unit: this.$t('grid.others.ton')
          }, {
            title: this.$t('grid.others.numberOfPiecesTag'),
            count: 0,
            unit: this.$t('grid.others.pieces')
          }
          // {
          //   title: this.$t('grid.title.amount'),
          //   count: 0,
          //   unit: this.$t('grid.others.yuan')
          // }
          ])
        })
      })
    },
    loadDetail(data) {
      return new Promise((resolve, reject) => {
        getStockAdjustListDetail({
          sPurContractId: this.sId,
          sTableType: data.sType,
          sId: data.sId
        }).then(res => {
          resolve(res.data)
        }).catch(() => {
          reject([])
        })
      })
    },
    gridSelectedChange() {
      this.$refs.aggrid.getSelectedData(list => {
        let vQtx = 0; let vQty = 0
        // let vSumAmt = 0
        list.forEach(el => {
          vQtx += el.vQtx
          vQty += el.vQty
          // vSumAmt += el.vSumAmt
        })
        this.footerTotal = [
          { count: list.length, key: 'count' },
          { title: this.$t('grid.title.quantity'), count: SteelFormat.formatThousandthSign(vQtx, 4), unit: this.$t('grid.others.ton') },
          { title: this.$t('grid.others.numberOfPiecesTag'), count: SteelFormat.formatThousandthSign(vQty), unit: this.$t('grid.others.pieces') }
          // { title: this.$t('grid.title.amount'), count: SteelFormat.formatPrice(vSumAmt), unit: this.$t('grid.others.yuan') }
        ]
      }, 'margeChild')
    }

  }
}
</script>
<style lang="scss" scoped>
.stellTradeAggridBor {
  ::v-deep .steel-trade-aggrid-header {
    border-right: 0;
    border-left: 0;
  }
  ::v-deep .ag-root-wrapper {
    border-right: 0;
    border-left: 0;
  }
  ::v-deep .cnd-pagination {
    border: 0;
  }
  ::v-deep .steel-trade-aggrid-footer {
    border-right: 0;
    border-left: 0;
  }
}
</style>
