<template>
  <div>
    <!-- 物流费用明细 -->
    <div class="btn-group">
      <div style="width: 50%">
        <el-row>
          <el-form ref="form" label-width="65px">
            <cnd-form-item v-show="vManagementCode !=='00014867'" label="税差算法" :custom-width="8">
              <el-select v-model="sIsDiff" size="mini" :disabled="disabled" @change="handleSelect">
                <el-option
                  v-for="item in diffList"
                  :key="item.sCodeValue"
                  :label="item.sCodeName"
                  :value="item.sCodeValue"
                />
              </el-select>
            </cnd-form-item>
            <!-- <cnd-form-item label="出票税点" size="mini" :custom-width="4">
              <cnd-input-number v-model="sOutTaxPoint" :disabled="disabled" type="percent" clearable />
            </cnd-form-item> -->
            <!-- <cnd-form-item label="进票税点" :custom-width="8">
              <div style="display: flex">
                <el-select v-model="sInTaxPoint" :disabled="disabled" size="mini" multiple clearable>
                  <el-option
                    v-for="item in inTaxPointList"
                    :key="item.sCodeValue"
                    :label="item.sCodeName"
                    :value="item.sCodeValue"
                  />
                </el-select>
              </div>
            </cnd-form-item> -->
            <!-- <div :custom-width="3">
              <el-button
                class="ml-10"
                type="primary"
                size="mini"
                :disabled="disabled"
                @click="saveInTaxPoint"
              >税点保存</el-button>
            </div> -->
          </el-form>
        </el-row>
      </div>
      <div>
        <template v-if="!isTrial">
          <el-button
            v-has:esc_pay_settle_logistics_fee_modifies
            type="primary"
            size="mini"
            :disabled="disabled"
            @click="openModifyDialog()"
          >
            {{ $t('grid.others.batchModify') }}
          </el-button>
          <el-button
            v-has:esc_con_settlement_detail_logistics_fee_getCarriageContractList
            type="primary"
            size="mini"
            :disabled="disabled"
            @click="openDialog"
          >选择运输合同</el-button>
          <el-button
            v-has:esc_con_settlement_detail_logistics_add
            type="primary"
            size="mini"
            :disabled="disabled"
            @click="add"
          >{{ $t('btns.add') }}</el-button>
          <el-button
            v-has:esc_con_settlement_detail_logistics_delete
            type="danger"
            size="mini"
            :disabled="disabled"
            @click="remove"
          >{{ $t('btns.delete') }}
          </el-button>
        </template>
        <el-button
          size="mini"
          @click="logisticsStatisticsinif=!logisticsStatisticsinif"
        >{{ $t('grid.others.viewLogisticsCostStatistics') }}</el-button>
      </div>
    </div>
    <!-- 建发金属 -->
    <steelTradeAggrid
      v-if="gridShow && vManagementCode && vManagementCode ==='00011061'"
      ref="aggrid"
      :auto-height="true"
      :min-height="150"
      :column-defs="columnDefs"
      :row-data="rowData"
      row-key="sId"
      table-selection="multiple"
      disable-paste="all"
      :paginationinif="false"
      :load-data="loadData"
      :full-row-type="fullRowType"
      @rowValueChanged="rowValueChanged"
      @selectedChange="handleChange"
    />
    <!-- 上海物资 -->
    <steelTradeAggrid
      v-if="gridShow && vManagementCode && vManagementCode ==='00014960'"
      ref="aggrid"
      :auto-height="true"
      :min-height="150"
      :column-defs="SHcolumnDefs"
      :row-data="rowData"
      row-key="sId"
      table-selection="multiple"
      disable-paste="all"
      :paginationinif="false"
      :load-data="loadData"
      :full-row-type="fullRowType"
      @rowValueChanged="rowValueChanged"
      @selectedChange="handleChange"
    />
    <!-- 厦门物资 -->
    <steelTradeAggrid
      v-if="gridShow && vManagementCode && vManagementCode ==='00014867'"
      ref="aggrid"
      :auto-height="true"
      :min-height="150"
      :column-defs="XMWZcolumnDefs"
      :row-data="rowData"
      row-key="sId"
      table-selection="multiple"
      disable-paste="all"
      :paginationinif="false"
      :load-data="loadData"
      :full-row-type="fullRowType"
      @rowValueChanged="rowValueChanged"
      @selectedChange="handleChange"
    />
    <div v-if="logisticsStatisticsinif" class="b-border" />
    <logisticsStatistics v-if="logisticsStatisticsinif" class="b-show" :s-id="sId" :info="newInfo" @close="logisticsStatisticsinif = false" />
    <transportContractDialog
      :id="sId"
      :dialog-visible="dialogVisible"
      @close="closeDialog"
      @select="onSelect"
    />
    <formDialog
      :visible="batchModifyDialogVisible"
      :form-items="formDialogItems"
      width="350px"
      height="70px"
      title="批量修改"
      success-no-visible
      @close="batchModifyDialogVisible = false"
      @success="onSuccess"
    />
  </div>
</template>

<script>
var Decimal = window.Decimal

import Vue from 'vue'
import { SteelFormat } from 'cnd-horizon-utils'
import { Middleware } from 'cndinfo-ui'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import { getArtnoDetail } from '@/api/contract'
import { computeCellTotal } from '@/utils/common'
import businessMixin from '@/utils/businessMixin'
import { deepClone } from '@/components/steelTradeAggrid/utils.js'
import {
  logisticsFeeList,
  logisticsFeeModifies,
  logisticsFeeRemoves,
  getDictet,
  payoverview,
  settdeliveryget,
  updatasettle
} from '@/api/settlement'
import logisticsStatistics from './logisticsStatistics'
import transportContractDialog from './dialog/transportContractDialog.vue'
import formDialog from '@/components/formDialog'

const fieldObj = {
  'sField1': '0.09',
  'sField2': '0.06',
  'sField3': '0.00',
  'sField4': '0.03',
  'sField6': '0.13',
  'sField11': '0.01',
  'sField12': '0.02',
  'sField13': '0.04',
  'sField14': '0.05',
  'sField15': '0.07',
  'sField16': '0.08',
  'sField17': '0.10',
  'sField18': '0.11',
  'sField19': '0.12'
}
export default {
  components: { steelTradeAggrid, logisticsStatistics, transportContractDialog, formDialog },
  mixins: [businessMixin],
  props: {
    sId: {
      type: String,
      default: null
    },
    info: {
      type: Object,
      default: () => {}
    },
    updateFn: {
      type: Function,
      default: () => {}
    }
  },
  data() {
    return {
      newInfo: null,
      sInTaxPoint: '', // 进票税点
      currInTaxPoint: '',
      sOutTaxPoint: '', // 出票税点
      sIsDiff: '0',
      showField: [],
      diffList: [
        {
          'sCodeValue': '1',
          'sCodeName': '有增收税差'
        }, {
          'sCodeValue': '0',
          'sCodeName': '无增收税差'
        }
      ],
      inTaxPointList: [
        {
          sCodeValue: 'sField1',
          sCodeName: '9%',
          taxValue: '0.09'
        },
        {
          sCodeValue: 'sField2',
          sCodeName: '6%',
          taxValue: '0.06'
        },
        {
          sCodeValue: 'sField3',
          sCodeName: '0%',
          taxValue: '0.00'
        },
        {
          sCodeValue: 'sField4',
          sCodeName: '3%',
          taxValue: '0.03'
        },
        {
          sCodeValue: 'sField6',
          sCodeName: '13%',
          taxValue: '0.13'
        },
        {
          sCodeValue: 'sField19',
          sCodeName: '12%',
          taxValue: '0.12'
        },
        {
          sCodeValue: 'sField18',
          sCodeName: '11%',
          taxValue: '0.11'
        },
        {
          sCodeValue: 'sField17',
          sCodeName: '10%',
          taxValue: '0.10'
        },
        {
          sCodeValue: 'sField16',
          sCodeName: '8%',
          taxValue: '0.08'
        },
        {
          sCodeValue: 'sField15',
          sCodeName: '7%',
          taxValue: '0.07'
        },
        {
          sCodeValue: 'sField14',
          sCodeName: '5%',
          taxValue: '0.05'
        },
        {
          sCodeValue: 'sField13',
          sCodeName: '4%',
          taxValue: '0.04'
        },
        {
          sCodeValue: 'sField12',
          sCodeName: '2%',
          taxValue: '0.02'
        },
        {
          sCodeValue: 'sField11',
          sCodeName: '1%',
          taxValue: '0.01'
        }
      ],
      gridShow: true,
      logisticsStatisticsinif: false,
      dialogVisible: false,
      batchModifyDialogVisible: false,
      modifyData: {},
      formDialogItems: [
        {
          label: '物流单价',
          value: 'sPrice',
          type: 'cndInputNumber',
          customWidth: 22,
          required: true,
          disabled: false
        },
        {
          label: '税差拆分',
          value: 'sField',
          type: 'elSelect',
          customWidth: 22,
          allHide: true,
          dict: '',
          errorMessage: this.$t('components.pleaseSelect')
        }
      ],
      columnDefs: [
        {
          headerName: this.$t('grid.others.transportContractNumber'),
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'sLogisticsContractCode',
              headerClass: 'c-header_child',
              editable: (params) => {
                return !this.disabled && !params.data._hiddenCheckbox
              }
            }
          ]
        },
        {
          headerName: this.$t('grid.others.logisticsSupplier'),
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'vSupplierName',
              headerClass: 'c-header_child',
              editable: (params) => {
                return !this.disabled && !params.data._hiddenCheckbox
              },
              cellEditorFramework: Vue.extend(Middleware.createComponent(
                'AgSearchSelect',
                {
                  mark: 'vSupplierName',
                  type: 'customer',
                  placeholder: this.$t('grid.others.pleaseEnterTheSearch')
                },
                {
                  getOption: (option, params) => {
                    this.rowData[params.rowIndex].sSupplierId = option.sId
                  }
                }
              ))
            }
          ]
        },
        {
          headerName: this.$t('grid.others.expenseType'),
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'sLogisticsType',
              headerClass: 'c-header_child',
              editable: (params) => {
                return !this.disabled && !params.data._hiddenCheckbox
              },
              valueGetter: params => {
                const filterItem = this.artnoList.filter(item => item.sArtnoCode === params.data.sLogisticsType)
                return filterItem.length ? filterItem[0].sCnName : params.data.sLogisticsType
              },
              cellEditorFramework: Vue.extend(Middleware.createComponent(
                'AgGridSelect',
                {
                  mark: 'sLogisticsType',
                  config: {
                    label: 'sCnName',
                    value: 'sArtnoCode'
                  },
                  filterable: false,
                  remote: false,
                  searchLimit: 0,
                  placeholder: this.$t('grid.others.pleaseEnterTheSearch'),
                  queryMethod: this.getArtnoList
                },
                {
                  getOption: (option, params, middleware) => {
                    console.log(middleware.rendered)
                    if (option.sArtnoCode === 'TRAN30') {
                      params.data.sField5 = 2
                      middleware.rendered.sField5.setValue(2)
                      this.calculateData(params.data, middleware)
                    } else {
                      params.data.sField5 = 0
                      middleware.rendered.sField5.setValue(0)
                      this.calculateData(params.data, middleware)
                    }
                    this.rowData[params.rowIndex].sLogisticsType = option.sArtnoCode
                    this.$refs.aggrid.gridApi.refreshCells(params)
                  }
                }
              ))
            }
          ]
        },
        {
          headerName: this.$t('grid.others.shipName'),
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'sVesselName',
              headerClass: 'c-header_child',
              editable: (params) => {
                return !this.disabled && !params.data._hiddenCheckbox
              }
            }
          ]
        },
        {
          headerName: this.$t('grid.title.quantity'),
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'sContractQty',
              headerClass: 'c-header_child',
              editable: (params) => {
                return !this.disabled && !params.data._hiddenCheckbox
              },
              cellStyle: { textAlign: 'right' },
              cellEditorFramework: Vue.extend(
                Middleware.createComponent(
                  'CndInputNumber',
                  {
                    mark: 'sContractQty',
                    type: 'number',
                    decimalDigit: 4
                  },
                  {
                    blur: ({ event, rowData, middleware }) => {
                      rowData.data.sContractQty = +event.target.value
                      this.calculateData(rowData.data, middleware)
                      this.$refs.aggrid.gridApi.refreshCells(rowData)
                    }
                  }
                )),
              valueFormatter: params => {
                return SteelFormat.formatThousandthSign(params.data.sContractQty, 4)
              }
            }
          ]
        },
        {
          headerName: this.$t('grid.others.logisticsUnitPrice'),
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'sPrice',
              headerClass: 'c-header_child',
              editable: (params) => {
                return !this.disabled && !params.data._hiddenCheckbox
              },
              cellStyle: { textAlign: 'right' },
              cellEditorFramework: Vue.extend(Middleware.createComponent(
                'CndInputNumber',
                {
                  mark: 'sPrice',
                  type: 'number',
                  decimalDigit: 6
                },
                {
                  blur: ({ event, rowData, middleware }) => {
                    rowData.data.sPrice = +event.target.value
                    this.calculateData(rowData.data, middleware)
                    this.$refs.aggrid.gridApi.refreshCells(rowData)
                  }
                }
              )),
              valueFormatter: params => {
                return params.data._hiddenCheckbox ? null : SteelFormat.formatThousandthSign(params.value, 6)
              }
            }
          ]
        },
        {
          headerName: this.$t('grid.others.taxDifferenceSplit'),
          children: [
            {
              headerName: '9%',
              field: 'sField1',
              editable: (params) => {
                return !this.disabled && !params.data._hiddenCheckbox
              },
              cellStyle: { textAlign: 'right' },
              cellEditorFramework: Vue.extend(Middleware.createComponent(
                'CndInputNumber',
                {
                  mark: 'sField1',
                  type: 'number',
                  decimalDigit: 6
                },
                {
                  blur: ({ event, rowData, middleware }) => {
                    rowData.data.sField1 = +event.target.value || 0
                    this.calculateData(rowData.data, middleware)
                    this.$refs.aggrid.gridApi.refreshCells(rowData)
                  }
                }
              )),
              valueFormatter: params => {
                return params.data._hiddenCheckbox ? null : SteelFormat.formatThousandthSign(params.value, 6)
              }
            },
            {
              headerName: '6%',
              field: 'sField2',
              editable: (params) => {
                return !this.disabled && !params.data._hiddenCheckbox
              },
              cellStyle: { textAlign: 'right' },
              cellEditorFramework: Vue.extend(Middleware.createComponent(
                'CndInputNumber',
                {
                  mark: 'sField2',
                  type: 'number',
                  decimalDigit: 6
                },
                {
                  blur: ({ event, rowData, middleware }) => {
                    rowData.data.sField2 = +event.target.value || 0
                    this.calculateData(rowData.data, middleware)
                    this.$refs.aggrid.gridApi.refreshCells(rowData)
                  }
                }
              )),
              valueFormatter: params => {
                return params.data._hiddenCheckbox ? null : SteelFormat.formatThousandthSign(params.value, 6)
              }
            },
            {
              headerName: '0%',
              field: 'sField3',
              editable: (params) => {
                return !this.disabled && !params.data._hiddenCheckbox
              },
              cellStyle: { textAlign: 'right' },
              cellEditorFramework: Vue.extend(Middleware.createComponent(
                'CndInputNumber',
                {
                  mark: 'sField3',
                  type: 'number',
                  decimalDigit: 6
                },
                {
                  blur: ({ event, rowData, middleware }) => {
                    rowData.data.sField3 = +event.target.value || 0
                    this.calculateData(rowData.data, middleware)
                    this.$refs.aggrid.gridApi.refreshCells(rowData)
                  }
                }
              )),
              valueFormatter: params => {
                return params.data._hiddenCheckbox ? null : SteelFormat.formatThousandthSign(params.value, 6)
              }
            },
            {
              headerName: '3%',
              field: 'sField4',
              editable: (params) => {
                return !this.disabled && !params.data._hiddenCheckbox
              },
              cellStyle: { textAlign: 'right' },
              cellEditorFramework: Vue.extend(Middleware.createComponent(
                'CndInputNumber',
                {
                  mark: 'sField4',
                  type: 'number',
                  decimalDigit: 6
                },
                {
                  blur: ({ event, rowData, middleware }) => {
                    rowData.data.sField4 = +event.target.value || 0
                    this.calculateData(rowData.data, middleware)
                    this.$refs.aggrid.gridApi.refreshCells(rowData)
                  }
                }
              )),
              valueFormatter: params => {
                return params.data._hiddenCheckbox ? null : SteelFormat.formatThousandthSign(params.value, 6)
              }
            },
            {
              headerName: '13%',
              field: 'sField6',
              editable: (params) => {
                return !this.disabled && !params.data._hiddenCheckbox
              },
              cellStyle: { textAlign: 'right' },
              cellEditorFramework: Vue.extend(Middleware.createComponent(
                'CndInputNumber',
                {
                  mark: 'sField6',
                  type: 'number',
                  decimalDigit: 6
                },
                {
                  blur: ({ event, rowData, middleware }) => {
                    rowData.data.sField6 = +event.target.value || 0
                    this.calculateData(rowData.data, middleware)
                    this.$refs.aggrid.gridApi.refreshCells(rowData)
                  }
                }
              )),
              valueFormatter: params => {
                return params.data._hiddenCheckbox ? null : SteelFormat.formatThousandthSign(params.value, 6)
              }
            },
            {
              headerName: '12%',
              field: 'sField19',
              editable: (params) => {
                return !this.disabled && !params.data._hiddenCheckbox
              },
              cellStyle: { textAlign: 'right' },
              cellEditorFramework: Vue.extend(Middleware.createComponent(
                'CndInputNumber',
                {
                  mark: 'sField19',
                  type: 'number',
                  decimalDigit: 6
                },
                {
                  blur: ({ event, rowData, middleware }) => {
                    rowData.data.sField19 = +event.target.value || 0
                    this.calculateData(rowData.data, middleware)
                    this.$refs.aggrid.gridApi.refreshCells(rowData)
                  }
                }
              )),
              valueFormatter: params => {
                return params.data._hiddenCheckbox ? null : SteelFormat.formatThousandthSign(params.value, 6)
              }
            },
            {
              headerName: '11%',
              field: 'sField18',
              editable: (params) => {
                return !this.disabled && !params.data._hiddenCheckbox
              },
              cellStyle: { textAlign: 'right' },
              cellEditorFramework: Vue.extend(Middleware.createComponent(
                'CndInputNumber',
                {
                  mark: 'sField18',
                  type: 'number',
                  decimalDigit: 6
                },
                {
                  blur: ({ event, rowData, middleware }) => {
                    rowData.data.sField18 = +event.target.value || 0
                    this.calculateData(rowData.data, middleware)
                    this.$refs.aggrid.gridApi.refreshCells(rowData)
                  }
                }
              )),
              valueFormatter: params => {
                return params.data._hiddenCheckbox ? null : SteelFormat.formatThousandthSign(params.value, 6)
              }
            },
            {
              headerName: '10%',
              field: 'sField17',
              editable: (params) => {
                return !this.disabled && !params.data._hiddenCheckbox
              },
              cellStyle: { textAlign: 'right' },
              cellEditorFramework: Vue.extend(Middleware.createComponent(
                'CndInputNumber',
                {
                  mark: 'sField17',
                  type: 'number',
                  decimalDigit: 6
                },
                {
                  blur: ({ event, rowData, middleware }) => {
                    rowData.data.sField17 = +event.target.value || 0
                    this.calculateData(rowData.data, middleware)
                    this.$refs.aggrid.gridApi.refreshCells(rowData)
                  }
                }
              )),
              valueFormatter: params => {
                return params.data._hiddenCheckbox ? null : SteelFormat.formatThousandthSign(params.value, 6)
              }
            },
            {
              headerName: '8%',
              field: 'sField16',
              editable: (params) => {
                return !this.disabled && !params.data._hiddenCheckbox
              },
              cellStyle: { textAlign: 'right' },
              cellEditorFramework: Vue.extend(Middleware.createComponent(
                'CndInputNumber',
                {
                  mark: 'sField16',
                  type: 'number',
                  decimalDigit: 6
                },
                {
                  blur: ({ event, rowData, middleware }) => {
                    rowData.data.sField16 = +event.target.value || 0
                    this.calculateData(rowData.data, middleware)
                    this.$refs.aggrid.gridApi.refreshCells(rowData)
                  }
                }
              )),
              valueFormatter: params => {
                return params.data._hiddenCheckbox ? null : SteelFormat.formatThousandthSign(params.value, 6)
              }
            },
            {
              headerName: '7%',
              field: 'sField15',
              editable: (params) => {
                return !this.disabled && !params.data._hiddenCheckbox
              },
              cellStyle: { textAlign: 'right' },
              cellEditorFramework: Vue.extend(Middleware.createComponent(
                'CndInputNumber',
                {
                  mark: 'sField15',
                  type: 'number',
                  decimalDigit: 6
                },
                {
                  blur: ({ event, rowData, middleware }) => {
                    rowData.data.sField15 = +event.target.value || 0
                    this.calculateData(rowData.data, middleware)
                    this.$refs.aggrid.gridApi.refreshCells(rowData)
                  }
                }
              )),
              valueFormatter: params => {
                return params.data._hiddenCheckbox ? null : SteelFormat.formatThousandthSign(params.value, 6)
              }
            },
            {
              headerName: '5%',
              field: 'sField14',
              editable: (params) => {
                return !this.disabled && !params.data._hiddenCheckbox
              },
              cellStyle: { textAlign: 'right' },
              cellEditorFramework: Vue.extend(Middleware.createComponent(
                'CndInputNumber',
                {
                  mark: 'sField14',
                  type: 'number',
                  decimalDigit: 6
                },
                {
                  blur: ({ event, rowData, middleware }) => {
                    rowData.data.sField14 = +event.target.value || 0
                    this.calculateData(rowData.data, middleware)
                    this.$refs.aggrid.gridApi.refreshCells(rowData)
                  }
                }
              )),
              valueFormatter: params => {
                return params.data._hiddenCheckbox ? null : SteelFormat.formatThousandthSign(params.value, 6)
              }
            },
            {
              headerName: '4%',
              field: 'sField13',
              editable: (params) => {
                return !this.disabled && !params.data._hiddenCheckbox
              },
              cellStyle: { textAlign: 'right' },
              cellEditorFramework: Vue.extend(Middleware.createComponent(
                'CndInputNumber',
                {
                  mark: 'sField13',
                  type: 'number',
                  decimalDigit: 6
                },
                {
                  blur: ({ event, rowData, middleware }) => {
                    rowData.data.sField13 = +event.target.value || 0
                    this.calculateData(rowData.data, middleware)
                    this.$refs.aggrid.gridApi.refreshCells(rowData)
                  }
                }
              )),
              valueFormatter: params => {
                return params.data._hiddenCheckbox ? null : SteelFormat.formatThousandthSign(params.value, 6)
              }
            },
            {
              headerName: '2%',
              field: 'sField12',
              editable: (params) => {
                return !this.disabled && !params.data._hiddenCheckbox
              },
              cellStyle: { textAlign: 'right' },
              cellEditorFramework: Vue.extend(Middleware.createComponent(
                'CndInputNumber',
                {
                  mark: 'sField12',
                  type: 'number',
                  decimalDigit: 6
                },
                {
                  blur: ({ event, rowData, middleware }) => {
                    rowData.data.sField12 = +event.target.value || 0
                    this.calculateData(rowData.data, middleware)
                    this.$refs.aggrid.gridApi.refreshCells(rowData)
                  }
                }
              )),
              valueFormatter: params => {
                return params.data._hiddenCheckbox ? null : SteelFormat.formatThousandthSign(params.value, 6)
              }
            },
            {
              headerName: '1%',
              field: 'sField11',
              editable: (params) => {
                return !this.disabled && !params.data._hiddenCheckbox
              },
              cellStyle: { textAlign: 'right' },
              cellEditorFramework: Vue.extend(Middleware.createComponent(
                'CndInputNumber',
                {
                  mark: 'sField11',
                  type: 'number',
                  decimalDigit: 6
                },
                {
                  blur: ({ event, rowData, middleware }) => {
                    rowData.data.sField11 = +event.target.value || 0
                    this.calculateData(rowData.data, middleware)
                    this.$refs.aggrid.gridApi.refreshCells(rowData)
                  }
                }
              )),
              valueFormatter: params => {
                return params.data._hiddenCheckbox ? null : SteelFormat.formatThousandthSign(params.value, 6)
              }
            }
          ]
        },
        {
          headerName: this.$t('grid.others.actualInsuredUnitPriceExcludingTax'),
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'sField5',
              width: '200',
              headerClass: 'c-header_child',
              editable: (params) => {
                return !this.disabled && !params.data._hiddenCheckbox
              },
              cellStyle: { textAlign: 'right' },
              cellEditorFramework: Vue.extend(Middleware.createComponent(
                'CndInputNumber',
                {
                  mark: 'sField5',
                  type: 'number',
                  decimalDigit: 2
                },
                {
                  blur: ({ event, rowData, middleware }) => {
                    rowData.data.sField5 = +event.target.value
                    this.calculateData(rowData.data, middleware)
                    this.$refs.aggrid.gridApi.refreshCells(rowData)
                  }
                }
              )),
              valueFormatter: params => {
                return params.data._hiddenCheckbox ? null : SteelFormat.formatPrice(params.data.sField5)
              }
            }
          ]
        },
        {
          headerName: this.$t('grid.others.taxDifferenceReceivable'),
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'sDiffTaxAmt',
              headerClass: 'c-header_child',
              cellStyle: { textAlign: 'right' },
              valueFormatter: params => {
                return SteelFormat.formatPrice(params.data.sDiffTaxAmt)
              }
            }
          ]
        },
        // {
        //   headerName: '实收税差',
        //   headerClass: 'c-header_parent',
        //   children: [
        //     {
        //       headerName: '',
        //       field: 'sDiffTaxAmt',
        //       headerClass: 'c-header_child',
        //       cellStyle: { textAlign: 'right' },
        //       editable: (params) => {
        //         return !this.disabled && !params.data._hiddenCheckbox
        //       },
        //       cellEditorFramework: Vue.extend(Middleware.createComponent(
        //         'CndInputNumber',
        //         {
        //           mark: 'sDiffTaxAmt',
        //           type: 'number',
        //           decimalDigit: 2
        //         },
        //         {
        //           blur: ({ event, rowData, middleware }) => {
        //             rowData.data.sDiffTaxAmt = +event.target.value
        //             this.calculateData(rowData.data, middleware, 'sDiffTaxAmt')
        //             this.$refs.aggrid.gridApi.refreshCells(rowData)
        //           }
        //         }
        //       )),
        //       valueFormatter: params => {
        //         return SteelFormat.formatPrice(params.data.sDiffTaxAmt)
        //       }
        //     }
        //   ]
        // },
        // {
        //   headerName: this.$t('grid.others.logisticsCostsReceiferenceKey'),
        //   headerClass: 'c-header_parent',
        //   children: [
        //     {
        //       headerName: '',
        //       field: 'sTaxAmt',
        //       width: '200',
        //       headerClass: 'c-header_child',
        //       cellStyle: { textAlign: 'right' },
        //       valueFormatter: params => {
        //         return SteelFormat.formatPrice(params.data.sTaxAmt)
        //       }
        //     }
        //   ]
        // },
        {
          headerName: this.$t('grid.others.logisticsFeesReceivferenceKey'),
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'sCdtTaxAmt',
              width: '200',
              headerClass: 'c-header_child',
              cellStyle: { textAlign: 'right' },
              valueFormatter: params => {
                return SteelFormat.formatPrice(params.data.sCdtTaxAmt)
              }
            }
          ]
        }
      ],
      SHcolumnDefs: [
        {
          headerName: this.$t('grid.others.transportContractNumber'),
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'sLogisticsContractCode',
              headerClass: 'c-header_child',
              editable: (params) => {
                return !this.disabled && !params.data._hiddenCheckbox
              }
            }
          ]
        },
        {
          headerName: this.$t('grid.others.logisticsSupplier'),
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'vSupplierName',
              headerClass: 'c-header_child',
              editable: (params) => {
                return !this.disabled && !params.data._hiddenCheckbox
              },
              cellEditorFramework: Vue.extend(Middleware.createComponent(
                'AgSearchSelect',
                {
                  mark: 'vSupplierName',
                  type: 'customer',
                  placeholder: this.$t('grid.others.pleaseEnterTheSearch')
                },
                {
                  getOption: (option, params) => {
                    this.rowData[params.rowIndex].sSupplierId = option.sId
                  }
                }
              ))
            }
          ]
        },
        {
          headerName: this.$t('grid.others.expenseType'),
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'sLogisticsType',
              headerClass: 'c-header_child',
              editable: (params) => {
                return !this.disabled && !params.data._hiddenCheckbox
              },
              valueGetter: params => {
                const filterItem = this.artnoList.filter(item => item.sArtnoCode === params.data.sLogisticsType)
                return filterItem.length ? filterItem[0].sCnName : params.data.sLogisticsType
              },
              cellEditorFramework: Vue.extend(Middleware.createComponent(
                'AgGridSelect',
                {
                  mark: 'sLogisticsType',
                  config: {
                    label: 'sCnName',
                    value: 'sArtnoCode'
                  },
                  filterable: false,
                  remote: false,
                  searchLimit: 0,
                  placeholder: this.$t('grid.others.pleaseEnterTheSearch'),
                  queryMethod: this.getArtnoList
                },
                {
                  getOption: (option, params, middleware) => {
                    if (option.sArtnoCode === 'TRAN30') {
                      params.data.sField5 = 2
                      middleware.rendered.sField5.setValue(2)
                      this.calculateData(params.data)
                    } else {
                      params.data.sField5 = 0
                      middleware.rendered.sField5.setValue(0)
                      this.calculateData(params.data)
                    }
                    this.rowData[params.rowIndex].sLogisticsType = option.sArtnoCode
                    this.$refs.aggrid.gridApi.refreshCells(params)
                  }
                }
              ))
            }
          ]
        },
        {
          headerName: this.$t('grid.others.shipName'),
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'sVesselName',
              headerClass: 'c-header_child',
              editable: (params) => {
                return !this.disabled && !params.data._hiddenCheckbox
              }
            }
          ]
        },
        {
          headerName: this.$t('grid.title.quantity'),
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'sContractQty',
              headerClass: 'c-header_child',
              editable: (params) => {
                return !this.disabled && !params.data._hiddenCheckbox
              },
              cellStyle: { textAlign: 'right' },
              cellEditorFramework: Vue.extend(
                Middleware.createComponent(
                  'CndInputNumber',
                  {
                    mark: 'sContractQty',
                    type: 'number',
                    decimalDigit: 4
                  },
                  {
                    blur: ({ event, rowData, middleware }) => {
                      rowData.data.sContractQty = +event.target.value
                      this.calculateData(rowData.data)
                      this.$refs.aggrid.gridApi.refreshCells(rowData)
                    }
                  }
                )),
              valueFormatter: params => {
                return SteelFormat.formatThousandthSign(params.data.sContractQty, 4)
              }
            }
          ]
        },
        {
          headerName: this.$t('grid.others.logisticsUnitPrice'),
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'sPrice',
              headerClass: 'c-header_child',
              editable: (params) => {
                return !this.disabled && !params.data._hiddenCheckbox
              },
              cellStyle: { textAlign: 'right' },
              cellEditorFramework: Vue.extend(Middleware.createComponent(
                'CndInputNumber',
                {
                  mark: 'sPrice',
                  type: 'number',
                  decimalDigit: 6
                },
                {
                  blur: ({ event, rowData, middleware }) => {
                    rowData.data.sPrice = +event.target.value
                    this.calculateData(rowData.data)
                    this.$refs.aggrid.gridApi.refreshCells(rowData)
                  }
                }
              )),
              valueFormatter: params => {
                return params.data._hiddenCheckbox ? null : SteelFormat.formatThousandthSign(params.value, 6)
              }
            }
          ]
        },
        {
          headerName: this.$t('grid.others.taxDifferenceSplit'),
          children: [
            {
              headerName: '13%',
              field: 'sField6',
              editable: (params) => {
                return !this.disabled && !params.data._hiddenCheckbox
              },
              cellStyle: { textAlign: 'right' },
              cellEditorFramework: Vue.extend(Middleware.createComponent(
                'CndInputNumber',
                {
                  mark: 'sField6',
                  type: 'number',
                  decimalDigit: 6
                }
              )),
              valueFormatter: params => {
                return params.data._hiddenCheckbox ? null : SteelFormat.formatThousandthSign(params.value, 6)
              }
            },
            {
              headerName: '9%',
              field: 'sField1',
              editable: (params) => {
                return !this.disabled && !params.data._hiddenCheckbox
              },
              cellStyle: { textAlign: 'right' },
              cellEditorFramework: Vue.extend(Middleware.createComponent(
                'CndInputNumber',
                {
                  mark: 'sField1',
                  type: 'number',
                  decimalDigit: 6
                },
                {
                  blur: ({ event, rowData, middleware }) => {
                    rowData.data.sField1 = +event.target.value || 0
                    this.calculateData(rowData.data)
                    this.$refs.aggrid.gridApi.refreshCells(rowData)
                  }
                }
              )),
              valueFormatter: params => {
                return params.data._hiddenCheckbox ? null : SteelFormat.formatThousandthSign(params.value, 6)
              }
            },
            {
              headerName: '6%',
              field: 'sField2',
              editable: (params) => {
                return !this.disabled && !params.data._hiddenCheckbox
              },
              cellStyle: { textAlign: 'right' },
              cellEditorFramework: Vue.extend(Middleware.createComponent(
                'CndInputNumber',
                {
                  mark: 'sField2',
                  type: 'number',
                  decimalDigit: 6
                },
                {
                  blur: ({ event, rowData, middleware }) => {
                    rowData.data.sField2 = +event.target.value || 0
                    this.calculateData(rowData.data)
                    this.$refs.aggrid.gridApi.refreshCells(rowData)
                  }
                }
              )),
              valueFormatter: params => {
                return params.data._hiddenCheckbox ? null : SteelFormat.formatThousandthSign(params.value, 6)
              }
            },
            {
              headerName: '0%',
              field: 'sField3',
              editable: (params) => {
                return !this.disabled && !params.data._hiddenCheckbox
              },
              cellStyle: { textAlign: 'right' },
              cellEditorFramework: Vue.extend(Middleware.createComponent(
                'CndInputNumber',
                {
                  mark: 'sField3',
                  type: 'number',
                  decimalDigit: 6
                },
                {
                  blur: ({ event, rowData, middleware }) => {
                    rowData.data.sField3 = +event.target.value || 0
                    this.calculateData(rowData.data)
                    this.$refs.aggrid.gridApi.refreshCells(rowData)
                  }
                }
              )),
              valueFormatter: params => {
                return params.data._hiddenCheckbox ? null : SteelFormat.formatThousandthSign(params.value, 6)
              }
            },
            {
              headerName: '3%',
              field: 'sField4',
              editable: (params) => {
                return !this.disabled && !params.data._hiddenCheckbox
              },
              cellStyle: { textAlign: 'right' },
              cellEditorFramework: Vue.extend(Middleware.createComponent(
                'CndInputNumber',
                {
                  mark: 'sField4',
                  type: 'number',
                  decimalDigit: 6
                },
                {
                  blur: ({ event, rowData, middleware }) => {
                    rowData.data.sField4 = +event.target.value || 0
                    this.calculateData(rowData.data)
                    this.$refs.aggrid.gridApi.refreshCells(rowData)
                  }
                }
              )),
              valueFormatter: params => {
                return params.data._hiddenCheckbox ? null : SteelFormat.formatThousandthSign(params.value, 6)
              }
            },
            {
              headerName: '12%',
              field: 'sField19',
              editable: (params) => {
                return !this.disabled && !params.data._hiddenCheckbox
              },
              cellStyle: { textAlign: 'right' },
              cellEditorFramework: Vue.extend(Middleware.createComponent(
                'CndInputNumber',
                {
                  mark: 'sField19',
                  type: 'number',
                  decimalDigit: 6
                },
                {
                  blur: ({ event, rowData, middleware }) => {
                    rowData.data.sField19 = +event.target.value || 0
                    this.calculateData(rowData.data, middleware)
                    this.$refs.aggrid.gridApi.refreshCells(rowData)
                  }
                }
              )),
              valueFormatter: params => {
                return params.data._hiddenCheckbox ? null : SteelFormat.formatThousandthSign(params.value, 6)
              }
            },
            {
              headerName: '11%',
              field: 'sField18',
              editable: (params) => {
                return !this.disabled && !params.data._hiddenCheckbox
              },
              cellStyle: { textAlign: 'right' },
              cellEditorFramework: Vue.extend(Middleware.createComponent(
                'CndInputNumber',
                {
                  mark: 'sField18',
                  type: 'number',
                  decimalDigit: 6
                },
                {
                  blur: ({ event, rowData, middleware }) => {
                    rowData.data.sField18 = +event.target.value || 0
                    this.calculateData(rowData.data, middleware)
                    this.$refs.aggrid.gridApi.refreshCells(rowData)
                  }
                }
              )),
              valueFormatter: params => {
                return params.data._hiddenCheckbox ? null : SteelFormat.formatThousandthSign(params.value, 6)
              }
            },
            {
              headerName: '10%',
              field: 'sField17',
              editable: (params) => {
                return !this.disabled && !params.data._hiddenCheckbox
              },
              cellStyle: { textAlign: 'right' },
              cellEditorFramework: Vue.extend(Middleware.createComponent(
                'CndInputNumber',
                {
                  mark: 'sField17',
                  type: 'number',
                  decimalDigit: 6
                },
                {
                  blur: ({ event, rowData, middleware }) => {
                    rowData.data.sField17 = +event.target.value || 0
                    this.calculateData(rowData.data, middleware)
                    this.$refs.aggrid.gridApi.refreshCells(rowData)
                  }
                }
              )),
              valueFormatter: params => {
                return params.data._hiddenCheckbox ? null : SteelFormat.formatThousandthSign(params.value, 6)
              }
            },
            {
              headerName: '8%',
              field: 'sField16',
              editable: (params) => {
                return !this.disabled && !params.data._hiddenCheckbox
              },
              cellStyle: { textAlign: 'right' },
              cellEditorFramework: Vue.extend(Middleware.createComponent(
                'CndInputNumber',
                {
                  mark: 'sField16',
                  type: 'number',
                  decimalDigit: 6
                },
                {
                  blur: ({ event, rowData, middleware }) => {
                    rowData.data.sField16 = +event.target.value || 0
                    this.calculateData(rowData.data, middleware)
                    this.$refs.aggrid.gridApi.refreshCells(rowData)
                  }
                }
              )),
              valueFormatter: params => {
                return params.data._hiddenCheckbox ? null : SteelFormat.formatThousandthSign(params.value, 6)
              }
            },
            {
              headerName: '7%',
              field: 'sField15',
              editable: (params) => {
                return !this.disabled && !params.data._hiddenCheckbox
              },
              cellStyle: { textAlign: 'right' },
              cellEditorFramework: Vue.extend(Middleware.createComponent(
                'CndInputNumber',
                {
                  mark: 'sField15',
                  type: 'number',
                  decimalDigit: 6
                },
                {
                  blur: ({ event, rowData, middleware }) => {
                    rowData.data.sField15 = +event.target.value || 0
                    this.calculateData(rowData.data, middleware)
                    this.$refs.aggrid.gridApi.refreshCells(rowData)
                  }
                }
              )),
              valueFormatter: params => {
                return params.data._hiddenCheckbox ? null : SteelFormat.formatThousandthSign(params.value, 6)
              }
            },
            {
              headerName: '5%',
              field: 'sField14',
              editable: (params) => {
                return !this.disabled && !params.data._hiddenCheckbox
              },
              cellStyle: { textAlign: 'right' },
              cellEditorFramework: Vue.extend(Middleware.createComponent(
                'CndInputNumber',
                {
                  mark: 'sField14',
                  type: 'number',
                  decimalDigit: 6
                },
                {
                  blur: ({ event, rowData, middleware }) => {
                    rowData.data.sField14 = +event.target.value || 0
                    this.calculateData(rowData.data, middleware)
                    this.$refs.aggrid.gridApi.refreshCells(rowData)
                  }
                }
              )),
              valueFormatter: params => {
                return params.data._hiddenCheckbox ? null : SteelFormat.formatThousandthSign(params.value, 6)
              }
            },
            {
              headerName: '4%',
              field: 'sField13',
              editable: (params) => {
                return !this.disabled && !params.data._hiddenCheckbox
              },
              cellStyle: { textAlign: 'right' },
              cellEditorFramework: Vue.extend(Middleware.createComponent(
                'CndInputNumber',
                {
                  mark: 'sField13',
                  type: 'number',
                  decimalDigit: 6
                },
                {
                  blur: ({ event, rowData, middleware }) => {
                    rowData.data.sField13 = +event.target.value || 0
                    this.calculateData(rowData.data, middleware)
                    this.$refs.aggrid.gridApi.refreshCells(rowData)
                  }
                }
              )),
              valueFormatter: params => {
                return params.data._hiddenCheckbox ? null : SteelFormat.formatThousandthSign(params.value, 6)
              }
            },
            {
              headerName: '2%',
              field: 'sField12',
              editable: (params) => {
                return !this.disabled && !params.data._hiddenCheckbox
              },
              cellStyle: { textAlign: 'right' },
              cellEditorFramework: Vue.extend(Middleware.createComponent(
                'CndInputNumber',
                {
                  mark: 'sField12',
                  type: 'number',
                  decimalDigit: 6
                },
                {
                  blur: ({ event, rowData, middleware }) => {
                    rowData.data.sField12 = +event.target.value || 0
                    this.calculateData(rowData.data, middleware)
                    this.$refs.aggrid.gridApi.refreshCells(rowData)
                  }
                }
              )),
              valueFormatter: params => {
                return params.data._hiddenCheckbox ? null : SteelFormat.formatThousandthSign(params.value, 6)
              }
            },
            {
              headerName: '1%',
              field: 'sField11',
              editable: (params) => {
                return !this.disabled && !params.data._hiddenCheckbox
              },
              cellStyle: { textAlign: 'right' },
              cellEditorFramework: Vue.extend(Middleware.createComponent(
                'CndInputNumber',
                {
                  mark: 'sField11',
                  type: 'number',
                  decimalDigit: 6
                },
                {
                  blur: ({ event, rowData, middleware }) => {
                    rowData.data.sField11 = +event.target.value || 0
                    this.calculateData(rowData.data, middleware)
                    this.$refs.aggrid.gridApi.refreshCells(rowData)
                  }
                }
              )),
              valueFormatter: params => {
                return params.data._hiddenCheckbox ? null : SteelFormat.formatThousandthSign(params.value, 6)
              }
            }
          ]
        },
        {
          headerName: this.$t('grid.others.actualInsuredUnitPriceExcludingTax'),
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'sField5',
              width: '200',
              headerClass: 'c-header_child',
              editable: (params) => {
                return !this.disabled && !params.data._hiddenCheckbox
              },
              cellStyle: { textAlign: 'right' },
              cellEditorFramework: Vue.extend(Middleware.createComponent(
                'CndInputNumber',
                {
                  mark: 'sField5',
                  type: 'number',
                  decimalDigit: 2
                },
                {
                  blur: ({ event, rowData, middleware }) => {
                    rowData.data.sField5 = +event.target.value
                    this.calculateData(rowData.data)
                    this.$refs.aggrid.gridApi.refreshCells(rowData)
                  }
                }
              )),
              valueFormatter: params => {
                return params.data._hiddenCheckbox ? null : SteelFormat.formatThousandthSign(params.value, 3)
              }
            }
          ]
        },
        {
          headerName: this.$t('grid.others.taxDifferenceReceivable'),
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'sDiffTaxAmt',
              headerClass: 'c-header_child',
              cellStyle: { textAlign: 'right' },
              valueFormatter: params => {
                return SteelFormat.formatPrice(params.data.sDiffTaxAmt)
              }
            }
          ]
        },
        {
          headerName: this.$t('grid.others.logisticsCostsReceiferenceKey'),
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'sTaxAmt',
              width: '200',
              headerClass: 'c-header_child',
              cellStyle: { textAlign: 'right' },
              valueFormatter: params => {
                return SteelFormat.formatPrice(params.data.sTaxAmt)
              }
            }
          ]
        },
        {
          headerName: this.$t('grid.others.logisticsFeesReceivferenceKey'),
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'sCdtTaxAmt',
              width: '200',
              headerClass: 'c-header_child',
              cellStyle: { textAlign: 'right' },
              valueFormatter: params => {
                return SteelFormat.formatPrice(params.data.sCdtTaxAmt)
              }
            }
          ]
        }
      ],
      XMWZcolumnDefs: [
        {
          headerName: this.$t('grid.others.transportContractNumber'),
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'sLogisticsContractCode',
              headerClass: 'c-header_child',
              editable: (params) => {
                return !this.disabled && !params.data._hiddenCheckbox
              }
            }
          ]
        },
        {
          headerName: this.$t('grid.others.logisticsSupplier'),
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'vSupplierName',
              headerClass: 'c-header_child',
              editable: (params) => {
                return !this.disabled && !params.data._hiddenCheckbox
              },
              cellEditorFramework: Vue.extend(Middleware.createComponent(
                'AgSearchSelect',
                {
                  mark: 'vSupplierName',
                  type: 'customer',
                  placeholder: this.$t('grid.others.pleaseEnterTheSearch')
                },
                {
                  getOption: (option, params) => {
                    this.rowData[params.rowIndex].sSupplierId = option.sId
                  }
                }
              ))
            }
          ]
        },
        {
          headerName: this.$t('grid.others.expenseType'),
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'sLogisticsType',
              headerClass: 'c-header_child',
              editable: (params) => {
                return !this.disabled && !params.data._hiddenCheckbox
              },
              valueGetter: params => {
                const filterItem = this.artnoList.filter(item => item.sArtnoCode === params.data.sLogisticsType)
                return filterItem.length ? filterItem[0].sCnName : params.data.sLogisticsType
              },
              cellEditorFramework: Vue.extend(Middleware.createComponent(
                'AgGridSelect',
                {
                  mark: 'sLogisticsType',
                  config: {
                    label: 'sCnName',
                    value: 'sArtnoCode'
                  },
                  filterable: false,
                  remote: false,
                  searchLimit: 0,
                  placeholder: this.$t('grid.others.pleaseEnterTheSearch'),
                  queryMethod: this.getArtnoList
                },
                {
                  getOption: (option, params, middleware) => {
                    if (option.sArtnoCode === 'TRAN30') {
                      params.data.sField5 = 2
                      middleware.rendered.sField5.setValue(2)
                      this.calculateData(params.data)
                    } else {
                      params.data.sField5 = 0
                      middleware.rendered.sField5.setValue(0)
                      this.calculateData(params.data)
                    }
                    this.rowData[params.rowIndex].sLogisticsType = option.sArtnoCode
                    this.$refs.aggrid.gridApi.refreshCells(params)
                  }
                }
              ))
            }
          ]
        },
        {
          headerName: this.$t('grid.others.shipName'),
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'sVesselName',
              headerClass: 'c-header_child',
              editable: (params) => {
                return !this.disabled && !params.data._hiddenCheckbox
              }
            }
          ]
        },
        {
          headerName: this.$t('grid.title.quantity'),
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'sContractQty',
              headerClass: 'c-header_child',
              editable: (params) => {
                return !this.disabled && !params.data._hiddenCheckbox
              },
              cellStyle: { textAlign: 'right' },
              cellEditorFramework: Vue.extend(
                Middleware.createComponent(
                  'CndInputNumber',
                  {
                    mark: 'sContractQty',
                    type: 'number',
                    decimalDigit: 4
                  },
                  {
                    blur: ({ event, rowData, middleware }) => {
                      rowData.data.sContractQty = +event.target.value
                      this.calculateData(rowData.data)
                      this.$refs.aggrid.gridApi.refreshCells(rowData)
                    }
                  }
                )),
              valueFormatter: params => {
                return SteelFormat.formatThousandthSign(params.data.sContractQty, 4)
              }
            }
          ]
        },
        {
          headerName: this.$t('grid.others.logisticsUnitPrice'),
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'sPrice',
              headerClass: 'c-header_child',
              editable: (params) => {
                return !this.disabled && !params.data._hiddenCheckbox
              },
              cellStyle: { textAlign: 'right' },
              cellEditorFramework: Vue.extend(Middleware.createComponent(
                'CndInputNumber',
                {
                  mark: 'sPrice',
                  type: 'number',
                  decimalDigit: 6
                },
                {
                  blur: ({ event, rowData, middleware }) => {
                    rowData.data.sPrice = +event.target.value
                    this.calculateData(rowData.data)
                    this.$refs.aggrid.gridApi.refreshCells(rowData)
                  }
                }
              )),
              valueFormatter: params => {
                return params.data._hiddenCheckbox ? null : SteelFormat.formatThousandthSign(params.value, 6)
              }
            }
          ]
        },
        {
          headerName: '税差系数',
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'sField7',
              headerClass: 'c-header_child',
              editable: (params) => {
                return !this.disabled && !params.data._hiddenCheckbox
              },
              cellStyle: { textAlign: 'right' },
              cellEditorFramework: Vue.extend(Middleware.createComponent(
                'CndInputNumber',
                {
                  mark: 'sField7',
                  type: 'number',
                  decimalDigit: 4,
                  range: [1.13, 1.2],
                  default: 1.1479
                },
                {
                  blur: ({ event, rowData, middleware }) => {
                    rowData.data.sField7 = +event.target.value
                    this.calculateData(rowData.data)
                    this.$refs.aggrid.gridApi.refreshCells(rowData)
                  }
                }
              )),
              valueFormatter: params => {
                return params.data._hiddenCheckbox ? null : SteelFormat.formatThousandthSign(params.data.sField7, 4)
              }
            }
          ]
        },
        {
          headerName: this.$t('grid.others.taxDifferenceSplit'),
          children: [
            {
              headerName: '13%',
              field: 'sField6',
              editable: (params) => {
                return !this.disabled && !params.data._hiddenCheckbox
              },
              cellStyle: { textAlign: 'right' },
              cellEditorFramework: Vue.extend(Middleware.createComponent(
                'CndInputNumber',
                {
                  mark: 'sField6',
                  type: 'number',
                  decimalDigit: 6
                },
                {
                  blur: ({ event, rowData, middleware }) => {
                    rowData.data.sField6 = +event.target.value || 0
                    this.calculateData(rowData.data)
                    this.$refs.aggrid.gridApi.refreshCells(rowData)
                  }
                }
              )),
              valueFormatter: params => {
                return params.data._hiddenCheckbox ? null : SteelFormat.formatThousandthSign(params.value, 6)
              }
            },
            {
              headerName: '9%',
              field: 'sField1',
              editable: (params) => {
                return !this.disabled && !params.data._hiddenCheckbox
              },
              cellStyle: { textAlign: 'right' },
              cellEditorFramework: Vue.extend(Middleware.createComponent(
                'CndInputNumber',
                {
                  mark: 'sField1',
                  type: 'number',
                  decimalDigit: 6
                },
                {
                  blur: ({ event, rowData, middleware }) => {
                    rowData.data.sField1 = +event.target.value || 0
                    this.calculateData(rowData.data)
                    this.$refs.aggrid.gridApi.refreshCells(rowData)
                  }
                }
              )),
              valueFormatter: params => {
                return params.data._hiddenCheckbox ? null : SteelFormat.formatThousandthSign(params.value, 6)
              }
            },
            {
              headerName: '6%',
              field: 'sField2',
              editable: (params) => {
                return !this.disabled && !params.data._hiddenCheckbox
              },
              cellStyle: { textAlign: 'right' },
              cellEditorFramework: Vue.extend(Middleware.createComponent(
                'CndInputNumber',
                {
                  mark: 'sField2',
                  type: 'number',
                  decimalDigit: 6
                },
                {
                  blur: ({ event, rowData, middleware }) => {
                    rowData.data.sField2 = +event.target.value || 0
                    this.calculateData(rowData.data)
                    this.$refs.aggrid.gridApi.refreshCells(rowData)
                  }
                }
              )),
              valueFormatter: params => {
                return params.data._hiddenCheckbox ? null : SteelFormat.formatThousandthSign(params.value, 6)
              }
            },
            {
              headerName: '0%',
              field: 'sField3',
              editable: (params) => {
                return !this.disabled && !params.data._hiddenCheckbox
              },
              cellStyle: { textAlign: 'right' },
              cellEditorFramework: Vue.extend(Middleware.createComponent(
                'CndInputNumber',
                {
                  mark: 'sField3',
                  type: 'number',
                  decimalDigit: 6
                },
                {
                  blur: ({ event, rowData, middleware }) => {
                    rowData.data.sField3 = +event.target.value || 0
                    this.calculateData(rowData.data)
                    this.$refs.aggrid.gridApi.refreshCells(rowData)
                  }
                }
              )),
              valueFormatter: params => {
                return params.data._hiddenCheckbox ? null : SteelFormat.formatThousandthSign(params.value, 6)
              }
            },
            {
              headerName: '3%',
              field: 'sField4',
              editable: (params) => {
                return !this.disabled && !params.data._hiddenCheckbox
              },
              cellStyle: { textAlign: 'right' },
              cellEditorFramework: Vue.extend(Middleware.createComponent(
                'CndInputNumber',
                {
                  mark: 'sField4',
                  type: 'number',
                  decimalDigit: 6
                },
                {
                  blur: ({ event, rowData, middleware }) => {
                    rowData.data.sField4 = +event.target.value || 0
                    this.calculateData(rowData.data)
                    this.$refs.aggrid.gridApi.refreshCells(rowData)
                  }
                }
              )),
              valueFormatter: params => {
                return params.data._hiddenCheckbox ? null : SteelFormat.formatThousandthSign(params.value, 6)
              }
            },
            {
              headerName: '12%',
              field: 'sField19',
              editable: (params) => {
                return !this.disabled && !params.data._hiddenCheckbox
              },
              cellStyle: { textAlign: 'right' },
              cellEditorFramework: Vue.extend(Middleware.createComponent(
                'CndInputNumber',
                {
                  mark: 'sField19',
                  type: 'number',
                  decimalDigit: 6
                },
                {
                  blur: ({ event, rowData, middleware }) => {
                    rowData.data.sField19 = +event.target.value || 0
                    this.calculateData(rowData.data, middleware)
                    this.$refs.aggrid.gridApi.refreshCells(rowData)
                  }
                }
              )),
              valueFormatter: params => {
                return params.data._hiddenCheckbox ? null : SteelFormat.formatThousandthSign(params.value, 6)
              }
            },
            {
              headerName: '11%',
              field: 'sField18',
              editable: (params) => {
                return !this.disabled && !params.data._hiddenCheckbox
              },
              cellStyle: { textAlign: 'right' },
              cellEditorFramework: Vue.extend(Middleware.createComponent(
                'CndInputNumber',
                {
                  mark: 'sField18',
                  type: 'number',
                  decimalDigit: 6
                },
                {
                  blur: ({ event, rowData, middleware }) => {
                    rowData.data.sField18 = +event.target.value || 0
                    this.calculateData(rowData.data, middleware)
                    this.$refs.aggrid.gridApi.refreshCells(rowData)
                  }
                }
              )),
              valueFormatter: params => {
                return params.data._hiddenCheckbox ? null : SteelFormat.formatThousandthSign(params.value, 6)
              }
            },
            {
              headerName: '10%',
              field: 'sField17',
              editable: (params) => {
                return !this.disabled && !params.data._hiddenCheckbox
              },
              cellStyle: { textAlign: 'right' },
              cellEditorFramework: Vue.extend(Middleware.createComponent(
                'CndInputNumber',
                {
                  mark: 'sField17',
                  type: 'number',
                  decimalDigit: 6
                },
                {
                  blur: ({ event, rowData, middleware }) => {
                    rowData.data.sField17 = +event.target.value || 0
                    this.calculateData(rowData.data, middleware)
                    this.$refs.aggrid.gridApi.refreshCells(rowData)
                  }
                }
              )),
              valueFormatter: params => {
                return params.data._hiddenCheckbox ? null : SteelFormat.formatThousandthSign(params.value, 6)
              }
            },
            {
              headerName: '8%',
              field: 'sField16',
              editable: (params) => {
                return !this.disabled && !params.data._hiddenCheckbox
              },
              cellStyle: { textAlign: 'right' },
              cellEditorFramework: Vue.extend(Middleware.createComponent(
                'CndInputNumber',
                {
                  mark: 'sField16',
                  type: 'number',
                  decimalDigit: 6
                },
                {
                  blur: ({ event, rowData, middleware }) => {
                    rowData.data.sField16 = +event.target.value || 0
                    this.calculateData(rowData.data, middleware)
                    this.$refs.aggrid.gridApi.refreshCells(rowData)
                  }
                }
              )),
              valueFormatter: params => {
                return params.data._hiddenCheckbox ? null : SteelFormat.formatThousandthSign(params.value, 6)
              }
            },
            {
              headerName: '7%',
              field: 'sField15',
              editable: (params) => {
                return !this.disabled && !params.data._hiddenCheckbox
              },
              cellStyle: { textAlign: 'right' },
              cellEditorFramework: Vue.extend(Middleware.createComponent(
                'CndInputNumber',
                {
                  mark: 'sField15',
                  type: 'number',
                  decimalDigit: 6
                },
                {
                  blur: ({ event, rowData, middleware }) => {
                    rowData.data.sField15 = +event.target.value || 0
                    this.calculateData(rowData.data, middleware)
                    this.$refs.aggrid.gridApi.refreshCells(rowData)
                  }
                }
              )),
              valueFormatter: params => {
                return params.data._hiddenCheckbox ? null : SteelFormat.formatThousandthSign(params.value, 6)
              }
            },
            {
              headerName: '5%',
              field: 'sField14',
              editable: (params) => {
                return !this.disabled && !params.data._hiddenCheckbox
              },
              cellStyle: { textAlign: 'right' },
              cellEditorFramework: Vue.extend(Middleware.createComponent(
                'CndInputNumber',
                {
                  mark: 'sField14',
                  type: 'number',
                  decimalDigit: 6
                },
                {
                  blur: ({ event, rowData, middleware }) => {
                    rowData.data.sField14 = +event.target.value || 0
                    this.calculateData(rowData.data, middleware)
                    this.$refs.aggrid.gridApi.refreshCells(rowData)
                  }
                }
              )),
              valueFormatter: params => {
                return params.data._hiddenCheckbox ? null : SteelFormat.formatThousandthSign(params.value, 6)
              }
            },
            {
              headerName: '4%',
              field: 'sField13',
              editable: (params) => {
                return !this.disabled && !params.data._hiddenCheckbox
              },
              cellStyle: { textAlign: 'right' },
              cellEditorFramework: Vue.extend(Middleware.createComponent(
                'CndInputNumber',
                {
                  mark: 'sField13',
                  type: 'number',
                  decimalDigit: 6
                },
                {
                  blur: ({ event, rowData, middleware }) => {
                    rowData.data.sField13 = +event.target.value || 0
                    this.calculateData(rowData.data, middleware)
                    this.$refs.aggrid.gridApi.refreshCells(rowData)
                  }
                }
              )),
              valueFormatter: params => {
                return params.data._hiddenCheckbox ? null : SteelFormat.formatThousandthSign(params.value, 6)
              }
            },
            {
              headerName: '2%',
              field: 'sField12',
              editable: (params) => {
                return !this.disabled && !params.data._hiddenCheckbox
              },
              cellStyle: { textAlign: 'right' },
              cellEditorFramework: Vue.extend(Middleware.createComponent(
                'CndInputNumber',
                {
                  mark: 'sField12',
                  type: 'number',
                  decimalDigit: 6
                },
                {
                  blur: ({ event, rowData, middleware }) => {
                    rowData.data.sField12 = +event.target.value || 0
                    this.calculateData(rowData.data, middleware)
                    this.$refs.aggrid.gridApi.refreshCells(rowData)
                  }
                }
              )),
              valueFormatter: params => {
                return params.data._hiddenCheckbox ? null : SteelFormat.formatThousandthSign(params.value, 6)
              }
            },
            {
              headerName: '1%',
              field: 'sField11',
              editable: (params) => {
                return !this.disabled && !params.data._hiddenCheckbox
              },
              cellStyle: { textAlign: 'right' },
              cellEditorFramework: Vue.extend(Middleware.createComponent(
                'CndInputNumber',
                {
                  mark: 'sField11',
                  type: 'number',
                  decimalDigit: 6
                },
                {
                  blur: ({ event, rowData, middleware }) => {
                    rowData.data.sField11 = +event.target.value || 0
                    this.calculateData(rowData.data, middleware)
                    this.$refs.aggrid.gridApi.refreshCells(rowData)
                  }
                }
              )),
              valueFormatter: params => {
                return params.data._hiddenCheckbox ? null : SteelFormat.formatThousandthSign(params.value, 6)
              }
            }
          ]
        },
        {
          headerName: this.$t('grid.others.logisticsFeesReceivferenceKey'),
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'sCdtTaxAmt',
              width: '200',
              headerClass: 'c-header_child',
              cellStyle: { textAlign: 'right' },
              valueFormatter: params => {
                return SteelFormat.formatPrice(params.data.sCdtTaxAmt)
              }
            }
          ]
        }
      ],
      rowData: [],
      currRowData: [],
      artnoList: [],
      vManagementCode: '',
      markupFactorType: ''
    }
  },
  computed: {
    sSheetStatus() {
      return this.newInfo.sSheetStatus
    },
    fullRowType() {
      return !this.disabled ? 'parent' : null
    },
    isTrial() {
      return this.info.sExtend23 === '1'
    },
    disabled() {
      return this.isBusinessDisabled('save', this.sSheetStatus) || this.$route.query.disabled === '1' || this.isTrial
    }
  },
  watch: {
    info: {
      immediate: true,
      deep: true,
      handler(val) {
        this.vManagementCode = val.vManagementCode
        this.markupFactorType = val.sExtend3
        this.sIsDiff = val.sIsDiff ?? '0'
        this.sInTaxPoint = val.sInTaxPoint ? val.sInTaxPoint?.split(',') : ['0.09', '0.06', '0.00', '0.03', '0.13']
        this.currInTaxPoint = deepClone(this.sInTaxPoint)
        this.sOutTaxPoint = val.sOutTaxPoint ?? '0.13'
        this.newInfo = val
      }
    }
  },
  mounted() {
    this.getArtnoList()
  },
  methods: {
    updateData() {
      this.$nextTick(() => {
        this.$refs.aggrid.loadTableData()
      })
    },
    handleSelect(e) {
      this.rowData.forEach(el => {
        this.calculateData(el)
      })
      const form = {
        ...this.newInfo,
        sIsDiff: this.sIsDiff
      }
      updatasettle(form).then((res) => {
        logisticsFeeModifies(this.rowData).then(res => {
          this.$message.success(this.$t('grid.others.editSuccessfully'))
          payoverview({ sSettlementId: this.sId }).then(() => {
            this.updateFn(['financialExpense'])
            this.setInfo()
          }).finally(() => {
            this.$refs.aggrid.loadTableData()
          })
        })
      })
    },
    setAggridVisible() {
      setTimeout(() => {
        // 设置显示内容
        this.showField = []
        const uniqueCodeValues = new Set() // 用于存储已处理的 taxValue
        // 筛选 inTaxPointList
        this.inTaxPointList = this.inTaxPointList.filter(item => {
          const isShow = this.sInTaxPoint?.length > 0 && this.sInTaxPoint.includes(item.taxValue)

          // 如果已经存在相同的 taxValue，则跳过
          if (uniqueCodeValues.has(item.taxValue)) {
            return false
          }

          if (isShow) {
            // 添加到已处理集合
            uniqueCodeValues.add(item.taxValue)
          }
          return isShow
        })

        // 设置列显示
        for (const key in fieldObj) {
          if (Object.hasOwnProperty.call(fieldObj, key)) {
            const isShow = this.sInTaxPoint?.length > 0 && this.sInTaxPoint.includes(fieldObj[key])
            isShow && this.showField.push(key)
            this.$refs.aggrid.columnApi.setColumnVisible(key, isShow)
          }
        }
        if (this.vManagementCode === '00014867') {
          this.$refs.aggrid.columnApi.setColumnVisible('sField7', this.markupFactorType === '1')
        }
      }, 0)
    },
    saveInTaxPoint() {
      const newField = []
      for (const key in fieldObj) {
        if (Object.hasOwnProperty.call(fieldObj, key)) {
          const isShow = this.sInTaxPoint?.length > 0 && this.sInTaxPoint.includes(fieldObj[key])
          isShow && newField.push(key)
        }
      }
      // 获取当前选择的进票税点，删除的的税点
      const different = this.showField.filter(el => newField.indexOf(el) === -1)
      // 存在删除税点
      if (different.length > 0) {
        const keyList = [] // 获取删除的税点，是否有值
        different.forEach(item => {
          const isTrue = this.currRowData.some(row => row[item] > 0)
          isTrue && keyList.push(Number(fieldObj[item]) * 100 + '%')
        })
        // 删除的税点 存在值 ： 报错，不可进行保存
        if (keyList.length > 0) {
          this.sInTaxPoint = this.currInTaxPoint
          this.$message.error(`${keyList.join(',')}税点存在值，请清零并且编辑成功后，再操作！`)
          return false
        }
      }
      const form = {
        ...this.newInfo,
        sInTaxPoint: this.sInTaxPoint.length > 0 ? this.sInTaxPoint.toString() : null
      }
      updatasettle(form).then((res) => {
        payoverview({ sSettlementId: this.sId }).then(res => {
          this.setInfo()
        })
      })
    },
    setInfo() {
      settdeliveryget({ id: this.sId }).then((res) => {
        this.sInTaxPoint = res.data.sInTaxPoint?.split(',')
        this.currInTaxPoint = deepClone(res.data.sInTaxPoint?.split(','))
        this.sOutTaxPoint = res.data.sOutTaxPoint ?? '0.13'
        this.sIsDiff = res.data.sIsDiff ?? '0'
        this.markupFactorType = res.data.sExtend3
        this.newInfo = res.data
        this.setAggridVisible()
      })
    },
    openDialog() {
      console.log('this.newInfo', JSON.parse(JSON.stringify(this.newInfo)))
      this.dialogVisible = true
    },
    closeDialog() {
      this.dialogVisible = false
      this.$refs.aggrid.loadTableData()
    },
    onSelect(data) {
      const selectedList = []
      data.forEach(item => {
        selectedList.push({
          sLogisticsContractId: item.sId,
          sLogisticsContractCode: item.sCode,
          sSupplierId: item.sSupplierId,
          vSupplierName: item.vSupplierName,
          sLogisticsType: item.sLoadType,
          sVesselName: item.sVesselName,
          sContractQty: item.vSumQty || 0,
          sPrice: item.vSumPrice || 0,
          sField1: '',
          sField2: '',
          sField3: '',
          sField4: '',
          sField5: item.sLoadType === 'TRAN30' ? 2 : 0,
          sField6: '',
          sField7: 1.1479,
          sDiffTaxAmt: '',
          sField8: '',
          sField11: '',
          sField12: '',
          sField13: '',
          sField14: '',
          sField15: '',
          sField16: '',
          sField17: '',
          sField18: '',
          sField19: '',
          sTaxAmt: '',
          sCdtTaxAmt: '',
          sIsDiff: '',
          sSettlementId: this.newInfo.sId
        })
      })
      selectedList.forEach(el => {
        this.calculateData(el)
      })
      logisticsFeeModifies(selectedList).then(res => {
        this.$message.success(this.$t('grid.others.editSuccessfully'))
        payoverview({ sSettlementId: this.sId }).then(() => {
          this.updateFn(['financialExpense'])
          this.setInfo()
        }).finally(() => {
          this.dialogVisible = false
          this.$refs.aggrid.loadTableData()
        })
      })
    },
    getArtnoList() {
      return new Promise((resolve, reject) => {
        getArtnoDetail({ supParam: '122111111111150' }).then(res => {
          // let artnoList
          // // if (this.vManagementCode === '00011061') {
          // //   artnoList = res.data
          // // }else if
          // if (this.vManagementCode === '00014867') {
          //   artnoList = res.data
          // } else {
          //   artnoList = res.data.filter(item => item.sArtnoCode !== 'TRAN130' && item.sArtnoCode !== 'TRAN120')
          // }
          this.artnoList = res.data
          console.log('artnoList', this.artnoList)
          resolve(this.artnoList)
        }).catch(err => {
          console.log(err)
        })
      })
    },
    getSelectType(key) {
      return () => {
        const keyList = [key]
        return new Promise((resolve, reject) => {
          getDictet(keyList).then((result) => {
            resolve(result.data[0].dicts)
          }).catch(err => {
            reject(err)
          })
        })
      }
    },
    loadData() {
      return new Promise((resolve, reject) => {
        logisticsFeeList({ id: this.sId }).then(res => {
          this.rowData = res.data.map(item => {
            item._selected = false
            return item
          })
          this.currRowData = deepClone(res.data)
          console.log('物流费用明细：', res.data)
          resolve(res.data.length)
          this.handleCellTotal()
          this.setAggridVisible()
        }).catch(() => {
          reject(0)
        })
      })
    },
    handleCellTotal(type = 'all') {
      setTimeout(() => {
        const totalList = computeCellTotal(
          this.rowData,
          {
            sContractQty: 0,
            sDiffTaxAmt: 0,
            sTaxAmt: 0,
            sCdtTaxAmt: 0,
            sField8: 0
          },
          {
            sLogisticsContractCode: type === 'all' ? this.$t('grid.others.amountTo') : `${this.$t('components.selected')}${this.rowData.filter(item => item._selected).length}${this.$t('pagination.items')}`,
            _selected: false,
            _hiddenCheckbox: true
          },
          type
        )
        if (this.rowData.length) {
          this.$refs.aggrid.gridApi.setPinnedBottomRowData([totalList])
        }
      }, 0)
    },
    handleChange() {
      this.$refs.aggrid.getSelectedData(res => {
        this.handleCellTotal(res.length ? 'selected' : 'all')
      })
    },
    add() {
      const data = {
        sLogisticsContractCode: '',
        vSupplierName: '',
        sLogisticsType: '',
        sVesselName: '',
        sContractQty: '',
        sPrice: '',
        sField1: '',
        sField2: '',
        sField3: '',
        sField4: '',
        sField5: 0,
        sField6: '',
        sField7: 1.1479,
        sDiffTaxAmt: '',
        sField8: '',
        sField11: '',
        sField12: '',
        sField13: '',
        sField14: '',
        sField15: '',
        sField16: '',
        sField17: '',
        sField18: '',
        sField19: '',
        sFeeType: '10',
        sTaxAmt: '',
        sCdtTaxAmt: '',
        sIsDiff: '',
        _selected: false,
        sSettlementId: this.newInfo.sId
      }
      this.rowData.push(data)
      const rowIndex = this.rowData.length - 1
      this.$nextTick(() => {
        this.$refs.aggrid.gridApi.setFocusedCell(rowIndex, 'sLogisticsContractCode')
        this.$refs.aggrid.gridApi.startEditingCell({
          rowIndex,
          colKey: 'sLogisticsContractCode'
        })
      })
    },
    resetRowChange(rowIndex, value) {
      this.$nextTick(() => {
        this.$refs.aggrid.gridApi.setFocusedCell(rowIndex, value)
        this.$refs.aggrid.gridApi.startEditingCell({
          rowIndex,
          colKey: value
        })
      })
    },
    rowValueChanged(params) {
      console.log(params.data)
      if (!this.isBusinessDisabled('save', this.sSheetStatus) && !params.data._hiddenCheckbox) {
        // if (!params.data.vSupplierName) {
        //   this.$message.error('物流供应商不能为空')
        //   return false
        // }
        if (!params.data.sLogisticsType) {
          this.$message.error('费用类型不能为空')
          this.resetRowChange(params.rowIndex, 'sLogisticsType')
          return false
        }
        if (params.data.sContractQty === null || params.data.sContractQty === '') {
          this.$message.error(this.$t('grid.others.quantityCannotBeEmpty'))
          this.resetRowChange(params.rowIndex, 'sContractQty')
          return false
        }
        if (params.data.sPrice === null || params.data.sPrice === '') {
          this.$message.error(this.$t('grid.others.logisticsUnitPriceCanNotBeEmpty'))
          this.resetRowChange(params.rowIndex, 'sPrice')
          return false
        }
        // if (params.data.sField5 === null || params.data.sField5 === '') {
        //   params.data.sField5 = 0
        //   // this.$message.error('实际投保单价不能为空')
        //   // return false
        // }
        if ((params.data.sField1 && params.data.sField1 > 0) ||
            (params.data.sField2 && params.data.sField2 > 0) ||
            (params.data.sField3 && params.data.sField3 > 0) ||
            (params.data.sField4 && params.data.sField4 > 0) ||
            (params.data.sField6 && params.data.sField6 > 0) ||
            (params.data.sField11 && params.data.sField11 > 0) ||
            (params.data.sField12 && params.data.sField12 > 0) ||
            (params.data.sField13 && params.data.sField13 > 0) ||
            (params.data.sField14 && params.data.sField14 > 0) ||
            (params.data.sField15 && params.data.sField15 > 0) ||
            (params.data.sField16 && params.data.sField16 > 0) ||
            (params.data.sField17 && params.data.sField17 > 0) ||
            (params.data.sField18 && params.data.sField18 > 0) ||
            (params.data.sField19 && params.data.sField19 > 0)
        ) {
          const totalNum = +new Decimal(+params.data.sField1)
            .add(+params.data.sField2)
            .add(+params.data.sField3)
            .add(+params.data.sField4)
            .add(+params.data.sField6)
            .add(+params.data.sField11)
            .add(+params.data.sField12)
            .add(+params.data.sField13)
            .add(+params.data.sField14)
            .add(+params.data.sField15)
            .add(+params.data.sField16)
            .add(+params.data.sField17)
            .add(+params.data.sField18)
            .add(+params.data.sField19)
          console.log('总和:', totalNum)
          if (totalNum !== +params.data.sPrice) {
            this.$message.error(this.$t('grid.others.logisticsUnitPriceIConfirmKey'))
            this.resetRowChange(params.rowIndex, 'sPrice')
            return false
          }
        }
        if (this.vManagementCode === '00014867' && this.markupFactorType === '1') {
          if (params.data.sField7 < 1.13 || params.data.sField7 > 1.2) {
            this.$message.error('税差系数填写范围为1.13-1.2')
            this.resetRowChange(params.rowIndex, 'sField7')
            return false
          }
        }
        // if (this.vManagementCode === '00011061') {
        //   if (+params.data.sDiffTaxAmt < +params.data.sField8) {
        //     this.$message.error('实收税差不能小于应收税差')
        //     this.resetRowChange(params.rowIndex, 'sDiffTaxAmt')
        //     return false
        //   }
        // }
        params.data.sLogisticsContractCode = params.data.sLogisticsContractCode && params.data.sLogisticsContractCode.trim()
        logisticsFeeModifies([params.data]).then(res => {
          this.$message.success(this.$t('grid.others.editSuccessfully'))
          payoverview({ sSettlementId: this.sId }).then(() => {
            this.updateFn(['financialExpense'])
            this.setInfo()
          }).finally(() => {
            this.$refs.aggrid.loadTableData()
          })
        })
      }
    },
    remove() {
      const delarr = []
      const vm = this
      this.rowData.map(item => {
        if (item._selected === true) {
          delarr.push(item.sId)
        }
      })
      if (delarr.length === 0) {
        this.$message({
          message: this.$t('grid.others.pleaseSelectAData'),
          type: 'warning'
        })
        return
      }

      this.$confirm(this.$t('grid.others.thisActionDeletesThntinueKey'), this.$t('grid.others.prompt'), {
        confirmButtonText: this.$t('btns.confirm'),
        cancelButtonText: this.$t('btns.cancel'),
        type: 'warning'
      })
        .then(() => {
          console.log(delarr)
          logisticsFeeRemoves(delarr).then((result) => {
            this.$message({
              message: this.$t('tips.deletedSuccessfully'),
              type: 'success'
            })
            payoverview({ sSettlementId: this.sId }).then(() => {
              this.updateFn(['financialExpense'])
              this.setInfo()
            })
            vm.gridShow = false
            vm.$nextTick(() => {
              vm.gridShow = true
            })
            // vm.$refs.aggrid.loadTableData()
            // vm.$refs.aggrid.gridApi.setPinnedBottomRowData([])
          })
            .catch(() => { })
        })
    },
    formula(data) {
      /* X：税差拆分百分点的值
      ** A: 税差拆分的百分点
      ** B: 出票税点的百分点
      ** 1.无增收税差：X / (1+A) * (B - A）
      **   无增收税差和 = (X1 / (1+A1) * B) + (X2 / (1+A2) * B) + ...
      **   应收税差 = 无增收税差和 * 物流单价
      */
      if (this.sIsDiff === '0') {
        const total = this.showField.reduce((prev, next) => {
          const X = new Decimal(+data[next])
          const A = new Decimal(+fieldObj[next])
          const B = new Decimal(+this.sOutTaxPoint)
          const One = new Decimal(1)
          const oldPrev = new Decimal(+prev)
          prev = new Decimal(X.div(One.add(A)).mul(B.sub(A))).add(oldPrev)
          // prev += data[next] / (1 + +fieldObj[next]) * (1 + +this.sOutTaxPoint)
          return +prev
        }, 0)
        return +this.$toFixed(total * data.sContractQty)
      } else if (this.sIsDiff === '1') {
        /* ********************************
      ** 2.有增收税差：X * (B - A)
      **   有增收税差和 = X1 * (B - A1) + X2 * (B - A2)...
      **   应收税差 = 有增收税差和 * 物流单价
      */
        const total = this.showField.reduce((prev, next) => {
          const X = new Decimal(+data[next])
          const A = new Decimal(+fieldObj[next])
          const B = new Decimal(+this.sOutTaxPoint)
          // const One = new Decimal(1)
          const oldPrev = new Decimal(+prev)
          prev = new Decimal(X.mul(B.sub(A))).add(oldPrev)
          // prev += data[next] * (1 + this.sOutTaxPoint - fieldObj[next])
          return +prev
        }, 0)
        return +this.$toFixed(total * data.sContractQty)
      }
    },
    XMFormula(data) {
      const isNull = this.showField.every(el => !data[el])
      if (isNull) {
        const sPrice = new Decimal(+data.sPrice)
        const sContractQty = new Decimal(+data.sContractQty)
        const sCdtTaxAmt = +this.$toFixed(sPrice.mul(sContractQty))
        return sCdtTaxAmt
      } else {
        if (this.markupFactorType === '2') {
          /* 分税率系数
          ** 应收物流费用(含税差)=（A单价/（1+A）*C+...）*数量
          ** 不含税运费加成率 C=((1-A*12%)*(1+B))/((1-B*12%)*(1+A))*(1+A)
          */
          const total = this.showField.reduce((prev, next) => {
            const X = new Decimal(+data[next])
            const A = new Decimal(+fieldObj[next])
            const B = new Decimal(+this.sOutTaxPoint)
            const One = new Decimal(1)
            const C = +this.$toFixed(new Decimal((One.sub(A * 0.12)) * (One.add(B))).div((One.sub(B * 0.12)) * (One.add(A))) * (One.add(A)), 4)
            const oldPrev = new Decimal(+prev)
            prev = new Decimal((X.div((One.add(A))).mul(C))).add(oldPrev)
            return +prev
          }, 0)
          const sCdtTaxAmt = +this.$toFixed(new Decimal(total).mul(+data.sContractQty))
          return sCdtTaxAmt
        } else {
          /* 固定系数
          ** 应收物流费用(含税差)=(1A单价/（1+A）+...）*税差系数*数量
          */
          const total = this.showField.reduce((prev, next) => {
            const X = new Decimal(+data[next])
            const A = new Decimal(+fieldObj[next])
            const One = new Decimal(1)
            const oldPrev = new Decimal(+prev)
            prev = new Decimal(X.div((One.add(A)))).add(oldPrev)
            return +prev
          }, 0)
          const sCdtTaxAmt = +this.$toFixed(new Decimal(total).mul(+data.sField7).mul(+data.sContractQty))
          return sCdtTaxAmt
        }
      }
    },
    calculateData(data) {
      if (this.vManagementCode === '00014867') {
        this.XMWZcalculateTable(data)
      } else if (this.vManagementCode === '00014960') {
        this.calculateTable(data)
      } else if (this.vManagementCode === '00011061') {
        this.JScalculateTable(data)
      }
    },
    calculateTable(data) {
      console.log('calculateTable', data)
      let sField5 = null
      if (data.sLogisticsType === 'TRAN30') {
        sField5 = data.sField5 ?? 2
      } else {
        sField5 = data.sField5 ?? 0
      }
      const sPrice = +data.sPrice || 0
      const sContractQty = +data.sContractQty || 0
      const sDiffTaxAmt = this.formula(data)
      const sTaxAmt = this.$toFixed((sPrice + +sField5 * (1 + 0.13)) * sContractQty)
      const sCdtTaxAmt = +sTaxAmt + +sDiffTaxAmt
      data.sDiffTaxAmt = sDiffTaxAmt
      data.sTaxAmt = sTaxAmt
      data.sCdtTaxAmt = sCdtTaxAmt
      data.sIsDiff = this.sIsDiff
    },
    JScalculateTable(data, middleware, value) {
      console.log('JScalculateTable', data)
      let sField5 = null
      if (data.sLogisticsType === 'TRAN30') {
        sField5 = data.sField5 ?? 2
      } else {
        sField5 = data.sField5 ?? 0
      }
      const sPrice = +data.sPrice || 0
      const sContractQty = +data.sContractQty || 0
      const sDiffTaxAmt = this.formula(data)
      const sTaxAmt = this.$toFixed((sPrice + +sField5 * (1 + 0.13)) * sContractQty)
      const sCdtTaxAmt = +sTaxAmt + +sDiffTaxAmt
      // const sField8 = this.formula(data)
      // let sCdtTaxAmt
      // if (value === 'sDiffTaxAmt') {
      //   sCdtTaxAmt = +sTaxAmt + +data.sDiffTaxAmt
      // } else {
      //   data.sDiffTaxAmt = +sField8
      //   middleware?.rendered?.sDiffTaxAmt?.setValue(+sField8)
      //   sCdtTaxAmt = +sTaxAmt + +sField8
      // }
      // data.sField8 = +sField8
      data.sDiffTaxAmt = sDiffTaxAmt
      data.sTaxAmt = +sTaxAmt
      data.sCdtTaxAmt = sCdtTaxAmt
      data.sIsDiff = this.sIsDiff
    },
    XMWZcalculateTable(data) {
      console.log('XMWZcalculateTable', data)
      data.sCdtTaxAmt = this.XMFormula(data)
    },

    openModifyDialog() {
      this.$refs.aggrid.getSelectedData(selectedData => {
        if (!selectedData.length) {
          this.$message.warning(this.$t('grid.others.pleaseSelectData'))
          return
        }
        this.modifyData = selectedData
        this.formDialogItems[1].dict = this.inTaxPointList
        this.formDialogItems[0].default = 0
        this.batchModifyDialogVisible = true
      })
    },

    onSuccess(form) {
      this.modifyData.forEach(el => {
        el.sPrice = form.sPrice
        this.inTaxPointList.forEach(item => {
          if (el[item.sCodeValue]) {
            el[item.sCodeValue] = 0
          }
        })
        // 如果选择了税差拆分，则设置对应字段的值
        if (form.sField) {
          el[form.sField] = +form.sPrice
        }
        this.calculateData(el)
      })
      logisticsFeeModifies(this.modifyData).then(res => {
        this.$message.success(this.$t('grid.others.editSuccessfully'))
        payoverview({ sSettlementId: this.sId }).then(() => {
          this.updateFn(['financialExpense'])
          this.setInfo()
          this.batchModifyDialogVisible = false
        }).finally(() => {
          this.$refs.aggrid.loadTableData()
        })
      })
    }
  }
}
</script>

<style scoped>
.b-border{
  border-top:1px solid rgb(223, 228, 237);
}
.b-show{
  margin:10px;
  background: rgb(223, 228, 237);
  border:1px solid rgb(223, 228, 237);
  -moz-box-shadow:0 0 10px rgb(223, 228, 237);
  -webkit-box-shadow:0 0 10px rgb(223, 228, 237);
  box-shadow:0 0 10px rgb(223, 228, 237);
}
</style>
