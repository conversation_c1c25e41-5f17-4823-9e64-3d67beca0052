<template>
  <div>
    <div class="btn-group">
      <div />
      <div>
        <el-button
          v-has:esc_pay_settle_detail_updateDetailQty
          type="primary"
          size="mini"
          :disabled="isDisableModify"
          @click="openModifyDialog"
        >
          修改结算数量
        </el-button>
        <el-button
          type="primary"
          size="mini"
          @click="annexVisible = true"
        >
          上传货值明细
        </el-button>
        <el-button
          type="primary"
          size="mini"
          :disabled="btndisabled"
          @click="mergeData"
        >
          {{ $t('grid.others.mergeData') }}
        </el-button>
        <el-button
          type="primary"
          size="mini"
          :disabled="btndisabled"
          @click="splitData"
        >
          {{ $t('grid.others.splitData') }}
        </el-button>
        <el-button
          type="primary"
          size="mini"
          :disabled="btndisabled"
          @click="add"
        >{{ $t('btns.add') }}</el-button>
        <el-button
          type="danger"
          size="mini"
          :disabled="btndisabled"
          @click="remove"
        >{{ $t('btns.delete') }}
        </el-button>
      </div>
    </div>

    <!-- 货值 -->
    <steelTradeAggrid
      v-if="gridShow"
      ref="aggrid"
      :auto-height="true"
      disable-paste="all"
      :column-defs="columnDefs"
      :row-data="rowData"
      :load-data="loadData"
      :paginationinif="false"
      table-selection="multiple"
      row-key="sId"
      :full-row-type="fullRowType"
      @rowValueChanged="rowValueChanged"
      @selectedChange="selectedChange"
      @rowEditingStopped="rowEditingStopped"
      @onPasteEnd="onPasteEnd"
    />
    <splitDataDialog
      v-if="splitDataVisible"
      :visible="splitDataVisible"
      :max-amt="maxAmt"
      :max-qty="maxQty"
      :form-data="selectSplit"
      :s-id="splitId"
      @success="successSplitDialog"
      @onClose="closeSplitDialog"
    />
    <steel-annex-dialog
      :visible="annexVisible"
      append-to-body
      :biz-id="sId"
      :option="{ sExtend1: 'JSHZ' }"
      :upload-option="{ sExtend1: 'JSHZ' }"
      :disabled-btn="{ scan: isBusinessDisabled('save', sSheetStatus)|| $route.query.disabled === '1', del: isBusinessDisabled('save', sSheetStatus)|| $route.query.disabled === '1' }"
      @onSelect="annexVisible = false"
    />
    <cnd-dialog
      v-if="modifyDialog"
      :visible="modifyDialog"
      :fullscreen="false"
      append-to-body
      title="修改结算数量"
      width="350px"
      height="40px"
      @close="modifyDialog = false"
    >
      <template slot="content">
        <div>
          <el-form
            :model="modifyForm"
            label-width="100px"
            size="small"
            @submit.native.prevent
          >
            <cnd-form-item
              label="结算数量"
              :custom-width="20"
            >
              <cnd-input-number v-model="modifyForm.sSettleQty" :decimal-digit="4" clearable :placeholder="$t('components.pleaseEnter')" @keyup.enter.native="modifySubmit" />
            </cnd-form-item>
          </el-form>
        </div>
      </template>
      <template slot="footer">
        <el-button
          size="mini"
          @click="modifyDialog = false"
        >{{ $t('btns.cancel') }}</el-button>
        <el-button
          type="primary"
          size="mini"
          @click="modifySubmit"
        >{{ $t('btns.confirm') }}</el-button>
      </template>
    </cnd-dialog>
  </div>
</template>

<script>
import Vue from 'vue'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import {
  getPaySettleDetail,
  putPaySettleDetail,
  addsPaySettleDetail,
  removesPaySettleDetail,
  payoverview,
  // settlePurInterestUpdate,
  settleDetailMergeSettlementDetail,
  updateDetailQty
} from '@/api/settlement'
import { SteelFormat } from 'cnd-horizon-utils'
import { Middleware } from 'cndinfo-ui'
import { computeCellTotal } from '@/utils/common'
import businessMixin from '@/utils/businessMixin'
import splitDataDialog from './dialog/splitDataDialog.vue'
export default {
  components: { steelTradeAggrid, splitDataDialog },
  mixins: [businessMixin],
  props: {
    sId: {
      type: String,
      default: null
    },
    info: {
      type: Object,
      default: () => {}
    },
    updateFn: {
      type: Function,
      default: () => {}
    }
  },
  data() {
    return {
      gridShow: true,
      modifyDialog: false,
      modifyForm: {
        sSettleQty: null
      },
      modifyData: null,
      splitDataVisible: false,
      annexVisible: false,
      splitId: null,
      maxAmt: null,
      maxQty: null,
      columnDefs: [
        // {
        //   headerName: this.$t('grid.title.purchaseContractNumber'),
        //   field: 'sPurContractCode',
        //   editable: (params) => {
        //     return (
        //       !this.isBusinessDisabled('save', this.sSheetStatus) &&
        //       !params.data._hiddenCheckbox &&
        //       this.$route.query.disabled !== '1' &&
        //       params.data.sIsAuto === '0'
        //     )
        //   }
        // },
        {
          headerName: '销售商品',
          field: 'sGoodsDesc',
          width: 206,
          editable: (params) => {
            return (
              !this.isBusinessDisabled('save', this.sSheetStatus) &&
              !params.data._hiddenCheckbox &&
              this.$route.query.disabled !== '1' &&
              params.data.sIsAuto === '0'
            )
          }
        },
        {
          headerName: this.$t('grid.others.settlementQuantity'),
          field: 'sContractQty',
          cellStyle: { textAlign: 'right' },
          valueFormatter(params) {
            return SteelFormat.formatThousandthSign(params.data.sContractQty, 4)
          },
          editable: (params) => {
            return !this.isBusinessDisabled('save', this.sSheetStatus) && !params.data._hiddenCheckbox && this.$route.query.disabled !== '1'
          },
          cellEditorFramework: Vue.extend(Middleware.createComponent(
            'CndInputNumber',
            {
              mark: 'sContractQty',
              type: 'number',
              decimalDigit: 4,
              autoFocus: true,
              focusSelect: true
            }
            // {
            //   blur: ({ event, rowData, middleware }) => {
            //     if (+event.target.value > 0) {
            //       rowData.data.sPrice = rowData.data.sTaxAmt / +event.target.value
            //     } else {
            //       middleware.rendered.sTaxAmt.setValue(0)
            //       rowData.data.sPrice = 0
            //     }
            //     this.$refs.aggrid.gridApi.refreshCells(rowData)
            //   }
            // }
          ))
        },
        {
          headerName: this.$t('grid.others.statementPrice'),
          field: 'sPrice',
          cellStyle: { textAlign: 'right' },
          valueFormatter(params) {
            return params.data._hiddenCheckbox ? '' : SteelFormat.formatPrice(params.data.sPrice)
          }
        },
        {
          headerName: '结算基础价',
          field: 'sTaxAmt',
          width: 180,
          cellStyle: { textAlign: 'right' },
          valueFormatter(params) {
            return SteelFormat.formatPrice(params.data.sTaxAmt)
          },
          // headerComponent: 'customHeaderComponent',
          // headerComponentParams: {
          //   popoverContent: `
          //     <div>
          //       <div>计算公式：*********</div>
          //       <div>计算公式：*********</div>
          //       <div>计算公式：*********</div>
          //     </div>
          //   `
          // },
          editable: (params) => {
            return !this.isBusinessDisabled('save', this.sSheetStatus) && !params.data._hiddenCheckbox && this.$route.query.disabled !== '1'
          },
          cellEditorFramework: Vue.extend(Middleware.createComponent(
            'CndInputNumber',
            {
              mark: 'sTaxAmt',
              type: 'number',
              decimalDigit: 2,
              focusSelect: true
            }
            // {
            //   blur: ({ event, rowData, middleware }) => {
            //     if (+rowData.data.sContractQty > 0) {
            //       rowData.data.sPrice = +event.target.value / rowData.data.sContractQty
            //     } else {
            //       rowData.data.sPrice = 0
            //       rowData.data.sTaxAmt = 0
            //       middleware.rendered.sTaxAmt.setValue(0)
            //     }
            //     this.$refs.aggrid.gridApi.refreshCells(rowData)
            //   }
            // }
          ))
        }
      ],
      rowData: []
    }
  },
  computed: {
    sSheetStatus() {
      return this.info.sSheetStatus
    },
    isDisableModify() {
      const { sSheetStatus, sNatureType } = this.info
      return !(sSheetStatus === '70' && sNatureType === '20')
    },
    fullRowType() {
      return !this.isBusinessDisabled('save', this.sSheetStatus) ? 'parent' : null
    },
    btndisabled() {
      return this.isBusinessDisabled('save', this.sSheetStatus) || this.$route.query.disabled === '1'
    }
  },
  methods: {
    splitData() {
      // this.$refs.aggrid.gridApi.forEachNodeAfterFilterAndSort((node, index) => {
      //   if (node.data) {
      //     this.seqList.push(node.data.sSeq)
      //   }
      // })
      this.$refs.aggrid.getSelectedData(res => {
        if (!res.length) {
          this.$message.error(this.$t('grid.others.pleaseSelectTheDataToBeSplit'))
          return
        }
        if (res.length > 1) {
          this.$message.error(this.$t('grid.others.onlySingleDataCanBelittingKey'))
          return
        }
        this.selectSplit = res[0]
        this.maxAmt = SteelFormat.formatPrice(res[0].sTaxAmt)
        this.maxQty = SteelFormat.formatThousandthSign(res[0].sContractQty, 4)
        this.splitId = res[0].sId
        this.splitDataVisible = true
      })
    },
    closeSplitDialog() {
      this.splitDataVisible = false
      this.selectSplit = null
    },
    successSplitDialog() {
      // settlePurInterestUpdate({ sSettlementId: this.sId, sSaleContractId: this.info.sSaleContractId }).then(() => {
      payoverview({ sSettlementId: this.sId }).then(() => {
        this.updateFn(['info'])
      }).finally(() => {
        this.$refs.aggrid.clearSelection()
        this.$refs.aggrid.reloadTableData()
      })
      // })
    },
    mergeData() {
      this.$refs.aggrid.getSelectedData(res => {
        if (!res.length) {
          this.$message.error(this.$t('grid.others.pleaseSelectTheDataToBeMerged'))
          return
        }
        if (res.length < 2) {
          this.$message.error(this.$t('grid.others.moreThan2ItemsNeedTrgeDataKey'))
          return
        }
        settleDetailMergeSettlementDetail(res).then(() => {
          this.$message.success(this.$t('grid.others.mergeSuccess'))
          // settlePurInterestUpdate({ sSettlementId: this.sId, sSaleContractId: this.info.sSaleContractId }).then(() => {
          payoverview({ sSettlementId: this.sId }).then(() => {
            this.updateFn(['info'])
          }).finally(() => {
            this.$refs.aggrid.clearSelection()
            this.$refs.aggrid.reloadTableData()
          })
          // })
        })
      })
    },
    loadData() {
      return new Promise((resolve, reject) => {
        getPaySettleDetail({ sSettlementId: this.sId }).then(res => {
          console.log('货值: ', res)
          this.rowData = res.data.map(item => {
            item._selected = false
            return item
          })
          resolve(res.data.length)
        })
      })
    },
    selectedChange(list) {
      this.$refs.aggrid.getSelectedData(res => {
        // this.$emit('changeSettlement', list)
        this.handleCellTotal(res.length ? 'selected' : 'all')
      })
    },
    handleCellTotal(type = 'all') {
      setTimeout(() => {
        const totalList = computeCellTotal(
          this.rowData,
          {
            sContractQty: 0,
            sTaxAmt: 0
          },
          {
            sGoodsDesc: type === 'all' ? this.$t('grid.others.amountTo') : `${this.$t('components.selected')}${this.rowData.filter(item => item._selected).length}${this.$t('pagination.items')}`,
            sPurContractCode: null,
            sPrice: null,
            _selected: false,
            _hiddenCheckbox: true
          },
          type
        )
        if (this.rowData.length) {
          this.$refs.aggrid.gridApi.setPinnedBottomRowData([totalList])
        }
      }, 0)
    },
    resetRowChange(rowIndex, value) {
      this.$nextTick(() => {
        this.$refs.aggrid.gridApi.setFocusedCell(rowIndex, value)
        this.$refs.aggrid.gridApi.startEditingCell({
          rowIndex,
          colKey: value
        })
      })
    },
    rowValueChanged(params) {
      console.log('params: ', params)
      if (!this.isBusinessDisabled('save', this.sSheetStatus) && !params.data._hiddenCheckbox) {
        if (params.data.sContractQty === null || params.data.sContractQty === '') {
          this.$message.error('结算数量不能为空')
          this.resetRowChange(params.rowIndex, 'sContractQty')
          return false
        }
        if (params.data.sTaxAmt === null || params.data.sTaxAmt === '') {
          this.$message.error('结算基础价不能为空')
          this.resetRowChange(params.rowIndex, 'sTaxAmt')
          return false
        }
        if (params.data.sContractQty === 0) {
          // params.data.sTaxAmt = 0
          params.data.sPrice = 0
        } else {
          params.data.sPrice = params.data.sTaxAmt / params.data.sContractQty
        }
        const api = params.data.sId ? putPaySettleDetail : addsPaySettleDetail
        api([params.data]).then(res => {
          this.$message.success(this.$t('grid.others.editSuccessfully'))
          // settlePurInterestUpdate({ sSettlementId: this.sId, sSaleContractId: this.info.sSaleContractId }).then(() => {
          payoverview({ sSettlementId: this.sId }).then(() => {
            this.updateFn(['info'])
          }).finally(() => {
            this.$refs.aggrid.loadTableData()
          })
          // })
        }).catch(() => {
          this.$refs.aggrid.loadTableData()
        })
      }
    },
    rowEditingStopped(params) {
      console.log('params: ', params)
    },
    onPasteEnd(params) {
      console.log('params: ', params)
    },
    openModifyDialog() {
      this.$refs.aggrid.getSelectedData(res => {
        if (res.length !== 1) {
          this.$message.error(this.$t('grid.others.pleaseSelectAData'))
          return
        } else {
          this.modifyForm.sSettleQty = null
          this.modifyDialog = true
          this.modifyData = res[0]
        }
      })
    },
    modifySubmit() {
      updateDetailQty(
        {
          sSettlementId: this.sId,
          sDetailId: this.modifyData.sId
        },
        {
          sSettleQty: this.modifyForm.sSettleQty
        }
      ).then(() => {
        this.$message.success(this.$t('grid.others.editSuccessfully'))
        this.modifyDialog = false
        // settlePurInterestUpdate({ sSettlementId: this.sId, sSaleContractId: this.info.sSaleContractId }).then(() => {
        payoverview({ sSettlementId: this.sId }).then(() => {
          this.updateFn(['info'])
        }).finally(() => {
          this.$refs.aggrid.loadTableData()
        })
        // })
      })
    },
    add() {
      const data = {
        sPurContractCode: '',
        sGoodsDesc: '',
        sPrice: '',
        sContractQty: '',
        sTaxAmt: '',
        sIsAuto: '0',
        _selected: false,
        sSettlementId: this.info.sId
      }
      this.rowData.push(data)
      const rowIndex = this.rowData.length - 1
      this.resetRowChange(rowIndex, 'sPurContractCode')
    },
    remove() {
      this.$refs.aggrid.getSelectedData(res => {
        if (!res.length) {
          this.$message.error(this.$t('grid.others.pleaseSelectData'))
          return false
        }
        this.$confirm(this.$t('grid.others.thisActionDeletesThntinueKey'), this.$t('grid.others.prompt'), {
          confirmButtonText: this.$t('btns.confirm'),
          cancelButtonText: this.$t('btns.cancel'),
          type: 'warning'
        })
          .then(() => {
            const ids = res.map(v => v.sId)
            removesPaySettleDetail(ids).then(() => {
              this.$message.success(this.$t('tips.deletedSuccessfully'))
              // settlePurInterestUpdate({ sSettlementId: this.sId, sSaleContractId: this.info.sSaleContractId }).then(() => {
              payoverview({ sSettlementId: this.sId }).then(() => {
                this.updateFn(['info'])
              })
              // })
              this.gridShow = false
              this.$nextTick(() => {
                this.gridShow = true
              })
            })
          }).catch(() => { })
      })
    }
  }
}
</script>
