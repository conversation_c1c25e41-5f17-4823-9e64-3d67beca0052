<!--
 * @Author: 沈鹭荣
 * @Date: 2021-03-19 16:49:01
 * @LastEditors: 沈鹭荣
 * @LastEditTime: 2021-04-26 10:39:23
 * @Description:
-->
<template>
  <div>
    <!-- 物流费用统计 -->
    <div class="btn-group">
      <div class="text">
        {{ $t('grid.others.logisticsCostStatistics') }}
      </div>
      <div @click="close">
        <a style="color:#262626" class="el-icon-close" />
      </div>
    </div>
    <steelTradeAggrid
      ref="aggrid"
      :auto-height="true"
      :column-defs="columnDefs"
      :row-data="rowData"
      :paginationinif="false"
      row-key="sPurContractId"
      :load-data="loadData"
      @rowClicked="handleInvoiceFooterCount"
    />
    <el-tabs
      v-if="iniftable"
      v-model="activeName"
      class="tabs-btn-position tabstablebor"
      @tab-click="tabClick"
    >
      <el-tab-pane
        :label="$t('grid.others.transportationContract')"
        name="1"
      >
        <steelTradeAggrid
          v-if="gridShow1"
          ref="aggrid1"
          :auto-height="true"
          :min-height="200"
          :column-defs="columnDefs1"
          :row-data="rowData1"
          row-key="sId"
          :paginationinif="false"
          table-selection="multiple"
          :load-data="loadDatas1"
          @selectedChange="selectedChange1"
        />
      </el-tab-pane>
      <el-tab-pane
        :label="$t('grid.others.expensePayment')"
        name="2"
      >
        <steelTradeAggrid
          v-if="gridShow2"
          ref="aggrid2"
          :auto-height="true"
          :min-height="200"
          :column-defs="columnDefs2"
          :row-data="rowData2"
          :paginationinif="false"
          row-key="sId"
          table-selection="multiple"
          :load-data="loadData2"
          @selectedChange="selectedChange2"
        />
      </el-tab-pane>
      <el-tab-pane
        :label="$t('grid.others.expenseInvoice')"
        name="3"
      >
        <steelTradeAggrid
          v-if="gridShow3"
          ref="aggrid3"
          :auto-height="true"
          :min-height="200"
          :column-defs="columnDefs3"
          :paginationinif="false"
          :row-data="rowData3"
          row-key="sId"
          table-selection="multiple"
          :load-data="loadData3"
          @selectedChange="selectedChange3"
        />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import { SteelFormat, DictUtil } from 'cnd-horizon-utils'
import { Moment } from 'cnd-utils'
import { computeCellTotal } from '@/utils/common'

import steelTradeAggrid from '@/components/steelTradeAggrid'
import {
  paysettlefeelist,
  paysettlefeelistloginst, payfeepaymentdetail, payinvoiceeceipt
} from '@/api/settlement'
import { getArtnoDetail } from '@/api/contract'
export default {
  components: { steelTradeAggrid },
  props: {
    sId: {
      type: String,
      default: null
    },
    info: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      activeName: '1',
      iniftable: false,
      columnDefs: [
        {
          headerName: this.$t('grid.title.purchaseContractNumber'),
          field: 'sPurContractCode'
        },
        {
          headerName: this.$t('grid.others.contractQuantity'),
          field: 'vContractQty',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            // vTaxAmt/vNetAmt
            return SteelFormat.formatThousandthSign(params.value, 4)
          }
        },
        {
          headerName: this.$t('grid.others.numberOfShipments'),
          field: 'vSendQtx',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            // vTaxAmt/vNetAmt
            return SteelFormat.formatThousandthSign(params.value, 4)
          }
        },
        {
          headerName: this.$t('grid.others.amountOfTransportationContract'),
          field: 'vLogisticsAmt',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            // vTaxAmt/vNetAmt
            return SteelFormat.formatPrice(params.value)
          }
        },
        {
          headerName: this.$t('grid.others.expensePaymentAmount'),
          field: 'vPayAmt',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return SteelFormat.formatPrice(params.value)
          }
        },
        {
          headerName: this.$t('grid.others.expenseInvoiceAmount'),
          field: 'vInvoiceAmt',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return SteelFormat.formatPrice(params.value)
          }
        }
      ],
      columnDefs1: [
        {
          headerName: this.$t('grid.others.itemNumberTag'),
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'sProjectCode',
              headerClass: 'c-header_child'
            }
          ]
        },
        {
          headerName: this.$t('grid.others.transportContractNumber'),
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'sCode',
              headerClass: 'c-header_child'
            }
          ]
        },
        {
          headerName: this.$t('grid.others.logisticsSupplier'),
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'sSupplierName',
              headerClass: 'c-header_child'
            }
          ]
        },
        {
          headerName: this.$t('grid.others.transportationMode'),
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'sTransportType',
              headerClass: 'c-header_child',
              valueGetter: (params) => {
                const status = this.options.artnoList.filter(item => item.sArtnoCode === params.data.sTransportType)
                return status.length ? status[0].sCnName : params.data.sTransportType
              }
            }
          ]
        },
        {
          headerName: this.$t('grid.others.shipName'),
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'sVesselName',
              headerClass: 'c-header_child'
            }
          ]
        },
        {
          headerName: this.$t('grid.title.quantity'),
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'sContractQty',
              headerClass: 'c-header_child'
            }
          ]
        },
        {
          headerName: this.$t('grid.others.freightUnitPrice'),
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'sFeePrice',
              headerClass: 'c-header_child',
              valueFormatter: (params) => {
                return SteelFormat.formatPrice(params.value)
              }
            }
          ]
        },
        {
          // headerName: this.$t('grid.others.amountReceivable'),
          headerName: '应收金额',
          children: [
            {
              headerName: this.$t('grid.others.seaFreightUnitPrice'),
              field: 'sLogisticsPrice',
              valueFormatter: (params) => {
                return SteelFormat.formatPrice(params.value)
              }
            },
            {
              headerName: this.$t('grid.others.terminalUnitPrice'),
              field: 'sWharfPrice',
              valueFormatter: (params) => {
                return SteelFormat.formatPrice(params.value)
              }
            }, {
              headerName: this.$t('grid.others.portBuiltUnitPrice'),
              field: 'sPortPrice',
              valueFormatter: (params) => {
                return SteelFormat.formatPrice(params.value)
              }
            }, {
              headerName: this.$t('grid.others.landTransportUnitPrice'),
              field: 'sLandPrice',
              valueFormatter: (params) => {
                return SteelFormat.formatPrice(params.value)
              }
            }, {
              headerName: this.$t('grid.others.otherUnitPrice'),
              field: 'sOtherPrice',
              valueFormatter: (params) => {
                return SteelFormat.formatPrice(params.value)
              }
            }
          ]
        },
        {
          headerName: this.$t('grid.others.systemInsuredUnitPrice'),
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'sSysPrice',
              headerClass: 'c-header_child',
              valueFormatter: (params) => {
                return SteelFormat.formatPrice(params.value)
              }
            }
          ]
        },
        {
          headerName: this.$t('grid.others.systemTaxDifference'),
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'sTaxPrice',
              headerClass: 'c-header_child',
              valueFormatter: (params) => {
                return SteelFormat.formatPrice(params.value)
              }
            }
          ]
        },
        {
          headerName: this.$t('grid.others.systemLogisticsCostTotal'),
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'vSumFeeAmt',
              headerClass: 'c-header_child',
              valueFormatter: (params) => {
                return SteelFormat.formatPrice(params.value)
              }
            }
          ]
        }
      ],
      columnDefs2: [
        {
          headerName: this.$t('grid.others.paymentOrderNumber'),
          field: 'sCode'
        },
        {
          headerName: this.$t('grid.others.paymentDate'),
          field: 'sRatifyDate',
          valueGetter: (params) => {
            return Moment.time('YYYY-MM-DD', params.data.sRatifyDate)
          }
        },
        // {
        //   headerName: this.$t('grid.others.expenseType'),
        //   field: 'sPaymentSubType',
        //   valueFormatter: (params) => {
        //     const status = this.options.feetype.filter(item => item.sCodeValue === params.value)
        //     return status.length ? status[0].sCodeName : params.value
        //   }
        // },
        {
          headerName: this.$t('grid.others.expenseType'),
          field: 'sConfigName'
        },
        {
          headerName: this.$t('grid.others.merchants'),
          field: 'vCustomerName'
        },
        {
          headerName: this.$t('grid.others.paymentAmount'),
          field: 'sTaxAmt',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return SteelFormat.formatPrice(params.value)
          }
        }
      ],
      columnDefs3: [
        {
          headerName: this.$t('grid.others.invoiceRegistrationNumber'),
          field: 'sCode'
        },
        {
          headerName: this.$t('grid.others.invoiceDateKey'),
          field: 'sInvoiceDate',
          valueGetter: (params) => {
            return Moment.time('YYYY-MM-DD', params.data.sInvoiceDate)
          }
        },
        {
          headerName: this.$t('grid.others.invoiceCustomerTag'),
          field: 'vCustomerName'
        },
        {
          headerName: this.$t('grid.others.numberOfInvoicesTag'),
          field: 'sInvQty',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            // vTaxAmt/vNetAmt
            return SteelFormat.formatThousandthSign(params.value, 4)
          }
        },
        {
          headerName: this.$t('grid.title.amountWithTax'),
          field: 'sTaxAmt',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return SteelFormat.formatPrice(params.value)
          }
        },
        {
          headerName: this.$t('grid.title.vatRate'),
          field: 'sVatRate',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return SteelFormat.toPercent(params.value, 2)
          }
        },
        {
          headerName: this.$t('grid.others.taxAmount'),
          field: 'sVatAmt',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return SteelFormat.formatPrice(params.value)
          }
        },
        {
          headerName: this.$t('grid.title.amountWithoutTax'),
          field: 'sNetAmt',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return SteelFormat.formatPrice(params.value)
          }
        }

      ],
      options: {
        status: [],
        feetype: [],
        artnoList: []
      },
      rowData: [],
      rowData1: [],
      rowData2: [],
      rowData3: [],
      gridShow1: true,
      gridShow2: true,
      gridShow3: true
    }
  },
  watch: {
    activeName(newVal, oldVal) {
      // this[`rowData${newVal}`] = []
      // this.$refs[`aggrid${newVal}`].loadTableData()
      // this.gridShow = false
      this[`gridShow${newVal}`] = false
      this.$nextTick(() => {
        this[`gridShow${newVal}`] = true
      })
    }
  },
  created() {
    DictUtil.getDict(['dev.common.sheet.status', 'pay.subtype'], res => {
      this.options.status = res[0].dicts
      this.options.feetype = res[1].dicts
    })
    getArtnoDetail({ supParam: '122111111111150' }).then(res => {
      this.options.artnoList = res.data
    })
  },
  methods: {
    close() {
      this.$emit('close')
    },
    tabClick(e) {
      console.log(e)
      this.activeName = e.name
    },
    handleInvoiceFooterCount(params) {
      // this.rowdataarr = []
      // this.rowData.map(item => {
      //   if (item._selected === true) {
      //     this.rowdataarr.push(item)
      //   }
      // })
      // if (this.rowdataarr.length > 0) {
      //   this.sPurContractId = this.rowdataarr[0].sPurContractId
      // } else {
      //   this.sPurContractId = ''
      //   this.rowData1 = []
      // }
      if (params.data._hiddenCheckbox) return false
      this.sPurContractId = params.data.sPurContractId
      if (params.data.sPurContractId) {
        if (this.$refs[`aggrid${this.activeName}`]) {
          this.$refs[`aggrid${this.activeName}`].loadTableData()
        }
      }
      // 选中后回掉
    },
    loadData3() {
      return new Promise((resolve, reject) => {
        payinvoiceeceipt({ sSaleContractId: this.info.sSaleContractId, sPurContractId: this.sPurContractId, sProjectCode: this.info.sProjectCode }).then(res => {
          this.rowData3 = res.data.map((item, index) => {
            item._selected = false
            return item
          })
          this.handleCellTotal3()
          resolve(res.data.length)
        }).catch(() => {
          reject(0)
        })
      })
    },
    loadData2() {
      return new Promise((resolve, reject) => {
        payfeepaymentdetail({ sSaleContractId: this.info.sSaleContractId, sPurContractId: this.sPurContractId, sProjectCode: this.info.sProjectCode }).then(res => {
          this.rowData2 = res.data.map((item, index) => {
            item._selected = false
            return item
          })
          this.handleCellTotal2()
          resolve(res.data.length)
        }).catch(() => {
          reject(0)
        })
      })
    },
    loadDatas1() {
      return new Promise((resolve, reject) => {
        paysettlefeelistloginst({ sSaleContractId: this.info.sSaleContractId, sPurContractId: this.sPurContractId, sProjectCode: this.info.sProjectCode }).then(res => {
          this.rowData1 = res.data.map((item, index) => {
            item._selected = false
            return item
          })
          this.handleCellTotal1()
          resolve(res.data.length)
        }).catch(() => {
          reject(0)
        })
      })
    },
    loadData() {
      return new Promise((resolve, reject) => {
        paysettlefeelist({ sSaleContractId: this.info.sSaleContractId, sProjectCode: this.info.sProjectCode }).then(res => {
          this.rowData = res.data.map((item, index) => {
            item._selected = false
            // if (index === 0) {
            //   item._selected = true
            //   this.sPurContractId = item.sPurContractId
            // }
            return item
          })
          if (res.data.length === 0) {
            this.sPurContractId = null
          } else {
            this.handleInvoiceFooterCount({ data: res.data[0] })
          }
          this.iniftable = true
          this.activeName = '1'
          this.handleCellTotal()
          resolve(res.data.length)
        }).catch(() => {
          reject(0)
        })
      })
    },
    handleCellTotal3(type = 'all') {
      setTimeout(() => {
        const totalList = computeCellTotal(
          this.rowData3,
          {
            sInvQty: 0,
            sTaxAmt: 0,
            sVatAmt: 0,
            sNetAmt: 0
          },
          {
            sCode: type === 'all' ? this.$t('grid.others.amountTo') : `${this.$t('components.selected')}${this.rowData3.filter(item => item._selected).length}${this.$t('pagination.items')}`,
            sInvoiceDate: null,
            sMaturityDate: null,
            sFreeExpiryDate: null,
            sMonthRate: null,
            sIrtDay: null,
            sSingleRate: null,
            _selected: false,
            _hiddenCheckbox: true
          },
          type
        )
        if (this.rowData3.length) {
          this.$refs.aggrid3.gridApi.setPinnedBottomRowData([totalList])
        }
      }, 0)
    },
    selectedChange1(list) {
      this.$refs.aggrid1.getSelectedData(res => {
        this.handleCellTotal1(res.length ? 'selected' : 'all')
      })
    },
    selectedChange2(list) {
      this.$refs.aggrid2.getSelectedData(res => {
        this.handleCellTotal2(res.length ? 'selected' : 'all')
      })
    },
    selectedChange3(list) {
      this.$refs.aggrid3.getSelectedData(res => {
        this.handleCellTotal3(res.length ? 'selected' : 'all')
      })
    },
    handleCellTotal2(type = 'all') {
      setTimeout(() => {
        const totalList = computeCellTotal(
          this.rowData2,
          {
            sTaxAmt: 0
          },
          {
            sCode: type === 'all' ? this.$t('grid.others.amountTo') : `${this.$t('components.selected')}${this.rowData2.filter(item => item._selected).length}${this.$t('pagination.items')}`,
            sRatifyDate: null,
            sMaturityDate: null,
            sFreeExpiryDate: null,
            sMonthRate: null,
            sIrtDay: null,
            sSingleRate: null,
            _selected: false,
            _hiddenCheckbox: true
          },
          type
        )
        if (this.rowData2.length) {
          this.$refs.aggrid2.gridApi.setPinnedBottomRowData([totalList])
        }
      }, 0)
    },
    handleCellTotal1(type = 'all') {
      setTimeout(() => {
        const totalList = computeCellTotal(
          this.rowData1,
          {
            sContractQty: 0,
            sLogisticsPrice: 0,
            sWharfPrice: 0,
            sPortPrice: 0,
            sFeePrice: 0,
            sLandPrice: 0,
            sOtherPrice: 0,
            sSysPrice: 0,
            sTaxPrice: 0,
            vSumFeeAmt: 0
          },
          {
            sCode: type === 'all' ? this.$t('grid.others.amountTo') : `${this.$t('components.selected')}${this.rowData1.filter(item => item._selected).length}${this.$t('pagination.items')}`,
            sBillCode: null,
            sMaturityDate: null,
            sFreeExpiryDate: null,
            sMonthRate: null,
            sIrtDay: null,
            sSingleRate: null,
            _selected: false,
            _hiddenCheckbox: true
          },
          type
        )
        if (this.rowData1.length) {
          this.$refs.aggrid1.gridApi.setPinnedBottomRowData([totalList])
        }
      }, 0)
    },
    handleCellTotal(type = 'all') {
      setTimeout(() => {
        const totalList = computeCellTotal(
          this.rowData,
          {
            vContractQty: 0,
            vSendQtx: 0,
            vLogisticsAmt: 0,
            vPayAmt: 0,
            sFeePrice: 0,
            vInvoiceAmt: 0
          },
          {
            sPurContractCode: this.$t('grid.others.amountTo'),
            _selected: false,
            _hiddenCheckbox: true
          },
          type
        )
        if (this.rowData.length) {
          this.$refs.aggrid.gridApi.setPinnedBottomRowData([totalList])
        }
      }, 0)
    }
  }
}
</script>
<style scoped>
.btn-group{
  background:#eaf3ff;
  padding: 2px 10px;
}
.text{
 color:#3e8ddc;
}
</style>
