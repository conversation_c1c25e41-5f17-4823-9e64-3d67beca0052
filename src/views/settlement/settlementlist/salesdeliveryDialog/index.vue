<template>
  <div>
    <cnd-dialog :title="$t('grid.tabs.basicInformation')" :visible="true" @close="handleClose">
      <template slot="leftBtn">
        <div v-show="!isTrial">
          <el-button
            v-has:esc_con_settlement_detail_submit
            class="mb-10"
            type="primary"
            size="mini"
            :disabled="(formDetail.sSheetStatus != 10 && formDetail.sSheetStatus != 11 && formDetail.sSheetStatus != 15) || $route.query.disabled === '1'"
            @click="subPurInvoice"
          >{{ $t('grid.others.submit') }}</el-button>
          <el-button
            v-has:esc_con_settlement_detail_delete
            type="danger"
            size="mini"
            :disabled="formDetail.sSheetStatus !== '10' && formDetail.sSheetStatus !== '11'"
            @click="delPurInvoice"
          >{{ $t('btns.delete') }}</el-button>
          <!-- <el-button
          v-has:esc_con_settlement_detail_auto_rate
          type="primary"
          :disabled="formDetail.sNatureType!=10"
          size="mini"
          @click="overviewoverview"
        >{{ $t('grid.others.automaticInterestUpdate') }}</el-button> -->
          <el-button
            v-has:esc_con_settlement_detail_auto_create
            type="primary"
            size="mini"
            :disabled="genDisabled"
            @click="settletoFinalfun"
          >{{ $t('grid.others.generateFinalSettlement')
          }}</el-button>
          <el-button
            v-has:esc_con_settlement_detail_estimate_settlement
            type="primary"
            :disabled="formDetail.sSheetStatus != 70 || formDetail.sNatureType != 20 || $route.query.disabled === '1' || (formDetail.sIsSign === '1' && formDetail.sSignStatus === '10')"
            size="mini"
            @click="settletoPrefun"
          >{{ $t('grid.btns.generateSettlementAdjustment') }}</el-button>
          <el-button
            v-has:esc_con_settlement_detail_ejx
            type="primary"
            :disabled="['10', '11', '15', '30'].includes(formDetail.vMaxStatus) || (formDetail.sSheetStatus != '70' && (formDetail.sIsSign != null || formDetail.sIsSign != '')) || $route.query.disabled === '1' || formDetail.sSheetVersion != 0"
            size="mini"
            @click="settlesendESCfun"
          >{{ $t('grid.btns.initiateESignature') }}</el-button>
          <el-button
            v-has:esc_con_settlement_detail_revoke_ejx
            type="warning"
            :disabled="revokeEjxDisabled"
            size="mini"
            @click="settlerevokeToESCfun"
          >{{
            $t('grid.others.eContractCancellation') }}</el-button>
          <el-button
            v-has:esc_con_settlement_detail_revoke_apply
            type="warning"
            size="mini"
            :disabled="withdrawDisabled"
            @click="revPurInvoice"
          >{{ $t('grid.others.withdrawRequest') }}</el-button>
          <el-button
            v-has:esc_con_settlement_detail_file_query
            type="primary"
            size="mini"
            @click="visibleDialog.annex = true"
          >{{ $t('btns.attachmentManagement') }}</el-button>
          <el-button v-has:esc_con_settlement_detail_approval type="primary" size="mini" @click="openApproval()">{{
            $t('grid.others.approvalStatus') }}</el-button>
          <el-button
            v-has:esc_con_settlement_detail_ver
            type="primary"
            size="mini"
            :disabled="verDisabled"
            @click="settlebackupfun"
          >{{ $t('grid.others.versionMaintenance') }}</el-button>
          <el-button
            v-has:esc_con_settlement_detail_print
            type="primary"
            size="mini"
            @click="printPreviewfun"
          >{{ $t('grid.others.printPreview') }}</el-button>
          <el-button
            v-has:esc_con_settlement_detail_export_excel
            type="primary"
            size="mini"
            @click="exportExcel"
          >导出excel</el-button>
          <el-button
            v-has:esc_con_settlement_detail_pur_auto_rate
            type="primary"
            class="mb-10"
            size="mini"
            :disabled="(formDetail.sSheetStatus != 10 && formDetail.sSheetStatus != 11 && formDetail.sSheetStatus != 15) || $route.query.disabled === '1'"
            @click="update"
          >{{ showNewFullAmount ? '更新折让' : $t('grid.others.updateReceiptInterest') }}</el-button>
          <template v-if="isShanghaiWz">
            <el-button
              v-has:esc_con_settlement_detail_sendPreESC
              type="primary"
              size="mini"
              :disabled="isSendPreESC"
              @click="onSendPreESC"
            >
              预结算发起电子签约
            </el-button>
            <el-button
              v-has:esc_con_settlement_detail_revokePreESC
              type="primary"
              size="mini"
              :disabled="isRevokePreESC"
              @click="onRevokePreESC"
            >
              预结算撤回
            </el-button>
          </template>
          <template v-if="showRefund">
            <el-button
              v-has:esc_pay_settle_doReleasesPayInteraction
              type="primary"
              size="mini"
              :disabled="refundDisabled"
              @click="onRefund"
            >
              退款
            </el-button>
          </template>
          <template v-if="isXiamenWz">
            <el-button
              v-has:esc_con_settlement_detail_erp_approval_query
              class="mb-10"
              type="primary"
              size="mini"
              @click="visibleDialog.approvalN8 = true"
            >ERP{{ $t('grid.others.approvalInquiry') }}</el-button>
          </template>
          <template v-if="showNewFullAmount">
            <el-button
              v-has:esc_pay_settle_createTrialSettle
              type="primary"
              size="mini"
              @click="createTrialSettle"
            >成本试算</el-button>
          </template>
          <template v-if="showCostSplit">
            <el-button
              v-has:esc_pay_settle_add_costsplitDetail
              type="primary"
              size="mini"
              @click="settletoCostSplit"
            >采购成本拆分</el-button>
          </template>
        </div>
        <div v-show="isTrial">
          <el-button
            v-has:esc_con_settlement_detail_delete
            type="danger"
            size="mini"
            :disabled="formDetail.sSheetStatus !== '10' && formDetail.sSheetStatus !== '11'"
            @click="delPurInvoice"
          >{{ $t('btns.delete') }}</el-button>
          <el-button
            v-has:esc_con_settlement_detail_export_excel
            type="primary"
            size="mini"
            @click="exportExcel"
          >导出excel</el-button>
          <el-button
            v-has:esc_con_settlement_detail_pur_auto_rate
            type="primary"
            size="mini"
            @click="update"
          >{{ $t('grid.others.updateReceiptInterest') }}</el-button>
          <el-button
            v-has:esc_pay_settle_settleDiff
            type="primary"
            size="mini"
            @click="settleDiffQuery"
          >应收差额查询</el-button>
          <el-button
            v-has:esc_pay_settle_syncOriSettleData
            type="primary"
            size="mini"
            @click="otherExpense"
          >代入其他费用</el-button>
          <template v-if="showCostSplit">
            <el-button
              v-has:esc_pay_settle_add_costsplitDetail
              type="primary"
              size="mini"
              @click="settletoCostSplit"
            >采购成本拆分</el-button>
          </template>
        </div>
      </template>
      <template slot="content">
        <el-tabs
          v-model="activeName"
          class="tabs-btn-position"
          :class="activeName === '1' ? 'detail-tabs' : ''"
          :before-leave="beforeLeave"
        >
          <el-tab-pane :label="$t('grid.tabs.basicInformation')" name="0">
            <cnd-btn-position top="7" right="10">
              <el-button
                v-has:esc_con_settlement_detail_base_save
                type="primary"
                size="mini"
                :disabled="(formDetail.sSheetStatus != 10 && formDetail.sSheetStatus != 15 && formDetail.sSheetStatus != 11) || $route.query.disabled === '1'"
                @click="savePurInvoice"
              >{{ $t('btns.save') }}</el-button>
            </cnd-btn-position>
            <baseInfo
              v-if="activeName == '0' && !showNewFullAmount"
              ref="basic"
              :info="formDetail"
              @handleUpdata="isUpdata = true"
              @saveSelect="saveSelect"
            />
            <baseInfoV2
              v-if="activeName == '0' && showNewFullAmount"
              ref="basic"
              :info="formDetail"
              @handleUpdata="isUpdata = true"
              @saveSelect="saveSelect"
            />
          </el-tab-pane>
          <el-tab-pane :label="$t('grid.others.statementDetails')" name="1">
            <!-- 结算单明细 -->
            <div v-if="!isTrial">
              <cnd-btn-position top="7" right="10">
                <el-button
                  v-has:esc_con_settlement_detail_purIds_sSettlementId
                  type="primary"
                  size="mini"
                  @click="openPurchaseAnnex"
                >采购合同附件</el-button>
                <el-button
                  v-has:esc_con_settlement_detail_file_import
                  type="primary"
                  size="mini"
                  @click="openSalesAnnex"
                >{{ $t('grid.columns.appendixSalesContract') }}</el-button>
              </cnd-btn-position>
            </div>
            <settlementInfo
              v-if="activeName == '1' && saleinfo && !showNewFullAmount"
              ref="settlementInfo"
              :s-id="id"
              :s-code="formDetail.vManagementCode"
            />
            <settlementInfoV2
              v-if="activeName == '1' && saleinfo && showNewFullAmount"
              ref="settlementInfoV2"
              :s-id="id"
              :s-code="formDetail.vManagementCode"
            />
          </el-tab-pane>
          <el-tab-pane :label="$t('grid.others.statementOverview')" name="2">
            <overview
              v-if="activeName == '2' && !showNewFullAmount"
              :id="id"
              ref="overview"
              :info="formDetail"
              @handleUpdata="(v) => isInvalidUpdata = v"
            />
            <overviewV3
              v-if="activeName == '2' && showNewFullAmount"
              :id="id"
              ref="overview"
              :info="formDetail"
              @handleUpdata="isInvalidUpdata = true"
            />
          </el-tab-pane>
          <el-tab-pane v-if="showguodu" :label="$t('grid.others.statementOverview')+'(总额法)'" name="3">
            <!-- <el-tab-pane v-if="formDetail && formDetail.sCheckType === '01'" :label="$t('grid.others.statementOverview')+'(总额法)'" name="3"> -->
            <overviewV2
              v-if="activeName == '3'"
              :id="id"
              ref="overviewV2"
              :info="formDetail"
              @handleUpdata="(v) => isInvalidUpdata = v"
            />
          </el-tab-pane>
        </el-tabs>
      </template>
    </cnd-dialog>
    <!-- 弹出层 -->
    <approval-dialog
      :id="id"
      :id-keys="approvalKeys"
      :solt-btn="false"
      :visible.sync="visibleDialog.approvaldia"
      @handleClose="visibleDialog.approvaldia = false"
    />
    <submitDialog
      :id="id"
      :visible="visibleDialog.submitDialog"
      :api-url="`/esc/pay/settle/desc/${id}`"
      :submit-desc="submitDesc"
      @handleClose="visibleDialog.submitDialog = false"
      @onSure="onSubmit"
    />
    <!-- <horizon-approval-dialog
      :id="id"
      :solt-btn="false"
      :visible.sync="visibleDialog.approvaldia"
      @handleClose="handleCloseApproval"
    /> -->
    <horizon-approval-dialog
      :id="id"
      :type="'n8'"
      :solt-btn="false"
      :visible="visibleDialog.approvalN8"
      @handleClose="visibleDialog.approvalN8 = false"
    />
    <steel-annex-dialog
      :visible="visibleDialog.annex"
      append-to-body
      :biz-id="id"
      :disabled-btn="{ scan: isBusinessDisabled('save', formDetail.sSheetStatus) || $route.query.disabled === '1', del: isBusinessDisabled('save', formDetail.sSheetStatus) || $route.query.disabled === '1' }"
      @onSelect="visibleDialog.annex = false"
    />
    <steel-annex-dialog
      :visible="visibleDialog.contractAnnex"
      append-to-body
      :biz-id="contractAnnexId"
      :option="{ vBusIds: purchaseAnnexId }"
      :disabled-btn="{ scan: true, del: true }"
      @onSelect="visibleDialog.contractAnnex = false"
    />
    <steel-annex-dialog
      :visible="visibleDialog.genAnnex"
      append-to-body
      :biz-id="id"
      :disabled-btn="{ scan: false, del: false }"
      :upload-option="{ sExtend1:'JSDFJ-XX' }"
      @onSelect="genAnnexClose"
    />
    <cnd-dialog
      :visible.sync="iframeVisible"
      :fullscreen="false"
      :title="$t('grid.others.printPreview')"
      width="80%"
      @close="iframeVisible = false"
    >
      <template slot="content">
        <iframe :src="pdfUrl" width="100%" height="500" frameborder="0" />
      </template>
    </cnd-dialog>
    <checkDialog
      :visible="visibleDialog.check"
      :account-id="accountId"
      @close="checkDialogClose"
      @success="settletoFinalfunV2"
    />
    <CallInInfoDialogV2
      v-if="visibleDialog.callinV2"
      :visible="visibleDialog.callinV2"
      :s-company-id="formDetail.sCompanyId"
      :s-customer-id="formDetail.sCustomerId"
      :s-root-unit-code="formDetail.vManagementCode"
      :formdata="formDetail"
      @onClose="()=>visibleDialog.callinV2 = false"
      @onSure="onSureCallInfo"
    />
    <formDialog
      v-if="visibleDialog.diffQuery"
      :visible="visibleDialog.diffQuery"
      :form-items="formDialogItems"
      width="350px"
      height="120px"
      label-width="120px"
      title="应收差额查询"
      @close="visibleDialog.diffQuery = false"
      @success="visibleDialog.diffQuery = false"
    />
    <formDialog
      v-if="visibleDialog.houHaiDialog"
      :visible="visibleDialog.houHaiDialog"
      :form-items="houHaiFormDialogItems"
      width="350px"
      height="40px"
      label-width="120px"
      title="过磅结算数量"
      success-no-visible
      @close="visibleDialog.houHaiDialog = false"
      @success="houHaiDialogSuccess"
    />
    <formDialog
      v-if="visibleDialog.RefundDialog"
      :visible="visibleDialog.RefundDialog"
      :form-items="RefundFormDialogItems"
      width="350px"
      height="80px"
      label-width="120px"
      title="退款"
      success-no-visible
      @close="visibleDialog.RefundDialog = false"
      @success="RefundDialogSuccess"
    />
    <CostSplitDialog
      v-if="visibleDialog.costSplitDialogVisible"
      :dialog-visible="visibleDialog.costSplitDialogVisible"
      :info="formDetail"
      @close="visibleDialog.costSplitDialogVisible = false"
      @success="costSplitDialogSuccess"
    />
  </div>
</template>
<script>
import settlementInfo from './settlementInfo'
import settlementInfoV2 from './settlementInfoV2'
import baseInfo from './baseInfo/index'
import baseInfoV2 from './baseInfo/indexV2'
import overview from './overview/index'
import overviewV2 from './overview/indexV2'
import overviewV3 from './overview/indexV3'
import businessMixin from '@/utils/businessMixin'
import approvalDialog from '@/components/approvalDialog'
import submitDialog from '@/components/submitDialog'
import checkDialog from '@/components/checkEJQDialog/index'
import CallInInfoDialogV2 from '../../CallInInfoDialogV2.vue'
import formDialog from '@/components/formDialog'
import CostSplitDialog from '../CostSplitDialog'

import {
  settdeliveryget,
  settleInterestList,
  submitPurstock,
  submitCheckPurstock,
  submitDescPurstock,
  updatasettle,
  settleOverviewUpdate,
  settleOverviewModifies,
  removePurstock,
  revokePurstock,
  settlebackup,
  // settletoFinal,
  settletoFinalV2,
  doFinalCheck,
  settletoPre,
  settleDetaileExport,
  settlerevokeToESC,
  settlePurInterestUpdatetop,
  settlesendESC,
  settlePurInterestUpdate,
  getOri,
  sendPreESC,
  revokePreESC,
  doReleasesPayInteraction,
  queryReleaseInfoDialog,
  paySettleDetailPurIds,
  updateInterestCheck,
  checkIsEjqCustomer,
  updateConSettlementInfo,
  createTrialSettle,
  settleDiff,
  syncOriSettleData,
  isExistTrialSettle
} from '@/api/settlement'
import { getSysParameter } from '@/api/common'
import { exportAll } from '@/api/export'
const rootUnitCode = {
  '00014960': '上海物资',
  '00014867': '厦门物资',
  '00011061': '建发金属'
}
export default {
  name: 'SettlementDetail',
  components: {
    baseInfo,
    baseInfoV2,
    overview,
    settlementInfo,
    settlementInfoV2,
    approvalDialog,
    submitDialog,
    checkDialog,
    overviewV2,
    overviewV3,
    CallInInfoDialogV2,
    formDialog,
    CostSplitDialog
  },
  mixins: [businessMixin],
  props: {},
  data() {
    return {
      iframeVisible: false,
      pdfUrl: null,
      visibleDialog: {
        approvalN8: false,
        annex: false,
        contractAnnex: false,
        approvaldia: false,
        submitDialog: false,
        check: false,
        genAnnex: false,
        callinV2: false,
        diffQuery: false,
        houHaiDialog: false,
        costSplitDialogVisible: false,
        RefundDialog: false
      },
      costSplitDialogTpye: '',
      submitDesc: null,
      formDetail: {},
      oldDetail: null,
      hasInterestList: false,
      isUpdata: false,
      isInvalidUpdata: false,
      activeName: '0',
      saleinfo: true,
      id: null,
      param: {},
      approvalKeys: null,
      contractAnnexId: '',
      purchaseAnnexId: '',
      accountId: '',
      isPreEsc: false,
      isPreAdjust: false,
      formDialogItems: [
        // recAmt(净额法应收)，totalRecAmt（总额法应收）,diffAmt(差值)
        {
          label: '净额法货值应收',
          value: 'recAmt',
          type: 'elInput',
          customWidth: 22,
          default: 0,
          disabled: true,
          tips: '净额法货值应收=货值+计息明细（利息加违约金）+固定加价'
        },
        {
          label: '总额法货值应收',
          value: 'totalRecAmt',
          type: 'elInput',
          customWidth: 22,
          default: 0,
          disabled: true,
          tips: '总额法货值应收=结算基础价-商业折让+超期结算费'
        },
        {
          label: '差值',
          value: 'diffAmt',
          type: 'elInput',
          customWidth: 22,
          default: 0,
          disabled: true,
          tips: '差值=净额法货值应收-总额法货值应收'
        }
      ],
      houHaiFormDialogItems: [
        {
          label: '过磅结算数量',
          value: 'sExtend13',
          type: 'elInput',
          customWidth: 22,
          default: 0,
          required: true,
          disabled: false
        }
      ],
      RefundFormDialogItems: [
        {
          label: '待释放金额',
          value: 'vLeftAmt',
          type: 'elInput',
          customWidth: 21,
          default: 0,
          disabled: true
        },
        {
          label: '本次释放金额',
          value: 'vCurReleaseAmt',
          type: 'cndInputNumber',
          customWidth: 21,
          decimalDigit: 2,
          default: 0,
          required: true,
          disabled: false
        }
      ]
    }
  },
  computed: {
    isDisabled() {
      return this.formDetail.sSheetStatus !== '10'
    },
    revokeEjxDisabled() {
      const { sIsSign, sSignStatus } = this.formDetail
      return sIsSign !== '1' || sSignStatus !== '10'
    },
    isShanghaiWz() {
      return this.formDetail.vManagementCode === '00014960'
    },
    isXiamenWz() {
      return this.formDetail.vManagementCode === '00014867'
    },
    verDisabled() {
      return ['11', '15', '30'].includes(this.formDetail.vMaxStatus) || this.formDetail.sExtend2 === '10' || this.formDetail.sSheetStatus !== '70' || this.formDetail.sNatureType !== '10' || this.$route.query.disabled === '1' || (this.formDetail.sIsSign === '1' && this.formDetail.sSignStatus === '10')
    },
    isSendPreESC() {
      return ['11', '15', '30'].includes(this.formDetail.vMaxStatus) || (!(this.formDetail.sExtend1 !== '1' && this.formDetail.sSheetStatus === '70' && this.formDetail.sNatureType === '10')) || this.formDetail.sSheetVersion !== 0
    },
    isRevokePreESC() {
      return !(this.formDetail.sExtend1 === '1' && this.formDetail.sExtend2 === '10' && this.formDetail.sSheetStatus === '70')
    },
    showRefund() {
      return this.formDetail.sExtend18 === '0'
    },
    sOriId() {
      return this.formDetail.sOriId
    },
    withdrawDisabled() {
      return this.formDetail.sSheetStatus !== '30' || this.$route.query.disabled === '1' || (this.formDetail.sSheetVersion !== '0' && this.formDetail.sExtend6 === '1')
    },
    genDisabled() {
      const { vMaxStatus, sSheetStatus, sNatureType, sSignStatus, sSheetVersion } = this.formDetail
      const { disabled } = this.$route.query
      return !(
        disabled !== '1' &&
        sSheetVersion === 0 &&
        sNatureType === '10' &&
        sSheetStatus === '70' &&
        ['00', '50'].includes(sSignStatus) &&
        !(['10', '11', '15', '30'].includes(vMaxStatus))
      )
    },
    showNewFullAmount() {
      // 结算单详情页 : sExtend21 合同核算方式  sCheckType  结算条款对应的核算方式
      // sExtend21= 01 & sCheckType=01，则是新版
      // sExtend21= 01 & sCheckType=02，则是过渡版
      // sExtend21= 02 则是旧版
      const { sExtend21, sCheckType } = this.formDetail
      return sExtend21 === '01' && sCheckType === '01'
    },
    showCostSplit() {
      const { sIsExistCostSplit } = this.formDetail
      return sIsExistCostSplit === '1'
    },
    showguodu() {
      const { sExtend21, sCheckType } = this.formDetail
      return sExtend21 === '01' && sCheckType === '02' && !this.isTrial
    },
    refundDisabled() {
      return ['70'].includes(this.formDetail.sExtend20)
    },
    isTrial() {
      // 是否试算
      return this.formDetail.sExtend23 === '1'
    }

  },
  watch: {
    // activeName(newVal, oldVal) {
    //   if (newVal === '0') {
    //     this.initFn()
    //   }
    //   if (newVal !== '0') {
    //     if (this.isUpdata) {
    //       this.savePurInvoice()
    //     }
    //   }
    // }
  },
  created() {
    this.param = this.$route.query
    this.id = this.param.id
    this.initFn()
    if (this.param.activeName) {
      this.$nextTick(() => {
        this.activeName = this.param.activeName
      })
    }
  },
  methods: {
    openApproval() {
      // console.log('sOriId', this.sOriId)
      // console.log('id', this.id)
      getOri({ id: this.sOriId }).then(res => {
        const data = [this.id]
        this.approvalKeys = res.data.length > 0 ? res.data : data
        this.visibleDialog.approvaldia = true
      })
    },
    openPurchaseAnnex() {
      this.contractAnnexId = ''
      paySettleDetailPurIds(this.formDetail.sProjectCode).then(res => {
        if (res.code === '0000') {
          this.purchaseAnnexId = res.data
          if (this.purchaseAnnexId) {
            this.visibleDialog.contractAnnex = true
          } else {
            this.$message.error('未找到对应采购合同附件')
          }
        }
      })
    },
    openSalesAnnex() {
      this.purchaseAnnexId = ''
      this.contractAnnexId = this.formDetail.sSaleContractId
      this.visibleDialog.contractAnnex = true
    },
    update() {
      setTimeout(() => {
        updateInterestCheck({ id: this.id }).then((res) => {
          if (res.code === '0000') {
            this.updateV2()
          }
        }).catch(err => {
          this.$message.closeAll()
          this.$confirm(err.message + '</br>' + '请确认是否继续', this.$t('grid.others.prompt'), {
            dangerouslyUseHTMLString: true,
            confirmButtonText: this.$t('btns.confirm'),
            cancelButtonText: this.$t('btns.cancel'),
            type: 'error'
          }).then(() => {
            this.updateV2()
          })
        })
      }, 500)
    },
    updateV2() {
      settlePurInterestUpdate({
        sSettlementId: this.formDetail.sId,
        sSaleContractId: this.formDetail.sSaleContractId
      })
        .then(res => {
          if (res.code === '0000') {
            this.$refs.settlementInfo && this.$refs.settlementInfo.updataFun()
            this.$refs.settlementInfoV2 && this.$refs.settlementInfoV2.updataFun()
            this.$refs.overview && this.$refs.overview.queryForm()
            this.$refs.overviewV2 && this.$refs.overviewV2.queryForm()
            this.$refs.basic && this.initFn()
            this.$message.success(this.$t('tips.operationSuccessful'))
          }
        })
    },
    async beforeLeave(activeName, oldActiveName) {
      let blFlag = false
      if (activeName === '0') {
        this.initFn()
        blFlag = true
      }
      if (oldActiveName === '2' && this.isInvalidUpdata) {
        const flag = new Promise((resolve, reject) => {
          this.$refs.overview.savelist(async(list) => {
            if (this.id) {
              const flagItem = new Promise((resolve, reject) => {
                settleOverviewModifies(list).then((res) => {
                  if (this.showNewFullAmount) {
                    this.initFn()
                    this.$message.success(this.$t('grid.others.modifySuccessfully'))
                    this.isInvalidUpdata = false
                    resolve(true)
                  } else {
                    settleOverviewUpdate({ id: this.id }).then(res => {
                      this.initFn()
                      this.$message.success(this.$t('grid.others.modifySuccessfully'))
                      this.isInvalidUpdata = false
                      resolve(true)
                    }).catch(() => {
                      resolve(true)
                    })
                  }
                })
              })
              const isTure = await flagItem
              resolve(isTure)
            } else {
              resolve(true)
            }
          })
        })
        blFlag = await flag
      }
      if (oldActiveName === '3' && this.isInvalidUpdata) {
        const flag = new Promise((resolve, reject) => {
          this.$refs.overviewV2.savelist(async(list) => {
            if (this.id) {
              // 编辑
              const flagItem = new Promise((resolve, reject) => {
                settleOverviewModifies(list).then((res) => {
                  this.initFn()
                  this.$message.success(this.$t('grid.others.modifySuccessfully'))
                  this.isInvalidUpdata = false
                  resolve(true)
                }).catch(() => {
                  resolve(false)
                })
              })
              const isTure = await flagItem
              resolve(isTure)
            } else {
              resolve(true)
            }
          })
        })
        blFlag = await flag
      }
      if (oldActiveName === '0' && this.isUpdata) {
        const flag = new Promise((resolve, reject) => {
          this.$refs.basic.arrivalModify(async(form) => {
            if (this.id) {
              const flagItem = new Promise((resolve, reject) => {
                updatasettle(form).then((res) => {
                  settleOverviewUpdate({ id: this.id }).then(res => {
                    this.initFn()
                    this.$message.success(this.$t('tips.saveSuccess'))
                    this.isUpdata = false
                    resolve(true)
                  }).catch(() => {
                    resolve(true)
                  })
                })
              })
              const isTure = await flagItem
              resolve(isTure)
            } else {
              resolve(true)
            }
          })
        })
        blFlag = await flag
      }
      return blFlag
    },
    // async beforeLeave(activeName, oldActiveName) {
    //   let blFlag = false
    //   if (activeName === '0') {
    //     this.initFn()
    //     blFlag = true
    //   } else {
    //     if (this.isUpdata) {
    //       const save = (form) => {
    //         if (this.id) {
    //           // 编辑
    //           updatasettle(form).then((res) => {
    //             this.updateForm()
    //             this.$message.success(this.$t('tips.saveSuccess'))
    //             this.isUpdata = false
    //           })
    //         }
    //       }
    //       // 1、判断是否修改过： 采购是否分批付款 保证金计息 利息起算标准 计息方式
    //       // 2、收款及计息明细 存在值
    //       const needInterestDict = ['sIsBatch', 'sIsMarginIrt', 'sInterestStart', 'sFormulaType']
    //       let isNeedInterest = false
    //       // 返回true or false
    //       const flag = new Promise((resolve, reject) => {
    //         this.$refs.basic.arrivalModify(async(form) => {
    //           const oldObj = JSON.parse(this.oldDetail)
    //           for (const key in form) {
    //             if (oldObj[key] !== form[key]) {
    //               console.log(key, 'old:', oldObj[key], 'new:', form[key])
    //               if (needInterestDict.includes(key)) {
    //                 isNeedInterest = true
    //               }
    //             }
    //           }
    //           if (this.hasInterestList && isNeedInterest) {
    //             const flagItem = new Promise((resolve, reject) => {
    //               this.$confirm(this.$t('grid.others.modificationWillCleModifyKey'), this.$t('grid.others.prompt'), {
    //                 confirmButtonText: this.$t('btns.confirm'),
    //                 cancelButtonText: this.$t('btns.cancel'),
    //                 type: 'warning'
    //               }).then(() => {
    //                 save(form)
    //                 resolve(true)
    //               }).catch(() => {
    //                 this.initFn()
    //                 resolve(true)
    //               })
    //             })
    //             const isTure = await flagItem
    //             resolve(isTure)
    //           } else {
    //             save(form)
    //           }
    //         })
    //       })
    //       blFlag = await flag
    //     } else {
    //       blFlag = true
    //     }
    //   }
    //   console.log('last', blFlag)
    //   return blFlag
    // },
    tabsqh() {
      this.activeName = '2'
    },
    async initFn(id) {
      const fetchData = async(fetchId) => {
        try {
          const [formDetailResponse, interestListResponse] = await Promise.all([
            settdeliveryget({ id: fetchId }),
            settleInterestList({ id: fetchId })
          ])
          console.log('formDetail', formDetailResponse.data)
          console.log('vManagementCode', formDetailResponse.data.vManagementCode, rootUnitCode[formDetailResponse.data.vManagementCode])
          this.formDetail = formDetailResponse.data
          this.oldDetail = JSON.stringify(formDetailResponse.data)
          this.$refs.overview && this.$refs.overview.queryForm()
          this.$refs.overviewV2 && this.$refs.overviewV2.queryForm()
          this.hasInterestList = !!interestListResponse.data.datas.length
          this.saleinfo = true
          this.isPreEsc = false
          this.isPreAdjust = false
        } catch (error) {
          console.error('Error in fetchData:', error)
        }
      }
      if (id || this.id) {
        return fetchData(id || this.id)
      } else {
        return Promise.reject('No ID provided')
      }
    },
    handleClose() {
      // 需要判断是否存在 修改未保存项
      if (this.isUpdata) {
        this.$confirm(this.$t('grid.tips.whetherToWaiveModification'), this.$t('grid.others.prompt'), {
          confirmButtonText: this.$t('btns.confirm'),
          cancelButtonText: this.$t('btns.cancel'),
          type: 'warning'
        }).then(() => {
          this.$emit('close')
        })
      } else {
        this.$emit('close')
      }
    },
    updateForm() {
      return new Promise((resolve, reject) => {
        settleOverviewUpdate({ id: this.id }).then(res => {
          this.initFn()
          resolve(true)
        }).catch(() => {
          reject(false)
        })
      })
    },
    savePurInvoice() {
      return new Promise((resolve, reject) => {
        this.$refs.basic.arrivalModify((form) => {
          if (this.id) {
            // 编辑
            updatasettle(form).then(async(res) => {
              const result = await this.updateForm()
              this.$message.success(this.$t('tips.saveSuccess'))
              this.isUpdata = false
              if (result) {
                resolve(true)
              } else {
                reject(false)
              }
            })
          }
        })
      })
    },
    saveSelect(type) {
      const titletips = type === 'sExtend3' ? '修改物流费用加价系数将重置物流费用信息，是否继续修改?' : this.$t('grid.others.modificationWillCleModifyKey')
      this.$confirm(titletips, this.$t('grid.others.prompt'), {
        confirmButtonText: this.$t('btns.confirm'),
        cancelButtonText: this.$t('btns.cancel'),
        type: 'warning'
      }).then(() => {
        this.$refs.basic.arrivalModify((form) => {
          updatasettle(form).then((res) => {
            this.updateForm()
            this.$message.success(this.$t('tips.saveSuccess'))
          }).catch(() => {
            this.initFn()
          })
        })
      }).catch(() => {
        this.initFn()
      })
    },
    // savePurInvoice() {
    //   const save = (form) => {
    //     if (this.id) {
    //       // 编辑
    //       updatasettle(form).then((res) => {
    //         this.updateForm()
    //         this.$message.success(this.$t('tips.saveSuccess'))
    //         this.isUpdata = false
    //       })
    //     }
    //   }
    //   // 1、判断是否修改过： 采购是否分批付款 保证金计息 利息起算标准 计息方式
    //   // 2、收款及计息明细 存在值
    //   const needInterestDict = ['sIsBatch', 'sIsMarginIrt', 'sInterestStart', 'sFormulaType']
    //   let isNeedInterest = false
    //   this.$refs.basic.arrivalModify((form) => {
    //     const oldObj = JSON.parse(this.oldDetail)
    //     for (const key in form) {
    //       if (oldObj[key] !== form[key]) {
    //         console.log(key, 'old:', oldObj[key], 'new:', form[key])
    //         if (needInterestDict.includes(key)) {
    //           isNeedInterest = true
    //         }
    //       }
    //     }
    //     if (this.hasInterestList && isNeedInterest) {
    //       this.$confirm(this.$t('grid.others.modificationWillCleModifyKey'), this.$t('grid.others.prompt'), {
    //         confirmButtonText: this.$t('btns.confirm'),
    //         cancelButtonText: this.$t('btns.cancel'),
    //         type: 'warning'
    //       }).then(() => {
    //         save(form)
    //       }).catch(() => {})
    //     } else {
    //       save(form)
    //     }
    //   })
    // },
    isToday(str) {
      return new Date(str).toDateString() === new Date().toDateString()
    },
    async subPurInvoice() {
      let saveDone = true
      this.isPreAdjust = false
      this.isPreEsc = false
      if (this.activeName === '0' && this.oldDetail !== JSON.stringify(this.formDetail)) {
        saveDone = await this.savePurInvoice()
      }
      if (saveDone) {
        const settleDate = JSON.parse(this.oldDetail).sSettleDate
        if (!this.isToday(settleDate)) {
          this.$message.closeAll()
          this.$confirm('结算单日期不为当天' + '</br>' + this.$t('grid.others.whetherToContinueToSubmit'), this.$t('grid.others.prompt'), {
            dangerouslyUseHTMLString: true,
            confirmButtonText: this.$t('btns.confirm'),
            cancelButtonText: this.$t('btns.cancel'),
            type: 'warning'
          }).then(() => {
            this.submitCheckPurstock()
          })
        } else {
          this.submitCheckPurstock()
        }
      }
    },
    submitCheckPurstock() {
      submitCheckPurstock({ sInvoiceId: this.id }).then((res) => {
        if (res.code === '0000' && res.data && res.data.length > 0) {
          const errorMessages = res.data.map(e => e.dataMessage)
          const showErrorRecursively = (errors, index = 0) => {
            if (index < errors.length) {
              this.$confirm(errors[index] + '</br>' + this.$t('grid.others.whetherToContinueToSubmit'), this.$t('grid.others.prompt'), {
                dangerouslyUseHTMLString: true,
                confirmButtonText: this.$t('btns.confirm'),
                cancelButtonText: this.$t('btns.cancel'),
                type: 'error'
              }).then(() => {
                showErrorRecursively(errors, index + 1)
              }).catch(() => {
              })
            } else {
              this.submitCheck()
            }
          }
          showErrorRecursively(errorMessages)
        } else {
          this.submitCheck()
        }
      }).catch(() => {
      })
    },
    submitCheck() {
      submitDescPurstock({ sInvoiceId: this.id }).then((res) => {
        if (res.data) {
          this.$confirm(res.data + '</br>' + this.$t('grid.others.whetherToContinueToSubmit'), this.$t('grid.others.prompt'), {
            dangerouslyUseHTMLString: true,
            confirmButtonText: this.$t('btns.confirm'),
            cancelButtonText: this.$t('btns.cancel'),
            type: 'error'
          }).then(() => {
            this.submitCheckV2()
          })
        } else {
          this.submitCheckV2()
        }
      }).catch((err) => {
        console.log('err: ', err)
        // this.$message.closeAll()
        // this.visibleDialog.submitDialog = true
        // this.$confirm(err.message + '</br>' + this.$t('grid.others.whetherToContinueToSubmit'), this.$t('grid.others.prompt'), {
        //   dangerouslyUseHTMLString: true,
        //   confirmButtonText: this.$t('btns.confirm'),
        //   cancelButtonText: this.$t('btns.cancel'),
        //   type: 'error'
        // }).then(() => {
        //   this.visibleDialog.submitDialog = true
        //   this.submitDesc = err.message
        // })
      })
    },
    async submitCheckV2() {
      const { sSheetVersion, sSheetStatus, sNatureType } = this.formDetail
      if (sSheetVersion > 0 && !['00', '10', '70'].includes(sSheetStatus) && sNatureType === '20') { // 判断是否终结算提交
        this.costSplitDialogTpye = 'submit'
        this.submitCheckV3()
      } else {
        this.checkHouHai()
      }
    },
    submitCheckV3() {
      const { sEjqCustomerId, sOperaPlat, sExtend17 } = this.formDetail
      if (sOperaPlat === '10' && sExtend17 !== null && sExtend17 !== '0') { // 判断是否需要E建签弹窗
        this.accountId = sEjqCustomerId
        this.visibleDialog.check = true
        this.isPreAdjust = true
      } else {
        this.isPreAdjust = false
        this.updateExtend18()
      }
    },
    async updateExtend18() {
      const sExtend18 = await this.checkRelease()
      updateConSettlementInfo({ id: this.id, field: 'sExtend18' }, { sExtend18 }).then(async(res) => {
        await this.initFn()
        this.checkHouHai()
      })
    },
    async checkHouHai() {
      const res = await getSysParameter()
      const XMHHGT_CUSTOMER_CODE = res.data.find(i => i.code === 'XMHHGT_CUSTOMER_CODE')?.value || ''
      const isHouHai = XMHHGT_CUSTOMER_CODE.includes(this.formDetail.vCustomerCode)
      console.log('isHouHai: ', isHouHai)
      if (isHouHai && !this.formDetail.existGoods) {
        this.houHaiFormDialogItems[0].default = this.formDetail.sExtend13 ?? ''
        this.visibleDialog.houHaiDialog = true
      } else {
        this.visibleDialog.submitDialog = true
      }
    },
    houHaiDialogSuccess({ sExtend13 }) {
      const isNumber = /^-?\d+(\.\d{1,4})?$/.test(sExtend13 ?? '')
      if (!isNumber) {
        this.$message.error('请输入正确的数字，最多保留4位小数')
        return false
      }
      updateConSettlementInfo({ id: this.id, field: 'sExtend13' }, { sExtend13 }).then(async(res) => {
        this.visibleDialog.houHaiDialog = false
        await this.initFn()
        this.visibleDialog.submitDialog = true
      })
    },
    onSubmit(value) {
      submitPurstock({ sInvoiceId: this.id }, { param: value }).then((res) => {
        this.$message.success(this.$t('tips.submitSuccess'))
        this.visibleDialog.submitDialog = false
        this.initFn()
      })
    },
    revPurInvoice() {
      this.$confirm(this.$t('grid.tips.isItConfirmedThatThcelledKey'), this.$t('grid.others.prompt'), {
        confirmButtonText: this.$t('btns.confirm'),
        cancelButtonText: this.$t('btns.cancel'),
        type: 'warning'
      }).then(() => {
        revokePurstock({ id: this.id }).then((res) => {
          this.$message.success(this.$t('tips.withdrawalSuccessTag'))
          this.initFn()
        })
      })
    },
    overviewoverview() {
      this.$confirm(this.$t('grid.others.whetherToAutomaticanterestKey'), this.$t('grid.others.prompt'), {
        confirmButtonText: this.$t('btns.confirm'),
        cancelButtonText: this.$t('btns.cancel'),
        type: 'warning'
      }).then(() => {
        this.saleinfo = false
        settlePurInterestUpdatetop({
          sSettlementId: this.id,
          sSaleContractId: this.formDetail.sSaleContractId
        }).then(res => {
          this.$message.success(this.$t('grid.others.updateSuccessful'))
          this.initFn()
        })
        // settlebackup({ id: this.id }).then((res) => {
        //   this.$message.success('确认成功')
        //   this.initFn()
        // })
      })
    },
    settlebackupfun() {
      const _this = this
      console.log(_this.param.sCode)
      this.$confirm(this.$t('grid.tips.isVersionMaintenanceConfirmed'), this.$t('grid.others.prompt'), {
        confirmButtonText: this.$t('btns.confirm'),
        cancelButtonText: this.$t('btns.cancel'),
        type: 'warning'
      }).then(() => {
        settlebackup({ id: this.id }).then((res) => {
          this.$message.success(this.$t('grid.tips.generateVersionMainssfullyKey'))
          this.initFn()
          this.$router.push({
            path: `/settlementDetail/${res.data}`,
            query: {
              id: res.data,
              type: 'edit',
              name: `${this.$t('grid.others.statementTag')}【${_this.param.sCode}】`,
              sCode: _this.param.sCode,
              activeId: localStorage.getItem('menuId'),
              fromPath: window.location.pathname + window.location.search
            }
          })
        })
      })
    },
    settlesendESCfun() {
      this.$confirm(this.$t('grid.tips.isItAnElectronicContract'), this.$t('grid.others.prompt'), {
        confirmButtonText: this.$t('btns.confirm'),
        cancelButtonText: this.$t('btns.cancel'),
        type: 'warning'
      }).then(() => {
        settlesendESC(this.id).then((res) => {
          this.$message.success(this.$t('grid.others.issuedSuccessfully'))
          this.initFn()
        })
      })
    },
    settlerevokeToESCfun() {
      this.$confirm(this.$t('grid.tips.isItAnESignatureWithdrawal'), this.$t('grid.others.prompt'), {
        confirmButtonText: this.$t('btns.confirm'),
        cancelButtonText: this.$t('btns.cancel'),
        type: 'warning'
      }).then(() => {
        settlerevokeToESC({ id: this.id }).then((res) => {
          this.$message.success(this.$t('grid.others.withdrawalSuccess'))
          this.initFn()
        })
      })
    },
    checkSendEjq() {
      const { sOperaPlat, sEjqCustomerId } = this.formDetail
      if (sOperaPlat === '10') {
        if (sEjqCustomerId) {
          this.$confirm('是否发起E建签回签？', this.$t('grid.others.prompt'), {
            distinguishCancelAndClose: true,
            confirmButtonText: '发起',
            cancelButtonText: '不发起',
            type: 'warning'
          }).then(() => {
            this.accountId = sEjqCustomerId
            this.visibleDialog.check = true
          }).catch(action => {
            if (action === 'cancel') {
              this.visibleDialog.genAnnex = true
            }
          })
        } else {
          this.visibleDialog.genAnnex = true
        }
      } else {
        this.settletoFinalfunV2()
      }
    },
    checkRelease() {
      return new Promise((resolve, reject) => {
        this.$confirm('是否自动释放余款？', this.$t('grid.others.prompt'), {
          distinguishCancelAndClose: true,
          confirmButtonText: '释放',
          cancelButtonText: '不释放',
          type: 'warning'
        }).then(() => {
          resolve('1')
        }).catch(action => {
          if (action === 'cancel') {
            resolve('0')
          }
        })
      })
    },
    settletoFinalfun() {
      this.$confirm(this.$t('grid.tips.doesItConfirmTheGenccountKey'), this.$t('grid.others.prompt'), {
        confirmButtonText: this.$t('btns.confirm'),
        cancelButtonText: this.$t('btns.cancel'),
        type: 'warning'
      }).then(() => {
        this.doFinalCheckFn()
      })
    },
    // 采购成本拆分弹窗按钮
    async settletoCostSplit() {
      await this.initFn()
      this.visibleDialog.costSplitDialogVisible = true
    },
    checkCostSplit() {
      return this.formDetail.sCheckType === '01' && this.formDetail.sIsExistCostSplit === '1'
    },
    costSplitDialogSuccess() {
      this.visibleDialog.costSplitDialogVisible = false
      this.initFn()
      // if (this.costSplitDialogTpye === 'final') { // 生成终结算
      //   this.doFinalCheckFn()
      // } else if (this.costSplitDialogTpye === 'submit') { // 终结算调整单提交
      //   this.submitCheckV3()
      // }
    },
    doFinalCheckFn() {
      doFinalCheck({ id: this.id }).then((res) => {
        if (res.code === '0000' && res.data && res.data.length > 0) {
          const errorMessages = res.data.map(e => e.dataMessage)
          const showErrorRecursively = (errors, index = 0) => {
            if (index < errors.length) {
              this.$confirm(errors[index], this.$t('grid.others.prompt'), {
                dangerouslyUseHTMLString: true,
                confirmButtonText: this.$t('btns.confirm'),
                cancelButtonText: this.$t('btns.cancel'),
                type: 'error'
              }).then(() => {
                showErrorRecursively(errors, index + 1)
              }).catch(() => {
              })
            } else {
              this.checkSendEjq()
            }
          }
          showErrorRecursively(errorMessages)
        } else {
          this.checkSendEjq()
        }
      }).catch(() => {
      })
    },
    async settletoFinalfunV2(payload) {
      const sIsCreateEjq = payload ? payload.ejqCustomerId : '0'
      if (this.isPreAdjust) {
        updateConSettlementInfo({ id: this.id, field: 'sExtend17' }, { sExtend17: sIsCreateEjq }).then((res) => {
          this.visibleDialog.check = false
          this.isPreAdjust = false
          this.updateExtend18()
        })
      } else if (this.isPreEsc) {
        this.sendPreESCfn(sIsCreateEjq)
      } else {
        const sIsAutoRelease = await this.checkRelease()
        settletoFinalV2({ id: this.id, sIsCreateEjq, sIsAutoRelease }).then((res) => {
          if (res.code === '0000') {
            this.visibleDialog.check = false
            this.$message.success(this.$t('grid.others.successfulOperation'))
            this.initFn()
          }
        })
      }
    },
    genAnnexClose() {
      this.visibleDialog.genAnnex = false
      this.settletoFinalfunV2()
    },
    checkDialogClose() {
      this.visibleDialog.check = false
      this.isPreAdjust = false
      this.isPreEsc = false
    },
    exportExcel() {
      exportAll(`/esc/pay/settle/detail/export/excel/${this.id}`).then(res => {
        try {
          const enc = new TextDecoder('utf-8')
          const jsonString = enc.decode(new Uint8Array(res))
          res = JSON.parse(jsonString)
          this.$message.error(res.message)
        } catch (err) {
          console.log('err: ', err)
          const link = document.createElement('a')
          const blob = new Blob([res], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
          link.style.display = 'none'
          link.href = URL.createObjectURL(blob)
          link.download = this.formDetail.sSaleContractCode + this.$t('grid.others.settlementList') + '.xlsx'
          document.body.appendChild(link)
          link.click()
        }
      })
    },
    printPreviewfun() {
      const winTitle = this.formDetail.sSaleContractCode + this.$t('grid.others.settlementList')
      settleDetaileExport({ id: this.id }).then((res) => {
        const blob = new Blob([res], {
          type: 'application/pdf;chartset=UTF-8'
        })
        const fileURL = URL.createObjectURL(blob)
        var win = window.open(fileURL)
        setTimeout(() => {
          win.document.title = winTitle
        }, 1000)
      })
    },
    // getObjectURL(file) {
    //   const pdfUrl = this.getObjectURL(res)
    //   var win = window.open(pdfUrl)
    //   setTimeout(() => {
    //     win.document.title = this.param.name
    //   }, 500)
    //   let url = null
    //   if (window.createObjectURL !== undefined) { // basic
    //     url = window.createObjectURL(file)
    //   } else if (window.webkitURL !== undefined) { // webkit or chrome
    //     try {
    //       url = window.webkitURL.createObjectURL(file)
    //     } catch (error) {
    //       console.log(error)
    //     }
    //   } else if (window.URL !== undefined) { // mozilla(firefox)
    //     try {
    //       url = window.URL.createObjectURL(file)
    //     } catch (error) {
    //       console.log(error)
    //     }
    //   }
    //   return url
    // },
    settletoPrefun() {
      const _this = this
      this.$confirm(this.$t('grid.tips.generateSettlementAdjustment'), this.$t('grid.others.prompt'), {
        confirmButtonText: this.$t('btns.confirm'),
        cancelButtonText: this.$t('btns.cancel'),
        type: 'warning'
      }).then(() => {
        settletoPre({ id: this.id }).then((res) => {
          this.$message.success(this.$t('grid.others.successfulOperation'))
          this.initFn()
          this.$router.push({
            path: `/settlementDetail/${res.data}`,
            query: {
              id: res.data,
              type: 'edit',
              name: `${this.$t('grid.others.statementTag')}【${_this.param.sCode}】`,
              sCode: _this.param.sCode,
              activeId: localStorage.getItem('menuId'),
              fromPath: window.location.pathname + window.location.search
            }
          })
        })
      })
    },

    delPurInvoice() {
      this.$confirm(this.$t('grid.tips.isTheDeletionConfirmed'), this.$t('grid.others.prompt'), {
        confirmButtonText: this.$t('btns.confirm'),
        cancelButtonText: this.$t('btns.cancel'),
        type: 'warning'
      }).then(() => {
        removePurstock({ id: this.id }).then((res) => {
          this.$message.success(this.$t('tips.deletedSuccessfully'))
          this.$tabDelete()
        })
      })
    },
    checkEjqCustomer() {
      return new Promise((resolve, reject) => {
        checkIsEjqCustomer({ id: this.id }).then(res => {
          resolve(res.data)
        }).catch(() => {
          reject()
        })
      })
    },
    onSendPreESC() {
      this.isPreEsc = false
      this.$confirm('是否预结算发起电子签约?', this.$t('grid.others.prompt'), {
        confirmButtonText: this.$t('btns.confirm'),
        cancelButtonText: this.$t('btns.cancel'),
        type: 'warning'
      }).then(async() => {
        const isEjq = await this.checkEjqCustomer()
        if (isEjq) {
          const { sOperaPlat, sEjqCustomerId } = this.formDetail
          if (sOperaPlat === '10') {
            this.accountId = sEjqCustomerId
            this.visibleDialog.check = true
            this.isPreEsc = true
          } else {
            this.isPreEsc = false
            this.sendPreESCfn()
          }
        } else {
          this.$message.error('非E建签客商无法发起！')
        }
      })
    },
    sendPreESCfn(v) {
      sendPreESC({ id: this.id }).then((res) => {
        this.$message.success(this.$t('grid.others.issuedSuccessfully'))
        this.visibleDialog.check = false
        if (this.isPreEsc) {
          updateConSettlementInfo({ id: this.id, field: 'sExtend19' }, { sExtend19: v }).then((res) => {
            this.initFn()
          })
        } else {
          this.initFn()
        }
      })
    },
    onRevokePreESC() {
      this.$confirm('是否预结算撤回?', this.$t('grid.others.prompt'), {
        confirmButtonText: this.$t('btns.confirm'),
        cancelButtonText: this.$t('btns.cancel'),
        type: 'warning'
      }).then(() => {
        revokePreESC({ id: this.id }).then((res) => {
          this.$message.success(this.$t('grid.others.withdrawalSuccess'))
          this.initFn()
        })
      })
    },
    onRefund() {
      queryReleaseInfoDialog({ id: this.id }).then((res) => {
        this.RefundFormDialogItems[0].default = res.data.vLeftAmt || 0
        this.RefundFormDialogItems[1].default = res.data.vCurReleaseAmt || 0
        this.visibleDialog.RefundDialog = true
      })
    },
    RefundDialogSuccess(data) {
      if (+data.vCurReleaseAmt <= 0) {
        this.$message.error('本次释放金额需大于0')
        return
      }
      if (data.vCurReleaseAmt > data.vLeftAmt) {
        this.$message.error('本次释放金额不能大于待释放金额')
        return
      }
      doReleasesPayInteraction({ id: this.id }, data).then((res) => {
        this.$message.success(this.$t('tips.operationSuccessful'))
        this.visibleDialog.RefundDialog = false
        this.initFn()
      })
    },
    handleCloseApproval() {
      this.visibleDialog.approvaldia = false
    },
    createTrialSettle() {
      isExistTrialSettle(this.id).then((res) => {
        if (res.code === '0000') {
          if (res.data !== null) {
            this.$router.push({
              path: `/settlementDetail/${res.data.sId}`,
              query: {
                id: res.data.sId,
                type: 'edit',
                name: `试算单【${res.data.sCode}】`,
                sCode: res.data.sCode,
                activeId: localStorage.getItem('menuId'),
                fromPath: window.location.pathname + window.location.search
              }
            })
          } else {
            this.visibleDialog.callinV2 = true
          }
        }
      })
    },
    onSureCallInfo(data) {
      createTrialSettle({
        sSettlementId: this.id,
        sSaleContractId: this.formDetail.sSaleContractId
      }, data).then((res) => {
        this.$message.success(this.$t('grid.others.successfulOperation'))
        this.initFn()
        this.$router.push({
          path: `/settlementDetail/${res.data.sId}`,
          query: {
            id: res.data.sId,
            type: 'edit',
            name: `试算单【${res.data.sCode}】`,
            sCode: res.data.sCode,
            activeId: localStorage.getItem('menuId'),
            fromPath: window.location.pathname + window.location.search
          }
        })
      })
    },
    settleDiffQuery() {
      settleDiff({
        sSettlementId: this.id,
        sSaleContractId: this.formDetail.sSaleContractId
      }).then((res) => {
        this.formDialogItems.forEach(item => {
          if (item.value === 'recAmt') {
            item.default = res.data.recAmt
          } else if (item.value === 'totalRecAmt') {
            item.default = res.data.totalRecAmt
          } else if (item.value === 'diffAmt') {
            item.default = res.data.diffAmt
          }
        })
        this.visibleDialog.diffQuery = true
      })
    },
    otherExpense() {
      syncOriSettleData({
        sSettlementId: this.id,
        sSaleContractId: this.formDetail.sSaleContractId
      }).then((res) => {
        if (res.code === '0000') {
          this.$refs.settlementInfo && this.$refs.settlementInfo.updataFunAll()
          this.$refs.overview && this.$refs.overview.queryForm()
          this.$refs.basic && this.initFn()
          this.$message.success(this.$t('tips.operationSuccessful'))
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
//固定tab页标签
.detail-tabs {
  .el-tabs__card {
    max-height: calc(100vh - 148px);
  }

  .el-tab-pane {
    max-height: calc(100vh - 148px);
    overflow-y: auto;
    overflow-x: hidden;
  }
}
</style>
