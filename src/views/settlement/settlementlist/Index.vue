<!--
 * @Author: 沈鹭荣
 * @Date: 2021-03-16 19:49:13
 * @LastEditors: 沈鹭荣
 * @LastEditTime: 2021-06-07 16:31:28
 * @Description:
-->
<template>
  <div class="page-container">
    <p class="page-title">{{ $t('grid.others.statementManagement') }}</p>
    <div class="layout-content auto-page-title flexV">
      <steel-search-form ref="searchForm" :form-items="formItems" @search="onSearch" />
      <div class="btn-group mt-10">
        <div class="text">
          {{ $t('grid.others.statementTag') }}{{ $t('grid.others.list') }}
        </div>
        <div>
          <el-button
            v-has:esc_con_settlement_list_auto_create
            type="primary"
            size="mini"
            :disabled="genDisabled"
            @click="settletoFinalfun"
          >{{ $t('grid.others.generateFinalSettlement') }}</el-button>
          <el-button
            v-has:esc_con_settlement_list_revokePreESC
            type="primary"
            size="mini"
            :disabled="isSendPreESC"
            @click="onSendPreESC"
          >
            预结算发起电子签约
          </el-button>
          <el-button
            key="comm_puragreement_list_add"
            v-has:esc_con_settlement_list_add
            type="primary"
            size="mini"
            @click="dialogVisibleadd=true"
          >{{ $t('btns.add') }}</el-button>
          <el-button
            key="comm_puragreement_list_add"
            v-has:esc_con_settlement_list_delete
            type="danger"
            size="mini"
            :disabled="disRemove"
            @click="agrRemovesfun"
          >{{ $t('btns.delete') }}</el-button>
          <el-button
            key="comm_puragreement_list_add"
            v-has:esc_con_settlement_list_ejx
            type="primary"
            size="mini"
            :disabled="escDisabled"
            @click="sendESCfun"
          >{{ $t('grid.btns.initiateESignature') }}</el-button>
          <export-btn
            v-has:esc_con_settlement_list_export
            class="ml-10"
            :file-name="$t('grid.others.statementManagement')"
            api-url="/esc/pay/settle/export"
            :post-params="searchInfo"
            :export-config="exportConfig"
            :get-grid-api="getGridApi"
          />
        </div>
      </div>
      <!-- :header-total="headerCount"
        :footer-total="footerCount" -->
      <!-- 结算单管理 -->
      <steelTradeAggrid
        ref="aggrid"
        :column-defs="columnDefs"
        :row-data="rowData"
        :load-data="loadData"
        :auto-load-data="false"
        table-selection="multiple"
        row-key="sId"
        @selectedChange="handleFooterCount"
        @rowDoubleClicked="onRowDoubleClicked"
      />
    </div>
    <add-plan
      v-if="dialogVisibleadd"
      :visible="dialogVisibleadd"
      @onclose="onCloseAdd"
      @onSuccess="onSuccess"
    />
    <checkDialog
      :visible="checkDialogVisible"
      :account-id="accountId"
      @close="checkDialogVisible = false"
      @success="settletoFinalfunV2"
    />
    <steel-annex-dialog
      :visible="visibleDialog.genAnnex"
      append-to-body
      :biz-id="selectId"
      :disabled-btn="{ scan: false, del: false }"
      :upload-option="{ sExtend1:'JSDFJ-XX' }"
      @onSelect="genAnnexClose"
    />
    <CostSplitDialog
      v-if="visibleDialog.costSplitDialogVisible"
      :dialog-visible="visibleDialog.costSplitDialogVisible"
      :info="selectedData[0]"
      qty-key="vContractQty"
      @close="visibleDialog.costSplitDialogVisible = false"
      @success="costSplitDialogSuccess"
    />
  </div>
</template>

<script>
import addPlan from './addPlan.vue'
import { SteelFormat, MessageUtil } from 'cnd-horizon-utils'
import { Moment } from 'cnd-utils'
var Decimal = window.Decimal
import {
  getContractSubmitControl
} from '@/api/contract'
import {
  getDictet, getCnDitc
} from '@/api/logistics/saleDelivery/saleorder'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import exportBtn from '@/components/exportBtnV2'
import { handleDict } from '@/utils/common'
import { statusDict4 } from '@/utils/dict'
import checkDialog from '@/components/checkEJQDialog/index'
import CostSplitDialog from './CostSplitDialog'
import {
  settdeliveryget
} from '@/api/settlement'
import {
  settlepage,
  agrRemoves,
  settlesendESC,
  // settletoFinal,
  sendPreESC,
  doFinalCheck,
  settletoFinalV2,
  checkIsEjqCustomer,
  updateConSettlementInfo
} from '@/api/settlement'
export default {
  name: 'Settlementlist',
  components: { steelTradeAggrid, addPlan, exportBtn, checkDialog, CostSplitDialog },
  data() {
    return {
      searchInfo: null,
      disRemove: true,
      escDisabled: true,
      genDisabled: true,
      isSendPreESC: true,
      visibleDialog: {
        genAnnex: false,
        costSplitDialogVisible: false
      },
      selectId: null,
      options: {
        'dev.common.sheet.status': '',
        'customer.sign.status': '',
        'esc.settle.nature.type': '',
        'base.yes-no': '',
        'opera.plat.value': '',
        'contract.sale.check.type': ''
      },
      headerCount: null,
      footerCount: null,
      formItems: [
        {
          label: this.$t('grid.others.statementNumber'),
          value: 'sCode',
          type: 'elInput',
          placeholder: this.$t('grid.others.enterBillingStatementNumber')
        },
        {
          label: this.$t('grid.others.itemNumberTag'),
          value: 'sProjectCode',
          type: 'elInput',
          placeholder: this.$t('grid.others.pleaseEnterTheItemNumberTag')
        },
        {
          label: this.$t('grid.title.salesContractNumber'),
          value: 'sSaleContractCode',
          type: 'elInput',
          placeholder: this.$t('grid.others.pleaseEnterTheSalesContractNumber')
        },
        {
          label: this.$t('grid.others.supplier'), // 供应商,
          value: 'sSupplierId',
          type: 'cndInputDialogItem',
          dialogType: 'customer',
          defaultUrl: '/esc/customer/page',
          option: { valueKey: 'sPath' },
          placeholder: this.$t('grid.others.pleaseSelectSupplier')
        },
        {
          label: this.$t('grid.others.customer'),
          value: 'sCustomerId',
          type: 'cndInputDialogItem',
          dialogType: 'customer',
          defaultUrl: '/esc/customer/page',
          option: { valueKey: 'sPath' },
          placeholder: this.$t('grid.others.pleaseSelectTheCustomer')
        },
        {
          label: this.$t('grid.title.company'),
          value: 'sCompanyId',
          type: 'cndInputDialog',
          dialogType: 'company',
          placeholder: this.$t('grid.others.pleaseSelectCompany')
        },
        {
          label: this.$t('grid.title.accountingGroup'),
          value: 'sCheckGroupId',
          type: 'cndInputDialog',
          dialogType: 'cost',
          placeholder: this.$t('grid.others.pleaseSelectAccountingGroup')
        },
        {
          label: this.$t('grid.others.department'),
          value: 'sDepartmentId',
          type: 'cndInputDialogItem',
          dialogType: 'depart',
          multiple: true,
          reserveKeyword: false,
          placeholder: this.$t('grid.others.pleaseSelectTheDepartment')
        },
        {
          label: this.$t('grid.title.personnel'),
          value: 'sStaffId',
          type: 'cndInputDialogItem',
          option: { valueKey: 'sPath' },
          dialogType: 'staff',
          itemType: 'occultation',
          placeholder: this.$t('grid.others.pleaseSelectPersonnel')
        },
        {
          label: this.$t('grid.others.statementNature'),
          value: 'sNatureType',
          type: 'elSelect',
          itemType: 'occultation',
          dict: 'esc.settle.nature.type',
          placeholder: this.$t('components.pleaseSelect')
        },
        {
          label: this.$t('grid.others.contractingStatus'),
          value: 'sSignStatus',
          type: 'elSelect',
          itemType: 'occultation',
          dict: 'customer.sign.status',
          placeholder: this.$t('grid.others.pleaseSelectStatus')
        },
        {
          label: this.$t('grid.title.createdBy'),
          value: 'sCreator',
          type: 'cndInputDialog',
          itemType: 'occultation',
          placeholder: this.$t('grid.others.pleaseSelectTheCreator'),
          dialogType: 'applicant'
        },
        {
          label: this.$t('grid.title.status'),
          value: 'sSheetStatus',
          type: 'elSelect',
          itemType: 'occultation',
          dict: statusDict4,
          placeholder: this.$t('grid.others.pleaseSelectStatus')
        },
        {
          label: this.$t('grid.title.createdAt'),
          value: ['sCreateTime', 'vCreateTimeTo'],
          placeholder: [this.$t('grid.others.pleaseSelectTheDate'), this.$t('grid.others.pleaseSelectTheDate')],
          itemType: 'occultation',
          unlinkPanels: true,
          type: 'elDatePicker'
        },
        {
          label: '生效时间',
          value: ['sRatifyDate', 'vRatifyDateTo'],
          placeholder: [this.$t('grid.others.pleaseSelectTheDate'), this.$t('grid.others.pleaseSelectTheDate')],
          default: ['', ''],
          itemType: 'occultation',
          unlinkPanels: true,
          type: 'elDatePicker'
        },
        {
          label: this.$t('grid.others.settlementDate'),
          value: ['sSettleDate', 'vSettleDateTo'],
          placeholder: [this.$t('grid.others.pleaseSelectTheDate'), this.$t('grid.others.pleaseSelectTheDate')],
          default: ['', ''],
          itemType: 'occultation',
          unlinkPanels: true,
          type: 'elDatePicker'
        },
        {
          label: '经营单位',
          value: 'sManagementId',
          type: 'cndInputDialog',
          dialogType: 'escOrg',
          itemType: 'occultation'
        },
        {
          label: '作业平台',
          value: 'sOperaPlat',
          type: 'elSelect',
          dict: 'opera.plat.value',
          placeholder: this.$t('components.pleaseSelect')
        },
        {
          label: '余款释放',
          value: 'sIsRelease',
          type: 'elSelect',
          dict: 'base.yes-no'
        },
        {
          label: '核算方式',
          value: 'sExtend15',
          type: 'elSelect',
          dict: 'contract.sale.check.type'
        }
      ],
      columnDefs: [
        {
          headerName: this.$t('grid.others.statementNumber'),
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              pinned: 'left',
              field: 'sCode',
              width: 128,
              headerClass: 'c-header_child'
            }
          ]
        },
        {
          headerName: this.$t('grid.others.itemNumberTag'),
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              pinned: 'left',
              field: 'sProjectCode',
              width: 108,
              headerClass: 'c-header_child'
            }
          ]
        },
        {
          headerName: this.$t('grid.title.salesContractNumber'),
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'sSaleContractCode',
              pinned: 'left',
              headerClass: 'c-header_child'
            }
          ]
        },
        {
          headerName: this.$t('grid.others.customer'),
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              pinned: 'left',
              field: 'vCustomerName',
              headerClass: 'c-header_child'
            }
          ]
        },
        {
          headerName: this.$t('grid.title.versionNumber'),
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'sSheetVersion',
              width: 64,
              headerClass: 'c-header_child',
              valueFormatter: params => {
                if (params.data._hiddenCheckbox) {
                  return ''
                } else {
                  return SteelFormat.formatThousandthSign(params.value)
                }
              }
            }
          ]
        },
        {
          headerName: this.$t('grid.title.status'),
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'sSheetStatus',
              headerClass: 'c-header_child',
              valueGetter: (params) => {
                // vSumQty/vSumPkgQty
                return getCnDitc(params, this.options['dev.common.sheet.status'], 'sSheetStatus')
              }
            }
          ]
        },
        {
          headerName: this.$t('grid.others.statementNature'),
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              headerClass: 'c-header_child',
              width: 84,
              field: 'sNatureType',
              valueGetter: (params) => {
                const status = this.options['esc.settle.nature.type'].filter(item => item.sCodeValue === params.data.sNatureType)
                return params.data._hiddenCheckbox ? '' : status.length ? status[0].sCodeName : '未结算'
              }
            }
          ]
        },
        {
          headerName: this.$t('grid.others.settlementDate'),
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              headerClass: 'c-header_child',
              field: 'sSettleDate',
              valueFormatter(params) {
                return Moment.time('YYYY-MM-DD', params.data.sSettleDate)
              }
            }
          ]
        },
        {
          headerName: this.$t('grid.others.contractingStatus'),
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              headerClass: 'c-header_child',
              field: 'sSignStatus',
              width: 84,
              valueGetter: (params) => {
                return getCnDitc(params, this.options['customer.sign.status'], 'sSignStatus')
              }
            }
          ]
        },
        {
          headerName: '返利返息',
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              headerClass: 'c-header_child',
              field: 'sRebateIsReturn',
              width: 74,
              valueGetter: (params) => {
                return getCnDitc(params, this.options['base.yes-no'], 'sRebateIsReturn')
              }
            }
          ]
        },
        {
          headerName: '余款释放',
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              headerClass: 'c-header_child',
              field: 'sIsRelease',
              width: 110,
              valueGetter: (params) => {
                return getCnDitc(params, this.options['base.yes-no'], 'sIsRelease')
              }
            }
          ]
        },
        {
          headerName: this.$t('grid.others.settlementQuantity'),
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'vContractQty',
              headerClass: 'c-header_child',
              cellStyle: { textAlign: 'right' },
              valueFormatter: (params) => {
                return SteelFormat.formatThousandthSign(params.value, 4)
              }
            }
          ]
        },
        {
          // headerName: this.$t('grid.others.amountReceivable'),
          headerName: '应收金额',
          children: [
            {
              field: 'vCfmAmt',
              headerName: this.$t('grid.others.purchaseValue'),
              cellStyle: { textAlign: 'right' },
              valueFormatter: params => {
                return SteelFormat.formatPrice(params.value)
              }
            },
            {
              field: 'vCfmAddAmt',
              headerName: this.$t('grid.others.addPrice'),
              cellStyle: { textAlign: 'right' },
              valueFormatter: params => {
                return SteelFormat.formatPrice(params.value)
              }
            },
            {
              field: 'vCfmIrtAmt',
              headerName: this.$t('grid.others.interestRate'),
              cellStyle: { textAlign: 'right' },
              valueFormatter: params => {
                return SteelFormat.formatPrice(params.value)
              }
            },
            {
              field: 'vCfmBreachAmt',
              headerName: this.$t('grid.others.defaultAmount'),
              cellStyle: { textAlign: 'right' },
              valueFormatter: params => {
                return SteelFormat.formatPrice(params.value)
              }
            },
            {
              field: 'vCfmTicketAmt',
              headerName: this.$t('grid.others.oneTicketLogisticsCosts'),
              width: '128',
              cellStyle: { textAlign: 'right' },
              valueFormatter: params => {
                return SteelFormat.formatPrice(params.value)
              }
            },
            {
              field: 'vCfmAgentAmt',
              width: '138',
              headerName: this.$t('grid.others.collectOnBehalfOfTheLogisticsCosts'),
              cellStyle: { textAlign: 'right' },
              valueFormatter: params => {
                return SteelFormat.formatPrice(params.value)
              }
            },
            {
              field: 'vDiffTaxAmt',
              width: '128',
              headerName: this.$t('grid.others.logisticsCostTaxDifference'),
              cellStyle: { textAlign: 'right' },
              valueFormatter: params => {
                return SteelFormat.formatPrice(params.value)
              }
            },
            {
              field: 'vCfmInsAmt',
              headerName: this.$t('grid.others.premiumTag'),
              cellStyle: { textAlign: 'right' },
              valueFormatter: params => {
                return SteelFormat.formatPrice(params.value)
              }
            },
            {
              field: 'vCfmAcceptAmt',
              headerName: this.$t('grid.others.discountRate'),
              cellStyle: { textAlign: 'right' },
              valueFormatter: params => {
                return SteelFormat.formatPrice(params.value)
              }
            },
            {
              field: 'vCfmReturnAmt',
              headerName: this.$t('grid.others.rebate'),
              cellStyle: { textAlign: 'right' },
              valueFormatter: params => {
                return SteelFormat.formatPrice(params.value)
              }
            },
            {
              field: 'vCfmDerateAmt',
              headerName: this.$t('grid.others.exemptions'),
              cellStyle: { textAlign: 'right' },
              valueFormatter: params => {
                return SteelFormat.formatPrice(params.value)
              }
            },
            {
              field: 'vCfmOtherAmt',
              headerName: this.$t('grid.others.others'),
              cellStyle: { textAlign: 'right' },
              valueFormatter: params => {
                return SteelFormat.formatPrice(params.value)
              }
            },
            {
              field: 'vCfmRebate',
              headerName: this.$t('grid.others.rebates'),
              cellStyle: { textAlign: 'right' },
              valueFormatter: params => {
                return SteelFormat.formatPrice(params.value)
              }
            },
            {
              field: 'overview280',
              headerName: '点价/含权盈亏金额',
              width: '142',
              cellStyle: { textAlign: 'right' },
              valueFormatter: params => {
                return SteelFormat.formatPrice(params.value)
              }
            }
          ]
        },
        {
          headerName: '最长逾期天数',
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'sMaxBreachDay',
              width: '106px',
              cellStyle: { textAlign: 'right' },
              headerClass: 'c-header_child'
            }
          ]
        },
        {
          headerName: '实际货值',
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'overview250',
              width: '106px',
              cellStyle: { textAlign: 'right' },
              headerClass: 'c-header_child',
              valueFormatter: (params) => {
                return SteelFormat.formatPrice(params.value)
              }
            }
          ]
        },
        {
          headerName: '实际运费',
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'overview260',
              width: '106px',
              cellStyle: { textAlign: 'right' },
              headerClass: 'c-header_child',
              valueFormatter: (params) => {
                return SteelFormat.formatPrice(params.value)
              }
            }
          ]
        },
        {
          headerName: this.$t('grid.title.settlementIncrement'),
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'vAddAmt',
              width: '106px',
              cellStyle: { textAlign: 'right' },
              headerClass: 'c-header_child',
              valueFormatter: (params) => {
                return SteelFormat.formatPrice(params.value)
              }
            }
          ]
        },
        {
          headerName: this.$t('grid.title.settlementProfit'),
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'vProAmt',
              width: '106px',
              cellStyle: { textAlign: 'right' },
              headerClass: 'c-header_child',
              valueFormatter: (params) => {
                return SteelFormat.formatPrice(params.value)
              }
            }
          ]
        },
        {
          headerName: this.$t('grid.title.profitPerTonSettlement'),
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              width: '106px',
              field: 'vOneProAmt',
              cellStyle: { textAlign: 'right' },
              headerClass: 'c-header_child',
              valueFormatter: (params) => {
                return SteelFormat.formatPrice(params.value)
              }
            }
          ]
        },
        {
          headerName: this.$t('grid.others.amountReceivableNd'),
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'vCfmRecAmt',
              cellStyle: { textAlign: 'right' },
              headerClass: 'c-header_child',
              valueFormatter: (params) => {
                return SteelFormat.formatPrice(params.value)
              }
            }
          ]
        },
        {
          headerName: this.$t('grid.others.amountReceivableSt'),
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'vCfmRealAmt',
              cellStyle: { textAlign: 'right' },
              headerClass: 'c-header_child',
              valueFormatter: (params) => {
                // vTaxAmt/vNetAmt
                return SteelFormat.formatPrice(params.value)
              }
            }
          ]
        },
        {
          headerName: '还需来款',
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'overview190',
              width: '106px',
              cellStyle: { textAlign: 'right' },
              headerClass: 'c-header_child',
              valueFormatter: (params) => {
                return SteelFormat.formatPrice(params.value)
              }
            }
          ]
        },
        {
          headerName: this.$t('grid.others.amountInvoicedPayable'),
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'vCfmInvAmt',
              cellStyle: { textAlign: 'right' },
              headerClass: 'c-header_child',
              valueFormatter: (params) => {
                // vTaxAmt/vNetAmt
                return SteelFormat.formatPrice(params.value)
              }
            }
          ]
        },
        {
          headerName: this.$t('grid.others.salesInvoicing'),
          children: [
            {
              field: 'vInvQty',
              headerName: this.$t('grid.others.invoiceQuantity'),
              cellStyle: { textAlign: 'right' },
              valueFormatter: params => {
                return SteelFormat.formatThousandthSign(params.value, 4)
              }
            },
            {
              field: 'vInvAmt',
              headerName: this.$t('grid.title.amountWithTax'),
              cellStyle: { textAlign: 'right' },
              valueFormatter: params => {
                return SteelFormat.formatPrice(params.value)
              }
            },
            {
              field: 'vInvDate',
              headerName: this.$t('grid.others.invoiceDateTag'),
              cellStyle: { textAlign: 'right' },
              valueFormatter(params) {
                return params.value
              }
            },
            {
              field: 'vLeftInvQty',
              headerName: '待开票数量',
              cellStyle: { textAlign: 'right' },
              valueFormatter: params => {
                return SteelFormat.formatThousandthSign(params.value, 4)
              }
            },
            {
              field: 'vLeftInvAmt',
              headerName: '待开票金额',
              cellStyle: { textAlign: 'right' },
              valueFormatter: params => {
                return SteelFormat.formatPrice(params.value)
              }
            }
          ]
        },
        {
          headerName: this.$t('grid.others.supplier'), // 供应商,
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'vSupplierName',
              headerClass: 'c-header_child'
            }
          ]
        },
        {
          headerName: this.$t('grid.others.department'),
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'vDepartmentName',
              headerClass: 'c-header_child'
            }
          ]
        },
        {
          headerName: this.$t('grid.title.accountingGroup'),
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'vCheckGroupName',
              headerClass: 'c-header_child'
            }
          ]
        },
        {
          headerName: this.$t('grid.title.personnel'),
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'vStaffName',
              headerClass: 'c-header_child'
            }
          ]
        },
        {
          headerName: this.$t('grid.title.company'),
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'vCompanyName',
              headerClass: 'c-header_child'
            }
          ]
        },
        {
          headerName: '作业平台',
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'sOperaPlat',
              headerClass: 'c-header_child',
              minWidth: 84,
              valueGetter: (params) => {
                return getCnDitc(params, this.options['opera.plat.value'], 'sOperaPlat')
              }
            }
          ]
        },
        {
          headerName: this.$t('grid.title.createdBy'),
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'vCreatorName',
              headerClass: 'c-header_child'
            }
          ]
        },
        {
          headerName: this.$t('grid.title.createdAt'),
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'sCreateTime',
              cellStyle: { textAlign: 'right' },
              headerClass: 'c-header_child',
              minWidth: 150,
              valueFormatter(params) {
                return Moment.time('YYYY-MM-DD HH:mm:ss', params.data.sCreateTime)
              }
            }
          ]
        },
        {
          headerName: '生效时间',
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'sRatifyDate',
              cellStyle: { textAlign: 'right' },
              headerClass: 'c-header_child',
              minWidth: 150,
              valueFormatter(params) {
                return Moment.time('YYYY-MM-DD HH:mm:ss', params.data.sRatifyDate)
              }
            }
          ]
        },
        {
          headerName: '经营单位',
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'vManagementName',
              headerClass: 'c-header_child'
            }
          ]
        },
        {
          headerName: '核算方式',
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'sExtend15',
              headerClass: 'c-header_child',
              minWidth: 84,
              valueGetter: (params) => {
                return getCnDitc(params, this.options['contract.sale.check.type'], 'sExtend15')
              }
            }
          ]
        }
      ],
      rowData: [],
      dialogVisibleadd: false,
      checkDialogVisible: false,
      accountId: false,
      selectedData: null,
      exportConfig: [
        { label: this.$t('grid.others.statementNumber'), value: 'sCode' },
        { label: this.$t('grid.others.itemNumberTag'), value: 'sProjectCode' },
        { label: this.$t('grid.title.salesContractNumber'), value: 'sSaleContractCode' },
        { label: this.$t('grid.others.customer'), value: 'vCustomerName' },
        { label: this.$t('grid.title.versionNumber'), value: 'sSheetVersion' },
        { label: this.$t('grid.title.status'), value: 'sSheetStatus',
          setValue: (value) => {
            return handleDict(value, this.options['dev.common.sheet.status'])
          }
        },
        { label: this.$t('grid.others.statementNature'), value: 'sNatureType',
          setValue: (value) => {
            return handleDict(value, this.options['esc.settle.nature.type'])
          }
        },
        { label: this.$t('grid.others.settlementDate'), value: 'sSettleDate',
          setValue: (value) => {
            return Moment.time('YYYY-MM-DD', value)
          }
        },
        { label: this.$t('grid.others.contractingStatus'), value: 'sSignStatus',
          setValue: (value) => {
            return handleDict(value, this.options['customer.sign.status'])
          }
        },
        { label: '返利返息', value: 'sRebateIsReturn',
          setValue: (value) => {
            return handleDict(value, this.options['base.yes-no'])
          }
        },
        { label: '余款释放', value: 'sIsRelease',
          setValue: (value) => {
            return handleDict(value, this.options['base.yes-no'])
          }
        },
        { label: this.$t('grid.others.settlementQuantity'), value: 'vContractQty',
          setValue: (value) => {
            return Number((+value).toFixed(4))
          }
        },
        { label: this.$t('grid.others.amountReceivablePurchaseValue'), value: 'vCfmAmt',
          setValue: value => { return Number((+value).toFixed(2)) }
        },
        { label: this.$t('grid.others.amountReceivableMarkup'), value: 'vCfmAddAmt',
          setValue: value => { return Number((+value).toFixed(2)) }
        },
        { label: this.$t('grid.others.amountReceivableInterestCharges'), value: 'vCfmIrtAmt',
          setValue: value => { return Number((+value).toFixed(2)) }
        },
        { label: this.$t('grid.others.amountReceivableForBreachOfContract'), value: 'vCfmBreachAmt',
          setValue: value => { return Number((+value).toFixed(2)) }
        },
        { label: this.$t('grid.others.amountReceivableOnecsCostsKey'), value: 'vCfmTicketAmt',
          setValue: value => { return Number((+value).toFixed(2)) }
        },
        { label: this.$t('grid.others.amountReceivableColcsCostsKey'), value: 'vCfmAgentAmt',
          setValue: value => { return Number((+value).toFixed(2)) }
        },
        { label: this.$t('grid.others.theAmountReceivableferenceKey'), value: 'vDiffTaxAmt',
          setValue: value => { return Number((+value).toFixed(2)) }
        },
        { label: this.$t('grid.others.amountReceivableRebate'), value: 'vCfmRebate',
          setValue: value => { return Number((+value).toFixed(2)) }
        },
        { label: '点价/含权盈亏金额', value: 'overview280',
          setValue: value => { return Number((+value).toFixed(2)) }
        },
        {
          label: '最长逾期天数',
          value: 'sMaxBreachDay'
        },
        { label: '实际货值', value: 'overview250',
          setValue: value => { return Number((+value).toFixed(2)) }
        },
        { label: '实际运费', value: 'overview260',
          setValue: value => { return Number((+value).toFixed(2)) }
        },
        { label: this.$t('grid.title.settlementIncrement'), value: 'vAddAmt',
          setValue: value => { return Number((+value).toFixed(2)) }
        },
        { label: this.$t('grid.title.settlementProfit'), value: 'vProAmt',
          setValue: value => { return Number((+value).toFixed(2)) }
        },
        { label: this.$t('grid.title.profitPerTonSettlement'), value: 'vOneProAmt',
          setValue: value => { return Number((+value).toFixed(2)) }
        },
        { label: this.$t('grid.others.amountReceivableNd'), value: 'vCfmRecAmt',
          setValue: value => { return Number((+value).toFixed(2)) }
        },
        { label: this.$t('grid.others.amountReceivableSt'), value: 'vCfmRealAmt',
          setValue: value => { return Number((+value).toFixed(2)) }
        },
        { label: '还需来款', value: 'overview190',
          setValue: value => { return Number((+value).toFixed(2)) }
        },
        { label: this.$t('grid.others.amountInvoicedPayable'), value: 'vCfmInvAmt',
          setValue: value => { return Number((+value).toFixed(2)) }
        },
        { label: this.$t('grid.others.salesInvoicingInvoiceQuantity'), value: 'vInvQty',
          setValue: (value) => {
            return Number((+value).toFixed(4))
          }
        },
        { label: this.$t('grid.others.salesInvoicingIncludingTaxAmount'), value: 'vInvAmt',
          setValue: value => { return Number((+value).toFixed(2)) }
        },
        { label: this.$t('grid.others.salesInvoicingInvoiceDate'), value: 'vInvDate' },
        { label: '待开票数量', value: 'vLeftInvQty',
          setValue: (value) => {
            return Number((+value).toFixed(4))
          }
        },
        { label: '待开票金额', value: 'vLeftInvAmt',
          setValue: value => { return Number((+value).toFixed(2)) }
        },
        { label: this.$t('grid.others.supplier'), value: 'vSupplierName' },
        { label: this.$t('grid.others.department'), value: 'vDepartmentName' },
        { label: this.$t('grid.title.accountingGroup'), value: 'vCheckGroupName' },
        { label: this.$t('grid.title.personnel'), value: 'vStaffName' },
        { label: this.$t('grid.title.company'), value: 'vCompanyName' },
        { label: '作业平台', value: 'sOperaPlat',
          setValue: (value) => {
            return handleDict(value, this.options['opera.plat.value'])
          }
        },
        { label: this.$t('grid.title.createdBy'), value: 'vCreatorName' },
        { label: this.$t('grid.title.createdAt'), value: 'sCreateTime',
          setValue: (value) => {
            return Moment.time('YYYY-MM-DD HH:mm:ss', value)
          }
        },
        { label: '生效时间', value: 'sRatifyDate',
          setValue: (value) => {
            return Moment.time('YYYY-MM-DD HH:mm:ss', value)
          }
        },
        { label: '经营单位', value: 'vManagementName' },
        { label: '核算方式', value: 'sExtend15',
          setValue: (value) => {
            return handleDict(value, this.options['contract.sale.check.type'])
          }
        }
      ],
      getGridApi: () => {
        return this.$refs.aggrid.gridApi
      },
      isPreEsc: false
    }
  },
  created() {
    // this.$refs.aggrid.loadTableData()
    getDictet([
      'dev.common.sheet.status', 'customer.sign.status', 'esc.settle.nature.type', 'base.yes-no', 'opera.plat.value', 'contract.sale.check.type'
    ]).then(result => {
      this.options['dev.common.sheet.status'] = result.data[0].dicts
      this.options['customer.sign.status'] = result.data[1].dicts
      this.options['esc.settle.nature.type'] = result.data[2].dicts
      this.options['base.yes-no'] = result.data[3].dicts
      this.options['opera.plat.value'] = result.data[4].dicts
      this.options['contract.sale.check.type'] = result.data[5].dicts
    }).catch(() => {
    })
  },
  mounted() {
    this.onSearch(false)
  },
  methods: {
    onSearch(load = true) {
      const searchInfo = this.$refs.searchForm.getSearchData()
      searchInfo.sDepartmentId = searchInfo.sDepartmentId?.toString()
      this.searchInfo = searchInfo
      if (load) {
        this.$refs.aggrid.loadTableData()
      }
    },
    setCount(vCount = 0, vContractQty = 0, vCfmAmt = 0, vCfmRecAmt = 0, vCfmInvAmt = 0, vInvQty = 0, vSumInvAmt = 0, flag) {
      this[flag] = [
        { count: vCount, key: 'count' },
        { title: this.$t('grid.others.settlementQuantity'), count: SteelFormat.formatThousandthSign(vContractQty, 4), unit: this.$t('grid.others.ton') },
        { title: this.$t('grid.others.valueOfGoods'), count: SteelFormat.formatPrice(vCfmAmt), unit: this.$t('grid.others.yuan') },
        { title: this.$t('grid.others.amountReceivableNd'), count: SteelFormat.formatPrice(vCfmRecAmt), unit: this.$t('grid.others.yuan') },
        { title: this.$t('grid.others.invoicePayable'), count: SteelFormat.formatPrice(vCfmInvAmt), unit: this.$t('grid.others.yuan') },
        { title: this.$t('grid.others.invoicedQuantity'), count: SteelFormat.formatThousandthSign(vInvQty, 4), unit: this.$t('grid.others.ton') },
        { title: this.$t('grid.others.invoicedAmount'), count: SteelFormat.formatPrice(vSumInvAmt), unit: this.$t('grid.others.yuan') }
        // 结算数量XXX吨，货值XXX，应收款XXX元，应开票XXX元，已开数量XX吨，已开金额XX元
      ]
    },
    // 点击 表格
    async onRowDoubleClicked(params) {
      console.log(params)
      if (params.data._hiddenCheckbox) return false
      let disabled = '0'
      if (params.data.sSaleContractId) {
        const res = await getContractSubmitControl({
          sSaleContractId: params.data.sSaleContractId,
          sPurContractId: null
        })
        disabled = res.data === false ? '1' : '0'
      }
      settdeliveryget({ id: params.data.sId }).then((res) => {
        if (res.data) {
          this.$router.push({ path: `/settlementDetail/${params.data.sId}`, query: { id: params.data.sId, type: 'edit', name: `${this.$t('grid.others.statementTag')}【${params.data.sCode}】`, sCode: params.data.sCode, disabled: disabled, activeId: localStorage.getItem('menuId') }})
        } else {
          MessageUtil.error(this.$t('grid.others.recordDoesNotExist'))
        }
      })
    },
    sendESCfun() {
      // 电子签约
      settlesendESC(this.selectedData[0].sId).then(e => {
        this.$message({
          message: this.$t('grid.others.issuedSuccessfully'),
          type: 'success'
        })
        this.$refs.aggrid.loadTableData()
      })
    },
    // 批量删除
    agrRemovesfun() {
      const delarr = []
      let handleDel = true
      const vm = this
      this.rowData.map(item => {
        if (item._selected === true) {
          delarr.push(item.sId)
          if (item['sSheetStatus'] !== '10' && item['sSheetStatus'] !== '11') {
            handleDel = false
          }
        }
      })
      if (delarr.length === 0) {
        this.$message({
          message: this.$t('grid.others.pleaseSelectAData'),
          type: 'warning'
        })
        return
      }
      if (handleDel) {
        this.$confirm(this.$t('grid.others.thisActionDeletesThntinueKey'), this.$t('grid.others.prompt'), {
          confirmButtonText: this.$t('btns.confirm'),
          cancelButtonText: this.$t('btns.cancel'),
          type: 'warning'
        })
          .then(() => {
            console.log(delarr)
            agrRemoves(delarr).then((result) => {
              this.$message({
                message: this.$t('tips.deletedSuccessfully'),
                type: 'success'
              })
              vm.$refs.aggrid.loadTableData()
            })
              .catch(() => { })
          })
      } else {
        this.$message({
          message: this.$t('grid.tips.youCanOnlyDeleteDateModifyKey'),
          type: 'warning'
        })
        return
      }
    },
    checkSendEjq() {
      const { sOperaPlat, sEjqCustomerId, sId } = this.selectedData[0]
      if (sOperaPlat === '10') {
        if (sEjqCustomerId) {
          this.$confirm('是否发起E建签回签？', this.$t('grid.others.prompt'), {
            distinguishCancelAndClose: true,
            confirmButtonText: '发起',
            cancelButtonText: '不发起',
            type: 'warning'
          }).then(() => {
            this.accountId = sEjqCustomerId
            this.checkDialogVisible = true
          }).catch(action => {
            if (action === 'cancel') {
              this.selectId = sId
              this.visibleDialog.genAnnex = true
            }
          })
        } else {
          this.selectId = sId
          this.visibleDialog.genAnnex = true
        }
      } else {
        this.settletoFinalfunV2()
      }
    },
    genAnnexClose() {
      this.selectId = null
      this.visibleDialog.genAnnex = false
      this.settletoFinalfunV2()
    },
    checkRelease() {
      return new Promise((resolve, reject) => {
        this.$confirm('是否自动释放余款？', this.$t('grid.others.prompt'), {
          distinguishCancelAndClose: true,
          confirmButtonText: '释放',
          cancelButtonText: '不释放',
          type: 'warning'
        }).then(() => {
          resolve('1')
        }).catch(action => {
          if (action === 'cancel') {
            resolve('0')
          }
        })
      })
    },
    async settletoFinalfunV2(payload) {
      const sIsCreateEjq = payload ? payload.ejqCustomerId : '0'
      if (this.isPreEsc) {
        this.sendPreESCfn(sIsCreateEjq)
      } else {
        const sIsAutoRelease = await this.checkRelease()
        settletoFinalV2({ id: this.selectedData[0].sId, sIsCreateEjq, sIsAutoRelease }).then((res) => {
          if (res.code === '0000') {
            this.checkDialogVisible = false
            this.$message.success(this.$t('grid.others.successfulOperation'))
          }
        })
      }
    },
    settletoFinalfun() {
      this.$confirm(this.$t('grid.tips.doesItConfirmTheGenccountKey'), this.$t('grid.others.prompt'), {
        confirmButtonText: this.$t('btns.confirm'),
        cancelButtonText: this.$t('btns.cancel'),
        type: 'warning'
      }).then(() => {
        // 删除
        // if (this.selectedData[0].sCheckType === '01' && this.selectedData[0].sIsExistCostSplit === '1') { // 判断是否需要 采购成本拆分 弹窗
        //   this.visibleDialog.costSplitDialogVisible = true
        // } else {
        //   this.doFinalCheckFn()
        // }
        this.doFinalCheckFn()
      })
    },
    costSplitDialogSuccess() {
      this.visibleDialog.costSplitDialogVisible = false
    },
    doFinalCheckFn() {
      doFinalCheck({ id: this.selectedData[0].sId }).then((res) => {
        if (res.code === '0000' && res.data && res.data.length > 0) {
          const errorMessages = res.data.map(e => e.dataMessage)
          const showErrorRecursively = (errors, index = 0) => {
            if (index < errors.length) {
              this.$confirm(errors[index], this.$t('grid.others.prompt'), {
                dangerouslyUseHTMLString: true,
                confirmButtonText: this.$t('btns.confirm'),
                cancelButtonText: this.$t('btns.cancel'),
                type: 'error'
              }).then(() => {
                showErrorRecursively(errors, index + 1)
              }).catch(() => {
              })
            } else {
              this.checkSendEjq()
            }
          }
          showErrorRecursively(errorMessages)
        } else {
          this.checkSendEjq()
        }
      }).catch(() => {
      })
    },
    onCloseAdd() {
      this.dialogVisibleadd = false
      this.$refs.aggrid.reloadTableData()
    },
    onSuccess(id, sCode) {
      this.dialogVisibleadd = false
      this.$refs.aggrid.reloadTableData()
      this.$router.push({
        path: `/settlementDetail/${id}`,
        query: {
          id: id,
          type: 'edit',
          name: `${this.$t('grid.others.statementTag')}【${sCode}】`,
          activeId: localStorage.getItem('menuId') }})
    },
    handleStockDetailCount(list) {
      let vContractQty = 0; let vCfmAmt = 0; let vCfmRecAmt = 0; let vCfmInvAmt = 0; let vInvQty = 0; let vSumInvAmt = 0
      list.forEach(el => {
        vContractQty += el.vContractQty
        vCfmAmt += el.vCfmAmt
        vCfmRecAmt += el.vCfmRealAmt
        vCfmInvAmt += el.vCfmInvAmt
        vInvQty += el.vInvQty
        vSumInvAmt += el.vInvAmt
      })
      return [
        { count: list.length, key: 'count' },
        { title: this.$t('grid.others.settlementQuantity'), count: SteelFormat.formatThousandthSign(vContractQty, 4), unit: this.$t('grid.others.ton') },
        { title: this.$t('grid.others.valueOfGoods'), count: SteelFormat.formatPrice(vCfmAmt), unit: '' },
        { title: this.$t('grid.others.amountReceivableNd'), count: SteelFormat.formatPrice(vCfmRecAmt), unit: this.$t('grid.others.yuan') },
        { title: this.$t('grid.others.invoicePayable'), count: SteelFormat.formatPrice(vCfmInvAmt), unit: this.$t('grid.others.yuan') },
        { title: this.$t('grid.tips.invoicedQuantityTag'), count: SteelFormat.formatThousandthSign(vInvQty, 4), unit: this.$t('grid.others.ton') },
        { title: this.$t('grid.tips.amountInvoiced'), count: SteelFormat.formatPrice(vSumInvAmt), unit: this.$t('grid.others.yuan') }
      ]
    },
    // 选中
    handleFooterCount(rowData) {
      const details = rowData.filter(item => item._selected)
      // const vCount = details.length
      this.disRemove = true
      this.escDisabled = true
      this.genDisabled = true
      this.isSendPreESC = true
      this.selectedData = details
      if (details.length > 0) {
        this.disRemove = !details.every(val => val.sSheetStatus === '10' || val.sSheetStatus === '11')
        // this.genDisabled = details.some(val => val.sSheetVersion !== 0 || ['10', '11', '15', '30'].includes(val.vMaxStatus) || val.sSheetStatus !== '70' || val.sNatureType !== '10')
        if (details.length === 1) {
          this.escDisabled = details.some(val => ['10', '11', '15', '30'].includes(val.vMaxStatus) || (val.sSheetStatus !== '70' && (val.sIsSign !== null || val.sIsSign !== '')) || val.sSheetVersion !== 0)
          this.genDisabled = !details.some(val => val.sSheetVersion === 0 && val.sNatureType === '10' && val.sSheetStatus === '70' && ['00', '50'].includes(val.sSignStatus) && !(['10', '11', '15', '30'].includes(val.vMaxStatus)))
          if (details[0]?.vManagementCode === '00014960') {
            this.isSendPreESC = details.some(val => ['11', '15', '30'].includes(val.vMaxStatus) || (!(val.sExtend1 !== '1' && val.sSheetStatus === '70' && val.sNatureType === '10')) || val.sSheetVersion !== 0)
          }
        }
      }
      // let vContractQty = 0; let vCfmAmt = 0; let vCfmRecAmt = 0; let vCfmInvAmt = 0; let vInvQty = 0; let vSumInvAmt = 0; let vAddAmt = 0; let vProAmt = 0; let vOneProAmt = 0
      // details.forEach(el => {
      //   vContractQty += el.vContractQty
      //   vCfmAmt += el.vCfmAmt
      //   vCfmRecAmt += el.vCfmRealAmt
      //   vCfmInvAmt += el.vCfmInvAmt
      //   vInvQty += el.vInvQty
      //   vSumInvAmt += el.vInvAmt
      //   vAddAmt += el.vAddAmt
      //   vProAmt += el.vProAmt
      //   vOneProAmt += el.vOneProAmt
      // })
      // this.setCount(vCount, vContractQty, vCfmAmt, vCfmRecAmt, vCfmInvAmt, vInvQty, vSumInvAmt, vAddAmt, vProAmt, vOneProAmt, 'footerCount')

      this.$refs.aggrid.getSelectedData(res => {
        setTimeout(() => {
          this.$refs.aggrid.gridApi.setPinnedBottomRowData([this.computeTotal(res.length ? 'selected' : 'all')])
        }, 0)
      })
    },
    computeTotal(type = 'all') {
      return Object.assign(this.rowData.reduce((prev, next) => {
        Object.keys(prev).forEach(key => {
          if (type === 'all') {
            prev[key] = new Decimal(+prev[key]).add(+next[key] || 0)
          } else {
            if (next._selected) {
              prev[key] = new Decimal(+prev[key]).add(+next[key] || 0)
            }
          }
        })
        return prev
      }, {
        vContractQty: 0,
        vCfmAmt: 0,
        vCfmRecAmt: 0,
        vCfmInvAmt: 0,
        vInvQty: 0,
        vSumInvAmt: 0,
        vCfmAddAmt: 0,
        vCfmIrtAmt: 0,
        vCfmBreachAmt: 0,
        vCfmTicketAmt: 0,
        vCfmAgentAmt: 0,
        vDiffTaxAmt: 0,
        vCfmRebate: 0,
        overview280: 0,
        vCfmRealAmt: 0,
        overview190: 0,
        vInvAmt: 0,
        vCfmInsAmt: 0,
        vCfmAcceptAmt: 0,
        vCfmReturnAmt: 0,
        vCfmDerateAmt: 0,
        vCfmOtherAmt: 0,
        overview250: 0,
        overview260: 0,
        vAddAmt: 0,
        vProAmt: 0,
        vOneProAmt: 0,
        vLeftInvQty: 0,
        vLeftInvAmt: 0
      }), {
        sCode: type === 'all' ? this.$t('grid.others.amountTo') : `${this.$t('components.selected')}${this.rowData.filter(item => item._selected).length}${this.$t('pagination.items')}`,
        sSettleDate: null,
        vInvDate: null,
        sCreateTime: null,
        sRatifyDate: null,
        sSheetVersion: null,
        _selected: false,
        _hiddenCheckbox: true
      })
    },
    // 主表
    loadData(pagination) {
      return new Promise((resolve, reject) => {
        settlepage(this.searchInfo, {
          ...pagination
        }).then(res => {
          this.rowData = res.data.datas.content.map(item => {
            item._selected = false
            return item
          })
          // const { vCount, vSumQty, vSumCfmAmt, vSumCfmRecAmt, vSumCfmInvAmt, vSumInvQty, vSumInvAmt } = res.data
          // this.setCount(vCount, vSumQty, vSumCfmAmt, vSumCfmRecAmt, vSumCfmInvAmt, vSumInvQty, vSumInvAmt, 'headerCount')

          resolve(res.data.datas)
        }).catch(() => {
          reject(0)
        })
      })
    },
    checkEjqCustomer() {
      return new Promise((resolve, reject) => {
        checkIsEjqCustomer({ id: this.selectedData[0].sId }).then(res => {
          resolve(res.data)
        }).catch(() => {
          reject()
        })
      })
    },
    onSendPreESC() {
      this.$refs.aggrid.getSelectedData(res => {
        if (!res.length || res.length > 1) {
          this.$message.warning(this.$t('grid.others.pleaseSelectAData'))
          return
        }
        this.$confirm('是否预结算发起电子签约?', this.$t('grid.others.prompt'), {
          confirmButtonText: this.$t('btns.confirm'),
          cancelButtonText: this.$t('btns.cancel'),
          type: 'warning'
        }).then(async() => {
          const isEjq = await this.checkEjqCustomer()
          if (isEjq) {
            const { sOperaPlat, sEjqCustomerId } = this.selectedData[0]
            if (sOperaPlat === '10') {
              this.accountId = sEjqCustomerId
              this.checkDialogVisible = true
              this.isPreEsc = true
            } else {
              this.isPreEsc = false
              this.sendPreESCfn()
            }
          } else {
            this.$message.error('非E建签客商无法发起！')
          }
        })
      })
    },
    sendPreESCfn(v) {
      sendPreESC({ id: this.selectedData[0].sId }).then((res) => {
        this.$message.success(this.$t('grid.others.issuedSuccessfully'))
        this.checkDialogVisible = false
        if (this.isPreEsc) {
          updateConSettlementInfo({ id: this.selectedData[0].sId, field: 'sExtend19' }, { sExtend19: v }).then((res) => {
            this.$refs.aggrid.reloadTableData()
          })
        }
      })
    }
  }
}
</script>
