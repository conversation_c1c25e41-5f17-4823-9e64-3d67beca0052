<template>
  <div>
    <el-form
      ref="baseForm"
      class="el-form-w100"
      label-width="100px"
      :inline="true"
      :model="baseForm"
      size="small"
      :rules="rules"
      :disabled="isBusinessDisabled('save', baseForm.sSheetStatus)"
    >
      <cnd-form-card-list ref="cndFormCardList" :active-panel="activeCollapseName" :error-position="true">
        <cnd-form-card :title="$t('grid.tabs.basicInformation')" name="1">
          <el-row>
            <cnd-form-item :label="$t('grid.others.paymentRequestNumberTag')">
              <el-input v-model="baseForm.sCode" disabled />
            </cnd-form-item>
            <cnd-form-item :label="$t('grid.title.applicationDate')">
              <el-input :value="handleTime(baseForm.sApplyDate,'YYYY-MM-DD')" disabled />
            </cnd-form-item>
            <cnd-form-item :label="$t('grid.title.company')">
              <el-input v-model="baseForm.vCompanyName" disabled />
            </cnd-form-item>
            <cnd-form-item v-if="['00011061','00014960'].includes(baseForm.vManagementCode)" label="退款函电签" :error-msg="rules.sExtend4[0].message" prop="sExtend4">
              <el-select v-model="baseForm.sExtend4" :placeholder="$t('components.pleaseSelect')" clearable>
                <el-option
                  v-for="item in selectOps['base.yes-no']"
                  :key="item.sCodeValue"
                  :label="item.sCodeName"
                  :value="item.sCodeValue"
                />
              </el-select>
            </cnd-form-item>
            <cnd-form-item v-if="baseForm.vManagementCode==='00011061'" :label="$t('grid.others.contractingStatus')">
              <el-select v-model="baseForm.sExtend5" disabled :placeholder="$t('grid.others.contractingStatus')">
                <el-option
                  v-for="item in selectOps['customer.sign.status']"
                  :key="item.sCodeValue"
                  :label="item.sCodeName"
                  :value="item.sCodeValue"
                />
              </el-select>
            </cnd-form-item>
          </el-row>
        </cnd-form-card>
        <cnd-form-card :title="$t('grid.others.paymentInformation')" name="2">
          <el-row>
            <cnd-form-item :label="$t('grid.others.paymentDate')">
              <!-- <el-input :value="handleTime(baseForm.sExpectPaymentDate,'YYYY-MM-DD')" /> -->
              <el-date-picker
                v-model="baseForm.sExpectPaymentDate"
                type="date"
                format="yyyy-MM-dd"
                clearable
              />
            </cnd-form-item>
            <cnd-form-item :label="$t('grid.others.paymentMethodTagKey') " :error-msg="rules.sPaymentType[0].message" prop="sPaymentType">
              <el-select v-model="baseForm.sPaymentType" :placeholder="$t('components.pleaseSelect')" clearable>
                <el-option
                  v-for="item in selectOps['pay.mode']"
                  :key="item.sCodeValue"
                  :label="item.sCodeName"
                  :value="item.sCodeValue"
                />
              </el-select>
            </cnd-form-item>
            <cnd-form-item :label="$t('grid.title.paymentPurpose')" :error-msg="rules.sPaymentPurpose[0].message" prop="sPaymentPurpose">
              <el-select v-model="baseForm.sPaymentPurpose" :placeholder="$t('components.pleaseSelect')" clearable>
                <el-option
                  v-for="item in selectOps['pay.purpose']"
                  :key="item.sCodeValue"
                  :label="item.sCodeName"
                  :value="item.sCodeValue"
                />
              </el-select>
            </cnd-form-item>
            <cnd-form-item :label="$t('grid.title.paymentCurrency')" :error-msg="rules.sPaymentCurrency[0].message" prop="sPaymentCurrency">
              <el-select v-model="baseForm.sPaymentCurrency" :placeholder="$t('components.pleaseSelect')" clearable>
                <el-option
                  v-for="item in selectOps['currencylist']"
                  :key="item.sId"
                  :label="item.sCnName"
                  :value="item.sId"
                />
              </el-select>
            </cnd-form-item>
          </el-row>
          <el-row>
            <cnd-form-item :label="$t('grid.others.paymentAmountKey')">
              <!-- <cnd-input-number v-model="baseForm.sPaymentAmt" type="amount" disabled /> -->
              <el-input :value="handleAmount(baseForm.sPaymentAmt)" disabled />
            </cnd-form-item>
            <cnd-form-item :label="$t('grid.title.recipientKey')">
              <el-input v-model="baseForm.vOppCustomerName" disabled />
            </cnd-form-item>
            <cnd-form-item :label="$t('grid.title.counterpartyBank')">
              <el-input v-model="baseForm.sOppBankName" disabled />
            </cnd-form-item>
            <cnd-form-item
              :label="$t('grid.title.counterpartyAccountNumber')"
              error-msg="请选择对方账号"
              :rules="[
                { required: baseForm.sExtend1 === '10', trigger: 'change' }
              ]"
              prop="sOppBankAccount"
            >
              <el-input
                v-model="baseForm.sOppBankAccount"
                :placeholder="$t('components.pleaseSelect')"
                class="input-with-select"
                suffix-icon="el-icon-search"
                @focus="handleCurrencyDialog()"
              />
            </cnd-form-item>
          </el-row>
          <el-row>
            <cnd-form-item :label="$t('grid.others.bankCnapsNumber')">
              <el-input v-model="baseForm.sOppBankCnapsCode" disabled />
            </cnd-form-item>
            <cnd-form-item label="SWIFT CODE">
              <el-input v-model="baseForm.sOppBankSwiftCode" disabled />
            </cnd-form-item>
            <cnd-form-item :label="$t('grid.others.dateOfActualPayment')">
              <el-input :value="handleTime(baseForm.sPayDate,'YYYY-MM-DD')" disabled />
            </cnd-form-item>
            <cnd-form-item label="退款类型" :error-msg="rules.sExtend1[0].message" prop="sExtend1">
              <el-select v-model="baseForm.sExtend1" :placeholder="$t('components.pleaseSelect')" @change="changeExtend1">
                <el-option
                  v-for="item in selectOps['pay.refund.type']"
                  :key="item.sCodeValue"
                  :label="item.sCodeName"
                  :value="item.sCodeValue"
                />
              </el-select>
            </cnd-form-item>
            <cnd-form-item :custom-width="18" label="附言" prop="sOppBankPostScript">
              <el-input v-model="baseForm.sOppBankPostScript" type="textarea" :rows="2" />
            </cnd-form-item>
            <!-- <cnd-form-item :custom-width="18" label="报审说明" prop="sRemark">
              <el-input v-model="baseForm.sRemark" type="textarea" :rows="2" />
            </cnd-form-item> -->
          </el-row>
        </cnd-form-card>
        <cnd-form-card v-show="baseForm.sPaymentType == '050'" :title="$t('grid.others.billReceivableInformation')" name="3" class="card-inc-ag-grid">
          <steelTradeAggrid
            ref="receiveAggrid"
            class="mb-10"
            row-key="sId"
            heightinif="230"
            :column-defs="receiveColumnDefs"
            :row-data="rowReceiveData"
            :load-data="loadReceiveData"
          />
        </cnd-form-card>
        <cnd-form-card v-show="baseForm.sPaymentType == '060'" :title="$t('grid.others.notePayableInformation')" name="4" class="card-inc-ag-grid">
          <steelTradeAggrid
            ref="payAggrid"
            row-key="sId"
            class="mb-10"
            heightinif="230"
            :column-defs="payColumnDefs"
            :row-data="rowPayData"
            :load-data="loadPayData"
          />
        </cnd-form-card>
        <cnd-form-card :title="$t('grid.tabs.systemInformation')" name="5">
          <el-row>
            <cnd-form-item :label="$t('grid.title.createdBy')">
              <el-input v-model="baseForm.vCreatorName" disabled />
            </cnd-form-item>
            <cnd-form-item :label="$t('grid.title.createdAt')">
              <el-input :value="handleTime(baseForm.sCreateTime)" disabled />
            </cnd-form-item>
            <!-- <cnd-form-item :label="$t('grid.title.modifiedBy')" >
              <el-input v-model="baseForm.vModifierName" disabled />
            </cnd-form-item>
            <cnd-form-item :label="$t('grid.title.modifiedAt')">
              <el-input :value="handleTime(baseForm.sModifyTime)" disabled />
            </cnd-form-item> -->
            <cnd-form-item :label="$t('grid.title.status')">
              <el-select v-model="baseForm.sSheetStatus" disabled>
                <el-option
                  v-for="item in selectOps['dev.common.sheet.status']"
                  :key="item.sCodeValue"
                  :label="item.sCodeName"
                  :value="item.sCodeValue"
                />
              </el-select>
            </cnd-form-item>
            <!-- <cnd-form-item :label="$t('grid.title.versionNumber')">
              <el-input v-model="baseForm.sSheetVersion" disabled />
            </cnd-form-item> -->
          </el-row>
        </cnd-form-card>
      </cnd-form-card-list>
    </el-form>
    <CustomerBankDialog
      :visible="showDialog"
      :s-customer-id="sCustomerId"
      :s-department-id="sDepartmentId"
      @onSelect="handleOnSelect"
      @onClose="showDialog = false"
    />
  </div>
</template>
<script>
import agreement from '@/api/agreement'
import {
  getDictet,
  getCnDitc
} from '@/api/logistics/saleDelivery/saleorder'
import { SteelFormat } from 'cnd-horizon-utils'
import { Moment } from 'cnd-utils'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import businessMixin from '@/utils/businessMixin'
import CustomerBankDialog from './dialog/CustomerBankDialog'
import {
  paymentBillList,
  paymentPayList
} from '@/api/money/paymentApply'
export default {
  components: { steelTradeAggrid, CustomerBankDialog },
  mixins: [businessMixin],
  props: {
    info: {
      type: Object,
      default: () => {}
    },
    sId: {
      type: String,
      default: null
    }
  },
  data() {
    const _this = this
    return {
      showDialog: false,
      activeCollapseName: ['1', '2', '3', '4', '5'],
      baseForm: null,
      selectOps: {
        'pay.mode': null,
        'invoice.subtype': null,
        'dev.common.sheet.status': null,
        'currencylist': null,
        'pay.purpose': null,
        'pay.bill.type': null,
        'base.yes-no': null,
        'customer.sign.status': null,
        'pay.refund.type': null
      },
      receiveColumnDefs: [
        {
          headerName: this.$t('grid.others.noteNumber'),
          field: 'sBillCode',
          width: 250
        },
        // {
        //   headerName: this.$t('grid.others.billType'),
        //   field: 'sBillType',
        //   valueGetter: (params) => {
        //     return getCnDitc(params, _this.selectOps['pay.bill.type'], 'sBillType')
        //   }
        // },
        {
          headerName: this.$t('grid.title.amount'),
          field: 'sTaxtAmt',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatPrice(params.value)
          }
        },
        {
          headerName: this.$t('grid.columns.expirationDateSt'),
          field: 'sMaturityDate',
          cellStyle: { textAlign: 'right' },
          valueGetter(params) {
            return Moment.time('YYYY-MM-DD', params.data.sMaturityDate)
          }
        },
        {
          headerName: this.$t('grid.title.billReceivingBank'),
          field: 'sRecBankName'
        },
        {
          headerName: this.$t('grid.others.issuingBank'),
          field: 'sDrawerBankDesc'
        },
        {
          headerName: this.$t('grid.title.personnel'),
          field: 'vStaffName'
        }
        // {
        //   headerName: this.$t('grid.others.monthlyDiscountRate'),
        //   field: 'sMdiscountRate',
        //   cellStyle: { textAlign: 'right' },
        //   valueFormatter: params => {
        //     // return (params.value * 100) + '%'
        //     return SteelFormat.toPercent(params.value)
        //   }
        // },
        // {
        //   headerName: this.$t('grid.others.numberOfDaysNd'),
        //   field: 'sDiscountDays',
        //   cellStyle: { textAlign: 'right' }
        // },
        // {
        //   headerName: this.$t('grid.others.amountOfDiscount'),
        //   field: 'sDiscountAmt',
        //   cellStyle: { textAlign: 'right' },
        //   valueFormatter: params => {
        //     return SteelFormat.formatPrice(params.value)
        //   }
        // }
      ],
      rowReceiveData: [],
      payColumnDefs: [
        {
          headerName: this.$t('grid.others.noteNumber'),
          field: 'sBillCode',
          width: 250
        },
        {
          headerName: this.$t('grid.others.billType'),
          field: 'sBillType',
          valueGetter: (params) => {
            return getCnDitc(params, _this.selectOps['pay.bill.type'], 'sBillType')
          }
        },
        {
          headerName: this.$t('grid.title.amount'),
          field: 'sTaxtAmt',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatPrice(params.value)
          }
        },
        {
          headerName: this.$t('grid.others.billOfExchangeOpeningDate'),
          field: 'sIssueDate',
          cellStyle: { textAlign: 'right' },
          valueGetter(params) {
            return Moment.time('YYYY-MM-DD', params.data.sIssueDate)
          }
        },
        {
          headerName: this.$t('grid.others.billOfExchangeExpiryDate'),
          field: 'sEndDate',
          cellStyle: { textAlign: 'right' },
          valueGetter(params) {
            return Moment.time('YYYY-MM-DD', params.data.sEndDate)
          }
        },
        {
          headerName: this.$t('grid.others.invoicingBankName'),
          field: 'sBillBankDesc'
        },
        {
          headerName: this.$t('grid.others.invoicingBankAccountNumber'),
          field: 'sBankAccount'
        },
        {
          headerName: this.$t('grid.title.personnel'),
          field: 'vStaffName'
        }
      ],
      rowPayData: [],
      rules: {
        sPaymentType: [
          { required: true, message: '请选择付款方式', trigger: 'change' }
        ],
        sPaymentPurpose: [
          { required: true, message: '请选择付款用途', trigger: 'change' }
        ],
        sPaymentCurrency: [
          { required: true, message: '请选择支付币种', trigger: 'change' }
        ],
        // sOppBankAccount: [
        //   { required: true, message: '请选择对方账号', trigger: 'change' }
        // ],
        sExtend4: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        sExtend1: [
          { required: true, message: '请选择', trigger: 'change' }
        ]
      }
    }
  },
  computed: {
    sCustomerId() {
      return this.baseForm.sOppCustomerId
    },
    sDepartmentId() {
      return this.baseForm.sDepartmentId
    }
  },
  watch: {
    info: {
      immediate: true,
      handler(val) {
        this.baseForm = val || {}
        this.$refs.receiveAggrid && this.$refs.receiveAggrid.loadTableData()
        this.$refs.payAggrid && this.$refs.payAggrid.loadTableData()
        // this.sCustomerId = val.sOppCustomerId || ''
      }
    }
  },
  beforeMount() {
    agreement.getDict([
      'pay.mode',
      'invoice.subtype',
      'dev.common.sheet.status',
      'pay.purpose',
      'base.yes-no',
      'customer.sign.status',
      'pay.refund.type'
    ]).then(result => {
      this.selectOps['pay.mode'] = result.data[0].dicts
      this.selectOps['invoice.subtype'] = result.data[1].dicts
      this.selectOps['dev.common.sheet.status'] = result.data[2].dicts // 状态
      this.selectOps['pay.purpose'] = result.data[3].dicts
      this.selectOps['base.yes-no'] = result.data[4].dicts
      this.selectOps['customer.sign.status'] = result.data[5].dicts
      this.selectOps['pay.refund.type'] = result.data[6].dicts
    })
    // 币种
    agreement.currencylist().then(result => {
      this.selectOps['currencylist'] = result.data
    })
    getDictet([
      'pay.bill.type'
    ]).then(result => {
      this.selectOps['pay.bill.type'] = result.data[0].dicts
    }).catch(() => {
    })
  },
  methods: {
    changeExtend1(value) {
      this.$refs.baseForm && this.$refs.baseForm.clearValidate(['sOppBankAccount'])
    },
    handleCurrencyDialog() {
      this.showDialog = true
    },
    handleTime(time, format = 'YYYY-MM-DD HH:mm:ss') {
      return time ? Moment.time(format, time) : ''
    },
    handleAmount(amount) {
      return SteelFormat.formatPrice(amount)
    },
    handleOnSelect(data) {
      this.baseForm.sOppBankCnapsCode = data.sCnaps
      this.baseForm.sOppBankSwiftCode = data.sSwifcode
      this.baseForm.sOppBankName = data.sBankName
      this.baseForm.sOppBankAccount = data.sBankAccount
    },
    // handleUpdata() {
    //   this.$emit('handleUpdata')
    // },
    loadReceiveData(pagination) {
      return new Promise((resolve, reject) => {
        paymentBillList(this.sId, pagination).then(res => {
          this.rowReceiveData = res.data.content.map(item => {
            item._selected = false
            item._selectedKeys = []
            item._details = []
            return item
          })
          resolve(res.data)
        }).catch(() => {
          reject([])
        })
      })
    },
    loadPayData(pagination) {
      return new Promise((resolve, reject) => {
        paymentPayList(this.sId, pagination).then(res => {
          this.rowPayData = res.data.content.map(item => {
            item._selected = false
            item._selectedKeys = []
            item._details = []
            return item
          })
          resolve(res.data)
        }).catch(() => {
          reject([])
        })
      })
    },
    changeBankOpp(v) {
      console.log(v)
    },
    saveForm(fn) {
      this.$refs.baseForm.validate((valid) => {
        console.log(valid)
        if (valid) {
          fn && fn(this.baseForm, true)
        } else {
          fn && fn(this.baseForm, false)
          return false
        }
      })
    }
  }
}
</script>
<style lang="scss">
.card-inc-ag-grid{
  .el-collapse-item {
    position: relative;
    .el-collapse-item__content{
      padding: 0;
    }
  }
}
</style>
