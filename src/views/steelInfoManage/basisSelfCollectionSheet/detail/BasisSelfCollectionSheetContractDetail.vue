<!-- 收集单-合同详情 -->
<template>
  <div>
    <cnd-dialog
      :title="$t('grid.tabs.basicInformation')"
      :visible="true"
      @close="onClose"
    >
      <template v-if="isMaintain !== '1'" slot="leftBtn">
        <el-button
          v-has:basis_self_collection_sheet_contract_detail_save
          type="primary"
          size="mini"
          @click="save"
        >{{ $t('btns.save') }}</el-button>

        <el-button
          v-has:basis_self_collection_sheet_contract_detail_delete
          type="danger"
          size="mini"
          @click="onDel"
        >{{ $t('btns.delete') }}</el-button>
        <el-button
          type="primary"
          size="mini"
          :disabled="!previousPeriodData.sId"
          @click="openLastReport"
        >查看上期填报</el-button>
      </template>
      <template slot="content">
        <el-form
          ref="form"
          class="el-form-w100"
          label-width="100px"
          :inline="true"
          :model="basicData"
          :rules="rules"
          size="small"
          :disabled="isRead"
        >
          <cnd-form-card-list :active-panel="activeCollapseName">
            <!-- 采购基础信息 -->
            <PurchaseContractBasicDetail
              v-if="(sContractType === 'pur' && isMaintain !== '1') || basicData.sBusinessType === '01' || basicData.sBusinessType === '03'"
              ref="basicDetail"
              :form-data="basicData"
            />
            <!-- 销售基础信息 -->
            <SellContractBasicDetail
              v-if="(sContractType === 'sale' && isMaintain !== '1') || basicData.sBusinessType === '02' || basicData.sBusinessType === '04'"
              ref="basicDetail"
              :form-data="basicData"
            />
            <ContractBasicDetail
              v-if="isMaintain === '1' && !basicData.sBusinessType"
              ref="contractBasicDetail"
              :form-data="basicData"
            />

            <!-- 期货信息 -->
            <FuturesList
              ref="FuturesList"
              :form-data="basicData"
              :row-data="futuresRowData"
            />
            <!-- 期货交易明细 -->
            <FuturesDealList
              :form-data="basicData"
              :row-data="futuresDealRowData"
              :s-hedging-collection-contract-id="selectId"
              :add-msb-basis-proprietary-hedging-collection-info="
                addMsbBasisProprietaryHedgingCollectionInfo
              "
              :is-edit="isMaintain !== '1'"
              :ops="selectOps"
              @refresh="refresh"
            />
          </cnd-form-card-list>
        </el-form>
      </template>
    </cnd-dialog>
  </div>
</template>

<script>
import { getDictet } from '@/api/logistics/saleDelivery/saleorder'
// import agreement from '@/api/agreement'
import businessMixin from '@/utils/businessMixin'
import PurchaseContractBasicDetail from '../components/PurchaseContractBasicDetail.vue'
import FuturesList from '../components/FuturesList.vue'
import FuturesDealList from '../components/FuturesDealList'
import SellContractBasicDetail from '../components/SellContractBasicDetail.vue'
import basisSelfCollectionSheet from '@/api/basisSelfCollectionSheet'
import ContractBasicDetail from '../components/ContractBasicDetail'
export default {
  name: 'BasisSelfCollectionSheetContractDetail',
  components: {
    PurchaseContractBasicDetail,
    FuturesList,
    FuturesDealList,
    SellContractBasicDetail,
    ContractBasicDetail
  },
  mixins: [businessMixin],
  props: {
    id: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      activeCollapseName: ['1', '2', '3'],
      selectOps: {
        'dev.common.sheet.status': [],
        'collection.state': [],
        'base.yes-no': [],
        'open.close.position': []
      },
      selectId: this.$route.query.Id,
      sContractType: this.$route.query.sContractType,
      sBusinessType: this.$route.query.sBusinessType,
      status: this.$route.query.status,
      sHedgingCollectionId: this.$route.query.sHedgingCollectionId,
      // 维护状态 0 查看 1 维护
      isMaintain: this.$route.query.isMaintain,
      basicData: {
        sBusinessType: this.$route.query.sContractType === 'pur' ? '01' : '02'
      },
      rules: {
        // 合同号
        sContractNo: [
          { required: true, message: '请输入合同号', trigger: 'blur' }
        ],
        // 项目号
        sProjectNo: [
          { required: true, message: '请输入项目号', trigger: 'blur' }
        ],
        // 合同数量
        sContractQty: [
          { required: true, message: '请输入合同数量', trigger: 'blur' }
        ],
        // 已销售数量
        sTradedQty: [
          { required: true, message: '请输入已销售数量', trigger: 'blur' }
        ],
        // 采购成本
        sTradedPrice: [
          { required: true, message: '请输入采购成本', trigger: 'blur' }
        ],
        // 部门
        vDepartmentName: [
          { required: true, message: '请选择部门', trigger: 'change' }
        ],
        // 人员
        vStaffName: [
          { required: true, message: '请选择人员', trigger: 'change' }
        ],
        // 公司
        vCompanyName: [
          { required: true, message: '请选择公司', trigger: 'change' }
        ],
        // 核算组
        vCheckGroupName: [
          { required: true, message: '请选择核算组', trigger: 'change' }
        ]
      },
      // 期货信息列表
      futuresRowData: [],
      // 期货交易明细列表
      futuresDealRowData: [],
      // 上期填报数据
      previousPeriodData: {}
    }
  },
  computed: {
    isRead() {
      console.log('🚀 ~ isRead ~ this.status:', this.status)

      return this.status !== '10' && this.status !== '15'
    }
  },
  async created() {
    const result = await getDictet(['dev.common.sheet.status', 'collection.state', 'base.yes-no', 'open.close.position'])
    this.selectOps['dev.common.sheet.status'] = result.data[0]?.dicts
    this.selectOps['collection.state'] = result.data[1]?.dicts
    this.selectOps['base.yes-no'] = result.data[2]?.dicts
    this.selectOps['open.close.position'] = result.data[3]?.dicts
    if (this.selectId) {
      this.getDetail()
      if (this.isMaintain !== '1') {
        this.getFuturesList()
        this.getFuturesDealList()
      }
    }
  },
  methods: {
    // 获取基本信息
    async getDetail(res) {
      if (this.isMaintain === '1') {
        const res = await basisSelfCollectionSheet
          .getHedgingContract(this.selectId)
        if (!res.data) return
        this.basicData = res.data || {}
        this.futuresRowData = res.data.futuresInfoVoList.map((item) => {
          item._selected = false
          item._selectedKeys = []
          return item
        })
        this.futuresDealRowData = res.data.futuresVoList.map((item) => {
          item._selected = false
          item._selectedKeys = []
          return item
        })
      } else {
        const res = await basisSelfCollectionSheet
          .getMsbBasisProprietaryHedgingCollectionInfo(this.selectId)
        this.basicData = res.data || {}
        console.log('🚀 ~ .then ~ this.basicData:', this.basicData)
        this.oldDetail = JSON.stringify(res.data)
        this.getReportContractInfoPreviousPeriod()
      }
    },
    // 获取期货信息列表
    getFuturesList() {
      basisSelfCollectionSheet
        .getMsbBasisProprietaryHedgingCollectionInfoList(this.selectId)
        .then((res) => {
          if (res.data) {
            this.futuresRowData = res.data.map((item) => {
              item._selected = false
              item._selectedKeys = []
              return item
            })
          } else {
            this.futuresRowData = []
          }
        })
    },
    // 获取期货交易明细列表
    getFuturesDealList() {
      basisSelfCollectionSheet
        .getMsbBasisProprietaryHedgingCollectionDetailFuturesList({
          sHedgingCollectionContractId: this.selectId
        })
        .then((res) => {
          if (res.data) {
            this.futuresDealRowData = res.data.map((item) => {
              item._selected = false
              item._selectedKeys = []
              return item
            })
          } else {
            this.futuresDealRowData = []
          }
        })
    },
    // 新增合同信息
    async addMsbBasisProprietaryHedgingCollectionInfo() {
      this.basicData = {
        ...this.$refs.basicDetail.form,
        sHedgingCollectionId: this.sHedgingCollectionId,
        sContractType: this.sContractType
      }
      const res =
        await basisSelfCollectionSheet.addMsbBasisProprietaryHedgingCollectionInfo(
          this.basicData
        )
      this.selectId = res.data.sId
      this.getDetail()
    },
    save() {
      this.basicData = { ...this.$refs.basicDetail.form }
      this.$refs.form.validate(async(valid) => {
        if (valid) {
          if (!this.basicData.sId) {
            await this.addMsbBasisProprietaryHedgingCollectionInfo()
          } else {
            const res = await basisSelfCollectionSheet
              .updateMsbBasisProprietaryHedgingCollectionInfo(this.basicData)
            this.basicData.sVersion = res.data.sVersion
          }
          this.$message.success('保存成功')
        }
      })
    },
    onDel() {
      this.$confirm('是否删除该条数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          basisSelfCollectionSheet
            .removesMsbBasisProprietaryHedgingCollectionInfo(
              [this.selectId]
            )
            .then(() => {
              this.$message.success('删除成功')
              this.$tabDelete(this.$route.query.fromPath)
            })
        })
        .catch(() => {})
    },
    refresh() {
      this.getFuturesList()
      this.getFuturesDealList()
    },
    openLastReport() {
      this.$router.push({
        path: `/basisSelfCollectionSheetContractPriorDetail/${this.selectId}`,
        query: {
          Id: this.selectId,
          sContractType: this.sContractType,
          name: `上期填报数据【${this.basicData.sContractNo}】`,
          activeId: localStorage.getItem('menuId'),
          sBusinessType: this.basicData.sBusinessType,
          sHedgingCollectionId: this.sHedgingCollectionId,
          sContractNo: this.basicData.sContractNo,
          type: 'read'
        }
      })
    },
    getReportContractInfoPreviousPeriod() {
      basisSelfCollectionSheet.getReportContractInfoPreviousPeriod({
        sId: this.sHedgingCollectionId,
        sContractNo: this.basicData.sContractNo
      }).then((res) => {
        if (res.data) {
          this.previousPeriodData = res.data
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.el-range-editor--small.el-input__inner {
  height: 26px;
}
::v-deep.form-item-container {
  .el-form-item .el-form-item__content {
    line-height: 24px !important;
  }
}
::v-deep.el-range-editor--small .el-range__icon {
  line-height: 20px;
}
</style>
