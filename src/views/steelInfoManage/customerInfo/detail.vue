<template>
  <div class="page-container">
    <cnd-dialog :title="$t('grid.tabs.basicInformation')" :visible="true" @close="onClose">
      <template slot="leftBtn">
        <span>
          <el-button
            type="primary"
            size="mini"
            @click="save"
          >{{ $t('btns.save') }}</el-button>
          <el-button
            v-if="$route.query.type !== 'add'"
            type="danger"
            size="mini"
            @click="remove"
          >
            {{ $t('btns.delete') }}
          </el-button>
        </span>
      </template>
      <template slot="content">
        <el-form
          ref="form"
          class="el-form-w100"
          label-width="90px"
          inline
          :model="form"
          :rules="rules"
          size="small"
        >
          <cnd-form-card-list :active-panel="activeCollapseName">
            <cnd-form-card class="mb-10" :title="$t('grid.tabs.basicInformation')" name="1">
              <el-row>
                <cnd-form-item
                  :label="$t('grid.others.customer')"
                  prop="vCustomerName"
                  :error-msg="rules.vCustomerName[0].message"
                >
                  <horizon-search-select
                    v-model="form.vCustomerName"
                    type="customer"
                    :customer-type="10"
                    :placeholder="$t('components.pleaseSelect')"
                    @change="handleChangeSelect($event, 'sCustomerId')"
                  />
                </cnd-form-item>
                <cnd-form-item
                  :label="$t('grid.title.company')"
                  prop="vCompanyName"
                  :error-msg="rules.vCompanyName[0].message"
                >
                  <horizon-search-select
                    v-model="form.vCompanyName"
                    type="company"
                    @change="handleChangeSelect($event, 'sCompanyId')"
                  />
                </cnd-form-item>
                <cnd-form-item
                  label="经营单位"
                  prop="sManagementId"
                  :error-msg="rules.sManagementId[0].message"
                >
                  <el-select v-model="form.sManagementId">
                    <el-option
                      v-for="item in orgList"
                      :key="item.sId"
                      :label="item.sName"
                      :value="item.sId"
                    />
                  </el-select>
                </cnd-form-item>
                <cnd-form-item
                  label="账户标识"
                  prop="sAccountId"
                  :error-msg="rules.sAccountId[0].message"
                >
                  <el-input v-model="form.sAccountId" />
                </cnd-form-item>
                <cnd-form-item
                  label="片区"
                  prop="sArea"
                  :error-msg="rules.sArea[0].message"
                >
                  <el-input v-model="form.sArea" />
                </cnd-form-item>
                <cnd-form-item
                  label="单位编码"
                  prop="sUnitCode"
                  :error-msg="rules.sUnitCode[0].message"
                >
                  <el-input v-model="form.sUnitCode" />
                </cnd-form-item>
                <cnd-form-item
                  label="备注/分户"
                  prop="sRemark"
                  :error-msg="rules.sRemark[0].message"
                >
                  <el-input
                    v-model="form.sRemark"
                    type="textarea"
                    :rows="2"
                    clearable
                  />
                </cnd-form-item>
              </el-row>
            </cnd-form-card>
            <cnd-form-card class="mb-10" :title="$t('grid.tabs.systemInformation')" name="2">
              <el-row>
                <cnd-form-item :label="$t('grid.title.createdBy')" prop="vCreatorName">
                  <el-input v-model="form.vCreatorName" disabled clearable />
                </cnd-form-item>
                <cnd-form-item :label="$t('grid.title.createdAt')" prop="sCreateTime">
                  <el-date-picker
                    v-model="form.sCreateTime"
                    format="yyyy-MM-dd HH:mm:ss"
                    type="date"
                    disabled
                    :placeholder="$t('grid.title.createdAt')"
                  />
                </cnd-form-item>
                <cnd-form-item :label="$t('grid.title.modifiedBy')" prop="vModifierName">
                  <el-input v-model="form.vModifierName" disabled clearable />
                </cnd-form-item>
                <cnd-form-item :label="$t('grid.title.modifiedAt')" prop="sModifyTime">
                  <el-date-picker
                    v-model="form.sModifyTime"
                    format="yyyy-MM-dd HH:mm:ss"
                    type="date"
                    disabled
                    :placeholder="$t('grid.title.modifiedAt')"
                  />
                </cnd-form-item>
              </el-row>
            </cnd-form-card>
          </cnd-form-card-list>
        </el-form>
      </template>
    </cnd-dialog>
  </div>
</template>

<script>
import {
  getCustomerDetail,
  saveCustomer,
  removeCustomers
} from '@/api/steelInfoManage/customerInfo'
import { getManagement } from '@/utils/auth'

import {
  getOrgDialog
} from '@/api/customerOutConfig'
export default {
  name: 'CustomerInfoDetail',
  data() {
    return {
      activeCollapseName: ['1', '2', '3'],
      form: {},
      rules: {
        vCustomerName: [
          { required: true, message: this.$t('components.pleaseSelect'), trigger: 'change' }
        ],
        vCompanyName: [
          { required: true, message: this.$t('components.pleaseSelect'), trigger: 'change' }
        ],
        sManagementId: [
          { required: true, message: this.$t('components.pleaseSelect'), trigger: 'change' }
        ],
        sAccountId: [
          { required: true, message: this.$t('components.pleaseEnter'), trigger: 'blur' }
        ],
        sArea: [
          { required: true, message: this.$t('components.pleaseEnter'), trigger: 'blur' }
        ],
        sUnitCode: [
          { required: true, message: this.$t('components.pleaseEnter'), trigger: 'blur' }
        ],
        sRemark: [
          { required: false, message: this.$t('components.pleaseEnter'), trigger: 'blur' }
        ]
      },
      orgList: [],
      dialogVisible: {
        annex: false
      }
    }
  },
  computed: {
    id() {
      return this.$route.query.id
    }
  },
  mounted() {
    this.loadDict()
    this.loadDetail()
  },
  methods: {
    loadDict() {
      getOrgDialog({}).then(res => {
        this.orgList = res.data.content
      })
    },
    loadDetail() {
      if (this.id) {
        getCustomerDetail(this.id).then(res => {
          this.form = res.data
          this.formDisabled = true
        })
      } else {
        if (this.$route.query.type === 'add') {
          this.form = {
            vCustomerName: null,
            sCustomerId: null,
            vCompanyName: null,
            sCompanyId: null,
            sManagementId: null,
            sAccountId: null,
            sArea: null,
            sUnitCode: null,
            sRemark: null
          }
          this.formDisabled = false
        }
      }
      if (!this.form.sManagementId) {
        const manage = getManagement('all')
        if (manage.length === 1) {
          this.form.sManagementId = manage[0].sId
        }
      }
    },
    save() {
      this.$refs.form.validate(valid => {
        if (valid) {
          if (this.id) {
            saveCustomer(this.form).then(res => {
              this.$message.success(this.$t('tips.saveSuccess'))
              this.loadDetail()
            })
          } else {
            saveCustomer(this.form).then(res => {
              this.$message.success(this.$t('tips.saveSuccess'))
              this.$tabDelete(
                `/egl/customerInfoDetail/${res.data.sId}?id=${res.data.sId}&type=edit&name=钢厂客户信息【${res.data.vCustomerName}】&activeId=${localStorage.getItem('menuId')}&random=${Math.random()}`
              )
            })
          }
        }
      })
    },
    remove() {
      this.$confirm(this.$t('tips.isItOkToDelete'), this.$t('grid.others.prompt'), {
        confirmButtonText: this.$t('btns.confirm'),
        cancelButtonText: this.$t('btns.cancel'),
        type: 'info'
      }).then(() => {
        removeCustomers([this.id]).then(() => {
          this.$tabDelete()
        })
      })
    },
    handleChangeSelect(val, key) {
      if (val) {
        this.form[key] = val.sId
      } else {
        this.form[key] = undefined
      }
    }
  }
}
</script>
