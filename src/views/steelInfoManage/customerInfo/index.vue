<template>
  <div class="page-container">
    <p class="page-title">钢厂客户信息</p>
    <div class="layout-content auto-page-title flexV">
      <steel-search-form ref="searchForm" :form-items="formItems" @search="onSearch" />
      <div class="btn-group mt-10" style="display: flex">
        <div class="text">
          钢厂客户信息列表
        </div>
        <div>
          <el-button
            type="primary"
            size="mini"
            @click="create"
          >
            {{ $t('btns.add') }}
          </el-button>
          <el-button
            type="danger"
            size="mini"
            class="mr-10"
            :disabled="delDisable"
            @click="removes"
          >
            {{ $t('btns.delete') }}
          </el-button>
        </div>
      </div>
      <steelTradeAggrid
        ref="aggrid"
        :column-defs="columnDefs"
        :row-data="rowData"
        :load-data="loadData"
        :auto-load-data="false"
        table-selection="multiple"
        @selectedChange="handleFooterCount"
        @rowDoubleClicked="onRowDoubleClicked"
      />
    </div>
  </div>
</template>

<script>
import steelTradeAggrid from '@/components/steelTradeAggrid'
import { Moment } from 'cnd-utils'
import {
  getCustomerPage,
  removeCustomers
} from '@/api/steelInfoManage/customerInfo'
export default {
  name: 'CustomerInfo',
  components: { steelTradeAggrid },
  data() {
    return {
      searchInfo: null,
      formItems: [
        {
          label: this.$t('grid.others.customer'),
          value: 'sCustomerId',
          type: 'cndInputDialogItem',
          dialogType: 'customer',
          defaultUrl: '/esc/customer/page',
          option: { valueKey: 'sPath' },
          placeholder: this.$t('grid.others.pleaseSelectTheCustomer')
        },
        {
          label: '账户标识',
          value: 'sAccountId',
          type: 'elInput'
        },
        {
          label: '片区',
          value: 'sArea',
          type: 'elInput'
        },
        {
          label: this.$t('grid.title.company'),
          value: 'sCompanyId',
          type: 'cndInputDialog',
          dialogType: 'company'
        },
        {
          label: '经营单位',
          value: 'sManagementId',
          type: 'cndInputDialog',
          dialogType: 'escOrg'
        },
        {
          label: this.$t('grid.title.createdAt'),
          value: ['sCreateTime', 'vCreateTimeTo'],
          placeholder: [this.$t('grid.others.startDate'), this.$t('grid.others.endDate')],
          unlinkPanels: true,
          type: 'elDatePicker'
        }
      ],
      columnDefs: [
        {
          headerName: '客户',
          field: 'vCustomerName'
        },
        {
          field: 'sArea',
          headerName: '片区'
        },
        {
          field: 'sAccountId',
          headerName: '账户标识'
        },
        {
          field: 'vCompanyName',
          headerName: '公司'
        },
        {
          field: 'vManagementName',
          headerName: '经营单位'
        },
        {
          field: 'sRemark',
          headerName: '备注/分户'
        },
        {
          field: 'vCreatorName',
          headerName: this.$t('grid.title.createdBy')
        },
        {
          field: 'sCreateTime',
          headerName: this.$t('grid.title.createdAt'),
          minWidth: 150,
          valueFormatter(params) {
            return Moment.time('YYYY-MM-DD HH:mm:ss', params.data.sCreateTime)
          }
        },
        {
          field: 'vModifierName',
          headerName: this.$t('grid.title.modifiedBy')
        },
        {
          headerName: this.$t('grid.title.modifiedAt'),
          field: 'sModifyTime',
          minWidth: 150,
          valueFormatter(params) {
            return Moment.time('YYYY-MM-DD HH:mm:ss', params.data.sModifyTime)
          }
        }
      ],
      rowData: []
    }
  },
  created() {
  },
  mounted() {
    this.onSearch(false)
  },
  methods: {
    onSearch(load = true) {
      const searchInfo = this.$refs.searchForm.getSearchData()
      searchInfo.sCustomerIds = searchInfo.sCustomerIds?.toString()
      this.searchInfo = searchInfo
      if (load) {
        this.$refs.aggrid.loadTableData()
      }
    },
    loadData(pagination) {
      return new Promise((resolve, reject) => {
        getCustomerPage(this.searchInfo, pagination).then(res => {
          this.rowData = res.data.content.map(item => {
            item._selected = false
            item._selectedKeys = []
            return item
          })
          resolve(res.data)
        }).catch(() => {
          reject([])
        })
      })
    },
    handleFooterCount(rowData) {
      // console.log(rowData)
    },

    create() {
      this.$router.push({
        path: `/customerInfoDetail/add`,
        query: {
          type: 'add',
          name: `新增钢厂客户信息`,
          activeId: localStorage.getItem('menuId')
        }
      })
    },
    removes() {
      this.$refs.aggrid.getSelectedData(res => {
        if (!res.length) {
          this.$message.warning(this.$t('grid.others.pleaseSelectData'))
          return
        }
        this.$confirm(this.$t('grid.tips.isTheDeletionConfirmed'), this.$t('grid.others.prompt'), {
          confirmButtonText: this.$t('grid.others.yes'),
          cancelButtonText: this.$t('btns.no'),
          type: 'warning'
        }).then(() => {
          removeCustomers(
            res.map(item => item.sId)
          ).then(() => {
            this.$message.success(this.$t('grid.tips.deletionSuccess'))
            this.$refs.aggrid.reloadTableData()
          })
        })
      })
    },
    onRowDoubleClicked(params) {
      this.$router.push({
        path: `/customerInfoDetail/${params.data.sId}`,
        query: {
          id: params.data.sId,
          type: 'edit',
          name: `钢厂客户信息【${params.data.vCustomerName}】`,
          activeId: localStorage.getItem('menuId')
        }
      })
    }
  }
}
</script>

<style scoped>

</style>
