<template>
  <div class="page-container">
    <cnd-dialog :title="$t('grid.tabs.basicInformation')" :visible="true" @close="onClose">
      <template slot="leftBtn">
        <span>
          <el-button
            type="primary"
            size="mini"
            @click="save"
          >{{ $t('btns.save') }}</el-button>
          <el-button
            v-if="$route.query.type !== 'add'"
            type="danger"
            size="mini"
            @click="remove"
          >
            {{ $t('btns.delete') }}
          </el-button>
        </span>
      </template>
      <template slot="content">
        <el-form
          ref="form"
          class="el-form-w100"
          label-width="90px"
          inline
          :model="form"
          :rules="rules"
          size="small"
        >
          <cnd-form-card-list :active-panel="activeCollapseName">
            <cnd-form-card class="mb-10" :title="$t('grid.tabs.basicInformation')" name="1">
              <el-row>
                <cnd-form-item
                  label="物料编码"
                  prop="sMaterialCode"
                  :error-msg="rules.sMaterialCode[0].message"
                >
                  <el-input v-model="form.sMaterialCode" />
                </cnd-form-item>
                <cnd-form-item
                  label="粗品名"
                  prop="sGoodsDetailName"
                  :error-msg="rules.sGoodsDetailName[0].message"
                >
                  <el-input
                    v-model="form.sGoodsDetailName"
                    readonly
                    class="input-with-select"
                    suffix-icon="el-icon-search"
                    placeholder="点击选择粗品名"
                    @click.native="openGoodsDetailDialog"
                  />
                </cnd-form-item>
                <cnd-form-item
                  label="细品名"
                  prop="sArtName"
                  :error-msg="rules.sArtName[0].message"
                >
                  <el-input
                    v-model="form.sArtName"
                    readonly
                    class="input-with-select"
                    suffix-icon="el-icon-search"
                    placeholder="点击选择细品名"
                    @click.native="openArtNameDialog"
                  />
                </cnd-form-item>
                <cnd-form-item
                  label="钢厂品种"
                  prop="sVariety"
                  :error-msg="rules.sVariety[0].message"
                >
                  <el-input v-model="form.sVariety" />
                </cnd-form-item>
                <cnd-form-item
                  label="钢厂材质"
                  prop="sMaterial"
                  :error-msg="rules.sMaterial[0].message"
                >
                  <el-input v-model="form.sMaterial" />
                </cnd-form-item>
                <cnd-form-item
                  label="钢厂规格"
                  prop="sSpec"
                  :error-msg="rules.sSpec[0].message"
                >
                  <el-input v-model="form.sSpec" />
                </cnd-form-item>
                <cnd-form-item
                  label="钢厂长度"
                  prop="sLength"
                >
                  <el-input v-model="form.sLength" />
                </cnd-form-item>
                <cnd-form-item
                  label="产地"
                  prop="sOrigin"
                >
                  <el-input v-model="form.sOrigin" />
                </cnd-form-item>
                <cnd-form-item
                  label="件重"
                  prop="sWeightPiece"
                  :error-msg="rules.sWeightPiece[0].message"
                >
                  <!-- <el-input-number v-model="form.sWeightPiece" /> -->
                  <cnd-input-number v-model="form.sWeightPiece" type="number" :decimal-digit="4" clearable />
                </cnd-form-item>
              </el-row>
            </cnd-form-card>
            <cnd-form-card class="mb-10" :title="$t('grid.tabs.systemInformation')" name="2">
              <el-row>
                <cnd-form-item :label="$t('grid.title.createdBy')" prop="vCreatorName">
                  <el-input v-model="form.vCreatorName" disabled clearable />
                </cnd-form-item>
                <cnd-form-item :label="$t('grid.title.createdAt')" prop="sCreateTime">
                  <el-date-picker
                    v-model="form.sCreateTime"
                    format="yyyy-MM-dd HH:mm:ss"
                    type="date"
                    disabled
                    :placeholder="$t('grid.title.createdAt')"
                  />
                </cnd-form-item>
                <cnd-form-item :label="$t('grid.title.modifiedBy')" prop="vModifierName">
                  <el-input v-model="form.vModifierName" disabled clearable />
                </cnd-form-item>
                <cnd-form-item :label="$t('grid.title.modifiedAt')" prop="sModifyTime">
                  <el-date-picker
                    v-model="form.sModifyTime"
                    format="yyyy-MM-dd HH:mm:ss"
                    type="date"
                    disabled
                    :placeholder="$t('grid.title.modifiedAt')"
                  />
                </cnd-form-item>
              </el-row>
            </cnd-form-card>
          </cnd-form-card-list>
        </el-form>
      </template>
    </cnd-dialog>

    <cnd-dialog-tree
      title="选择粗品名"
      :visible="visible"
      :req-config="reqConfig"
      :show-search="true"
      :placeholder="'请输入品名搜索'"
      @onSelect="onSelect"
    />
    <dialog-table
      title="选择细品名"
      :visible="visibleArtName"
      :req-config="reqConfigArtName"
      :column-defs="columnDefs"
      :show-search="true"
      :placeholder="'请输入品名搜索'"
      @onSelect="onSelectArtName"
    />
  </div>
</template>

<script>
import {
  getProductDetail,
  saveProduct,
  removeProducts
} from '@/api/steelInfoManage/productInfo'
import { getManagement } from '@/utils/auth'
import DialogTable from '@/components/DialogTable'

import {
  getOrgDialog
} from '@/api/customerOutConfig'
export default {
  name: 'ProductInfoDetail',
  components: {
    DialogTable
  },
  data() {
    return {
      activeCollapseName: ['1', '2', '3'],
      form: {},
      rules: {
        sMaterialCode: [
          { required: true, message: this.$t('components.pleaseEnter'), trigger: 'blur' }
        ],
        sGoodsDetailName: [
          { required: true, message: this.$t('components.pleaseSelect'), trigger: 'change' }
        ],
        sArtName: [
          { required: true, message: this.$t('components.pleaseSelect'), trigger: 'change' }
        ],
        sVariety: [
          { required: true, message: this.$t('components.pleaseEnter'), trigger: 'blur' }
        ],
        sMaterial: [
          { required: true, message: this.$t('components.pleaseEnter'), trigger: 'blur' }
        ],
        sSpec: [
          { required: true, message: this.$t('components.pleaseEnter'), trigger: 'blur' }
        ],
        sWeightPiece: [
          { required: true, message: this.$t('components.pleaseEnter'), trigger: 'blur' }
        ]
      },
      orgList: [],
      dialogVisible: {
        annex: false
      },
      visible: false,
      visibleArtName: false,
      vPath: ''
    }
  },
  computed: {
    id() {
      return this.$route.query.id
    },
    columnDefs() {
      return [
        { field: 'sName', headerName: '品名' }
      ]
    },
    reqConfig() {
      return {
        url: '/goods/detail/tree',
        method: 'POST',
        keyword: 'name',
        radio: true,
        treeProps: {
          label: 'name'
        }
      }
    },
    reqConfigArtName() {
      return {
        url: '/artno/page/{pageNo}/{pageSize}',
        method: 'POST',
        params: {
          pageNo: 0,
          pageSize: 30,
          vPath: this.form.sGoodsDetailId
        },
        keyword: 'sName',
        dataFormat: {
          key: 'content'
        }
      }
    }
  },
  mounted() {
    this.loadDict()
    this.loadDetail()
  },
  methods: {
    onClose() {
      this.$tabDelete()
    },
    loadDict() {
      getOrgDialog({}).then(res => {
        this.orgList = res.data.content
      })
    },
    loadDetail() {
      if (this.id) {
        getProductDetail(this.id).then(res => {
          this.form = res.data
          this.formDisabled = true
        })
      } else {
        if (this.$route.query.type === 'add') {
          this.form = {
            sMaterialCode: null,
            sGoodsDetailName: null,
            sArtName: null,
            sVariety: null,
            sMaterial: null,
            sSpec: null,
            sLength: null,
            sOrigin: null,
            sWeightPiece: null,
            sGoodsDetailId: null,
            sArtId: null
          }
          this.formDisabled = false
        }
      }
      if (!this.form.sManagementId) {
        const manage = getManagement('all')
        if (manage.length === 1) {
          this.form.sManagementId = manage[0].sId
        }
      }
    },
    save() {
      this.$refs.form.validate(valid => {
        if (valid) {
          if (this.id) {
            saveProduct(this.form).then(res => {
              this.$message.success(this.$t('tips.saveSuccess'))
              this.loadDetail()
            })
          } else {
            saveProduct(this.form).then(res => {
              this.$message.success(this.$t('tips.saveSuccess'))
              this.$tabDelete(
                `/egl/productInfoDetail/${res.data.sId}?id=${res.data.sId}&type=edit&name=钢厂产品信息【${res.data.sMaterialCode}】&activeId=${localStorage.getItem('menuId')}&random=${Math.random()}`
              )
            })
          }
        }
      })
    },
    remove() {
      this.$confirm(this.$t('tips.isItOkToDelete'), this.$t('grid.others.prompt'), {
        confirmButtonText: this.$t('btns.confirm'),
        cancelButtonText: this.$t('btns.cancel'),
        type: 'info'
      }).then(() => {
        removeProducts([this.id]).then(() => {
          this.$tabDelete()
        })
      })
    },
    openGoodsDetailDialog() {
      this.visible = true
    },
    onSelect(option) {
      console.log('option: ', option)
      if (option.length) {
        this.form.sGoodsDetailName = option[0].name
        this.form.sGoodsDetailId = option[0].id
        this.form.sArtName = ''
        this.form.sArtId = ''
        this.visible = false
      } else {
        this.visible = false
      }
    },
    openArtNameDialog() {
      if (this.form.sGoodsDetailId) {
        this.visibleArtName = true
      } else {
        this.$message.warning('请先选择粗品名')
      }
    },
    onSelectArtName(option) {
      if (option.length > 0) {
        this.form.sArtName = option[0].sName
        this.form.sArtId = option[0].sId
        this.visibleArtName = false
      } else {
        this.visibleArtName = false
      }
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .input-with-select {
  .el-input__suffix {
    top: -3px;
  }
}
</style>
