<template>
  <div class="errPage-container flexVCC">
    <img width="400" height="400" src="../../assets/404_images/404.png" alt="">
    <el-button type="primary" icon="arrow-left" size="mini" @click="back"> {{$t('btns.return')}} </el-button>
  </div>
</template>

<script>
export default {
  name: 'Page404',
  data() {
    return {
    }
  },
  methods: {
    back() {
      if (this.$route.query.noGoBack) {
        this.$router.push({ path: '/navbar' })
      } else {
        this.$router.go(-1)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.errPage-container {
  height: 100%;
  width: 100%;
  text-align: center;
  button {
    width: 90px;
  }
  // img {
  //   width: 45%;
  // }
}
</style>
