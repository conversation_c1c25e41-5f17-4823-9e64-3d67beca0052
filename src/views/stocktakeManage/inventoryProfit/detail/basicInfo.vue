<template>
  <div>
    <cnd-btn-position top="6" right="10">
      <el-button
        v-has:esc_process_profit_modify
        type="primary"
        size="mini"
        :disabled="id && isBusinessDisabled('save', sSheetStatus)"
        @click="save"
      >{{ $t('btns.save') }}</el-button>
    </cnd-btn-position>
    <el-form
      ref="form"
      class="el-form-w100"
      label-width="110px"
      :inline="true"
      :model="form"
      :rules="rules"
      size="small"
      :disabled="id && isBusinessDisabled('save', sSheetStatus)"
    >
      <cnd-form-card-list :active-panel="activeCollapseName">
        <cnd-form-card :title="$t('grid.tabs.basicInformation')" name="1">
          <el-row>
            <cnd-form-item label="盘盈通知单" prop="sCode">
              <el-input v-model="form.sCode" disabled />
            </cnd-form-item>
            <cnd-form-item label="盘盈类型" prop="sInventoryType" :error-msg="rules.sInventoryType[0].message">
              <el-select v-model="form.sInventoryType" clearable>
                <el-option
                  v-for="item in selectOps['inventory.profit.type']"
                  :key="item.sCodeValue"
                  :label="item.sCodeName"
                  :value="item.sCodeValue"
                />
              </el-select>
            </cnd-form-item>
            <!-- <cnd-form-item label="关联单据号">
              <el-input v-model="form.vNoticePurArrivalCode" disabled />
            </cnd-form-item> -->
            <cnd-form-item :label="$t('grid.others.warehouse')" prop="sWarehouseName" :error-msg="rules.sWarehouseName[0].message">
              <horizon-search-select
                v-model="form.sWarehouseName"
                :default-url="`/esc/inventory/warehouse/${id}`"
                @change="handleChangeSelect($event, 'sWarehouseId')"
              />
            </cnd-form-item>
            <cnd-form-item :label="$t('grid.title.company')" prop="vCompanyName">
              <horizon-search-select
                v-model="form.vCompanyName"
                disabled
                type="company"
                :placeholder="$t('components.pleaseSelect')"
                @change="handleChangeSelect($event, 'sCompanyId')"
              />
            </cnd-form-item>
            <cnd-form-item :label="$t('grid.others.department')" prop="vDepartmentName">
              <horizon-search-select
                v-model="form.vDepartmentName"
                type="depart"
                disabled
                :placeholder="$t('components.pleaseSelect')"
                @change="handleChangeSelect($event, 'sDepartmentId')"
              />
            </cnd-form-item>
            <cnd-form-item :label="$t('grid.title.accountingGroup')">
              <horizon-search-select
                v-model="form.vCheckGroupName"
                disabled
                type="cost"
                :placeholder="$t('components.pleaseSelect')"
                @change="handleChangeSelect($event, 'sCheckGroupId')"
              />
            </cnd-form-item>
            <cnd-form-item :label="$t('grid.title.personnel')" prop="vStaffName">
              <horizon-search-select
                v-model="form.vStaffName"
                disabled
                type="staff"
                :placeholder="$t('components.pleaseSelect')"
                @change="handleChangeSelect($event, 'sStaffId')"
              />
            </cnd-form-item>
            <cnd-form-item label="预计入仓时间" prop="sPreInwarehouseDate" :error-msg="rules.sPreInwarehouseDate[0].message">
              <el-date-picker
                v-model="form.sPreInwarehouseDate"
                format="yyyy-MM-dd"
                type="date"
                placeholder="预计出仓时间"
              />
            </cnd-form-item>
            <cnd-form-item :custom-width="12" label="备注" prop="sRemark" :error-msg="rules.sRemark[0].message">
              <el-input v-model="form.sRemark" maxlength="500" type="textarea" :rows="2" />
            </cnd-form-item>
          </el-row>
        </cnd-form-card>
        <cnd-form-card :title="$t('grid.tabs.systemInformation')" name="2">
          <el-row>
            <cnd-form-item :label="$t('grid.title.createdBy')" prop="vCreatorName">
              <el-input v-model="form.vCreatorName" :disabled="true" clearable />
            </cnd-form-item>
            <cnd-form-item :label="$t('grid.title.createdAt')" prop="sCreateTime">
              <el-date-picker
                v-model="form.sCreateTime"
                format="yyyy-MM-dd HH:mm:ss"
                type="date"
                :disabled="true"
                :placeholder="$t('grid.title.createdAt')"
              />
            </cnd-form-item>
            <cnd-form-item :label="$t('grid.title.modifiedBy')" prop="vModifierName">
              <el-input v-model="form.vModifierName" :disabled="true" clearable />
            </cnd-form-item>
            <cnd-form-item :label="$t('grid.title.modifiedAt')" prop="sModifyTime">
              <el-date-picker
                v-model="form.sModifyTime"
                format="yyyy-MM-dd HH:mm:ss"
                type="date"
                :disabled="true"
                :placeholder="$t('grid.title.modifiedAt')"
              />
            </cnd-form-item>
            <cnd-form-item :label="$t('grid.title.status')" prop="sSheetStatus">
              <el-select v-model="form.sSheetStatus" disabled>
                <el-option
                  v-for="item in selectOps['dev.common.sheet.status']"
                  :key="item.sCodeValue"
                  :label="item.sCodeName"
                  :value="item.sCodeValue"
                />
              </el-select>
            </cnd-form-item>
          </el-row>
        </cnd-form-card>
      </cnd-form-card-list>
    </el-form>
  </div>
</template>

<script>
import businessMixin from '@/utils/businessMixin'
import {
  inventoryModify
} from '@/api/stocktakeManage/index.js'
import {
  getDictet
} from '@/api/logistics/saleDelivery/saleorder'
export default {
  mixins: [businessMixin],
  props: {
    id: {
      type: String,
      default: null
    },
    info: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      activeCollapseName: ['1', '2'],
      form: {},
      selectOps: {
        'dev.common.sheet.status': [],
        'inventory.profit.type': []
      },
      rules: {
        sInventoryType: [
          { required: false, message: this.$t('components.pleaseSelect'), trigger: 'change' }
        ],
        sWarehouseName: [
          { required: true, message: this.$t('components.pleaseSelect'), trigger: 'change' }
        ],
        sPreInwarehouseDate: [
          { required: true, message: this.$t('components.pleaseSelect'), trigger: 'change' }
        ],
        sRemark: [
          { required: false, message: this.$t('components.pleaseEnter'), trigger: 'change' }
        ]
      }
    }
  },
  computed: {
    sSheetStatus() {
      return this.info?.sSheetStatus
    }
  },
  watch: {
    info: {
      immediate: true,
      handler(val) {
        this.form = val
        this.$refs.form && this.$refs.form.resetFields()
      }
    }
  },
  beforeMount() {
    getDictet(['dev.common.sheet.status', 'inventory.profit.type']).then(result => {
      this.selectOps['dev.common.sheet.status'] = result.data[0].dicts
      this.selectOps['inventory.profit.type'] = result.data[1].dicts
    })
  },
  methods: {
    saveForm(fn) {
      this.$refs.form.validate((valid) => {
        if (valid) {
          fn && fn(true)
        } else {
          fn && fn(false)
          return false
        }
      })
    },
    save() {
      return new Promise((resolve, reject) => {
        this.$refs.form.validate(valid => {
          if (valid) {
            inventoryModify(this.form)
              .then(() => {
                this.$message.success('保存成功')
                this.$emit('success')
                resolve(true)
              }).catch(() => {
                reject(false)
              })
          } else {
            reject(false)
          }
        })
      })
    },
    handleChangeSelect(selected, val) {
      this.form[val] = selected ? selected.sId : undefined
    }
  }

}
</script>

<style scoped>

</style>
