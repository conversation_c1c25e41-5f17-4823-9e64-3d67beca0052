<!--
 * @Description: 商品明细
-->
<template>
  <div class="auto-page-title">
    <!-- <cnd-form-card-list :active-panel="activeCollapseName"> -->
    <!-- 基础信息 -->
    <!-- <cnd-form-card class="mb-10" title="合同商品" name="1"> -->
    <div class="btn-group">
      <div class="text">
        {{ $t('grid.others.contractGoodsDetails') }}
      </div>
      <div>
        <importBtn
          v-if="rowdataarr.length > 0"
          v-has:esc_oplan_goods_goods_import
          class="mr-10"
          :action="`/api/esc/order/plan/goods/import/${rowdataarr[0].sId}`"
          :btn-text="$t('excel.import')"
          :title="$t('excel.import')"
          :disabled="btnDisabled"
          @success="onSuccess"
        />
        <el-button
          v-has:esc_oplan_goods_goods_add
          type="primary"
          size="mini"
          :disabled="btnDisabled"
          @click="phyDialogVisibleadd = true"
        >{{ $t('btns.add') }}</el-button>
        <el-button
          type="danger"
          size="mini"
          :disabled="btnDisabled"
          @click="delGoods"
        >{{ $t('btns.delete') }}</el-button>
      </div>
    </div>
    <!-- 表格 -->
    <!-- <table-tool :id="id" ref="goodsTable" height="300" :is-edit="true" edit-type="fullRow" params-id-key="sContractId" :column-defs="goodsColumn" :req="getPlanGoodsDetail" /> -->
    <steelTradeAggrid
      ref="aggrid"
      :heightinif="300"
      :column-defs="goodsColumn"
      :row-data="rowData"
      :load-data="loadData"
      table-selection="single"
      row-key="sId"
      :header-total="headerCount"
      :footer-total="footerCount"
      @selectedChange="handleInvoiceFooterCount"
    />
    <!-- </cnd-form-card> -->
    <!-- 基础信息 -->
    <!-- <cnd-form-card class="mb-10" title="计划明细" name="2"> -->
    <div class="btn-group">
      <div class="text">
        <!-- 计划明细 -->
        {{ $t('grid.columns.planDetails') }}
      </div>
      <div>
        <!-- <el-button v-has:esc_oplan_goods_detail_split type="primary" size="mini" @click="splitVisible = true">拆分</el-button> -->
        <el-button
          v-has:esc_oplan_goods_detail_add
          type="primary"
          size="mini"
          :disabled="rowdataarr.length == 0 || isRead"
          @click="specMerger"
        >{{ $t('grid.others.combineBySpecification') }}</el-button>
        <el-button
          v-has:esc_oplan_goods_detail_add
          type="primary"
          size="mini"
          :disabled="rowdataarr.length == 0 || isRead"
          @click="phyDialogVisible = true"
        >{{ $t('btns.add') }}</el-button>
        <el-button
          v-has:esc_oplan_goods_detail_delete
          type="danger"
          size="mini"
          :disabled="rowdataarr.length == 0 || isRead"
          @click="deletePlan"
        >{{ $t('btns.delete') }}</el-button>
      </div>
    </div>
    <!-- 表格 -->
    <steelTradeAggrid
      ref="aggrids"
      :heightinif="300"
      :column-defs="planColumn"
      :row-data="rowDatas"
      :load-data="loadDatas"
      :auto-load-data="false"
      table-selection="multiple"
      row-key="sId"
      :full-row-type="fullRowType"
      :header-total="headerCounts"
      :footer-total="footerCounts"
      @selectedChange="handleFooterCount"
      @rowValueChanged="rowValueChanged"
    />
    <!-- 核算组弹出层 -->
    <horizon-cost-dialog
      :visible="sSupplierIdVisible"
      :append-to-body="true"
      @onSelect="onSelCost"
    />
    <el-dialog
      :title="$t('grid.others.split')"
      :visible.sync="splitVisible"
      width="20%"
      :append-to-body="true"
    >
      <el-form ref="form">
        <el-row>
          <cnd-form-item
            :custom-width="24"
            :label="$t('grid.others.splitQuantity')"
            prop="splitnum"
          >
            <el-input
              v-model="splitnum"
              type="number"
              :placeholder="$t('grid.others.pleaseFillInTheQuantity')"
              clearable
            />
          </cnd-form-item>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button
          size="small"
          type="primary"
          :disabled="!splitnum && splitnum !== 0"
          @click="splitPlan"
        >{{ $t('btns.confirm') }}</el-button>
        <el-button size="small" @click="splitVisible = false">{{
          $t('btns.cancel')
        }}</el-button>
      </div>
    </el-dialog>
    <phyInvalidDialog
      v-if="phyDialogVisible"
      :invoice-id="inids"
      :data="rowdataarr[0]"
      :type="type"
      :dialog-visible="phyDialogVisible"
      @close="closePhyInvalidDialog"
    />
    <editGoodaddcon
      v-if="phyDialogVisibleadd"
      :invoice-id="inids"
      :datagetp="datagetp"
      :data="rowdataarr[0]"
      :type="type"
      :dialog-visible="phyDialogVisibleadd"
      @close="closePhyInvalidDialogs"
    />
  </div>
</template>

<script>
import Vue from 'vue'
import { SteelFormat } from 'cnd-horizon-utils'
import { Middleware } from 'cndinfo-ui'
import {
  getPlanGoodsDetail,
  planGoodsModifys,
  getPlanGoodsPage,
  addPlanGoodsModify,
  removesPlanGoodsModify,
  planGoodsAutoModifys,
  deldetailplan,
  getPlanDetail,
  goodsSplit
} from '@/api/contract'
import importBtn from '@/components/importBtn'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import phyInvalidDialog from './phyInvalidDialog'
import editGoodaddcon from './EditGoodaddcon'
export default {
  name: 'EditGoods',
  components: { importBtn, steelTradeAggrid, phyInvalidDialog, editGoodaddcon },
  props: {
    type: {
      type: String,
      default: ''
    },
    id: {
      type: String,
      default: ''
    },
    isRead: {
      type: String,
      default: 'fasle'
    },
    info: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      splitnum: '',
      rowdataarr: [{ sId: null }],
      invoiceIndex: 0,
      rowdataarrs: [],
      rowDatas: [],
      splitVisible: false,
      phyDialogVisibleadd: false,
      sSupplierIdVisible: false,
      activeCollapseName: ['1', '2'],
      rowData: [],
      // 合同商品
      goodsColumn: [
        {
          field: 'sProjectId',
          headerName: this.$t('grid.others.itemNumberId'),
          hide: true
        },
        {
          field: 'sProjectCode',
          headerName: this.$t('grid.others.itemNumberTag')
          // cellStyle: { textAlign: 'left' },
          // cellRenderer: 'dialogRenderer',
          // cellEditor: 'dialogEditor',
          // meta: {
          //   dialog: 'project',
          //   relationIdFiled: 'sProjectId'
          // }
        },
        {
          field: 'sPurContractCode',
          headerName: this.$t('grid.title.purchaseContractNumber')
        },
        {
          field: 'sSaleContractCode',
          headerName: this.$t('grid.title.salesContractNumber')
        },
        {
          field: 'sGoodsDesc',
          headerName: this.$t('grid.others.item'),
          cellStyle: { textAlign: 'left' }
        },
        {
          field: 'vLeftQty',
          headerName: this.$t('grid.others.remainingQuantity'),
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            // vTaxAmt/vNetAmt
            return SteelFormat.formatThousandthSign(params.value, 4)
          }
        },
        {
          headerName: this.$t('grid.others.quantityConfirmedThisTime'),
          field: 'sContractQty',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            // vTaxAmt/vNetAmt
            return SteelFormat.formatThousandthSign(params.value, 4)
          }
        },
        {
          field: 'sPrice',
          headerName: this.$t('grid.title.unitPrice'),
          valueFormatter: (params) => {
            // vTaxAmt/vNetAmt
            return SteelFormat.formatPrice(params.value)
          },
          cellStyle: { textAlign: 'right' }
          //
          // meta: {
          //   type: 'money'
          // },
        },
        {
          field: 'sTaxAmt',
          headerName: this.$t('grid.title.amountWithTax'),
          valueFormatter: (params) => {
            // vTaxAmt/vNetAmt
            return SteelFormat.formatPrice(params.value)
          },
          cellStyle: { textAlign: 'right' }
        },
        {
          field: 'sCheckGroupId',
          headerName: this.$t('grid.others.accountingGroupId'),
          hide: true
        },
        {
          field: 'vCheckGroupName',
          headerName: this.$t('grid.title.accountingGroup'),
          cellStyle: { textAlign: 'left' }
        },
        {
          field: 'vStaffNameId',
          headerName: this.$t('grid.others.personId'),
          hide: true
        },
        {
          field: 'vStaffName',
          headerName: this.$t('grid.title.personnel')
        }
      ],
      // 计划明细
      planColumn: [
        {
          field: 'sProjectId',
          headerName: this.$t('grid.others.itemNumberId'),
          hide: true
        },
        {
          field: 'sProjectCode',
          headerName: this.$t('grid.others.itemNumberTag')
        },
        {
          field: 'sPurContractCode',
          headerName: this.$t('grid.title.purchaseContractNumber'),
          cellStyle: { textAlign: 'left' }
        },
        {
          field: 'sSaleContractCode',
          headerName: this.$t('grid.title.salesContractNumber'),
          cellStyle: { textAlign: 'left' }
        },
        {
          field: 'vArtName',
          headerName: this.$t('grid.others.item'),
          cellStyle: { textAlign: 'left' }
        },
        {
          headerName: this.$t('grid.title.quantity'),
          field: 'sContractQty',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return SteelFormat.formatThousandthSign(params.value, 4)
          },
          editable: () => {
            return !this.isRead
          },
          cellEditorFramework: Vue.extend(
            Middleware.createComponent('CndInputNumber', {
              mark: 'sContractQty',
              type: 'number',
              decimalDigit: 4,
              autoFocus: true,
              focusSelect: true
            })
          )
        },
        {
          headerName: this.$t('grid.others.numberOfPiecesTag'),
          field: 'sQty',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return SteelFormat.formatThousandthSign(params.value)
          },
          editable: () => {
            return !this.isRead
          },
          cellEditorFramework: Vue.extend(
            Middleware.createComponent('CndInputNumber', {
              mark: 'sQty',
              type: 'number',
              decimalDigit: 0,
              focusSelect: true
            })
          )
        },
        {
          headerName: this.$t('grid.title.unitPrice'),
          field: 'sPrice',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return SteelFormat.formatPrice(params.value)
          },
          editable: () => {
            return !this.isRead
          },
          cellEditorFramework: Vue.extend(
            Middleware.createComponent('CndInputNumber', {
              mark: 'sPrice',
              type: 'number',
              decimalDigit: 2,
              focusSelect: true
            })
          )
        },
        {
          field: 'sTaxAmt',
          headerName: this.$t('grid.title.amount'),
          valueFormatter: (params) => {
            // vTaxAmt/vNetAmt
            return SteelFormat.formatPrice(params.value)
          },
          cellStyle: { textAlign: 'right' }
        }
      ],
      phyDialogVisible: false,
      inids: '',
      datagetp: {},
      headerCount: null,
      footerCount: null,
      headerCounts: null,
      footerCounts: null
    }
  },
  computed: {
    fullRowType() {
      return !this.isRead ? 'parent' : null
    },
    btnDisabled() {
      return +this.info.sSheetVersion > 0 || this.isRead
    }
  },
  mounted() {
    this.inids = this.id
    getPlanDetail({
      id: this.id
    }).then((res) => {
      this.datagetp = res.data
    })
  },
  methods: {
    onSuccess(res) {
      if (res) {
        this.$refs.aggrid.loadTableData()
      }
    },
    setCount(vCount = 0, vSumQtx = 0, vSumAmt = 0, flag) {
      this[flag] = [
        flag === 'footerCount'
          ? { title: this.$t('pagination.total'), count: vCount, key: 'count' }
          : { count: vCount, key: 'count' },
        {
          title: this.$t('grid.title.quantity'),
          count: SteelFormat.formatThousandthSign(vSumQtx, 4),
          unit: this.$t('grid.others.ton')
        },
        {
          title: this.$t('grid.title.amount'),
          count: SteelFormat.formatPrice(vSumAmt),
          unit: this.$t('grid.others.yuan')
        }
      ]
    },
    getPlanGoodsDetail,
    closePhyInvalidDialog() {
      this.phyDialogVisible = false
      this.$refs.aggrids.loadTableData()
    },
    closePhyInvalidDialogs() {
      this.invoiceIndex = 0
      this.phyDialogVisibleadd = false
      this.$refs.aggrid.loadTableData()
    },
    saveGoods() {
      const $goodsTable = this.$refs.goodsTable
      const rowData = $goodsTable.getRowData()
      rowData.map((item) => {
        item['sOrderPlanId'] = this.id
      })
      $goodsTable.gridApi.clearFocusedCell()
      planGoodsModifys(rowData).then((res) => {
        if (res.code === '0000') {
          this.$message.success(this.$t('grid.others.successfulOperation'))
          $goodsTable.query()
        }
      })
    },
    // 拆分
    splitPlan() {
      const $planTable = this.$refs.planTable
      goodsSplit({
        qty: this.splitnum
      }).then((res) => {
        if (res.code === '0000') {
          this.$message.success(this.$t('grid.others.successfulOperation'))
          $planTable.query()
        }
        this.splitVisible = false
      })
    },
    handleFooterCount(rowData) {
      const details = rowData.filter((item) => item._selected)
      const vCount = details.length
      let sQtxcount = 0
      let vAmtcount = 0
      this.rowDatas.map((item) => {
        if (item._selected) {
          sQtxcount += item.sContractQty
          vAmtcount += item.sTaxAmt
        }
      })
      this.setCount(vCount, sQtxcount, vAmtcount, 'footerCounts')
    },
    handleInvoiceFooterCount(rowData) {
      this.rowdataarr = []
      const invoiceIndex = rowData.findIndex((item) => item._selected)
      this.invoiceIndex = invoiceIndex
      this.rowData.map((item) => {
        if (item._selected === true) {
          this.rowdataarr.push(item)
        }
      })
      this.$refs.aggrids.reloadTableData()
      // 选中后回掉
    },
    loadData(pagination) {
      return new Promise((resolve, reject) => {
        getPlanGoodsDetail({ sContractId: this.id, ...pagination })
          .then((res) => {
            this.rowData = res.data.content.map((item, index) => {
              item._selectedKeys = []
              item._selected = index === this.invoiceIndex
              // if (_this.rowdataarr.sId === item.sId) {
              //   item._selected = true
              // } else {
              //   item._selected = index === 0
              // }
              item.dtlVos = []
              return item
            })
            const details = this.rowData
            const vCount = details.length
            let sQtxcount = 0
            let vAmtcount = 0
            this.rowData.map((item) => {
              sQtxcount += item.sContractQty
              vAmtcount += item.sTaxAmt
            })
            this.setCount(vCount, sQtxcount, vAmtcount, 'footerCount')
            resolve(res.data)
          })
          .catch(() => {
            this.totalRowData = null
            reject(0)
          })
      })
    },
    loadDatas(pagination) {
      return new Promise((resolve, reject) => {
        getPlanGoodsPage({ sContractId: this.rowdataarr[0].sId, ...pagination })
          .then((res) => {
            this.rowDatas = res.data.goodsList.content.map((item) => {
              item._selected = false
              item._selectedKeys = []
              // item.dtlVos = []
              return item
            })
            const { vCount, vSumQty, vSumAmt } = res.data
            this.setCount(vCount, vSumQty, vSumAmt, 'headerCounts')
            resolve(res.data.goodsList)
          })
          .catch(() => {
            this.totalRowData = null
            reject(0)
          })
      })
    },
    savePlan() {
      const $planTable = this.$refs.planTable
      const rowData = $planTable.getRowData()
      rowData.map((item) => {
        item['sOrderPlanId'] = this.id
      })
      addPlanGoodsModify(rowData).then((res) => {
        if (res.code === '0000') {
          this.$message.success(this.$t('grid.others.successfulOperation'))
          $planTable.query()
        }
      })
    },
    rowValueChanged(params) {
      const { sPrice, sContractQty } = params.data
      params.data.sTaxAmt = sPrice * sContractQty
      addPlanGoodsModify([params.data])
        .then(() => {
          this.$refs.aggrids.reloadTableData()
          this.$refs.aggrid.reloadTableData()
        })
        .catch(() => {
          this.$refs.aggrids.reloadTableData()
        })
    },
    delGoods() {
      const delarr = []
      const vm = this
      this.rowData.map((item) => {
        if (item._selected === true) {
          delarr.push(item.sId)
        }
      })
      if (delarr.length === 0) {
        this.$message({
          message: this.$t('grid.others.pleaseSelectAData'),
          type: 'warning'
        })
        return
      }
      this.$confirm(
        this.$t('grid.others.thisActionDeletesThntinueKey'),
        this.$t('grid.others.prompt'),
        {
          confirmButtonText: this.$t('btns.confirm'),
          cancelButtonText: this.$t('btns.cancel'),
          type: 'warning'
        }
      ).then(() => {
        deldetailplan(delarr)
          .then((result) => {
            this.$message({
              message: this.$t('tips.deletedSuccessfully'),
              type: 'success'
            })
            vm.$refs.aggrid.loadTableData()
          })
          .catch(() => {})
      })
    },
    specMerger() {
      planGoodsAutoModifys({
        sOrderPlanDetailId: this.rowdataarr[0].sId,
        sOrderPlanId: this.id
      }).then((res) => {
        this.$message.success(this.$t('grid.others.mergeSuccessfully'))
        this.$refs.aggrids.reloadTableData()
      })
    },
    // 删除
    deletePlan() {
      const delarr = []
      const vm = this
      this.rowDatas.map((item) => {
        if (item._selected === true) {
          delarr.push(item.sId)
        }
      })
      if (delarr.length === 0) {
        this.$message({
          message: this.$t('grid.others.pleaseSelectAData'),
          type: 'warning'
        })
        return
      }
      this.$confirm(
        this.$t('grid.others.thisActionDeletesThntinueKey'),
        this.$t('grid.others.prompt'),
        {
          confirmButtonText: this.$t('btns.confirm'),
          cancelButtonText: this.$t('btns.cancel'),
          type: 'warning'
        }
      ).then(() => {
        removesPlanGoodsModify(delarr)
          .then((result) => {
            this.$message({
              message: this.$t('tips.deletedSuccessfully'),
              type: 'success'
            })
            vm.$refs.aggrids.reloadTableData()
            vm.$refs.aggrid.reloadTableData()
          })
          .catch(() => {})
      })
    }
  }
}
</script>
<style lang="scss" scoped></style>
