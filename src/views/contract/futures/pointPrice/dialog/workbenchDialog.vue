
<template>
  <cnd-dialog
    title="新增点价平仓单"
    append-to-body
    width="80%"
    height="70vh"
    :fullscreen="false"
    :visible.sync="dialogVisible"
    @close="handleClose"
  >
    <template v-if="dialogVisible" slot="content">
      <steel-search-form ref="searchForm" :form-items="formItems" @search="onSearch" />
      <auto-wrap class="mt-10">
        <steelTradeAggrid
          ref="aggrid"
          :column-defs="columnDefs"
          :row-data="rowData"
          :load-data="loadData"
          :header-total="headerCount"
          :footer-total="footerCount"
          :auto-load-data="false"
          row-key="sId"
          full-row-type="parent"
          table-selection="multiple"
          @selectedChange="handleSelectedChange"
          @rowValueChanged="handleRowValueChanged"
          @cellValueChanged="handleCellValueChanged"
        />
      </auto-wrap>
      <saleCodeDialog
        :id="selectId"
        :dialog-visible.sync="saleDialogVisible"
        @onSuccess="onSuccess"
      />
    </template>
    <template slot="footer" class="dialog-footer">
      <el-button size="mini" @click="handleClose">{{ $t('btns.cancel') }}</el-button>
      <el-button type="primary" size="mini" @click="handleSelect">{{ $t('btns.confirm') }}</el-button>
    </template>
  </cnd-dialog>
</template>

<script>
var Decimal = window.Decimal
import Vue from 'vue'
import { Middleware } from 'cndinfo-ui'
import { SteelFormat } from 'cnd-horizon-utils'
import { Moment } from 'cnd-utils'
import {
  pointPriceWbPage,
  pointPriceCreate,
  profitLossDetailAdd
} from '@/api/logistics/pointPrice.js'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import saleCodeDialog from './saleCodeDialog.vue'
export default {
  components: { steelTradeAggrid, saleCodeDialog },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    sId: {
      type: String,
      default: null
    },
    info: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      searchInfo: null,
      formItems: [
        {
          label: this.$t('grid.others.itemNumberTag'),
          value: 'sProjectCode',
          type: 'elInput',
          placeholder: this.$t('grid.others.pleaseEnterTheItemNumberTag')
        },
        {
          label: this.$t('grid.title.purchaseContractNumber'),
          value: 'sPurContractCode',
          type: 'elInput',
          placeholder: this.$t('grid.others.enterPurchaseContractNumber')
        },
        // {
        //   label: this.$t('grid.title.salesContractNumber'),
        //   value: 'sSaleContractCode',
        //   type: 'elInput',
        //   placeholder: this.$t('grid.others.pleaseEnterTheSalesContractNumber')
        // },
        {
          label: this.$t('grid.others.customer'),
          value: 'sCustomerId',
          type: 'cndInputDialogItem',
          dialogType: 'customer',
          defaultUrl: '/esc/customer/page',
          option: { valueKey: 'sPath' },
          default: this.sId ? this.info.sCustomerId : null,
          labelDefault: this.sId ? this.info.vCustomerName : null,
          disabled: this.sId
        },
        {
          label: this.$t('grid.title.company'),
          value: 'sCompanyId',
          type: 'cndInputDialog',
          dialogType: 'company',
          default: this.sId ? this.info.sCompanyId : null,
          labelDefault: this.sId ? this.info.vCompanyName : null,
          disabled: this.sId,
          placeholder: this.$t('grid.others.pleaseSelectCompany')
        },
        {
          label: this.$t('grid.title.accountingGroup'),
          value: 'sCheckGroupId',
          type: 'cndInputDialog',
          dialogType: 'cost',
          placeholder: this.$t('grid.others.pleaseSelectAccountingGroup')
        },
        {
          label: this.$t('grid.title.personnel'),
          value: 'sStaffId',
          type: 'cndInputDialogItem',
          option: { valueKey: 'sPath' },
          dialogType: 'staff',
          placeholder: this.$t('grid.others.pleaseSelectPersonnel')
        },

        {
          label: this.$t('grid.title.createdBy'),
          value: 'sCreator',
          type: 'cndInputDialog',
          dialogType: 'creater',
          placeholder: this.$t('grid.others.pleaseSelectTheCreator'),
          itemType: 'occultation'
        },
        {
          label: this.$t('grid.others.accountingFilter'),
          value: 'vIsCor',
          type: 'elSelect',
          default: '0',
          itemType: 'occultation',
          dict: 'dev.common.verify.finish.type',
          placeholder: this.$t('grid.others.pleaseSelectTheUnderwritingFilter')
        },
        {
          label: this.$t('grid.title.createdAt'),
          value: ['sCreateTime', 'vCreateTimeTo'],
          type: 'elDatePicker',
          unlinkPanels: true,
          itemType: 'occultation'
        }
        // {
        //   label: '经营单位',
        //   value: 'sManagementId',
        //   type: 'cndInputDialog',
        //   dialogType: 'escOrg',
        //   itemType: 'occultation'
        // }
      ],
      columnDefs: [
        {
          field: 'sProjectCode',
          headerName: this.$t('grid.others.itemNumberTag')
        },
        {
          field: 'sPurContractCode',
          headerName: this.$t('grid.title.purchaseContractNumber')
        },
        // {
        //   field: 'sSaleContractCode',
        //   headerName: this.$t('grid.title.salesContractNumber')
        // },
        {
          field: 'vCompanyName',
          headerName: this.$t('grid.title.company')
        },
        {
          field: 'sGoodsDetailName',
          headerName: this.$t('grid.others.item')
        },
        {
          field: 'sFuturesTradeNum',
          headerName: '期货成交数量',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return SteelFormat.formatThousandthSign(params.value, 4)
          }
        },
        {
          field: 'sOperateNum',
          headerName: '可操作数量',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return SteelFormat.formatThousandthSign(params.value, 4)
          }
        },
        {
          field: 'sClosePositionNum',
          headerName: '已平仓数量',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return SteelFormat.formatThousandthSign(params.value, 4)
          }
        },
        {
          field: 'sSurplusNum',
          headerName: '剩余数量',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return SteelFormat.formatThousandthSign(params.value, 4)
          }
        },
        {
          headerName: '本次平仓数量',
          field: 'sThisClosePositionNum',
          editable: true,
          cellStyle: { textAlign: 'right' },
          cellEditorFramework: Vue.extend(
            Middleware.createComponent(
              'CndInputNumber',
              {
                mark: 'sThisClosePositionNum',
                type: 'number',
                decimalDigit: 4
              },
              {
                blur: ({ event, rowData, middleware }) => {
                  console.log('event: ', event)
                  this.$refs.aggrid.gridApi.refreshCells(rowData)
                }
              }
            )
          ),
          valueFormatter: params => {
            return SteelFormat.formatThousandthSign(params.value, 4)
          }
        },
        {
          headerName: '开仓单价',
          field: 'sOpenPositionPrice',
          editable: true,
          cellStyle: { textAlign: 'right' },
          cellEditorFramework: Vue.extend(
            Middleware.createComponent(
              'CndInputNumber',
              {
                mark: 'sOpenPositionPrice',
                type: 'number',
                decimalDigit: 2
              },
              {
                blur: ({ event, rowData, middleware }) => {
                  console.log('event: ', event)
                  this.$refs.aggrid.gridApi.refreshCells(rowData)
                }
              }
            )
          ),
          valueFormatter: params => {
            return SteelFormat.formatPrice(params.value)
          }
        },
        {
          headerName: '平仓单价',
          field: 'sClosePositionPrice',
          editable: true,
          cellStyle: { textAlign: 'right' },
          cellEditorFramework: Vue.extend(
            Middleware.createComponent(
              'CndInputNumber',
              {
                mark: 'sClosePositionPrice',
                type: 'number',
                decimalDigit: 2
              },
              {
                blur: ({ event, rowData, middleware }) => {
                  console.log('event: ', event)
                  this.$refs.aggrid.gridApi.refreshCells(rowData)
                }
              }
            )
          ),
          valueFormatter: params => {
            return SteelFormat.formatPrice(params.value)
          }
        },
        {
          field: 'vCustomerName',
          headerName: this.$t('grid.others.customer')
        },
        {
          field: 'vCheckGroupName',
          headerName: this.$t('grid.title.accountingGroup')
        },
        {
          field: 'vStaffName',
          headerName: this.$t('grid.title.personnel')
        },
        {
          field: 'sCreateTime',
          headerName: this.$t('grid.title.createdAt'),
          minWidth: 150,
          valueGetter(params) {
            return Moment.time('YYYY-MM-DD HH:mm:ss', params.data.sCreateTime)
          }
        },
        {
          field: 'vCreatorName',
          headerName: this.$t('grid.title.createdBy')
        }
      ],
      headerCount: null,
      footerCount: null,
      rowData: [],
      saleDialogVisible: false,
      selectId: null,
      profitLossWbList: []
    }
  },
  watch: {
    dialogVisible(val) {
      if (val) {
        this.$nextTick(() => {
          this.onSearch()
        })
      }
    }
  },
  created() {
  },
  methods: {
    onSearch() {
      const searchInfo = this.$refs.searchForm.getSearchData()
      this.searchInfo = searchInfo
      this.$refs.aggrid.loadTableData()
    },
    setCount(vCount = 0, leftqty = 0, currentqty = 0, flag) {
      this[flag] = [
        { count: vCount, key: 'count' },
        { title: '剩余数量', count: SteelFormat.formatThousandthSign(leftqty, 4), unit: this.$t('grid.others.ton') }
      ]
      if (flag === 'footerCount') {
        this[flag].push(
          { title: '本次平仓数量', count: SteelFormat.formatThousandthSign(currentqty, 4), unit: this.$t('grid.others.ton') }
        )
      }
    },
    loadData(pagination) {
      return new Promise((resolve, reject) => {
        pointPriceWbPage(
          this.searchInfo,
          pagination
        ).then(res => {
          console.log(res)
          this.rowData = res.data.list.content.map(item => {
            item._selected = false
            item._selectedKeys = []
            return item
          })
          const { count, sSumSurplusNum } = res.data
          this.setCount(count, sSumSurplusNum, 0, 'headerCount')
          resolve(res.data.list)
        }).catch(() => {
          reject([])
        })
      })
    },
    handleSelectedChange(rowData) {
      this.$refs.aggrid.getSelectedData(res => {
        if (res.length) {
          this.delDisable = !res.every(val => val.sSheetStatus === '10')
        }
        const vCount = res.length
        let sSurplusNum = new Decimal(0)
        let sThisClosePositionNum = new Decimal(0)
        res.forEach(el => {
          sSurplusNum = sSurplusNum.add(el.sSurplusNum)
          sThisClosePositionNum = sThisClosePositionNum.add(el.sThisClosePositionNum)
        })
        this.setCount(vCount, sSurplusNum, sThisClosePositionNum, 'footerCount')
      })
    },
    rowValueChanged(params) {
      console.log('params: ', params)
      const { data, rowIndex } = params
      this.rowData[data.parentId].details[rowIndex] = data
    },
    handleClose() {
      this.$emit('close')
    },

    handleSelect() {
      this.$refs.aggrid.gridApi.stopEditing()
      this.$refs.aggrid.getSelectedData(res => {
        if (!res.length) {
          this.$message.warning(this.$t('grid.others.pleaseSelectData'))
          return false
        }
        if (this.sId) {
          profitLossDetailAdd({
            sId: this.sId,
            sSaleContractId: this.info.sSaleContractId,
            profitLossWbList: res
          }).then(v => {
            this.$message.success('新增成功')
            this.handleClose()
          })
        } else {
          this.selectId = res[0].sPurContractId
          this.profitLossWbList = res
          this.saleDialogVisible = true
          console.log('this.saleDialogVisible: ', this.saleDialogVisible)
        }
      })
    },

    onSuccess({ sSaleContractId }) {
      pointPriceCreate({
        sSaleContractId: sSaleContractId,
        profitLossWbList: this.profitLossWbList
      }).then(v => {
        this.$message.success('生成成功')
        this.$router.push({
          path: `/pointPriceDetail/${v.data.sId}`,
          query: {
            Id: v.data.sId,
            status: v.data.sSheetStatus,
            type: 'edit',
            name: `点价平仓单【${v.data.sCode}】`,
            activeId: localStorage.getItem('menuId')
          }
        })
        this.handleClose()
      })
    }
  }
}
</script>
