<!--
 * @Description: 结算信息
-->
<template>
  <div>
    <cnd-btn-position top="7" right="10">
      <el-button
        v-if="showCycleRepayTerms"
        type="primary"
        size="mini"
        @click="viewCycleRepayTerms"
      >查看周期回款条款</el-button>
    </cnd-btn-position>
    <cnd-form-card-list :active-panel="activeCollapseName">
      <!-- 结算方式 -->
      <cnd-form-card
        class=""
        :title="$t('grid.title.settlementMethod')"
        name="1"
      >
        <!-- 表格 -->
        <!-- <table-tool
          v-if="initsts"
          :id="id"
          ref="tableTool"
          height="300"
          :has-pages="false"
          params-id-key="sContractId"
          :column-defs="settlementMethodColumn"
          :req="getSettlementDetail"
        /> -->
        <steelTradeAggrid
          ref="tableTool"
          :heightinif="300"
          :column-defs="settlementMethodColumn"
          :row-data="rowData"
          :load-data="loadData"
          :auto-load-data="false"
          row-key="sId"
        />
      </cnd-form-card>
      <!-- 结算条款 -->
      <cnd-form-card
        :title="$t('grid.others.settlementTerms')"
        name="2"
      >
        <el-form
          ref="form"
          class="el-form-w100 pt-10 pr-10 pl-10"
          label-width="113px"
          :disabled="true"
          :inline="true"
          :model="form"
          size="small"
        >
          <el-row>
            <cnd-form-item
              v-show="!showFullAmount"
              :label="$t('grid.others.numberOfDaysUnderwritten')"
              prop="sMinDays"
            >
              <el-input
                v-model="form.sMinDays"
                :disabled="true"
                clearable
              />
            </cnd-form-item>
            <cnd-form-item
              :label="$t('grid.others.settlementDays')"
              prop="sPayDays"
            >
              <el-input
                v-model="form.sPayDays"
                clearable
              />
            </cnd-form-item>
            <cnd-form-item
              v-show="form.sIsCashRt === '0'"
              :label="$t('grid.tips.doesItInvolveCashBackInterest')"
              prop="sIsCashRt"
            >
              <!-- <el-input
                v-model="form.sIsCashRt"
                clearable
              /> -->
              <el-select v-model="form.sIsCashRt" filterable :placeholder="$t('components.pleaseSelect')">
                <el-option
                  v-for="item in options['base.yes-no']"
                  :key="item.sCodeValue"
                  :label="item.sCodeName"
                  :value="item.sCodeValue"
                />
              </el-select>
            </cnd-form-item>
            <!-- <cnd-form-item
              v-show="form.sIsCashRt === '1'"
              :label="this.$t('grid.others.cashRebateMethod')"
              prop="sCashRtType"
            >
              <el-select v-model="form.sCashRtType" filterable :placeholder="$t('components.pleaseSelect')">
                <el-option
                  v-for="item in options['trade.irt.return.type']"
                  :key="item.sCodeValue"
                  :label="item.sCodeName"
                  :value="item.sCodeValue"
                />
              </el-select>
            </cnd-form-item> -->

            <cnd-form-item
              v-show="form.sIsCashRt === '1' && form.sCashRtType==='20'"
              :label="$t('grid.others.cashRefundRateMonthlyInterest')"
              prop="sCashRtRate"
            >
              <cnd-input-number v-model="form.sCashRtRate" :disabled="true" type="percent" clearable />
              <!-- <el-input
                v-model="form.sCashRtRate"
                :disabled="true"
                clearable
              /> -->
            </cnd-form-item>
            <cnd-form-item
              v-show="form.sIsCashRt === '1' && form.sCashRtType==='10'"
              :label="$t('grid.others.singleTonInterestRebate')"
              prop="sCashSignalRate"
            >
              <el-input
                v-model="form.sCashSignalRate"
                clearable
              />
            </cnd-form-item>
            <cnd-form-item
              v-show="form.sIsAcceptRt === '0'"
              :label="$t('grid.tips.doesItInvolveThePromissoryNote')"
              prop="sIsAcceptRt"
            >
              <el-select v-model="form.sIsAcceptRt" filterable :placeholder="$t('components.pleaseSelect')">
                <el-option
                  v-for="item in options['base.yes-no']"
                  :key="item.sCodeValue"
                  :label="item.sCodeName"
                  :value="item.sCodeValue"
                />
              </el-select>
            </cnd-form-item>
            <!-- <cnd-form-item
              v-show="form.sIsAcceptRt === '1'"
              :label="$t('grid.others.methodOfDiscounting')"
              prop="sAcceptRtType"
            >
              <el-select v-model="form.sAcceptRtType" filterable :placeholder="$t('components.pleaseSelect')">
                <el-option
                  v-for="item in options['trade.irt.return.type']"
                  :key="item.sCodeValue"
                  :label="item.sCodeName"
                  :value="item.sCodeValue"
                />
              </el-select>
            </cnd-form-item> -->

            <cnd-form-item
              v-show="form.sIsAcceptRt === '1' && form.sAcceptRtType==='20'"
              :label="$t('grid.others.acceptanceDiscountRnterestKey')"
              prop="sAcceptRtRate"
            >
              <!-- <el-input
                v-model="form.sAcceptSignalRate"
                :disabled="true"
                clearable
              /> -->
              <cnd-input-number v-model="form.sAcceptRtRate" :disabled="true" type="percent" clearable />

            </cnd-form-item>
            <cnd-form-item
              v-show="form.sIsAcceptRt === '1' && form.sAcceptRtType==='10'"
              :label="$t('grid.others.singleTonDiscount')"
              prop="sContractConfirmCode"
            >
              <el-input
                v-model="form.sAcceptSignalRate"
                clearable
              />
            </cnd-form-item>
            <template v-if="!showFullAmount">
              <cnd-form-item
                :label="$t('grid.others.interestStartingStandard')"
              >
                <el-select v-model="form.sInterestStart" :placeholder="$t('components.pleaseSelect')" clearable>
                  <el-option
                    v-for="item in options['esc.settle.interest.start']"
                    :key="item.sCodeValue"
                    :label="item.sCodeName"
                    :value="item.sCodeValue"
                  />
                </el-select>
              </cnd-form-item>
              <cnd-form-item
                v-if="showStopIrtType"
                label="止息方式"
              >
                <el-select v-model="form.sStopIrtType" :placeholder="$t('components.pleaseSelect')">
                  <el-option
                    v-for="item in options['esc.stopirt.type']"
                    :key="item.sCodeValue"
                    :label="item.sCodeName"
                    :value="item.sCodeValue"
                  />
                </el-select>
              </cnd-form-item>
            </template>
            <template v-if="showFullAmount">
              <cnd-form-item label="商业折让方式" prop="sRebateType">
                <el-select v-model="form.sRebateType" disabled :placeholder="$t('components.pleaseSelect')">
                  <el-option
                    v-for="item in options['esc.rebate.type']"
                    :key="item.sCodeValue"
                    :label="item.sCodeName"
                    :value="item.sCodeValue"
                  />
                </el-select>
              </cnd-form-item>
              <cnd-form-item label="锁定一票制" prop="sIsLockOne">
                <el-select v-model="form.sIsLockOne" disabled :placeholder="$t('components.pleaseSelect')">
                  <el-option
                    v-for="item in options['base.yes-no']"
                    :key="item.sCodeValue"
                    :label="item.sCodeName"
                    :value="item.sCodeValue"
                  />
                </el-select>
              </cnd-form-item>
            </template>
          </el-row>
        </el-form>
      </cnd-form-card>
      <!--  结算条款明细 -->
      <cnd-form-card
        :title="$t('grid.others.settlementTermsDetail')"
        name="3"
      >
        <steelTradeAggrid
          v-if="initsts"
          ref="aggrid"
          :heightinif="300"
          :column-defs="settlementDetailColumn"
          :row-data="rowDataSettlementDetail"
          :load-data="loadDataSettlementDetail"
          :auto-load-data="false"
          row-key="sId"
        />
      </cnd-form-card>
    </cnd-form-card-list>
    <cycleRepayTermsDialog
      v-if="dialogVisible"
      :id="id"
      :dialog-visible="dialogVisible"
      @close="()=>dialogVisible=false"
    />
  </div>
</template>

<script>
import {
  getSettlementDetail,
  getSaleSettleClause,
  getSaleSettleClauseDetail
} from '@/api/contract'
import TableTool from '@/components/TableTool/TableTool.vue'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import { SteelFormat } from 'cnd-horizon-utils'
import { Moment } from 'cnd-utils'
import { getDictList, getCnDitc } from '@/utils/dict'
import cycleRepayTermsDialog from './cycleRepayTermsDialog'
export default {
  name: 'DomesticSalesEditSettlementDetail',
  components: { TableTool, steelTradeAggrid, cycleRepayTermsDialog },
  props: {
    id: {
      type: String,
      default: ''
    },
    formData: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      dialogVisible: false,
      rowData: [],
      initsts: true,
      activeCollapseName: ['1', '2', '3'],
      settlementMethodColumn: [
        {
          field: 'sSettlementType',
          headerName: this.$t('grid.others.beforeAndAfterTheGoods'),
          cellStyle: { textAlign: 'left' },
          valueGetter: (params) => {
            // vSumQty/vSumPkgQty
            return getCnDitc(params, this.options['trade.settlement.type'], 'sSettlementType')
          }
        },
        {
          field: 'sIsRiskTransfer',
          headerName: this.$t('grid.others.whetherRiskTransfer'),
          cellStyle: { textAlign: 'left' },
          valueGetter: params => {
            return getCnDitc(params, this.options['base.yes-no'], 'sIsRiskTransfer')
          }
        },
        {
          field: 'sPaymentType',
          headerName: this.$t('grid.title.paymentMethod'),
          cellStyle: { textAlign: 'left' },
          valueGetter: params => {
            return getCnDitc(params, this.options['trade.pay.mode'], 'sPaymentType')
          }
        },
        {
          field: 'sDateType',
          headerName: this.$t('grid.others.dateMethod'),
          cellStyle: { textAlign: 'left' },
          valueGetter: params => {
            return getCnDitc(params, this.options['trade.date.type'], 'sDateType')
          }
        },
        {
          field: 'sDays',
          headerName: this.$t('grid.others.Days'),
          cellStyle: { textAlign: 'left' }
        },
        {
          field: 'sDateTypeDescribe',
          headerName: this.$t('grid.others.dateMethodDescription'),
          cellStyle: { textAlign: 'left' }
        },
        {
          field: 'sPaymentRate',
          headerName: this.$t('grid.title.paymentRatio'),
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            // return (params.value * 100) + '%'
            return SteelFormat.toPercent(params.value)
          }
        },
        {
          field: 'sTaxAmt',
          headerName: this.$t('grid.others.originalCurrencyAmount'),
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatPrice(params.value, 2)
          }
        }
      ], // 结算方式表头
      columnConfig: [
        {
          field: 'sSheetStatus',
          headerName: this.$t('grid.title.serialNumber'),
          cellStyle: { textAlign: 'left' },
          valueFormatter(params) {
            const node = params.node.childIndex + 1
            return node
          }
        },
        {
          field: 'sField1',
          headerName: this.$t('grid.others.interestBearingItems'),
          cellStyle: { textAlign: 'left' },
          valueGetter: params => {
            return getCnDitc(params, this.options['esc.clause.item'], 'sField1')
          }
        },
        {
          field: 'sField11',
          headerName: this.$t('grid.others.startDays'),
          cellStyle: { textAlign: 'right' }
        },
        {
          field: 'sField12',
          headerName: this.$t('grid.others.cutOffDays'),
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            console.log(params)
            return params.data.sField2 === '1' ? params.value : SteelFormat.formatThousandthSign(params.value)
          }
        },
        // {
        //   field: 'sField13',
        //   headerName: this.$t('grid.others.numberOfDays'),
        //   cellStyle: { textAlign: 'right' }
        // },
        {
          field: 'sField2',
          headerName: this.$t('grid.others.whetherUnlimited'),
          cellStyle: { textAlign: 'left' },
          valueGetter: params => {
            return getCnDitc(params, this.options['base.yes-no'], 'sField2')
          }
        },
        {
          field: 'sField26',
          headerName: '截止日期',
          cellStyle: { textAlign: 'left' },
          valueGetter: params => {
            return Moment.time('YYYY-MM-DD ', params.data.sField26)
          }
        },
        {
          field: 'sField6',
          headerName: this.$t('grid.others.interestRateTag'),
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            if (params.value) {
              const Digits = String(params.value).length - (String(params.value).indexOf('.') + 1)
              if (Digits > 3) {
                return SteelFormat.toPercent(params.value)
              } else {
                return SteelFormat.toPercent(params.value, 2)
              }
            } else {
              return ''
            }
          }
        },
        {
          field: 'sField7',
          headerName: this.$t('grid.others.singleTonMarkup'),
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return params.value ? SteelFormat.formatPrice(params.value) : ''
          }
        }
      ],
      columnConfigV2: [
        {
          field: 'sSheetStatus',
          headerName: this.$t('grid.title.serialNumber'),
          cellStyle: { textAlign: 'left' },
          valueFormatter(params) {
            const node = params.node.childIndex + 1
            return node
          }
        },
        {
          field: 'sField1',
          headerName: '结算项目',
          cellStyle: { textAlign: 'left' },
          valueGetter: params => {
            return getCnDitc(params, this.options['esc.clause.item'], 'sField1')
          }
        },
        {
          field: 'sField11',
          headerName: '提前/逾期天数从',
          cellStyle: { textAlign: 'right' }
        },
        {
          field: 'sField12',
          headerName: '提前/逾期天数到',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            console.log(params)
            return params.data.sField2 === '1' ? params.value : SteelFormat.formatThousandthSign(params.value)
          }
        },
        // {
        //   field: 'sField13',
        //   headerName: this.$t('grid.others.numberOfDays'),
        //   cellStyle: { textAlign: 'right' }
        // },
        {
          field: 'sField2',
          headerName: this.$t('grid.others.whetherUnlimited'),
          cellStyle: { textAlign: 'left' },
          valueGetter: params => {
            return getCnDitc(params, this.options['base.yes-no'], 'sField2')
          }
        },
        {
          field: 'sField6',
          headerName: '费率',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            if (params.value) {
              const Digits = String(params.value).length - (String(params.value).indexOf('.') + 1)
              if (Digits > 3) {
                return SteelFormat.toPercent(params.value)
              } else {
                return SteelFormat.toPercent(params.value, 2)
              }
            } else {
              return ''
            }
          }
        }
      ],
      settlementTerms: null,
      options: {
        'trade.settlement.type': '',
        'trade.pay.mode': '',
        'esc.clause.item': '',
        'base.yes-no': [],
        'esc.settle.interest.start': null,
        'trade.irt.return.type': null,
        'trade.date.type': '',
        'esc.stopirt.type': '',
        'esc.rebate.type': []
      },
      settlementDetailColumn: this.columnConfig,
      form: {}, // 结算条款
      rowDataSettlementDetail: [],
      showFullAmount: false
    }
  },
  computed: {
    showStopIrtType() {
      return this.formData.sRootUnitCode === '00014960' && this.form.sStopIrtType
    },
    showCycleRepayTerms() { // 日期方式存在结算周期
      return this.rowData.some(item => item.sDateType === 'JSZQ')
    }
  },
  beforeCreate() {
  },
  created() {
    getDictList(this.options).then(() => {
      this.$refs.aggrid.loadTableData()
      this.$refs.tableTool.loadTableData()
    })
    this.getSaleSettleClause()
  },
  methods: {
    setAggridVisible() {
      setTimeout(() => {
        this.$refs.tableTools?.columnApi?.setColumnVisible('sField26', this.showStopIrtType && this.form.sStopIrtType === '20')
      }, 0)
    },
    // 结算条款
    getSaleSettleClause() {
      getSaleSettleClause({
        sContractId: this.id
      }).then((res) => {
        this.form = res.data || {}
        this.setAggridVisible()
      })
    },
    loadData(pagination) {
      console.log(pagination)
      return new Promise((resolve, reject) => {
        getSettlementDetail({ sContractId: this.id, ...pagination }).then(res => {
          this.rowData = res.data.content.map(item => {
            item._selected = false
            return item
          })
          setTimeout(() => {
            this.$refs.tableTool?.gridApi?.setPinnedBottomRowData([this.computeTotal()])
          }, 0)
          resolve(res.data)
        }).catch(() => {
          reject([])
        })
      })
    },
    loadDataSettlementDetail(pagination) {
      return new Promise((resolve, reject) => {
        getSaleSettleClauseDetail({
          classifyId: '136111111111111',
          sUpId: this.id,
          ...pagination
        }).then(res => {
          this.rowDataSettlementDetail = res.data.content.map(item => {
            item._selected = false
            return item
          })
          this.showFullAmount = this.rowDataSettlementDetail.some(item => item.sField1 && ['40', '50', '60'].includes(item.sField1))
          if (this.showFullAmount === true) {
            this.$refs.aggrid.setColumnDefs(this.columnConfigV2)
          } else {
            this.$refs.aggrid.setColumnDefs(this.columnConfig)
          }
          resolve(res.data)
        }).catch(() => {
          reject([])
        })
      })
    },
    computeTotal() {
      let sTaxAmt = 0
      this.rowData.forEach(el => {
        sTaxAmt += el.sTaxAmt
      })
      const obj = {
        sSettlementType: this.$t('grid.others.amountTo'),
        sTaxAmt,
        _selected: false
      }
      return obj
    },
    refresh() {
      this.$refs.tableTool.query(true)
      this.$refs.tableTools.query(true)
    },
    inifs() {
      this.refresh()
      this.getSaleSettleClause()
      this.initsts = false
      this.initsts = true
    },
    viewCycleRepayTerms() {
      this.dialogVisible = true
    }
  }
}
</script>
<style>
.tableborder .padding-show .el-collapse-item .el-collapse-item__content {
    padding:  0;
}
.tableborder .el-collapse-item .el-collapse-item__header.is-active{
  border-bottom: 0;
}
</style>
<style scoped>
 ::v-deep .padding-show .el-collapse-item .el-collapse-item__content {
    padding: 0;
}
</style>
