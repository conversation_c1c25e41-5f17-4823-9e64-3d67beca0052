# just a flag
NODE_ENV = 'development'

# base api
VUE_APP_BASE_API = '/api'
VUE_APP_CUSTOM_API='/custom-api'
# Sub-application-addr
VUE_APP_SYSTEM_URL = '//localhost:7002'
VUE_APP_TEXTILE_URL = '//localhost:7003'
VUE_APP_SCM_URL = '//localhost:7004'
VUE_APP_MALLADMIN_URL = '//localhost:7005'
VUE_APP_EDITOR_URL = '//localhost:7006'
VUE_APP_WIIS_URL = '//localhost:7007'
VUE_APP_EGL_URL = '//localhost:7008'
VUE_APP_MALL-MANAGE_URL = '//localhost:7009'
VUE_APP_AUTO_QUERY = true
# websocket地址
VUE_APP_SOCKET_URL = 'ws://*************:8080/esc'

# vue-cli uses the VUE_CLI_BABEL_TRANSPILE_MODULES environment variable,
# to control whether the babel-plugin-dynamic-import-node plugin is enabled.
# It only does one thing by converting all import() to require().
# This configuration can significantly increase the speed of hot updates,
# when you have a large number of pages.
# Detail:  https://github.com/vuejs/vue-cli/blob/dev/packages/@vue/babel-preset-app/index.js

VUE_CLI_BABEL_TRANSPILE_MODULES = true
