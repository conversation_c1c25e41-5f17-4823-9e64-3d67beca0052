<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta http-equiv="pragram" content="no-cache" />
    <meta
      http-equiv="cache-control"
      content="no-cache, no-store, must-revalidate"
    />
    <meta name="viewport" content="width=device-width,initial-scale=1.0" />
    <link rel="icon" href="<%= BASE_URL %>horizon.png" />
    <!-- 使用CDN的CSS文件 -->
    <% for (var i in htmlWebpackPlugin.options.cdn &&
    htmlWebpackPlugin.options.cdn.css) { %>
    <link href="<%= htmlWebpackPlugin.options.cdn.css[i] %>" rel="stylesheet" />
    <% } %>
    <!-- 使用CDN的CSS文件 -->
    <!-- 使用CDN的预加载文件 -->
    <!-- <% for (var i in htmlWebpackPlugin.options.cdn && htmlWebpackPlugin.options.cdn.preload) { %> -->
    <!-- <link as="script" rel="preload" href="<%= htmlWebpackPlugin.options.cdn.preload[i] %>" onload="var script = document.createElement('script'); script.src = this.href; document.head.appendChild(script);"> -->
    <!-- <% } %> -->
    <!-- 使用CDN的预加载文件 -->
    <title><%= htmlWebpackPlugin.options.title %></title>
  </head>

  <body>
    <noscript>
      <strong
        >We're sorry but <%= htmlWebpackPlugin.options.title %> doesn't work
        properly without JavaScript enabled. Please enable it to
        continue.</strong
      >
    </noscript>
    <div id="app"></div>
    <!-- 使用CDN的JS文件 -->
    <% for (var i in htmlWebpackPlugin.options.cdn &&
    htmlWebpackPlugin.options.cdn.js) { %>
    <script src="<%= htmlWebpackPlugin.options.cdn.js[i] %>"></script>
    <% } %>
    <!-- <script src="https://front-end-huawei-cdn.devops.cndinfo.com/lang/zh.min.js"></script>
  <script src="https://front-end-huawei-cdn.devops.cndinfo.com/lang/en.min.js"></script> -->
    <!-- 使用CDN的JS文件 -->
  </body>
</html>
