version: "3"
services:
  portal:
    build:
      context: ./
      dockerfile: ./docker/Dockerfile
      args:
        NODE_ENV: testing
        NGINX_PORT: 80
        API_ENV: api
        API_URL: http://172.18.39.200:8080
        WEBAPP: esc
        SYSTEM_URL: 172.18.39.200:3003
        SCM_URL: 172.18.39.200:3007
        EGL_URL: 172.18.39.200:3004
        SOCKET_URL: ws://172.18.39.200:8080/esc
    restart: always
    image: docker.cndinfo.com/egl/portal-test:latest
    volumes:
      - /home/<USER>/releases:/home/<USER>/releases
    ports:
      - 3001:80
    container_name: portal
        networks:
      - app-net
networks:
  app-net:
    external:
      name: app-net

