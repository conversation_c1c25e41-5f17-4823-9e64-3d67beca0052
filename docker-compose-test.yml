version: "3"
services:
  system:
    build:
      context: ./
      dockerfile: ./docker/Dockerfile
      args:
        NODE_ENV: testing
        NGINX_PORT: 80
        API_ENV: api
        API_URL: http://172.18.39.200:8080
        WEBAPP: esc
    restart: always
    image: docker.cndinfo.com/egl/egl-test:latest
    ports:
      - 3004:80
    container_name: egl
    networks:
      - app-net
networks:
  app-net:
    external:
      name: app-net
