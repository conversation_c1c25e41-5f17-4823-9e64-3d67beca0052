version: "3"
services:
  portal:
    build:
      context: ./
      dockerfile: ./docker/Dockerfile
      args:
        NODE_ENV: staging
        NGINX_PORT: 80
        API_ENV: api
        API_URL: http://172.18.64.200:8080
        WEBAPP: escmall
        SYSTEM_URL: 172.18.64.200:3002
        SCM_URL: 172.18.64.200:3003
        EGL_URL: 172.18.64.200:3004
        SOCKET_URL: ws://172.18.64.200:8080/escmall
    restart: always
    image: docker.cndinfo.com/egl/mall-portal-test:latest
    volumes:
      - /home/<USER>/releases:/home/<USER>/releases
    ports:
      - 3001:80
    networks:
      - app-net
networks:
  app-net:
    external:
      name: app-net
